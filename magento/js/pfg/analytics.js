/**
 * PFG Analytics JavaScript
 * 
 * This file contains all JavaScript functionality for the PFG Analytics module
 * including chart rendering, date filtering, and UI interactions.
 */

var PFGAnalytics = {

    // Configuration
    config: {
        chartColors: {
            primary: '#007bff',
            comparison: '#28a745',
            background: 'rgba(0, 123, 255, 0.1)',
            comparisonBackground: 'rgba(40, 167, 69, 0.1)'
        },
        dateFormat: 'YYYY-MM-DD',
        maxDateRange: 730, // 2 years in days
        selectors: {
            filterForm: '#analytics-filter-form',
            chartCanvas: '#analytics-chart',
            statusPills: '.status-pill.main-pill',
            dateInputs: '.date-input',
            quickFilters: '.quick-filter-btn'
        }
    },

    // Chart instance
    chartInstance: null,

    // State management
    state: {
        isInitialized: false,
        currentFilters: {},
        activeDropdown: null
    },

    /**
     * Initialize the analytics module
     */
    init: function() {
        this.initDatePickers();
        this.initQuickFilters();
        this.initFormValidation();
        this.bindEvents();
        this.initStatusPills();
    },

    /**
     * Initialize date picker calendars
     */
    initDatePickers: function() {
        try {
            // Initialize from_date calendar
            if ($('from_date')) {
                Calendar.setup({
                    inputField: 'from_date',
                    ifFormat: '%Y-%m-%d',
                    button: 'from_date_trig',
                    align: 'Bl',
                    singleClick: true,
                    onUpdate: this.validateDateRange.bind(this)
                });
            }

            // Initialize to_date calendar
            if ($('to_date')) {
                Calendar.setup({
                    inputField: 'to_date',
                    ifFormat: '%Y-%m-%d',
                    button: 'to_date_trig',
                    align: 'Bl',
                    singleClick: true,
                    onUpdate: this.validateDateRange.bind(this)
                });
            }
        } catch (error) {
            console.error('Error initializing date pickers:', error);
        }
    },

    /**
     * Initialize quick filter buttons
     */
    initQuickFilters: function() {
        var self = this;
        
        $$('.quick-filter').each(function(button) {
            button.observe('click', function(event) {
                event.preventDefault();
                self.applyQuickFilter(button.getAttribute('data-days'));
            });
        });
    },

    /**
     * Apply quick filter for specified number of days
     */
    applyQuickFilter: function(days) {
        try {
            var today = new Date();
            var fromDate = new Date();
            fromDate.setDate(today.getDate() - parseInt(days));

            if ($('from_date')) {
                $('from_date').value = this.formatDate(fromDate);
            }
            if ($('to_date')) {
                $('to_date').value = this.formatDate(today);
            }

            this.validateDateRange();
        } catch (error) {
            // Silently handle error
        }
    },

    /**
     * Format date for input fields
     */
    formatDate: function(date) {
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        return year + '-' + month + '-' + day;
    },

    /**
     * Validate date range
     */
    validateDateRange: function() {
        try {
            var fromDate = $('from_date') ? $('from_date').value : '';
            var toDate = $('to_date') ? $('to_date').value : '';

            if (fromDate && toDate) {
                var from = new Date(fromDate);
                var to = new Date(toDate);
                var diffTime = Math.abs(to - from);
                var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                // Check if from date is after to date
                if (from > to) {
                    this.showError('From date cannot be later than to date.');
                    return false;
                }

                // Check if date range exceeds maximum
                if (diffDays > this.config.maxDateRange) {
                    this.showError('Date range cannot exceed ' + (this.config.maxDateRange / 365) + ' years.');
                    return false;
                }

                this.clearErrors();
                return true;
            }
        } catch (error) {
            console.error('Error validating date range:', error);
            return false;
        }
    },

    /**
     * Initialize form validation
     */
    initFormValidation: function() {
        var form = $('analytics-filter-form');
        if (form) {
            form.observe('submit', this.validateForm.bind(this));
        }
    },

    /**
     * Validate form before submission
     */
    validateForm: function(event) {
        if (!this.validateDateRange()) {
            event.preventDefault();
            return false;
        }
        return true;
    },

    /**
     * Bind additional events
     */
    bindEvents: function() {
        // Bind comparison mode change
        var comparisonSelect = $('comparison_mode');
        if (comparisonSelect) {
            comparisonSelect.observe('change', this.onComparisonModeChange.bind(this));
        }

        // Bind chart grouping change
        var groupingSelect = $('chart_grouping');
        if (groupingSelect) {
            groupingSelect.observe('change', this.onChartGroupingChange.bind(this));
        }
    },

    /**
     * Handle comparison mode change
     */
    onComparisonModeChange: function() {
        // Auto-submit form when comparison mode changes
        var form = $('analytics-filter-form');
        if (form) {
            form.submit();
        }
    },

    /**
     * Handle chart grouping change
     */
    onChartGroupingChange: function() {
        // Auto-submit form when chart grouping changes
        var form = $('analytics-filter-form');
        if (form) {
            form.submit();
        }
    },

    /**
     * Render chart with given data
     */
    renderChart: function(chartData) {
        try {
            var ctx = $('analytics-chart');
            if (!ctx) {
                return;
            }

            // Destroy existing chart
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }

            var datasets = [];

            // Primary dataset
            if (chartData.primary && chartData.primary.labels) {
                datasets.push({
                    label: 'Current Period',
                    data: chartData.primary.data || [],
                    borderColor: this.config.chartColors.primary,
                    backgroundColor: this.config.chartColors.background,
                    tension: 0.1
                });
            }

            // Comparison dataset
            if (chartData.comparison && chartData.comparison.labels) {
                datasets.push({
                    label: 'Comparison Period',
                    data: chartData.comparison.data || [],
                    borderColor: this.config.chartColors.comparison,
                    backgroundColor: this.config.chartColors.comparisonBackground,
                    tension: 0.1
                });
            }

            // Create chart
            this.chartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.primary ? chartData.primary.labels : [],
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: datasets.length > 1
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });

        } catch (error) {
            this.showError('Failed to render chart. Please try again.');
        }
    },

    /**
     * Show error message
     */
    showError: function(message) {
        var errorContainer = $('analytics-errors');
        if (errorContainer) {
            errorContainer.innerHTML = '<div class="error-msg"><span>' + message + '</span></div>';
            errorContainer.show();
        } else {
            alert(message);
        }
    },

    /**
     * Clear error messages
     */
    clearErrors: function() {
        var errorContainer = $('analytics-errors');
        if (errorContainer) {
            errorContainer.innerHTML = '';
            errorContainer.hide();
        }
    },

    /**
     * Show loading indicator
     */
    showLoading: function() {
        var loadingContainer = $('analytics-loading');
        if (loadingContainer) {
            loadingContainer.show();
        }
    },

    /**
     * Hide loading indicator
     */
    hideLoading: function() {
        var loadingContainer = $('analytics-loading');
        if (loadingContainer) {
            loadingContainer.hide();
        }
    },

    /**
     * Initialize status pill dropdown functionality
     */
    initStatusPills: function() {
        // Close any open dropdowns when clicking outside
        document.observe('click', function(event) {
            var target = event.target || event.srcElement;
            if (!target.closest('.status-pill-container')) {
                $$('.status-breakdown-dropdown').each(function(dropdown) {
                    dropdown.hide();
                });
                $$('.status-pill.main-pill').each(function(pill) {
                    pill.removeClassName('expanded');
                });
            }
        });
    }
};

// Global function for status breakdown toggle (called from template)
function toggleStatusBreakdown(rowId) {
    var dropdown = $(rowId + '-breakdown');
    var pill = $$('.status-pill.main-pill')[0]; // Get the pill that was clicked

    if (dropdown) {
        if (dropdown.visible()) {
            dropdown.hide();
            if (pill) pill.removeClassName('expanded');
        } else {
            // Hide all other dropdowns first
            $$('.status-breakdown-dropdown').each(function(otherDropdown) {
                if (otherDropdown !== dropdown) {
                    otherDropdown.hide();
                }
            });
            $$('.status-pill.main-pill').each(function(otherPill) {
                if (otherPill !== pill) {
                    otherPill.removeClassName('expanded');
                }
            });

            dropdown.show();
            if (pill) pill.addClassName('expanded');
        }
    }
}

// Initialize when DOM is ready
document.observe('dom:loaded', function() {
    PFGAnalytics.init();
});
