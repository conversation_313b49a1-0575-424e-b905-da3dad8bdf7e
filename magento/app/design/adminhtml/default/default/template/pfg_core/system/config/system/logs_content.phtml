<?php
/**
 * PFG Core System Logs Content Template (for AJAX refresh)
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$logs = $this->getLogs();
?>

<?php if (empty($logs)): ?>
    <div class="notice-msg">
        <ul>
            <li>
                <span><?php echo $this->__('No log entries found.') ?></span>
            </li>
        </ul>
    </div>
<?php else: ?>
    <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; background: #f9f9f9;">
        <table cellspacing="0" cellpadding="5" style="width: 100%; background: white; font-size: 11px;">
            <thead>
                <tr style="background: #f0f0f0;">
                    <th style="text-align: left; padding: 6px; border-bottom: 1px solid #ddd; width: 120px;"><?php echo $this->__('Time') ?></th>
                    <th style="text-align: left; padding: 6px; border-bottom: 1px solid #ddd; width: 60px;"><?php echo $this->__('Level') ?></th>
                    <th style="text-align: left; padding: 6px; border-bottom: 1px solid #ddd; width: 80px;"><?php echo $this->__('Type') ?></th>
                    <th style="text-align: left; padding: 6px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Message') ?></th>
                    <th style="text-align: left; padding: 6px; border-bottom: 1px solid #ddd; width: 80px;"><?php echo $this->__('User') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($logs as $log): ?>
                    <tr style="border-bottom: 1px solid #eee;">
                        <td style="padding: 6px; font-size: 10px;">
                            <?php echo $this->formatDate($log['created_at'], 'short', true) ?>
                        </td>
                        <td style="padding: 6px;">
                            <?php
                            $level = $log['level'];
                            $colors = array(
                                PFG_Core_Model_Logger::LOG_LEVEL_EMERGENCY => '#8B0000',
                                PFG_Core_Model_Logger::LOG_LEVEL_ALERT => '#DC143C',
                                PFG_Core_Model_Logger::LOG_LEVEL_CRITICAL => '#FF0000',
                                PFG_Core_Model_Logger::LOG_LEVEL_ERROR => '#df280a',
                                PFG_Core_Model_Logger::LOG_LEVEL_WARNING => '#f18500',
                                PFG_Core_Model_Logger::LOG_LEVEL_NOTICE => '#2f55d4',
                                PFG_Core_Model_Logger::LOG_LEVEL_INFO => '#3d6611',
                                PFG_Core_Model_Logger::LOG_LEVEL_DEBUG => '#666666',
                            );
                            
                            $labels = array(
                                PFG_Core_Model_Logger::LOG_LEVEL_EMERGENCY => 'EMERG',
                                PFG_Core_Model_Logger::LOG_LEVEL_ALERT => 'ALERT',
                                PFG_Core_Model_Logger::LOG_LEVEL_CRITICAL => 'CRIT',
                                PFG_Core_Model_Logger::LOG_LEVEL_ERROR => 'ERROR',
                                PFG_Core_Model_Logger::LOG_LEVEL_WARNING => 'WARN',
                                PFG_Core_Model_Logger::LOG_LEVEL_NOTICE => 'NOTICE',
                                PFG_Core_Model_Logger::LOG_LEVEL_INFO => 'INFO',
                                PFG_Core_Model_Logger::LOG_LEVEL_DEBUG => 'DEBUG',
                            );
                            
                            $color = isset($colors[$level]) ? $colors[$level] : '#666';
                            $label = isset($labels[$level]) ? $labels[$level] : 'UNKNOWN';
                            ?>
                            <span style="color: <?php echo $color ?>; font-weight: bold; font-size: 9px;">
                                <?php echo $this->escapeHtml($label) ?>
                            </span>
                        </td>
                        <td style="padding: 6px; font-size: 10px;">
                            <?php
                            $typeLabels = array(
                                PFG_Core_Model_Logger::LOG_TYPE_SYSTEM => 'SYS',
                                PFG_Core_Model_Logger::LOG_TYPE_SECURITY => 'SEC',
                                PFG_Core_Model_Logger::LOG_TYPE_INSTALLATION => 'INST',
                                PFG_Core_Model_Logger::LOG_TYPE_API => 'API',
                                PFG_Core_Model_Logger::LOG_TYPE_BACKUP => 'BAK',
                            );
                            
                            $typeLabel = isset($typeLabels[$log['type']]) ? $typeLabels[$log['type']] : strtoupper(substr($log['type'], 0, 4));
                            echo $this->escapeHtml($typeLabel);
                            ?>
                        </td>
                        <td style="padding: 6px;">
                            <?php
                            $message = $log['message'];
                            if (strlen($message) > 80) {
                                $truncated = substr($message, 0, 80) . '...';
                                echo sprintf('<span title="%s">%s</span>', 
                                    $this->escapeHtml($message), 
                                    $this->escapeHtml($truncated)
                                );
                            } else {
                                echo $this->escapeHtml($message);
                            }
                            ?>
                        </td>
                        <td style="padding: 6px; font-size: 10px;">
                            <?php
                            if ($log['user_id']) {
                                try {
                                    $user = Mage::getModel('admin/user')->load($log['user_id']);
                                    echo $user->getId() ? $this->escapeHtml($user->getUsername()) : 'Unknown';
                                } catch (Exception $e) {
                                    echo 'Unknown';
                                }
                            } else {
                                echo '<em>System</em>';
                            }
                            ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 10px; text-align: right;">
        <small style="color: #666;">
            <?php echo $this->__('Showing last %d log entries', count($logs)) ?>
        </small>
    </div>
<?php endif; ?>
