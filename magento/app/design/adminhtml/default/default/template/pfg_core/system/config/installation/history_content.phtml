<?php
/**
 * PFG Core Installation History Content Template (for AJAX refresh)
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$installations = $this->getInstallations();
?>

<?php if ($installations->getSize() == 0): ?>
    <div class="notice-msg">
        <ul>
            <li>
                <span><?php echo $this->__('No installation history found.') ?></span>
            </li>
        </ul>
    </div>
<?php else: ?>
    <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; background: #f9f9f9;">
        <table cellspacing="0" cellpadding="5" style="width: 100%; background: white;">
            <thead>
                <tr style="background: #f0f0f0;">
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Module') ?></th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Type') ?></th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Status') ?></th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Version') ?></th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Date') ?></th>
                    <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Actions') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($installations as $installation): ?>
                    <tr style="border-bottom: 1px solid #eee;">
                        <td style="padding: 8px;">
                            <strong><?php echo $this->escapeHtml($installation->getModuleName()) ?></strong>
                            <?php if ($installation->getRepositoryName()): ?>
                                <br><small style="color: #666;"><?php echo $this->escapeHtml($installation->getRepositoryName()) ?></small>
                            <?php endif; ?>
                        </td>
                        <td style="padding: 8px;">
                            <?php
                            $typeLabels = array(
                                PFG_Core_Model_Installation::TYPE_INSTALL => $this->__('Install'),
                                PFG_Core_Model_Installation::TYPE_UPDATE => $this->__('Update'),
                                PFG_Core_Model_Installation::TYPE_ROLLBACK => $this->__('Rollback'),
                                PFG_Core_Model_Installation::TYPE_UNINSTALL => $this->__('Uninstall')
                            );
                            echo isset($typeLabels[$installation->getInstallationType()]) 
                                ? $typeLabels[$installation->getInstallationType()] 
                                : $installation->getInstallationType();
                            ?>
                        </td>
                        <td style="padding: 8px;">
                            <?php
                            $status = $installation->getInstallationStatus();
                            $colors = array(
                                PFG_Core_Model_Installation::STATUS_PENDING => '#f18500',
                                PFG_Core_Model_Installation::STATUS_IN_PROGRESS => '#2f55d4',
                                PFG_Core_Model_Installation::STATUS_COMPLETED => '#3d6611',
                                PFG_Core_Model_Installation::STATUS_FAILED => '#df280a',
                                PFG_Core_Model_Installation::STATUS_ROLLED_BACK => '#eb5202',
                            );
                            
                            $labels = array(
                                PFG_Core_Model_Installation::STATUS_PENDING => $this->__('Pending'),
                                PFG_Core_Model_Installation::STATUS_IN_PROGRESS => $this->__('In Progress'),
                                PFG_Core_Model_Installation::STATUS_COMPLETED => $this->__('Completed'),
                                PFG_Core_Model_Installation::STATUS_FAILED => $this->__('Failed'),
                                PFG_Core_Model_Installation::STATUS_ROLLED_BACK => $this->__('Rolled Back'),
                            );
                            
                            $color = isset($colors[$status]) ? $colors[$status] : '#666';
                            $label = isset($labels[$status]) ? $labels[$status] : $status;
                            ?>
                            <span style="color: <?php echo $color ?>; font-weight: bold; font-size: 11px;">
                                <?php echo $this->escapeHtml($label) ?>
                            </span>
                        </td>
                        <td style="padding: 8px;">
                            <?php echo $this->escapeHtml($installation->getVersionInstalled() ?: '-') ?>
                        </td>
                        <td style="padding: 8px;">
                            <?php echo $this->formatDate($installation->getCreatedAt(), 'short', true) ?>
                        </td>
                        <td style="padding: 8px;">
                            <?php if ($installation->getBackupId() && 
                                     $installation->getInstallationStatus() == PFG_Core_Model_Installation::STATUS_COMPLETED): ?>
                                <button type="button" 
                                        onclick="pfgCoreRollbackInstallation(<?php echo $installation->getId() ?>)" 
                                        class="scalable" 
                                        style="font-size: 11px;"
                                        title="<?php echo $this->__('Rollback this installation') ?>">
                                    <span><span><span><?php echo $this->__('Rollback') ?></span></span></span>
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <div style="margin-top: 10px; text-align: right;">
        <small style="color: #666;">
            <?php echo $this->__('Showing last %d installations', $installations->getSize()) ?>
        </small>
    </div>
<?php endif; ?>
