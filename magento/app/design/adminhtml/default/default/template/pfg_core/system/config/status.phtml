<?php
/**
 * PFG Core Connection Status Template
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$status = $this->getStatus();
?>
<div id="<?php echo $this->getHtmlId() ?>_status" class="pfg-core-status">
    <table cellspacing="0" cellpadding="0" style="width: 100%;">
        <tr>
            <td style="width: 150px; font-weight: bold;"><?php echo $this->__('Module Status:') ?></td>
            <td>
                <?php if ($status['enabled']): ?>
                    <span style="color: #3d6611; font-weight: bold;">✓ <?php echo $this->__('Enabled') ?></span>
                <?php else: ?>
                    <span style="color: #df280a; font-weight: bold;">✗ <?php echo $this->__('Disabled') ?></span>
                <?php endif; ?>
            </td>
        </tr>
        <tr>
            <td style="font-weight: bold;"><?php echo $this->__('Credentials:') ?></td>
            <td>
                <?php if ($status['has_credentials']): ?>
                    <span style="color: #3d6611; font-weight: bold;">✓ <?php echo $this->__('Configured') ?></span>
                <?php else: ?>
                    <span style="color: #df280a; font-weight: bold;">✗ <?php echo $this->__('Not Configured') ?></span>
                <?php endif; ?>
            </td>
        </tr>
        <tr>
            <td style="font-weight: bold;"><?php echo $this->__('Workspace:') ?></td>
            <td><?php echo $this->escapeHtml($status['workspace']) ?></td>
        </tr>
        <tr>
            <td style="font-weight: bold;"><?php echo $this->__('Project:') ?></td>
            <td><?php echo $this->escapeHtml($status['project']) ?></td>
        </tr>
    </table>
    
    <?php if (!$status['enabled']): ?>
        <div style="margin-top: 10px; padding: 10px; background-color: #fcf8e3; border: 1px solid #fbeed5; color: #c09853;">
            <strong><?php echo $this->__('Note:') ?></strong> <?php echo $this->__('Enable the module to use PFG Core functionality.') ?>
        </div>
    <?php elseif (!$status['has_credentials']): ?>
        <div style="margin-top: 10px; padding: 10px; background-color: #fcf8e3; border: 1px solid #fbeed5; color: #c09853;">
            <strong><?php echo $this->__('Note:') ?></strong> <?php echo $this->__('Configure Bitbucket credentials to connect to the repository.') ?>
        </div>
    <?php endif; ?>
</div>

<style type="text/css">
.pfg-core-status table td {
    padding: 5px 10px 5px 0;
    vertical-align: top;
}
</style>
