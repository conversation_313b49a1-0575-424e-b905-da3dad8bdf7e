<?php
/**
 * PFG Core Backup Management Template
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$backups = $this->getBackups();
$ajaxUrls = $this->getAjaxUrls();
$formKey = $this->getFormKey();
?>

<div id="pfg-core-backup-management" style="margin-top: 10px;">
    <div style="margin-bottom: 15px;">
        <button type="button" onclick="pfgCoreCleanupBackups()" class="scalable">
            <span><span><span><?php echo $this->__('Cleanup Old Backups') ?></span></span></span>
        </button>
    </div>
    
    <?php if ($backups->getSize() == 0): ?>
        <div class="notice-msg">
            <ul>
                <li>
                    <span><?php echo $this->__('No backup files found.') ?></span>
                </li>
            </ul>
        </div>
    <?php else: ?>
        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; background: #f9f9f9;">
            <table cellspacing="0" cellpadding="5" style="width: 100%; background: white;">
                <thead>
                    <tr style="background: #f0f0f0;">
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Module') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Type') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Size') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Created') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Actions') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($backups as $backup): ?>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px;">
                                <strong><?php echo $this->escapeHtml($backup->getModuleName()) ?></strong>
                                <?php if ($backup->getDescription()): ?>
                                    <br><small style="color: #666;"><?php echo $this->escapeHtml($backup->getDescription()) ?></small>
                                <?php endif; ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php
                                $typeLabels = array(
                                    'installation' => $this->__('Installation'),
                                    'update' => $this->__('Update'),
                                    'manual' => $this->__('Manual')
                                );
                                echo isset($typeLabels[$backup->getBackupType()]) 
                                    ? $typeLabels[$backup->getBackupType()] 
                                    : $backup->getBackupType();
                                ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php echo $backup->getBackupSize() ? Mage::helper('pfg_core')->formatFileSize($backup->getBackupSize()) : '-' ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php echo $this->formatDate($backup->getCreatedAt(), 'short', true) ?>
                            </td>
                            <td style="padding: 8px;">
                                <button type="button" 
                                        onclick="pfgCoreDownloadBackup(<?php echo $backup->getId() ?>)" 
                                        class="scalable" 
                                        style="font-size: 11px; margin-right: 5px;"
                                        title="<?php echo $this->__('Download backup file') ?>">
                                    <span><span><span><?php echo $this->__('Download') ?></span></span></span>
                                </button>
                                
                                <button type="button" 
                                        onclick="pfgCoreRestoreBackup(<?php echo $backup->getId() ?>)" 
                                        class="scalable" 
                                        style="font-size: 11px;"
                                        title="<?php echo $this->__('Restore from this backup') ?>">
                                    <span><span><span><?php echo $this->__('Restore') ?></span></span></span>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div style="margin-top: 10px; text-align: right;">
            <small style="color: #666;">
                <?php echo $this->__('Showing last %d backups', $backups->getSize()) ?>
            </small>
        </div>
    <?php endif; ?>
</div>

<script type="text/javascript">
//<![CDATA[
function pfgCoreDownloadBackup(backupId) {
    window.location.href = '<?php echo $ajaxUrls['download_backup'] ?>?id=' + backupId + '&form_key=<?php echo $formKey ?>';
}

function pfgCoreRestoreBackup(backupId) {
    if (!confirm('<?php echo $this->__('Are you sure you want to restore from this backup? This will overwrite current files and cannot be undone.') ?>')) {
        return;
    }
    
    var button = event.target;
    var originalText = button.innerHTML;
    button.innerHTML = '<span><span><span><?php echo $this->__('Restoring...') ?></span></span></span>';
    button.disabled = true;
    
    new Ajax.Request('<?php echo $ajaxUrls['restore_backup'] ?>', {
        method: 'post',
        parameters: {
            'form_key': '<?php echo $formKey ?>',
            'id': backupId
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                if (result.success) {
                    alert('<?php echo $this->__('Backup restored successfully') ?>');
                    // Refresh the page to show updated status
                    window.location.reload();
                } else {
                    alert('<?php echo $this->__('Restore failed: ') ?>' + (result.error || '<?php echo $this->__('Unknown error') ?>'));
                }
            } catch (e) {
                alert('<?php echo $this->__('Invalid response from server') ?>');
            }
        },
        onFailure: function() {
            alert('<?php echo $this->__('Restore request failed') ?>');
        },
        onComplete: function() {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    });
}

function pfgCoreCleanupBackups() {
    if (!confirm('<?php echo $this->__('Are you sure you want to cleanup old backup files? This will remove backups older than the retention period.') ?>')) {
        return;
    }
    
    var button = event.target;
    var originalText = button.innerHTML;
    button.innerHTML = '<span><span><span><?php echo $this->__('Cleaning up...') ?></span></span></span>';
    button.disabled = true;
    
    new Ajax.Request('<?php echo $ajaxUrls['cleanup_backups'] ?>', {
        method: 'post',
        parameters: {
            'form_key': '<?php echo $formKey ?>'
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                if (result.success) {
                    alert('<?php echo $this->__('Cleanup completed successfully') ?>');
                    // Refresh the page to show updated list
                    window.location.reload();
                } else {
                    alert('<?php echo $this->__('Cleanup failed: ') ?>' + (result.error || '<?php echo $this->__('Unknown error') ?>'));
                }
            } catch (e) {
                alert('<?php echo $this->__('Invalid response from server') ?>');
            }
        },
        onFailure: function() {
            alert('<?php echo $this->__('Cleanup request failed') ?>');
        },
        onComplete: function() {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    });
}
//]]>
</script>
