<?php
/**
 * PFG Alt Currency Performance Status Template
 * 
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */

$metrics = $this->getPerformanceMetrics();
$systemStatus = $this->getSystemStatus();
$formattedMetrics = $this->formatMetrics($metrics);
?>

<div id="<?php echo $this->getHtmlId() ?>" class="pfg-altcurrency-performance">
    <!-- System Status Section -->
    <div class="status-section">
        <h4>System Status</h4>
        <table class="form-list" style="width: 100%;">
            <tr>
                <td class="label">Module Status:</td>
                <td class="value">
                    <span class="status-indicator <?php echo $systemStatus['module_enabled'] ? 'enabled' : 'disabled' ?>">
                        <?php echo $systemStatus['module_enabled'] ? '✓ Enabled' : '✗ Disabled' ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="label">Configuration:</td>
                <td class="value">
                    <span class="status-indicator <?php echo $systemStatus['configuration_valid'] ? 'valid' : 'invalid' ?>">
                        <?php echo $systemStatus['configuration_valid'] ? '✓ Valid' : '✗ Invalid' ?>
                    </span>
                    <?php if (!$systemStatus['configuration_valid'] && isset($systemStatus['configuration_errors'])): ?>
                        <div class="config-errors">
                            <?php foreach ($systemStatus['configuration_errors'] as $error): ?>
                                <div class="error-item">• <?php echo $this->escapeHtml($error) ?></div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td class="label">Log File:</td>
                <td class="value">
                    <span class="status-indicator <?php echo $systemStatus['log_file_writable'] ? 'writable' : 'not-writable' ?>">
                        <?php echo $systemStatus['log_file_writable'] ? '✓ Writable' : '✗ Not Writable' ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="label">Cache Status:</td>
                <td class="value">
                    <span class="status-indicator <?php echo $systemStatus['cache_enabled'] ? 'enabled' : 'disabled' ?>">
                        <?php echo $systemStatus['cache_enabled'] ? '✓ Enabled' : '⚠ Disabled' ?>
                    </span>
                </td>
            </tr>
        </table>
    </div>

    <!-- Performance Metrics Section -->
    <div class="metrics-section" style="margin-top: 20px;">
        <h4>Performance Metrics 
            <button type="button" class="scalable" onclick="pfgAltCurrencyRefreshMetrics()" style="float: right; font-size: 11px;">
                <span>Refresh</span>
            </button>
        </h4>
        <table class="form-list" style="width: 100%;">
            <tr>
                <td class="label">Total Calls:</td>
                <td class="value" id="metric-total-calls"><?php echo $formattedMetrics['total_calls'] ?></td>
            </tr>
            <tr>
                <td class="label">Processed Elements:</td>
                <td class="value" id="metric-processed-elements"><?php echo $formattedMetrics['processed_elements'] ?></td>
            </tr>
            <tr>
                <td class="label">Cache Hits:</td>
                <td class="value" id="metric-cache-hits"><?php echo $formattedMetrics['cache_hits'] ?></td>
            </tr>
            <tr>
                <td class="label">Processing Time:</td>
                <td class="value" id="metric-processing-time"><?php echo $formattedMetrics['processing_time'] ?></td>
            </tr>
            <tr>
                <td class="label">Memory Usage:</td>
                <td class="value" id="metric-memory-usage"><?php echo $formattedMetrics['memory_usage'] ?></td>
            </tr>
            <tr>
                <td class="label">Last Updated:</td>
                <td class="value" id="metric-last-updated"><?php echo $formattedMetrics['last_updated'] ?></td>
            </tr>
        </table>
    </div>

    <!-- System Information Section -->
    <div class="system-info-section" style="margin-top: 20px;">
        <h4>System Information</h4>
        <table class="form-list" style="width: 100%;">
            <tr>
                <td class="label">Memory Limit:</td>
                <td class="value"><?php echo $systemStatus['memory_limit'] ?></td>
            </tr>
            <tr>
                <td class="label">Max Execution Time:</td>
                <td class="value"><?php echo $systemStatus['max_execution_time'] ?>s</td>
            </tr>
        </table>
    </div>
</div>

<script type="text/javascript">
//<![CDATA[
function pfgAltCurrencyRefreshMetrics() {
    var button = event.target;
    var originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = 'Refreshing...';
    
    new Ajax.Request('<?php echo $this->getRefreshUrl() ?>', {
        method: 'post',
        parameters: {'form_key': FORM_KEY},
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                if (result.success) {
                    // Refresh the metrics display
                    pfgAltCurrencyLoadMetrics();
                } else {
                    alert('Failed to refresh metrics: ' + result.message);
                }
            } catch (e) {
                alert('Error refreshing metrics');
            }
        },
        onFailure: function() {
            alert('Failed to refresh metrics');
        },
        onComplete: function() {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    });
}

function pfgAltCurrencyLoadMetrics() {
    new Ajax.Request('<?php echo $this->getAjaxUrl() ?>', {
        method: 'post',
        parameters: {'form_key': FORM_KEY},
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                if (result.success && result.data.metrics) {
                    var metrics = result.data.metrics;
                    $('metric-total-calls').innerHTML = metrics.total_calls;
                    $('metric-processed-elements').innerHTML = metrics.processed_elements;
                    $('metric-cache-hits').innerHTML = metrics.cache_hits;
                    $('metric-processing-time').innerHTML = metrics.processing_time;
                    $('metric-memory-usage').innerHTML = metrics.memory_usage;
                    $('metric-last-updated').innerHTML = metrics.last_updated;
                }
            } catch (e) {
                console.log('Error loading metrics: ' + e.message);
            }
        }
    });
}

// Auto-refresh metrics every 30 seconds
setInterval(pfgAltCurrencyLoadMetrics, 30000);
//]]>
</script>

<style type="text/css">
.pfg-altcurrency-performance {
    border: 1px solid #ddd;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.pfg-altcurrency-performance h4 {
    margin: 0 0 10px 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.pfg-altcurrency-performance .form-list td.label {
    width: 200px;
    font-weight: bold;
    color: #666;
}

.pfg-altcurrency-performance .status-indicator.enabled,
.pfg-altcurrency-performance .status-indicator.valid,
.pfg-altcurrency-performance .status-indicator.writable {
    color: #3d6611;
}

.pfg-altcurrency-performance .status-indicator.disabled,
.pfg-altcurrency-performance .status-indicator.invalid,
.pfg-altcurrency-performance .status-indicator.not-writable {
    color: #df280a;
}

.pfg-altcurrency-performance .config-errors {
    margin-top: 5px;
    padding: 5px;
    background-color: #ffeaea;
    border: 1px solid #f5c6cb;
    border-radius: 3px;
}

.pfg-altcurrency-performance .error-item {
    color: #721c24;
    font-size: 11px;
}
</style>
