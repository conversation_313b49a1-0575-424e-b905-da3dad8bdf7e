<?php
/**
 * PFG Alt Currency Test Configuration Template
 * 
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
?>
<button id="<?php echo $this->getHtmlId() ?>" type="button" class="scalable" onclick="pfgAltCurrencyTestConfig()">
    <span><span><span><?php echo $this->escapeHtml($this->getButtonLabel()) ?></span></span></span>
</button>

<div id="pfg_altcurrency_test_result" style="margin-top: 10px; display: none;">
    <div id="pfg_altcurrency_test_message"></div>
    <div id="pfg_altcurrency_test_examples" style="margin-top: 10px; display: none;">
        <h4>Conversion Examples:</h4>
        <table class="form-list" style="width: 100%;">
            <thead>
                <tr>
                    <th>Original Price</th>
                    <th>Converted Price</th>
                    <th>Rate Applied</th>
                </tr>
            </thead>
            <tbody id="pfg_altcurrency_examples_body">
            </tbody>
        </table>
    </div>
</div>

<script type="text/javascript">
//<![CDATA[
function pfgAltCurrencyTestConfig() {
    var button = $('<?php echo $this->getHtmlId() ?>');
    var resultDiv = $('pfg_altcurrency_test_result');
    var messageDiv = $('pfg_altcurrency_test_message');
    var examplesDiv = $('pfg_altcurrency_test_examples');
    var examplesBody = $('pfg_altcurrency_examples_body');
    
    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<span><span><span>Testing...</span></span></span>';
    
    // Hide previous results
    resultDiv.style.display = 'none';
    examplesDiv.style.display = 'none';
    
    // Get current form values
    var formData = {
        'form_key': FORM_KEY,
        'enabled': getFieldValue('pfg_altcurrency_settings_enabled'),
        'symbol': getFieldValue('pfg_altcurrency_settings_symbol'),
        'rate': getFieldValue('pfg_altcurrency_settings_rate')
    };
    
    new Ajax.Request('<?php echo $this->getAjaxUrl() ?>', {
        method: 'post',
        parameters: formData,
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                resultDiv.style.display = 'block';
                
                if (result.success) {
                    messageDiv.className = 'success-msg';
                    messageDiv.innerHTML = '<ul><li><span>' + result.message + '</span></li></ul>';
                    
                    // Show conversion examples
                    if (result.data && result.data.examples) {
                        displayConversionExamples(result.data.examples);
                        examplesDiv.style.display = 'block';
                    }
                } else {
                    messageDiv.className = 'error-msg';
                    messageDiv.innerHTML = '<ul><li><span>' + result.message + '</span></li></ul>';
                }
            } catch (e) {
                messageDiv.className = 'error-msg';
                messageDiv.innerHTML = '<ul><li><span>Invalid response from server</span></li></ul>';
                resultDiv.style.display = 'block';
            }
        },
        onFailure: function() {
            messageDiv.className = 'error-msg';
            messageDiv.innerHTML = '<ul><li><span>Configuration test failed</span></li></ul>';
            resultDiv.style.display = 'block';
        },
        onComplete: function() {
            // Re-enable button
            button.disabled = false;
            button.innerHTML = '<span><span><span><?php echo $this->escapeHtml($this->getButtonLabel()) ?></span></span></span>';
        }
    });
}

function getFieldValue(fieldId) {
    var field = $(fieldId);
    if (field) {
        if (field.type === 'checkbox' || field.type === 'radio') {
            return field.checked ? field.value : '';
        } else if (field.tagName === 'SELECT') {
            return field.options[field.selectedIndex].value;
        } else {
            return field.value;
        }
    }
    return '';
}

function displayConversionExamples(examples) {
    var tbody = $('pfg_altcurrency_examples_body');
    tbody.innerHTML = '';
    
    examples.each(function(example) {
        var row = document.createElement('tr');
        row.innerHTML = 
            '<td>$' + example.original + '</td>' +
            '<td>' + example.symbol + ' ' + example.converted + '</td>' +
            '<td>' + example.rate + '</td>';
        tbody.appendChild(row);
    });
}
//]]>
</script>

<style type="text/css">
#pfg_altcurrency_test_examples table {
    border-collapse: collapse;
    margin-top: 10px;
}

#pfg_altcurrency_test_examples th,
#pfg_altcurrency_test_examples td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

#pfg_altcurrency_test_examples th {
    background-color: #f5f5f5;
    font-weight: bold;
}

#pfg_altcurrency_test_examples tr:nth-child(even) {
    background-color: #f9f9f9;
}
</style>
