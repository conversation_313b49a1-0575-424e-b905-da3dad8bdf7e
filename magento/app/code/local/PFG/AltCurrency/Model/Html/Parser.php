<?php
/**
 * PFG Alt Currency HTML Parser
 *
 * Secure HTML parsing class that replaces complex regex patterns
 * with safer, more maintainable parsing logic to prevent ReDoS attacks
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Model_Html_Parser extends Mage_Core_Model_Abstract
{
    /**
     * Price class patterns for detection
     * @var array
     */
    protected $_priceClasses = array(
        'price',
        'regular-price',
        'special-price',
        'old-price',
        'price-excluding-tax',
        'price-including-tax',
        'minimal-price',
        'configured-price'
    );
    
    /**
     * Maximum HTML length to process (security limit)
     */
    const MAX_HTML_LENGTH = 1048576; // 1MB
    
    /**
     * Maximum processing time in seconds
     */
    const MAX_PROCESSING_TIME = 5;
    
    /**
     * Check if HTML contains price elements using safe string operations
     *
     * @param string $html
     * @return bool
     */
    public function hasPriceElements($html)
    {
        // Security check: limit HTML size
        if (strlen($html) > self::MAX_HTML_LENGTH) {
            Mage::helper('altcurrency')->log('HTML too large for processing', 'warning', array(
                'size' => strlen($html),
                'limit' => self::MAX_HTML_LENGTH
            ));
            return false;
        }
        
        // Quick check for price-box
        if (strpos($html, 'price-box') !== false) {
            return true;
        }
        
        // Check for price classes using safe string operations
        foreach ($this->_priceClasses as $class) {
            if ($this->_hasClass($html, $class)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Extract price-box divs using safe parsing
     *
     * @param string $html
     * @return array
     */
    public function extractPriceBoxDivs($html)
    {
        $startTime = microtime(true);
        $priceBoxes = array();
        
        try {
            // Use DOMDocument for safe HTML parsing
            $dom = new DOMDocument();
            $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
            
            $xpath = new DOMXPath($dom);
            $nodes = $xpath->query('//div[contains(@class, "price-box")]');
            
            foreach ($nodes as $node) {
                // Check processing time limit
                if (microtime(true) - $startTime > self::MAX_PROCESSING_TIME) {
                    Mage::helper('altcurrency')->log('HTML parsing timeout reached', 'warning');
                    break;
                }
                
                $priceBoxes[] = $dom->saveHTML($node);
            }
            
        } catch (Exception $e) {
            // Fallback to string-based parsing if DOM fails
            Mage::helper('altcurrency')->log('DOM parsing failed, using fallback: ' . $e->getMessage(), 'warning');
            $priceBoxes = $this->_extractPriceBoxesFallback($html);
        }
        
        return $priceBoxes;
    }
    
    /**
     * Extract price spans using safe parsing
     *
     * @param string $html
     * @return array
     */
    public function extractPriceSpans($html)
    {
        $startTime = microtime(true);
        $priceSpans = array();
        
        try {
            $dom = new DOMDocument();
            $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
            
            $xpath = new DOMXPath($dom);
            
            // Build XPath query for price classes
            $classConditions = array();
            foreach ($this->_priceClasses as $class) {
                $classConditions[] = 'contains(@class, "' . $class . '")';
            }
            $xpathQuery = '//span[' . implode(' or ', $classConditions) . ']';
            
            $nodes = $xpath->query($xpathQuery);
            
            foreach ($nodes as $node) {
                // Check processing time limit
                if (microtime(true) - $startTime > self::MAX_PROCESSING_TIME) {
                    Mage::helper('altcurrency')->log('Price span parsing timeout reached', 'warning');
                    break;
                }
                
                $priceSpans[] = array(
                    'html' => $dom->saveHTML($node),
                    'text' => $node->textContent,
                    'class' => $node->getAttribute('class')
                );
            }
            
        } catch (Exception $e) {
            // Fallback to string-based parsing if DOM fails
            Mage::helper('altcurrency')->log('DOM price span parsing failed, using fallback: ' . $e->getMessage(), 'warning');
            $priceSpans = $this->_extractPriceSpansFallback($html);
        }
        
        return $priceSpans;
    }
    
    /**
     * Check if HTML contains a specific class using safe string operations
     *
     * @param string $html
     * @param string $class
     * @return bool
     */
    protected function _hasClass($html, $class)
    {
        // Check for exact class matches
        $patterns = array(
            'class="' . $class . '"',
            'class="' . $class . ' ',
            'class=" ' . $class . '"',
            ' ' . $class . '"',
            ' ' . $class . ' '
        );
        
        foreach ($patterns as $pattern) {
            if (strpos($html, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Fallback method for extracting price boxes using simple string operations
     *
     * @param string $html
     * @return array
     */
    protected function _extractPriceBoxesFallback($html)
    {
        $priceBoxes = array();
        $offset = 0;
        
        while (($pos = strpos($html, 'price-box', $offset)) !== false) {
            // Find the start of the div tag
            $divStart = strrpos(substr($html, 0, $pos), '<div');
            if ($divStart === false) {
                $offset = $pos + 1;
                continue;
            }
            
            // Find the end of the div tag
            $divEnd = $this->_findMatchingClosingTag($html, $divStart, 'div');
            if ($divEnd === false) {
                $offset = $pos + 1;
                continue;
            }
            
            $priceBoxes[] = substr($html, $divStart, $divEnd - $divStart + 6); // +6 for </div>
            $offset = $divEnd + 1;
        }
        
        return $priceBoxes;
    }
    
    /**
     * Fallback method for extracting price spans
     *
     * @param string $html
     * @return array
     */
    protected function _extractPriceSpansFallback($html)
    {
        $priceSpans = array();
        
        foreach ($this->_priceClasses as $class) {
            $offset = 0;
            while (($pos = strpos($html, $class, $offset)) !== false) {
                // Find the start of the span tag
                $spanStart = strrpos(substr($html, 0, $pos), '<span');
                if ($spanStart === false) {
                    $offset = $pos + 1;
                    continue;
                }
                
                // Find the end of the span tag
                $spanEnd = strpos($html, '</span>', $spanStart);
                if ($spanEnd === false) {
                    $offset = $pos + 1;
                    continue;
                }
                
                $spanHtml = substr($html, $spanStart, $spanEnd - $spanStart + 7); // +7 for </span>
                $priceSpans[] = array(
                    'html' => $spanHtml,
                    'text' => strip_tags($spanHtml),
                    'class' => $class
                );
                
                $offset = $spanEnd + 1;
            }
        }
        
        return $priceSpans;
    }
    
    /**
     * Find matching closing tag for a given opening tag
     *
     * @param string $html
     * @param int $startPos
     * @param string $tagName
     * @return int|false
     */
    protected function _findMatchingClosingTag($html, $startPos, $tagName)
    {
        $openCount = 1;
        $pos = $startPos;
        
        while ($openCount > 0 && $pos < strlen($html)) {
            $nextOpen = strpos($html, '<' . $tagName, $pos + 1);
            $nextClose = strpos($html, '</' . $tagName . '>', $pos + 1);
            
            if ($nextClose === false) {
                return false;
            }
            
            if ($nextOpen !== false && $nextOpen < $nextClose) {
                $openCount++;
                $pos = $nextOpen;
            } else {
                $openCount--;
                $pos = $nextClose;
            }
        }
        
        return $openCount === 0 ? $pos : false;
    }
}
