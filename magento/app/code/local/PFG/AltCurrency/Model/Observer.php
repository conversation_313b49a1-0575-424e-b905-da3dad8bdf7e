<?php
/**
 * PFG Alt Currency Observer
 *
 * Handles price conversion and display for alternative currency with comprehensive
 * error handling, performance optimization, and security features.
 *
 * This observer listens to the 'core_block_abstract_to_html_after' event and processes
 * HTML output to add alternative currency prices alongside original prices.
 *
 * Key Features:
 * - Secure HTML parsing using DOMDocument with regex fallbacks
 * - Performance tracking and optimization
 * - Memory leak prevention through hash cleanup
 * - Comprehensive error handling with structured exceptions
 * - Configuration validation and caching
 * - ReDoS attack prevention through safe parsing methods
 *
 * Security Measures:
 * - HTML size limits to prevent DoS attacks
 * - Processing time limits to prevent infinite loops
 * - Input sanitization and validation
 * - Safe regex patterns with complexity limits
 *
 * Performance Optimizations:
 * - Configuration value caching
 * - Hash-based deduplication with cleanup
 * - Early exit conditions for non-relevant content
 * - Optimized HTML element detection
 * - Memory usage tracking and limits
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 *
 * @method PFG_AltCurrency_Helper_Data _getHelper() Get helper instance
 * @method PFG_AltCurrency_Model_Html_Parser _getHtmlParser() Get HTML parser instance
 * @method array _getConfiguration() Get cached configuration values
 * @method bool _isModuleEnabled() Check if module is enabled with caching
 * @method bool _shouldSkipProcessing(string $html) Determine if processing should be skipped
 * @method bool _hasPriceElementsOptimized(string $html) Optimized price element detection
 * @method string _processHtmlSafely(string $html, float $rate, string $symbol) Process HTML safely
 * @method void _trackPerformanceMetrics(float $startTime, int $startMemory) Track performance
 * @method void _savePerformanceMetricsToCache() Save metrics to cache
 * @method void _cleanupHashCache() Cleanup hash cache to prevent memory leaks
 */
class PFG_AltCurrency_Model_Observer
{
    /**
     * Cache for processed hashes to prevent duplicate processing
     * @var array
     */
    protected static $_processedHashes = array();

    /**
     * Configuration cache to avoid repeated database calls
     * @var array
     */
    protected $_configCache = null;

    /**
     * Hash cleanup counter to prevent memory leaks
     * @var int
     */
    protected static $_hashCleanupCounter = 0;

    /**
     * Hash cleanup threshold
     */
    const HASH_CLEANUP_THRESHOLD = 1000;

    /**
     * Helper instance cache
     * @var PFG_AltCurrency_Helper_Data
     */
    protected $_helper = null;

    /**
     * Performance metrics tracking
     * @var array
     */
    protected static $_performanceMetrics = array(
        'total_calls' => 0,
        'processed_elements' => 0,
        'cache_hits' => 0,
        'processing_time' => 0,
        'memory_usage' => 0
    );

    /**
     * Block type cache for performance optimization
     * @var array
     */
    protected static $_blockTypeCache = array();

    /**
     * Regex pattern cache to avoid recompilation
     * @var array
     */
    protected static $_regexCache = array();

    /**
     * Main method to append converted prices to HTML output
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function appendConvertedPrice(Varien_Event_Observer $observer)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        try {
            // Increment performance counter
            self::$_performanceMetrics['total_calls']++;

            // Get helper instance
            $helper = $this->_getHelper();

            // Check if module is enabled (with caching)
            if (!$this->_isModuleEnabled()) {
                return;
            }

            $transport = $observer->getTransport();
            $html = $transport->getHtml();

            // Early exit optimizations
            if ($this->_shouldSkipProcessing($html)) {
                return;
            }

            // Validate configuration (cached)
            $config = $this->_getConfiguration();
            if (!$config['valid']) {
                $helper->log('Invalid configuration, skipping price conversion', 'warning', array(
                    'errors' => $config['errors']
                ));
                return;
            }

            // Optimized price element detection
            if (!$this->_hasPriceElementsOptimized($html)) {
                // Debug: Log when we skip processing
                if (strlen($html) > 50 && (strpos($html, '$') !== false || strpos($html, 'product') !== false)) {
                    $this->_getHelper()->log('Skipped HTML processing - no price elements detected', 'debug', array(
                        'html_preview' => substr($html, 0, 200),
                        'html_length' => strlen($html),
                        'contains_dollar' => strpos($html, '$') !== false,
                        'contains_product' => strpos($html, 'product') !== false
                    ));
                }
                return;
            }

            // Process HTML with performance tracking
            $processedHtml = $this->_processHtmlSafely($html, $config['rate'], $config['symbol']);
            $transport->setHtml($processedHtml);

            // Cleanup hash cache periodically
            $this->_cleanupHashCache();

            // Track performance metrics
            $this->_trackPerformanceMetrics($startTime, $startMemory);

        } catch (Exception $e) {
            $this->_getHelper()->log('Error in appendConvertedPrice: ' . $e->getMessage(), 'error', array(
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'performance_metrics' => self::$_performanceMetrics
            ));
            // Don't break the page - return original HTML
        }
    }

    /**
     * Get helper instance
     *
     * @return PFG_AltCurrency_Helper_Data
     */
    protected function _getHelper()
    {
        if ($this->_helper === null) {
            $this->_helper = Mage::helper('altcurrency');
        }
        return $this->_helper;
    }

    /**
     * Get HTML parser instance
     *
     * @return PFG_AltCurrency_Model_Html_Parser
     */
    protected function _getHtmlParser()
    {
        return Mage::getModel('pfg_altcurrency/html_parser');
    }

    /**
     * Get and cache configuration values
     *
     * @return array
     */
    protected function _getConfiguration()
    {
        if ($this->_configCache === null) {
            $this->_configCache = $this->_getHelper()->validateConfiguration();
        } else {
            self::$_performanceMetrics['cache_hits']++;
        }
        return $this->_configCache;
    }

    /**
     * Check if module is enabled with caching
     *
     * @return bool
     */
    protected function _isModuleEnabled()
    {
        static $enabled = null;

        if ($enabled === null) {
            $enabled = $this->_getHelper()->isEnabled();
        }

        return $enabled;
    }

    /**
     * Optimized method to determine if processing should be skipped
     *
     * @param string $html
     * @return bool
     */
    protected function _shouldSkipProcessing($html)
    {
        // Quick length check
        if (empty($html) || strlen($html) < 20) {
            return true;
        }

        // Check if already processed
        if (strpos($html, 'alt-currency-box') !== false) {
            return true;
        }

        // Skip if no HTML tags (plain text)
        if (strpos($html, '<') === false) {
            return true;
        }

        return false;
    }

    /**
     * Performance-optimized price element detection using HTML parser
     *
     * @param string $html
     * @return bool
     */
    protected function _hasPriceElementsOptimized($html)
    {
        try {
            $parser = $this->_getHtmlParser();
            return $parser->hasPriceElements($html);
        } catch (Exception $e) {
            // Fallback to simple string detection if parser fails
            $this->_getHelper()->log('HTML parser failed, using fallback detection: ' . $e->getMessage(), 'warning');
            return $this->_hasPriceElementsFallback($html);
        }
    }

    /**
     * Fallback price element detection using simple string operations
     *
     * @param string $html
     * @return bool
     */
    protected function _hasPriceElementsFallback($html)
    {
        $pricePatterns = array(
            'price-box',
            'class="price"',
            'class="regular-price"',
            'class="special-price"',
            'class="old-price"'
        );

        foreach ($pricePatterns as $pattern) {
            if (strpos($html, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Track performance metrics for monitoring
     *
     * @param float $startTime
     * @param int $startMemory
     */
    protected function _trackPerformanceMetrics($startTime, $startMemory)
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        self::$_performanceMetrics['processing_time'] += ($endTime - $startTime);
        self::$_performanceMetrics['memory_usage'] += ($endMemory - $startMemory);
        self::$_performanceMetrics['last_updated'] = time();

        // Save metrics to cache every 10 calls for real-time monitoring
        if (self::$_performanceMetrics['total_calls'] % 10 === 0) {
            $this->_savePerformanceMetricsToCache();
        }

        // Log performance metrics every 100 calls
        if (self::$_performanceMetrics['total_calls'] % 100 === 0) {
            $this->_getHelper()->log('Performance metrics update', 'debug', self::$_performanceMetrics);
        }
    }

    /**
     * Save performance metrics to cache for admin monitoring
     */
    protected function _savePerformanceMetricsToCache()
    {
        try {
            $cache = Mage::app()->getCache();
            $cacheKey = 'pfg_altcurrency_performance_metrics';
            $cache->save(serialize(self::$_performanceMetrics), $cacheKey, array(), 3600); // 1 hour TTL
        } catch (Exception $e) {
            // Silently fail to avoid breaking the page
            $this->_getHelper()->log('Failed to save performance metrics to cache: ' . $e->getMessage(), 'warning');
        }
    }

    /**
     * Check if HTML contains price elements using optimized detection
     *
     * @param string $html
     * @return bool
     */
    protected function _hasPriceElements($html)
    {
        // Quick checks first for performance
        if (strpos($html, 'price-box') !== false) {
            return true;
        }

        // Check for common price classes
        $priceClasses = array('price', 'regular-price', 'special-price', 'old-price');
        foreach ($priceClasses as $class) {
            if (strpos($html, 'class="' . $class . '"') !== false ||
                strpos($html, 'class="' . $class . ' ') !== false ||
                strpos($html, ' ' . $class . '"') !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Process HTML with comprehensive error handling using secure parser
     *
     * @param string $html
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _processHtmlSafely($html, $rate, $symbol)
    {
        try {
            // Skip if already contains alt-currency elements
            if (strpos($html, 'alt-currency-box') !== false) {
                return $html;
            }

            // Process price-box divs only - this handles the main product price display
            $html = $this->_processPriceBoxes($html, $rate, $symbol);

            return $html;

        } catch (PFG_AltCurrency_Exception_Processing $e) {
            // Specific processing exception - log with context
            $this->_getHelper()->log('Processing exception: ' . $e->getFormattedMessage(), 'warning');
            return $html;
        } catch (Exception $e) {
            // Generic exception - log and return original HTML
            $this->_getHelper()->log('Error processing HTML: ' . $e->getMessage(), 'error', array(
                'html_length' => strlen($html),
                'rate' => $rate,
                'symbol' => $symbol
            ));
            return $html;
        }
    }

    /**
     * Process price-box divs with smart price detection
     *
     * @param string $html
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _processPriceBoxes($html, $rate, $symbol)
    {
        // Find price-box divs using simple string operations
        $offset = 0;
        while (($pos = strpos($html, 'price-box', $offset)) !== false) {
            // Find the start of the div tag
            $divStart = strrpos(substr($html, 0, $pos), '<div');
            if ($divStart === false) {
                $offset = $pos + 1;
                continue;
            }

            // Find the end of the div tag
            $divEnd = $this->_findMatchingClosingTag($html, $divStart, 'div');
            if ($divEnd === false) {
                $offset = $pos + 1;
                continue;
            }

            $priceBoxHtml = substr($html, $divStart, $divEnd - $divStart + 6); // +6 for </div>

            // Generate hash to prevent duplicate processing
            $hash = md5($priceBoxHtml);
            if (isset(self::$_processedHashes[$hash])) {
                $offset = $divEnd + 1;
                continue;
            }

            // Process this price box
            $processedPriceBox = $this->_processSinglePriceBox($priceBoxHtml, $rate, $symbol);

            if ($processedPriceBox !== $priceBoxHtml) {
                $html = str_replace($priceBoxHtml, $processedPriceBox, $html);
                self::$_processedHashes[$hash] = true;
                self::$_performanceMetrics['processed_elements']++;
            }

            $offset = $divEnd + 1;
        }

        return $html;
    }

    /**
     * Process a single price box to add alt currency
     *
     * @param string $priceBoxHtml
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _processSinglePriceBox($priceBoxHtml, $rate, $symbol)
    {
        // Find the most relevant price to convert
        $priceToConvert = $this->_findMainPrice($priceBoxHtml);

        if (empty($priceToConvert)) {
            return $priceBoxHtml;
        }

        // Convert the price
        $convertedPrice = $this->_convertPrice($priceToConvert, $rate, $symbol);

        if (empty($convertedPrice)) {
            return $priceBoxHtml;
        }

        // Add the converted price at the end of the price box
        $altCurrencyHtml = '<div class="alt-currency-price" style="margin-top: 5px; font-size: 14px; color: #666;">' .
                          '<span class="alt-currency-label">Alt Price: </span>' .
                          '<span class="alt-currency-value">' . $convertedPrice . '</span>' .
                          '</div>';

        // Insert before the closing </div> tag
        $closingPos = strrpos($priceBoxHtml, '</div>');
        if ($closingPos !== false) {
            $priceBoxHtml = substr($priceBoxHtml, 0, $closingPos) . $altCurrencyHtml . substr($priceBoxHtml, $closingPos);
        }

        return $priceBoxHtml;
    }

    /**
     * Find the main price to convert (prioritize special price over regular price)
     *
     * @param string $html
     * @return string
     */
    protected function _findMainPrice($html)
    {
        // Priority order: special-price > regular-price > price
        $priceClasses = array('special-price', 'regular-price', 'price');

        foreach ($priceClasses as $class) {
            if (preg_match('/<span[^>]*class="[^"]*' . preg_quote($class, '/') . '[^"]*"[^>]*>(.*?)<\/span>/is', $html, $match)) {
                $priceText = strip_tags($match[1]);
                $priceText = html_entity_decode($priceText, ENT_QUOTES, 'UTF-8');

                // Extract numeric value
                if (preg_match('/[\d\s,\.]+/', $priceText, $numMatch)) {
                    return trim($numMatch[0]);
                }
            }
        }

        return '';
    }

    /**
     * Convert price to alternative currency
     *
     * @param string $priceText
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _convertPrice($priceText, $rate, $symbol)
    {
        // Clean the price text
        $cleanPrice = str_replace(array(' ', ','), array('', '.'), $priceText);
        $cleanPrice = preg_replace('/[^\d\.]/', '', $cleanPrice);

        $price = floatval($cleanPrice);

        if ($price <= 0) {
            return '';
        }

        $converted = $price * $rate;
        $formatted = number_format($converted, 2, ',', ' ');

        return $this->_getHelper()->sanitizeSymbol($symbol) . ' ' . $formatted;
    }

    /**
     * Find matching closing tag for a given opening tag
     *
     * @param string $html
     * @param int $startPos
     * @param string $tagName
     * @return int|false
     */
    protected function _findMatchingClosingTag($html, $startPos, $tagName)
    {
        $openCount = 1;
        $pos = $startPos;

        while ($openCount > 0 && $pos < strlen($html)) {
            $nextOpen = strpos($html, '<' . $tagName, $pos + 1);
            $nextClose = strpos($html, '</' . $tagName . '>', $pos + 1);

            if ($nextClose === false) {
                return false;
            }

            if ($nextOpen !== false && $nextOpen < $nextClose) {
                $openCount++;
                $pos = $nextOpen;
            } else {
                $openCount--;
                $pos = $nextClose;
            }
        }

        return $openCount === 0 ? $pos : false;
    }











    /**
     * Cleanup hash cache to prevent memory leaks
     *
     * @return void
     */
    protected function _cleanupHashCache()
    {
        self::$_hashCleanupCounter++;

        if (self::$_hashCleanupCounter >= self::HASH_CLEANUP_THRESHOLD) {
            self::$_processedHashes = array();
            self::$_hashCleanupCounter = 0;

            $this->_getHelper()->log('Hash cache cleaned up', 'debug', array(
                'threshold' => self::HASH_CLEANUP_THRESHOLD
            ));
        }
    }
}