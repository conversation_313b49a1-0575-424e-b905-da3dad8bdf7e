<?php
class PFG_AltCurrency_Model_Observer
{
    protected static $processedHashes = [];

    public function appendConvertedPrice(Varien_Event_Observer $observer)
    {
        $transport = $observer->getTransport();
        $html = $transport->getHtml();

        // Skip if HTML already contains alt-currency-box elements (already processed)
        if (strpos($html, 'alt-currency-box') !== false) {
            return;
        }

        // Check if HTML contains price-box divs or standalone price spans
        if (strpos($html, 'price-box') === false &&
            !preg_match('/<span[^>]*class="[^"]*\b(?:price|regular-price|special-price|old-price|price-excluding-tax|price-including-tax|minimal-price|configured-price)\b[^"]*"[^>]*>/i', $html)) {
            return;
        }

        $symbol = Mage::getStoreConfig('pfg_altcurrency/settings/symbol');
        $rate = floatval(Mage::getStoreConfig('pfg_altcurrency/settings/rate'));
        if ($rate <= 0 || empty($symbol)) return;

        // First, process price-box divs that do NOT have alt-currency-box class
        $priceBoxPattern = '/(<div[^>]*class="(?:(?!alt-currency-box)[^"])*\bprice-box\b[^"]*"[^>]*>.*?<\/div>)/is';
        $html = preg_replace_callback($priceBoxPattern, function ($matches) use ($rate, $symbol) {
            return $this->processElement($matches[1], $rate, $symbol, 'div');
        }, $html);

        // Then, process standalone price spans that are NOT inside any price-box div
        $html = $this->processStandalonePriceSpans($html, $rate, $symbol);

        $transport->setHtml($html);
    }

    /**
     * Process individual price element (div or span)
     *
     * @param string $originalElement
     * @param float $rate
     * @param string $symbol
     * @param string $elementType ('div' or 'span')
     * @return string
     */
    protected function processElement($originalElement, $rate, $symbol, $elementType, $position = '')
    {
        // Skip if already contains alt-currency-box (safety check)
        if (strpos($originalElement, 'alt-currency-box') !== false) {
            return $originalElement;
        }

        // For div elements, use hash-based deduplication to prevent multiple alt-currency-box divs
        if ($elementType === 'div') {
            // Create a normalized hash based on the structure and price content
            $normalizedElement = preg_replace('/\s+/', ' ', trim($originalElement)); // Normalize whitespace
            $priceContent = '';
            if (preg_match('/<span[^>]*class="[^"]*\bprice\b[^"]*"[^>]*>(.*?)<\/span>/is', $normalizedElement, $priceMatch)) {
                $priceContent = trim(strip_tags($priceMatch[1]));
            }
            $hash = md5($normalizedElement . '|' . $priceContent . '|' . $elementType);

            if (isset(self::$processedHashes[$hash])) {
                return $originalElement;
            }
            self::$processedHashes[$hash] = true;
        }
        // For span elements, don't use hash-based deduplication to allow multiple identical spans

        $altElement = $originalElement;

        // Add alt-currency-box class and id based on element type
        if ($elementType === 'div') {
            // For div elements (price-box containers)
            $altElement = preg_replace('/<div([^>]*)class="/i', '<div id="alt-currency-box" class="alt-currency-box ', $altElement, 1);
        } else {
            // For span elements (standalone price spans)
            $altElement = preg_replace('/<span([^>]*)class="([^"]*)"/', '<span$1class="$2 alt-currency-box"', $altElement, 1);
        }

        // Replace prices with converted values - convert ALL price spans
        $altElement = preg_replace_callback('/<span([^>]*class="[^"]*\bprice\b[^"]*"[^>]*)>(.*?)<\/span>/is', function ($spanMatches) use ($rate, $symbol, $elementType) {
            $content = strip_tags($spanMatches[2]);

            // Skip if this already looks like a converted price (contains the symbol)
            if (strpos($content, $symbol) !== false) {
                return $spanMatches[0];
            }

            // Convert the price
            $decoded = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
            $clean = preg_replace('/[^\d,.\s]/', '', $decoded);
            $clean = str_replace([' ', ','], ['', '.'], trim($clean));
            $price = floatval($clean);

            if ($price <= 0) return $spanMatches[0];

            $converted = $price * $rate;
            $formatted = number_format($converted, 2, ',', ' ');

            $value = $symbol . ' ' . $formatted;
            if ($elementType === 'div') {
                $value = '<span class="price">' . $value . '</span>';
            }

            return '<span' . $spanMatches[1] . '>' . $value . '</span>';
        }, $altElement);

        return $originalElement . "\n" . $altElement;
    }

    /**
     * Process standalone price spans that are not inside price-box divs
     *
     * @param string $html
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function processStandalonePriceSpans($html, $rate, $symbol)
    {
        // Split HTML by price-box divs to identify standalone spans
        $parts = preg_split('/(<div[^>]*class="[^"]*\bprice-box\b[^"]*"[^>]*>.*?<\/div>)/is', $html, -1, PREG_SPLIT_DELIM_CAPTURE);

        $result = '';
        $spanCounter = 0; // Counter to ensure each span gets a unique position

        for ($i = 0; $i < count($parts); $i++) {
            $part = $parts[$i];

            // If this part is a price-box div, keep it as is (already processed)
            if (preg_match('/^<div[^>]*class="[^"]*\bprice-box\b[^"]*"[^>]*>/i', $part)) {
                $result .= $part;
            } else {
                // This is content outside price-box divs, process standalone price spans
                $part = preg_replace_callback('/(<span[^>]*class="(?:(?!alt-currency-box)[^"])*\b(?:price|regular-price|special-price|old-price|price-excluding-tax|price-including-tax|minimal-price|configured-price)\b[^"]*"[^>]*>.*?<\/span>)/is', function ($matches) use ($rate, $symbol, &$spanCounter) {
                    $element = $matches[1];
                    // Skip if already contains alt-currency-box
                    if (strpos($element, 'alt-currency-box') !== false) {
                        return $element;
                    }
                    $spanCounter++;
                    $position = 'span_' . $spanCounter; // Unique position for each span
                    return $this->processElement($element, $rate, $symbol, 'span', $position);
                }, $part);
                $result .= $part;
            }
        }

        return $result;
    }
}

