<?php
/**
 * PFG Alt Currency Observer
 *
 * Handles price conversion and display for alternative currency
 * with comprehensive error handling and performance optimization
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Model_Observer
{
    /**
     * Cache for processed hashes to prevent duplicate processing
     * @var array
     */
    protected static $_processedHashes = array();

    /**
     * Configuration cache to avoid repeated database calls
     * @var array
     */
    protected $_configCache = null;

    /**
     * Hash cleanup counter to prevent memory leaks
     * @var int
     */
    protected static $_hashCleanupCounter = 0;

    /**
     * Hash cleanup threshold
     */
    const HASH_CLEANUP_THRESHOLD = 1000;

    /**
     * Helper instance cache
     * @var PFG_AltCurrency_Helper_Data
     */
    protected $_helper = null;

    /**
     * Performance metrics tracking
     * @var array
     */
    protected static $_performanceMetrics = array(
        'total_calls' => 0,
        'processed_elements' => 0,
        'cache_hits' => 0,
        'processing_time' => 0,
        'memory_usage' => 0
    );

    /**
     * Block type cache for performance optimization
     * @var array
     */
    protected static $_blockTypeCache = array();

    /**
     * Regex pattern cache to avoid recompilation
     * @var array
     */
    protected static $_regexCache = array();

    /**
     * Main method to append converted prices to HTML output
     *
     * @param Varien_Event_Observer $observer
     * @return void
     */
    public function appendConvertedPrice(Varien_Event_Observer $observer)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        try {
            // Increment performance counter
            self::$_performanceMetrics['total_calls']++;

            // Get helper instance
            $helper = $this->_getHelper();

            // Check if module is enabled (with caching)
            if (!$this->_isModuleEnabled()) {
                return;
            }

            $transport = $observer->getTransport();
            $html = $transport->getHtml();

            // Early exit optimizations
            if ($this->_shouldSkipProcessing($html)) {
                return;
            }

            // Validate configuration (cached)
            $config = $this->_getConfiguration();
            if (!$config['valid']) {
                $helper->log('Invalid configuration, skipping price conversion', 'warning', array(
                    'errors' => $config['errors']
                ));
                return;
            }

            // Optimized price element detection
            if (!$this->_hasPriceElementsOptimized($html)) {
                return;
            }

            // Process HTML with performance tracking
            $processedHtml = $this->_processHtmlSafely($html, $config['rate'], $config['symbol']);
            $transport->setHtml($processedHtml);

            // Cleanup hash cache periodically
            $this->_cleanupHashCache();

            // Track performance metrics
            $this->_trackPerformanceMetrics($startTime, $startMemory);

        } catch (Exception $e) {
            $this->_getHelper()->log('Error in appendConvertedPrice: ' . $e->getMessage(), 'error', array(
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'performance_metrics' => self::$_performanceMetrics
            ));
            // Don't break the page - return original HTML
        }
    }

    /**
     * Get helper instance
     *
     * @return PFG_AltCurrency_Helper_Data
     */
    protected function _getHelper()
    {
        if ($this->_helper === null) {
            $this->_helper = Mage::helper('altcurrency');
        }
        return $this->_helper;
    }

    /**
     * Get and cache configuration values
     *
     * @return array
     */
    protected function _getConfiguration()
    {
        if ($this->_configCache === null) {
            $this->_configCache = $this->_getHelper()->validateConfiguration();
        } else {
            self::$_performanceMetrics['cache_hits']++;
        }
        return $this->_configCache;
    }

    /**
     * Check if module is enabled with caching
     *
     * @return bool
     */
    protected function _isModuleEnabled()
    {
        static $enabled = null;

        if ($enabled === null) {
            $enabled = $this->_getHelper()->isEnabled();
        }

        return $enabled;
    }

    /**
     * Optimized method to determine if processing should be skipped
     *
     * @param string $html
     * @return bool
     */
    protected function _shouldSkipProcessing($html)
    {
        // Quick length check
        if (empty($html) || strlen($html) < 20) {
            return true;
        }

        // Check if already processed
        if (strpos($html, 'alt-currency-box') !== false) {
            return true;
        }

        // Skip if no HTML tags (plain text)
        if (strpos($html, '<') === false) {
            return true;
        }

        return false;
    }

    /**
     * Performance-optimized price element detection
     *
     * @param string $html
     * @return bool
     */
    protected function _hasPriceElementsOptimized($html)
    {
        // Use cached regex patterns for better performance
        static $pricePatterns = null;

        if ($pricePatterns === null) {
            $pricePatterns = array(
                'price-box',
                'class="price"',
                'class="regular-price"',
                'class="special-price"',
                'class="old-price"'
            );
        }

        // Quick string searches first (faster than regex)
        foreach ($pricePatterns as $pattern) {
            if (strpos($html, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Track performance metrics for monitoring
     *
     * @param float $startTime
     * @param int $startMemory
     */
    protected function _trackPerformanceMetrics($startTime, $startMemory)
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        self::$_performanceMetrics['processing_time'] += ($endTime - $startTime);
        self::$_performanceMetrics['memory_usage'] += ($endMemory - $startMemory);
        self::$_performanceMetrics['last_updated'] = time();

        // Save metrics to cache every 10 calls for real-time monitoring
        if (self::$_performanceMetrics['total_calls'] % 10 === 0) {
            $this->_savePerformanceMetricsToCache();
        }

        // Log performance metrics every 100 calls
        if (self::$_performanceMetrics['total_calls'] % 100 === 0) {
            $this->_getHelper()->log('Performance metrics update', 'debug', self::$_performanceMetrics);
        }
    }

    /**
     * Save performance metrics to cache for admin monitoring
     */
    protected function _savePerformanceMetricsToCache()
    {
        try {
            $cache = Mage::app()->getCache();
            $cacheKey = 'pfg_altcurrency_performance_metrics';
            $cache->save(serialize(self::$_performanceMetrics), $cacheKey, array(), 3600); // 1 hour TTL
        } catch (Exception $e) {
            // Silently fail to avoid breaking the page
            $this->_getHelper()->log('Failed to save performance metrics to cache: ' . $e->getMessage(), 'warning');
        }
    }

    /**
     * Check if HTML contains price elements using optimized detection
     *
     * @param string $html
     * @return bool
     */
    protected function _hasPriceElements($html)
    {
        // Quick checks first for performance
        if (strpos($html, 'price-box') !== false) {
            return true;
        }

        // Check for common price classes
        $priceClasses = array('price', 'regular-price', 'special-price', 'old-price');
        foreach ($priceClasses as $class) {
            if (strpos($html, 'class="' . $class . '"') !== false ||
                strpos($html, 'class="' . $class . ' ') !== false ||
                strpos($html, ' ' . $class . '"') !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Process HTML with comprehensive error handling
     *
     * @param string $html
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _processHtmlSafely($html, $rate, $symbol)
    {
        try {
            // First, process price-box divs
            $html = $this->_processPriceBoxDivs($html, $rate, $symbol);

            // Then, process standalone price spans
            $html = $this->_processStandalonePriceSpans($html, $rate, $symbol);

            return $html;

        } catch (Exception $e) {
            $this->_getHelper()->log('Error processing HTML: ' . $e->getMessage(), 'error', array(
                'html_length' => strlen($html),
                'rate' => $rate,
                'symbol' => $symbol
            ));
            return $html; // Return original HTML on error
        }
    }

    /**
     * Process price-box divs with error handling and performance optimization
     *
     * @param string $html
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _processPriceBoxDivs($html, $rate, $symbol)
    {
        try {
            // Use cached regex pattern for better performance
            $patternKey = 'price_box_divs';
            if (!isset(self::$_regexCache[$patternKey])) {
                self::$_regexCache[$patternKey] = '/<div[^>]*class="[^"]*price-box[^"]*"[^>]*>.*?<\/div>/is';
            }

            $pattern = self::$_regexCache[$patternKey];

            return preg_replace_callback($pattern, function ($matches) use ($rate, $symbol) {
                self::$_performanceMetrics['processed_elements']++;
                return $this->_processElement($matches[0], $rate, $symbol, 'div');
            }, $html);

        } catch (Exception $e) {
            $this->_getHelper()->log('Error processing price-box divs: ' . $e->getMessage(), 'error');
            return $html;
        }
    }

    /**
     * Process standalone price spans that are not inside price-box divs
     *
     * @param string $html
     * @param float $rate
     * @param string $symbol
     * @return string
     */
    protected function _processStandalonePriceSpans($html, $rate, $symbol)
    {
        try {
            // Split HTML by price-box divs to identify standalone spans
            $parts = preg_split('/<div[^>]*class="[^"]*price-box[^"]*"[^>]*>.*?<\/div>/is', $html, -1, PREG_SPLIT_DELIM_CAPTURE);

            $result = '';
            $spanCounter = 0;

            foreach ($parts as $part) {
                // If this part is a price-box div, keep it as is (already processed)
                if (preg_match('/^<div[^>]*class="[^"]*price-box[^"]*"/i', $part)) {
                    $result .= $part;
                } else {
                    // Process standalone price spans in this part
                    $part = $this->_processStandalonePriceSpansInPart($part, $rate, $symbol, $spanCounter);
                    $result .= $part;
                }
            }

            return $result;

        } catch (Exception $e) {
            $this->_getHelper()->log('Error processing standalone price spans: ' . $e->getMessage(), 'error');
            return $html;
        }
    }

    /**
     * Process standalone price spans in a specific HTML part
     *
     * @param string $part
     * @param float $rate
     * @param string $symbol
     * @param int &$spanCounter
     * @return string
     */
    protected function _processStandalonePriceSpansInPart($part, $rate, $symbol, &$spanCounter)
    {
        $priceClasses = array('price', 'regular-price', 'special-price', 'old-price',
                             'price-excluding-tax', 'price-including-tax', 'minimal-price', 'configured-price');

        foreach ($priceClasses as $class) {
            $pattern = '/<span[^>]*class="[^"]*' . preg_quote($class, '/') . '[^"]*"[^>]*>.*?<\/span>/is';

            $part = preg_replace_callback($pattern, function ($matches) use ($rate, $symbol, &$spanCounter) {
                $element = $matches[0];

                // Skip if already contains alt-currency-box
                if (strpos($element, 'alt-currency-box') !== false) {
                    return $element;
                }

                $spanCounter++;
                return $this->_processElement($element, $rate, $symbol, 'span');
            }, $part);
        }

        return $part;
    }

    /**
     * Process individual price element (div or span) with error handling
     *
     * @param string $originalElement
     * @param float $rate
     * @param string $symbol
     * @param string $elementType
     * @return string
     */
    protected function _processElement($originalElement, $rate, $symbol, $elementType)
    {
        try {
            // Skip if already processed
            if (strpos($originalElement, 'alt-currency-box') !== false) {
                return $originalElement;
            }

            // For div elements, use hash-based deduplication
            if ($elementType === 'div') {
                $hash = $this->_generateElementHash($originalElement, $elementType);
                if (isset(self::$_processedHashes[$hash])) {
                    return $originalElement;
                }
                self::$_processedHashes[$hash] = true;
            }

            // Create alternative currency element
            $altElement = $this->_createAltCurrencyElement($originalElement, $rate, $symbol, $elementType);

            return $originalElement . "\n" . $altElement;

        } catch (Exception $e) {
            $this->_getHelper()->log('Error processing element: ' . $e->getMessage(), 'error', array(
                'element_type' => $elementType,
                'element_length' => strlen($originalElement)
            ));
            return $originalElement;
        }
    }

    /**
     * Generate hash for element deduplication
     *
     * @param string $element
     * @param string $elementType
     * @return string
     */
    protected function _generateElementHash($element, $elementType)
    {
        // Normalize whitespace for consistent hashing
        $normalized = preg_replace('/\s+/', ' ', trim($element));

        // Extract price content for more accurate hashing
        $priceContent = '';
        if (preg_match('/<span[^>]*class="[^"]*price[^"]*"[^>]*>(.*?)<\/span>/is', $normalized, $match)) {
            $priceContent = trim(strip_tags($match[1]));
        }

        return md5($normalized . '|' . $priceContent . '|' . $elementType);
    }

    /**
     * Create alternative currency element
     *
     * @param string $originalElement
     * @param float $rate
     * @param string $symbol
     * @param string $elementType
     * @return string
     */
    protected function _createAltCurrencyElement($originalElement, $rate, $symbol, $elementType)
    {
        $altElement = $originalElement;

        // Add alt-currency-box class
        if ($elementType === 'div') {
            $altElement = preg_replace('/<div([^>]*)class="([^"]*)"/',
                '<div$1class="$2 alt-currency-box" id="alt-currency-box"', $altElement, 1);
        } else {
            $altElement = preg_replace('/<span([^>]*)class="([^"]*)"/',
                '<span$1class="$2 alt-currency-box"', $altElement, 1);
        }

        // Convert prices
        $altElement = $this->_convertPricesInElement($altElement, $rate, $symbol, $elementType);

        return $altElement;
    }

    /**
     * Convert prices within an element
     *
     * @param string $element
     * @param float $rate
     * @param string $symbol
     * @param string $elementType
     * @return string
     */
    protected function _convertPricesInElement($element, $rate, $symbol, $elementType)
    {
        return preg_replace_callback('/<span([^>]*class="[^"]*price[^"]*"[^>]*)>(.*?)<\/span>/is',
            function ($matches) use ($rate, $symbol, $elementType) {
                return $this->_convertPriceSpan($matches, $rate, $symbol, $elementType);
            }, $element);
    }

    /**
     * Convert individual price span
     *
     * @param array $matches
     * @param float $rate
     * @param string $symbol
     * @param string $elementType
     * @return string
     */
    protected function _convertPriceSpan($matches, $rate, $symbol, $elementType)
    {
        try {
            $content = strip_tags($matches[2]);

            // Skip if already converted
            if (strpos($content, $symbol) !== false) {
                return $matches[0];
            }

            // Extract and convert price
            $price = $this->_extractPrice($content);
            if ($price <= 0) {
                return $matches[0];
            }

            $converted = $price * $rate;
            $formatted = $this->_formatPrice($converted, $symbol);

            if ($elementType === 'div') {
                $formatted = '<span class="price">' . $formatted . '</span>';
            }

            return '<span' . $matches[1] . '>' . $formatted . '</span>';

        } catch (Exception $e) {
            $this->_getHelper()->log('Error converting price span: ' . $e->getMessage(), 'error');
            return $matches[0];
        }
    }

    /**
     * Extract numeric price from text content
     *
     * @param string $content
     * @return float
     */
    protected function _extractPrice($content)
    {
        // Decode HTML entities
        $decoded = html_entity_decode($content, ENT_QUOTES, 'UTF-8');

        // Remove non-numeric characters except decimal separators
        $clean = preg_replace('/[^\d,.\s]/', '', $decoded);

        // Normalize decimal separators
        $clean = str_replace(array(' ', ','), array('', '.'), trim($clean));

        return floatval($clean);
    }

    /**
     * Format converted price with symbol
     *
     * @param float $price
     * @param string $symbol
     * @return string
     */
    protected function _formatPrice($price, $symbol)
    {
        $formatted = number_format($price, 2, ',', ' ');
        return $this->_getHelper()->sanitizeSymbol($symbol) . ' ' . $formatted;
    }

    /**
     * Cleanup hash cache to prevent memory leaks
     *
     * @return void
     */
    protected function _cleanupHashCache()
    {
        self::$_hashCleanupCounter++;

        if (self::$_hashCleanupCounter >= self::HASH_CLEANUP_THRESHOLD) {
            self::$_processedHashes = array();
            self::$_hashCleanupCounter = 0;

            $this->_getHelper()->log('Hash cache cleaned up', 'debug', array(
                'threshold' => self::HASH_CLEANUP_THRESHOLD
            ));
        }
    }
}