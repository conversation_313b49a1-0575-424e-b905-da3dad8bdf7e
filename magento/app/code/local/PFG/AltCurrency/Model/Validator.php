<?php
/**
 * PFG Alt Currency Validator
 *
 * Centralized validation logic for currency configuration and input data
 * Provides comprehensive validation with detailed error reporting
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Model_Validator extends Mage_Core_Model_Abstract
{
    /**
     * Validation rules configuration
     * @var array
     */
    protected $_validationRules = array(
        'symbol' => array(
            'required' => true,
            'max_length' => 10,
            'min_length' => 1,
            'allowed_chars' => '/^[^\<\>\"\'\&\;]+$/',
            'sanitize' => true
        ),
        'rate' => array(
            'required' => true,
            'type' => 'float',
            'min_value' => 0.00000001,
            'max_value' => 1000000,
            'max_decimals' => 8
        ),
        'enabled' => array(
            'required' => false,
            'type' => 'boolean'
        )
    );
    
    /**
     * Validate currency symbol
     *
     * @param string $symbol
     * @return array
     * @throws PFG_AltCurrency_Exception_Configuration
     */
    public function validateSymbol($symbol)
    {
        $rules = $this->_validationRules['symbol'];
        $errors = array();
        $warnings = array();
        
        // Required check
        if ($rules['required'] && empty($symbol)) {
            throw PFG_AltCurrency_Exception_Configuration::missingRequired('symbol');
        }
        
        if (!empty($symbol)) {
            // Length validation
            if (strlen($symbol) > $rules['max_length']) {
                $errors[] = sprintf('Symbol cannot exceed %d characters', $rules['max_length']);
            }
            
            if (strlen($symbol) < $rules['min_length']) {
                $errors[] = sprintf('Symbol must be at least %d character', $rules['min_length']);
            }
            
            // Character validation
            if (!preg_match($rules['allowed_chars'], $symbol)) {
                $errors[] = 'Symbol contains invalid characters (HTML entities not allowed)';
            }
            
            // Security checks
            if (preg_match('/<script|javascript:|on\w+=/i', $symbol)) {
                $errors[] = 'Symbol contains potentially dangerous content';
            }
            
            // Common currency symbol validation
            if (!$this->_isValidCurrencySymbol($symbol)) {
                $warnings[] = 'Symbol does not appear to be a standard currency symbol';
            }
        }
        
        if (!empty($errors)) {
            throw PFG_AltCurrency_Exception_Configuration::invalidSymbol($symbol, $errors);
        }
        
        return array(
            'valid' => true,
            'sanitized' => $rules['sanitize'] ? htmlspecialchars($symbol, ENT_QUOTES, 'UTF-8') : $symbol,
            'warnings' => $warnings
        );
    }
    
    /**
     * Validate currency rate
     *
     * @param mixed $rate
     * @return array
     * @throws PFG_AltCurrency_Exception_Configuration
     */
    public function validateRate($rate)
    {
        $rules = $this->_validationRules['rate'];
        $errors = array();
        $warnings = array();
        
        // Required check
        if ($rules['required'] && ($rate === null || $rate === '')) {
            throw PFG_AltCurrency_Exception_Configuration::missingRequired('rate');
        }
        
        // Type conversion and validation
        $numericRate = floatval($rate);
        
        if (!is_numeric($rate) || $numericRate != $rate) {
            $errors[] = 'Rate must be a valid number';
        } else {
            // Range validation
            if ($numericRate < $rules['min_value']) {
                $errors[] = sprintf('Rate must be greater than %f', $rules['min_value']);
            }
            
            if ($numericRate > $rules['max_value']) {
                $errors[] = sprintf('Rate cannot exceed %s', number_format($rules['max_value']));
            }
            
            // Decimal places validation
            $decimalPlaces = $this->_getDecimalPlaces($rate);
            if ($decimalPlaces > $rules['max_decimals']) {
                $warnings[] = sprintf('Rate precision will be limited to %d decimal places', $rules['max_decimals']);
                $numericRate = round($numericRate, $rules['max_decimals']);
            }
            
            // Reasonableness checks
            if ($numericRate > 100) {
                $warnings[] = 'Rate seems unusually high - please verify';
            }
            
            if ($numericRate < 0.001) {
                $warnings[] = 'Rate seems unusually low - please verify';
            }
        }
        
        if (!empty($errors)) {
            throw PFG_AltCurrency_Exception_Configuration::invalidRate($rate, $errors);
        }
        
        return array(
            'valid' => true,
            'value' => $numericRate,
            'warnings' => $warnings
        );
    }
    
    /**
     * Validate enabled flag
     *
     * @param mixed $enabled
     * @return array
     */
    public function validateEnabled($enabled)
    {
        $boolValue = (bool)$enabled;
        
        return array(
            'valid' => true,
            'value' => $boolValue,
            'warnings' => array()
        );
    }
    
    /**
     * Validate complete configuration
     *
     * @param array $config
     * @return array
     */
    public function validateConfiguration($config)
    {
        $results = array(
            'valid' => true,
            'errors' => array(),
            'warnings' => array(),
            'sanitized' => array()
        );
        
        try {
            // Validate symbol
            if (isset($config['symbol'])) {
                $symbolResult = $this->validateSymbol($config['symbol']);
                $results['sanitized']['symbol'] = $symbolResult['sanitized'];
                $results['warnings'] = array_merge($results['warnings'], $symbolResult['warnings']);
            }
            
            // Validate rate
            if (isset($config['rate'])) {
                $rateResult = $this->validateRate($config['rate']);
                $results['sanitized']['rate'] = $rateResult['value'];
                $results['warnings'] = array_merge($results['warnings'], $rateResult['warnings']);
            }
            
            // Validate enabled
            if (isset($config['enabled'])) {
                $enabledResult = $this->validateEnabled($config['enabled']);
                $results['sanitized']['enabled'] = $enabledResult['value'];
                $results['warnings'] = array_merge($results['warnings'], $enabledResult['warnings']);
            }
            
        } catch (PFG_AltCurrency_Exception_Configuration $e) {
            $results['valid'] = false;
            $results['errors'][] = $e->getMessage();
            $results['errors'] = array_merge($results['errors'], $e->getFailedRules());
        }
        
        return $results;
    }
    
    /**
     * Check if symbol appears to be a valid currency symbol
     *
     * @param string $symbol
     * @return bool
     */
    protected function _isValidCurrencySymbol($symbol)
    {
        $commonSymbols = array('$', '€', '£', '¥', '₹', '₽', '₩', '₪', '₦', '₡', '₨', '₱', '₫', '₴', '₵', '₸');
        
        // Check if it's a common symbol
        if (in_array($symbol, $commonSymbols)) {
            return true;
        }
        
        // Check if it's a 3-letter currency code
        if (preg_match('/^[A-Z]{3}$/', $symbol)) {
            return true;
        }
        
        // Check if it contains currency-related characters
        if (preg_match('/[\$€£¥₹₽₩₪₦₡₨₱₫₴₵₸]/', $symbol)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Get number of decimal places in a number
     *
     * @param mixed $number
     * @return int
     */
    protected function _getDecimalPlaces($number)
    {
        $string = (string)$number;
        $decimalPos = strpos($string, '.');
        
        if ($decimalPos === false) {
            return 0;
        }
        
        return strlen($string) - $decimalPos - 1;
    }
}
