<?php
/**
 * PFG Alt Currency Rate Backend Model
 *
 * Validates currency rate configuration values
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Model_System_Config_Backend_Rate extends Mage_Core_Model_Config_Data
{
    /**
     * Validate currency rate before saving using validator
     *
     * @return PFG_AltCurrency_Model_System_Config_Backend_Rate
     * @throws Mage_Core_Exception
     */
    protected function _beforeSave()
    {
        $value = $this->getValue();
        $helper = Mage::helper('altcurrency');

        try {
            $validator = Mage::getModel('pfg_altcurrency/validator');
            $result = $validator->validateRate($value);

            // Set the validated value
            $this->setValue($result['value']);

            // Log warnings if any
            if (!empty($result['warnings'])) {
                $helper->log('Currency rate validation warnings', 'warning', array(
                    'warnings' => $result['warnings'],
                    'original_value' => $value,
                    'validated_value' => $result['value']
                ));
            }

            $helper->log('Currency rate updated successfully', 'info', array(
                'old_value' => $this->getOldValue(),
                'new_value' => $result['value'],
                'scope' => $this->getScope(),
                'scope_id' => $this->getScopeId()
            ));

        } catch (PFG_AltCurrency_Exception_Configuration $e) {
            $helper->log('Currency rate validation failed: ' . $e->getFormattedMessage(), 'error');
            throw new Mage_Core_Exception($e->getMessage());
        } catch (Exception $e) {
            $helper->log('Failed to save currency rate: ' . $e->getMessage(), 'error', array(
                'value' => $value,
                'scope' => $this->getScope(),
                'scope_id' => $this->getScopeId()
            ));
            throw $e;
        }

        return parent::_beforeSave();
    }
    
    /**
     * Validate after loading from database
     *
     * @return PFG_AltCurrency_Model_System_Config_Backend_Rate
     */
    protected function _afterLoad()
    {
        $value = $this->getValue();
        
        if (!empty($value)) {
            $rate = floatval($value);
            
            // Validate loaded value
            if ($rate <= 0) {
                Mage::helper('altcurrency')->log('Invalid currency rate loaded from database', 'error', array(
                    'value' => $value,
                    'converted' => $rate
                ));
                // Set to default rate to prevent errors
                $this->setValue(1.0);
            }
        }
        
        return parent::_afterLoad();
    }
}
