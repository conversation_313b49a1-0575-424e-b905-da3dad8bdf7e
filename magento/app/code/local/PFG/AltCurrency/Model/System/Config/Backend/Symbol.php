<?php
/**
 * PFG Alt Currency Symbol Backend Model
 *
 * Validates and sanitizes currency symbol configuration values
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Model_System_Config_Backend_Symbol extends Mage_Core_Model_Config_Data
{
    /**
     * Validate and sanitize currency symbol before saving using validator
     *
     * @return PFG_AltCurrency_Model_System_Config_Backend_Symbol
     * @throws Mage_Core_Exception
     */
    protected function _beforeSave()
    {
        $value = $this->getValue();
        $helper = Mage::helper('altcurrency');

        try {
            $validator = Mage::getModel('pfg_altcurrency/validator');
            $result = $validator->validateSymbol($value);

            // Set the sanitized value
            $this->setValue($result['sanitized']);

            // Log warnings if any
            if (!empty($result['warnings'])) {
                $helper->log('Currency symbol validation warnings', 'warning', array(
                    'warnings' => $result['warnings'],
                    'value' => $value,
                    'sanitized' => $result['sanitized']
                ));
            }

            $helper->log('Currency symbol updated successfully', 'info', array(
                'old_value' => $this->getOldValue(),
                'new_value' => $result['sanitized'],
                'scope' => $this->getScope(),
                'scope_id' => $this->getScopeId()
            ));

        } catch (PFG_AltCurrency_Exception_Configuration $e) {
            $helper->log('Currency symbol validation failed: ' . $e->getFormattedMessage(), 'error');
            throw new Mage_Core_Exception($e->getMessage());
        } catch (Exception $e) {
            $helper->log('Failed to save currency symbol: ' . $e->getMessage(), 'error', array(
                'value' => $value,
                'scope' => $this->getScope(),
                'scope_id' => $this->getScopeId()
            ));
            throw $e;
        }

        return parent::_beforeSave();
    }
    
    /**
     * Validate after loading from database
     *
     * @return PFG_AltCurrency_Model_System_Config_Backend_Symbol
     */
    protected function _afterLoad()
    {
        $value = $this->getValue();
        
        if (!empty($value)) {
            // Ensure the value is properly sanitized even when loaded from DB
            $sanitized = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            if ($sanitized !== $value) {
                Mage::helper('altcurrency')->log('Currency symbol required sanitization on load', 'warning', array(
                    'original' => $value,
                    'sanitized' => $sanitized
                ));
                $this->setValue($sanitized);
            }
        }
        
        return parent::_afterLoad();
    }
}
