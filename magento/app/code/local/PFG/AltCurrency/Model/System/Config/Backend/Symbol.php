<?php
/**
 * PFG Alt Currency Symbol Backend Model
 *
 * Validates and sanitizes currency symbol configuration values
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Model_System_Config_Backend_Symbol extends Mage_Core_Model_Config_Data
{
    /**
     * Validate and sanitize currency symbol before saving
     *
     * @return PFG_AltCurrency_Model_System_Config_Backend_Symbol
     * @throws Mage_Core_Exception
     */
    protected function _beforeSave()
    {
        $value = $this->getValue();
        $helper = Mage::helper('altcurrency');
        
        try {
            // Basic validation
            if (empty($value)) {
                throw new Mage_Core_Exception('Currency symbol cannot be empty');
            }
            
            // Sanitize the value
            $value = trim($value);
            $value = strip_tags($value);
            
            // Length validation
            if (strlen($value) > 10) {
                throw new Mage_Core_Exception('Currency symbol cannot exceed 10 characters');
            }
            
            // Check for potentially dangerous characters
            if (preg_match('/[<>"\']/', $value)) {
                throw new Mage_Core_Exception('Currency symbol contains invalid characters');
            }
            
            // Sanitize for safe HTML output
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            
            // Set the cleaned value
            $this->setValue($value);
            
            $helper->log('Currency symbol updated successfully', 'info', array(
                'old_value' => $this->getOldValue(),
                'new_value' => $value,
                'scope' => $this->getScope(),
                'scope_id' => $this->getScopeId()
            ));
            
        } catch (Exception $e) {
            $helper->log('Failed to save currency symbol: ' . $e->getMessage(), 'error', array(
                'value' => $this->getValue(),
                'scope' => $this->getScope(),
                'scope_id' => $this->getScopeId()
            ));
            throw $e;
        }
        
        return parent::_beforeSave();
    }
    
    /**
     * Validate after loading from database
     *
     * @return PFG_AltCurrency_Model_System_Config_Backend_Symbol
     */
    protected function _afterLoad()
    {
        $value = $this->getValue();
        
        if (!empty($value)) {
            // Ensure the value is properly sanitized even when loaded from DB
            $sanitized = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            if ($sanitized !== $value) {
                Mage::helper('altcurrency')->log('Currency symbol required sanitization on load', 'warning', array(
                    'original' => $value,
                    'sanitized' => $sanitized
                ));
                $this->setValue($sanitized);
            }
        }
        
        return parent::_afterLoad();
    }
}
