<?php
/**
 * PFG Alt Currency Admin Controller
 *
 * Handles AJAX requests for configuration testing and performance monitoring
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Adminhtml_Pfg_AltcurrencyController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Test configuration action
     * Validates currency settings and provides conversion examples
     */
    public function testConfigAction()
    {
        $result = array(
            'success' => false,
            'message' => '',
            'data' => array()
        );
        
        try {
            // Validate CSRF token
            if (!$this->_validateFormKey()) {
                throw new Exception('Invalid form key. Please refresh the page and try again.');
            }
            
            $helper = Mage::helper('altcurrency');
            
            // Get configuration from request or current settings
            $symbol = $this->getRequest()->getParam('symbol', $helper->getCurrencySymbol());
            $rate = floatval($this->getRequest()->getParam('rate', $helper->getCurrencyRate()));
            $enabled = $this->getRequest()->getParam('enabled', $helper->isEnabled());
            
            // Validate configuration
            $validation = $this->_validateTestConfiguration($symbol, $rate, $enabled);
            
            if (!$validation['valid']) {
                $result['message'] = 'Configuration validation failed: ' . implode(', ', $validation['errors']);
            } else {
                // Generate conversion examples
                $examples = $this->_generateConversionExamples($rate, $symbol);
                
                $result['success'] = true;
                $result['message'] = 'Configuration is valid! Currency conversion is working correctly.';
                $result['data'] = array(
                    'symbol' => $symbol,
                    'rate' => $rate,
                    'enabled' => $enabled,
                    'examples' => $examples,
                    'validation' => $validation
                );
                
                // Log successful test
                $helper->log('Configuration test completed successfully', 'info', array(
                    'symbol' => $symbol,
                    'rate' => $rate,
                    'examples_count' => count($examples)
                ));
            }
            
        } catch (Exception $e) {
            $result['message'] = 'Test failed: ' . $e->getMessage();
            Mage::helper('altcurrency')->log('Configuration test failed: ' . $e->getMessage(), 'error');
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(json_encode($result));
    }
    
    /**
     * Get performance metrics action
     */
    public function getPerformanceMetricsAction()
    {
        $result = array(
            'success' => false,
            'message' => '',
            'data' => array()
        );
        
        try {
            if (!$this->_validateFormKey()) {
                throw new Exception('Invalid form key');
            }
            
            $performanceBlock = Mage::app()->getLayout()
                ->createBlock('pfg_altcurrency/adminhtml_system_config_performance');
            
            $metrics = $performanceBlock->getPerformanceMetrics();
            $systemStatus = $performanceBlock->getSystemStatus();
            $formattedMetrics = $performanceBlock->formatMetrics($metrics);
            
            $result['success'] = true;
            $result['data'] = array(
                'metrics' => $formattedMetrics,
                'raw_metrics' => $metrics,
                'system_status' => $systemStatus,
                'timestamp' => time()
            );
            
        } catch (Exception $e) {
            $result['message'] = 'Failed to get performance metrics: ' . $e->getMessage();
            Mage::helper('altcurrency')->log('Performance metrics request failed: ' . $e->getMessage(), 'error');
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(json_encode($result));
    }
    
    /**
     * Refresh performance metrics action
     */
    public function refreshPerformanceMetricsAction()
    {
        $result = array(
            'success' => false,
            'message' => ''
        );
        
        try {
            if (!$this->_validateFormKey()) {
                throw new Exception('Invalid form key');
            }
            
            // Clear performance metrics cache
            $cache = Mage::app()->getCache();
            $cacheKey = 'pfg_altcurrency_performance_metrics';
            $cache->remove($cacheKey);
            
            $result['success'] = true;
            $result['message'] = 'Performance metrics have been refreshed successfully.';
            
            Mage::helper('altcurrency')->log('Performance metrics refreshed', 'info');
            
        } catch (Exception $e) {
            $result['message'] = 'Failed to refresh performance metrics: ' . $e->getMessage();
            Mage::helper('altcurrency')->log('Performance metrics refresh failed: ' . $e->getMessage(), 'error');
        }
        
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(json_encode($result));
    }
    
    /**
     * Validate test configuration
     *
     * @param string $symbol
     * @param float $rate
     * @param bool $enabled
     * @return array
     */
    protected function _validateTestConfiguration($symbol, $rate, $enabled)
    {
        $errors = array();
        
        if (empty($symbol)) {
            $errors[] = 'Currency symbol is required';
        } elseif (strlen($symbol) > 10) {
            $errors[] = 'Currency symbol cannot exceed 10 characters';
        }
        
        if ($rate <= 0) {
            $errors[] = 'Currency rate must be greater than 0';
        } elseif ($rate > 1000000) {
            $errors[] = 'Currency rate seems unreasonably high';
        }
        
        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'symbol' => htmlspecialchars($symbol, ENT_QUOTES, 'UTF-8'),
            'rate' => $rate,
            'enabled' => (bool)$enabled
        );
    }
    
    /**
     * Generate conversion examples
     *
     * @param float $rate
     * @param string $symbol
     * @return array
     */
    protected function _generateConversionExamples($rate, $symbol)
    {
        $samplePrices = array(10.00, 25.50, 99.99, 199.00, 1299.95);
        $examples = array();
        
        foreach ($samplePrices as $price) {
            $converted = $price * $rate;
            $examples[] = array(
                'original' => number_format($price, 2),
                'converted' => number_format($converted, 2, ',', ' '),
                'symbol' => htmlspecialchars($symbol, ENT_QUOTES, 'UTF-8'),
                'rate' => $rate
            );
        }
        
        return $examples;
    }
    
    /**
     * Check ACL permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('system/config/pfg_altcurrency');
    }
}
