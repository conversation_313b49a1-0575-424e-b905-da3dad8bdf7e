<?php
/**
 * PFG Alt Currency Helper
 *
 * Provides utility methods and configuration management for the Alt Currency module:
 * - Configuration value retrieval and validation
 * - Logging functionality with multiple levels
 * - Input sanitization and security validation
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'pfg_altcurrency/settings/enabled';
    const XML_PATH_SYMBOL = 'pfg_altcurrency/settings/symbol';
    const XML_PATH_RATE = 'pfg_altcurrency/settings/rate';

    /**
     * Log file name for Alt Currency module logging
     */
    const LOG_FILE = 'pfg_altcurrency.log';

    /**
     * Log levels
     */
    const LOG_LEVEL_DEBUG = 1;
    const LOG_LEVEL_INFO = 2;
    const LOG_LEVEL_WARNING = 3;
    const LOG_LEVEL_ERROR = 4;
    const LOG_LEVEL_CRITICAL = 5;

    /**
     * Get currency symbol from configuration
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getCurrencySymbol($store = null)
    {
        return trim(Mage::getStoreConfig(self::XML_PATH_SYMBOL, $store));
    }

    /**
     * Get currency rate from configuration
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return float
     */
    public function getCurrencyRate($store = null)
    {
        return floatval(Mage::getStoreConfig(self::XML_PATH_RATE, $store));
    }

    /**
     * Check if module is enabled
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return bool
     */
    public function isEnabled($store = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED, $store);
    }

    /**
     * Validate configuration values
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return array
     */
    public function validateConfiguration($store = null)
    {
        $symbol = $this->getCurrencySymbol($store);
        $rate = $this->getCurrencyRate($store);

        $errors = array();

        if (empty($symbol)) {
            $errors[] = 'Currency symbol is not configured';
        } elseif (strlen($symbol) > 10) {
            $errors[] = 'Currency symbol cannot exceed 10 characters';
        }

        if ($rate <= 0) {
            $errors[] = 'Currency rate must be greater than 0';
        } elseif ($rate > 1000000) {
            $errors[] = 'Currency rate seems unreasonably high';
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'symbol' => $symbol,
            'rate' => $rate
        );
    }

    /**
     * Sanitize currency symbol for safe output
     *
     * @param string $symbol
     * @return string
     */
    public function sanitizeSymbol($symbol)
    {
        return htmlspecialchars(trim($symbol), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Log message with context
     *
     * @param string $message
     * @param string $level
     * @param array $context
     */
    public function log($message, $level = 'info', $context = array())
    {
        $numericLevel = $this->_getNumericLogLevel($level);

        // Create structured log entry
        $logEntry = array(
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'module' => 'PFG_AltCurrency',
            'message' => $message
        );

        // Add request context if available
        if (Mage::app()->getRequest()) {
            $logEntry['request_id'] = substr(md5(uniqid()), 0, 8);
            $logEntry['user_agent'] = Mage::app()->getRequest()->getHeader('User-Agent');
        }

        // Format for file logging
        $logMessage = sprintf(
            '%s [%s] %s: %s',
            $logEntry['timestamp'],
            $logEntry['level'],
            $logEntry['module'],
            $message
        );

        if (!empty($context)) {
            $logMessage .= ' | Context: ' . json_encode($context, JSON_UNESCAPED_SLASHES);
        }

        // Log to file
        Mage::log($logMessage, null, self::LOG_FILE);

        // For critical errors, also log to system log
        if ($numericLevel >= self::LOG_LEVEL_CRITICAL) {
            Mage::log($logMessage, Zend_Log::CRIT, 'system.log');
        }
    }

    /**
     * Convert log level string to numeric value
     *
     * @param string $level
     * @return int
     */
    protected function _getNumericLogLevel($level)
    {
        $levels = array(
            'debug' => self::LOG_LEVEL_DEBUG,
            'info' => self::LOG_LEVEL_INFO,
            'warning' => self::LOG_LEVEL_WARNING,
            'error' => self::LOG_LEVEL_ERROR,
            'critical' => self::LOG_LEVEL_CRITICAL
        );

        return isset($levels[strtolower($level)]) ? $levels[strtolower($level)] : self::LOG_LEVEL_INFO;
    }
}

