<?php
/**
 * PFG Alt Currency Helper
 *
 * Provides utility methods and configuration management for the Alt Currency module:
 * - Configuration value retrieval and validation
 * - Logging functionality with multiple levels
 * - Input sanitization and security validation
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'pfg_altcurrency/settings/enabled';
    const XML_PATH_SYMBOL = 'pfg_altcurrency/settings/symbol';
    const XML_PATH_RATE = 'pfg_altcurrency/settings/rate';

    /**
     * Log file name for Alt Currency module logging
     */
    const LOG_FILE = 'pfg_altcurrency.log';

    /**
     * Log levels
     */
    const LOG_LEVEL_DEBUG = 1;
    const LOG_LEVEL_INFO = 2;
    const LOG_LEVEL_WARNING = 3;
    const LOG_LEVEL_ERROR = 4;
    const LOG_LEVEL_CRITICAL = 5;

    /**
     * Get currency symbol from configuration
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return string
     */
    public function getCurrencySymbol($store = null)
    {
        return trim(Mage::getStoreConfig(self::XML_PATH_SYMBOL, $store));
    }

    /**
     * Get currency rate from configuration
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return float
     */
    public function getCurrencyRate($store = null)
    {
        return floatval(Mage::getStoreConfig(self::XML_PATH_RATE, $store));
    }

    /**
     * Check if module is enabled
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return bool
     */
    public function isEnabled($store = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED, $store);
    }

    /**
     * Validate configuration values using centralized validator
     *
     * @param int|string|Mage_Core_Model_Store $store
     * @return array
     */
    public function validateConfiguration($store = null)
    {
        try {
            $validator = Mage::getModel('pfg_altcurrency/validator');

            $config = array(
                'enabled' => $this->isEnabled($store),
                'symbol' => $this->getCurrencySymbol($store),
                'rate' => $this->getCurrencyRate($store)
            );

            $result = $validator->validateConfiguration($config);

            // Convert to expected format
            return array(
                'valid' => $result['valid'],
                'errors' => $result['errors'],
                'warnings' => isset($result['warnings']) ? $result['warnings'] : array(),
                'symbol' => isset($result['sanitized']['symbol']) ? $result['sanitized']['symbol'] : $config['symbol'],
                'rate' => isset($result['sanitized']['rate']) ? $result['sanitized']['rate'] : $config['rate'],
                'enabled' => isset($result['sanitized']['enabled']) ? $result['sanitized']['enabled'] : $config['enabled']
            );

        } catch (Exception $e) {
            $this->log('Configuration validation failed: ' . $e->getMessage(), 'error');

            // Return basic validation as fallback
            return array(
                'valid' => false,
                'errors' => array('Configuration validation system error'),
                'warnings' => array(),
                'symbol' => $this->getCurrencySymbol($store),
                'rate' => $this->getCurrencyRate($store),
                'enabled' => $this->isEnabled($store)
            );
        }
    }

    /**
     * Sanitize currency symbol for safe output
     *
     * @param string $symbol
     * @return string
     */
    public function sanitizeSymbol($symbol)
    {
        return htmlspecialchars(trim($symbol), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Log message with context
     *
     * @param string $message
     * @param string $level
     * @param array $context
     */
    public function log($message, $level = 'info', $context = array())
    {
        $numericLevel = $this->_getNumericLogLevel($level);

        // Create structured log entry
        $logEntry = array(
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'module' => 'PFG_AltCurrency',
            'message' => $message
        );

        // Add request context if available
        if (Mage::app()->getRequest()) {
            $logEntry['request_id'] = substr(md5(uniqid()), 0, 8);
            $logEntry['user_agent'] = Mage::app()->getRequest()->getHeader('User-Agent');
        }

        // Format for file logging
        $logMessage = sprintf(
            '%s [%s] %s: %s',
            $logEntry['timestamp'],
            $logEntry['level'],
            $logEntry['module'],
            $message
        );

        if (!empty($context)) {
            $logMessage .= ' | Context: ' . json_encode($context, JSON_UNESCAPED_SLASHES);
        }

        // Log to file
        Mage::log($logMessage, null, self::LOG_FILE);

        // For critical errors, also log to system log
        if ($numericLevel >= self::LOG_LEVEL_CRITICAL) {
            Mage::log($logMessage, Zend_Log::CRIT, 'system.log');
        }
    }

    /**
     * Convert log level string to numeric value
     *
     * @param string $level
     * @return int
     */
    protected function _getNumericLogLevel($level)
    {
        $levels = array(
            'debug' => self::LOG_LEVEL_DEBUG,
            'info' => self::LOG_LEVEL_INFO,
            'warning' => self::LOG_LEVEL_WARNING,
            'error' => self::LOG_LEVEL_ERROR,
            'critical' => self::LOG_LEVEL_CRITICAL
        );

        return isset($levels[strtolower($level)]) ? $levels[strtolower($level)] : self::LOG_LEVEL_INFO;
    }

    /**
     * Get performance optimization recommendations
     *
     * @return array
     */
    public function getPerformanceRecommendations()
    {
        $recommendations = array();

        // Check if caching is enabled
        if (!Mage::app()->useCache('config')) {
            $recommendations[] = array(
                'type' => 'warning',
                'message' => 'Configuration cache is disabled. Enable it for better performance.',
                'action' => 'Enable configuration cache in System > Cache Management'
            );
        }

        // Check memory limit
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = $this->_convertToBytes($memoryLimit);
        if ($memoryLimitBytes < 256 * 1024 * 1024) { // Less than 256MB
            $recommendations[] = array(
                'type' => 'warning',
                'message' => 'Memory limit is low (' . $memoryLimit . '). Consider increasing it.',
                'action' => 'Increase PHP memory_limit to at least 256M'
            );
        }

        // Check if log file is getting too large
        $logFile = Mage::getBaseDir('var') . '/log/' . self::LOG_FILE;
        if (file_exists($logFile) && filesize($logFile) > 10 * 1024 * 1024) { // 10MB
            $recommendations[] = array(
                'type' => 'info',
                'message' => 'Log file is getting large. Consider rotating logs.',
                'action' => 'Archive or truncate ' . self::LOG_FILE
            );
        }

        return $recommendations;
    }

    /**
     * Convert memory limit string to bytes
     *
     * @param string $val
     * @return int
     */
    protected function _convertToBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = (int)$val;

        switch($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }
}

