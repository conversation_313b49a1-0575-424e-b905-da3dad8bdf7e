<?php
/**
 * PFG Alt Currency Base Exception
 *
 * Base exception class for all Alt Currency module exceptions
 * Provides structured error handling and logging capabilities
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Exception_Base extends Mage_Core_Exception
{
    /**
     * Exception context data
     * @var array
     */
    protected $_context = array();
    
    /**
     * Exception severity level
     * @var string
     */
    protected $_severity = 'error';
    
    /**
     * Whether to log this exception automatically
     * @var bool
     */
    protected $_autoLog = true;
    
    /**
     * Constructor
     *
     * @param string $message
     * @param int $code
     * @param array $context
     * @param string $severity
     */
    public function __construct($message = '', $code = 0, $context = array(), $severity = 'error')
    {
        parent::__construct($message, $code);
        
        $this->_context = $context;
        $this->_severity = $severity;
        
        if ($this->_autoLog) {
            $this->_logException();
        }
    }
    
    /**
     * Get exception context data
     *
     * @return array
     */
    public function getContext()
    {
        return $this->_context;
    }
    
    /**
     * Set exception context data
     *
     * @param array $context
     * @return PFG_AltCurrency_Exception_Base
     */
    public function setContext($context)
    {
        $this->_context = $context;
        return $this;
    }
    
    /**
     * Get exception severity level
     *
     * @return string
     */
    public function getSeverity()
    {
        return $this->_severity;
    }
    
    /**
     * Set exception severity level
     *
     * @param string $severity
     * @return PFG_AltCurrency_Exception_Base
     */
    public function setSeverity($severity)
    {
        $this->_severity = $severity;
        return $this;
    }
    
    /**
     * Get formatted exception message with context
     *
     * @return string
     */
    public function getFormattedMessage()
    {
        $message = $this->getMessage();
        
        if (!empty($this->_context)) {
            $message .= ' | Context: ' . json_encode($this->_context, JSON_UNESCAPED_SLASHES);
        }
        
        return $message;
    }
    
    /**
     * Log exception automatically
     *
     * @return void
     */
    protected function _logException()
    {
        try {
            $helper = Mage::helper('altcurrency');
            
            $logContext = array_merge($this->_context, array(
                'exception_class' => get_class($this),
                'file' => $this->getFile(),
                'line' => $this->getLine(),
                'trace' => $this->getTraceAsString()
            ));
            
            $helper->log($this->getMessage(), $this->_severity, $logContext);
            
        } catch (Exception $e) {
            // Prevent infinite loops if logging fails
            error_log('Failed to log PFG Alt Currency exception: ' . $e->getMessage());
        }
    }
    
    /**
     * Convert exception to array for API responses
     *
     * @return array
     */
    public function toArray()
    {
        return array(
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'severity' => $this->_severity,
            'context' => $this->_context,
            'file' => $this->getFile(),
            'line' => $this->getLine()
        );
    }
    
    /**
     * Create exception from array data
     *
     * @param array $data
     * @return PFG_AltCurrency_Exception_Base
     */
    public static function fromArray($data)
    {
        return new static(
            isset($data['message']) ? $data['message'] : '',
            isset($data['code']) ? $data['code'] : 0,
            isset($data['context']) ? $data['context'] : array(),
            isset($data['severity']) ? $data['severity'] : 'error'
        );
    }
}
