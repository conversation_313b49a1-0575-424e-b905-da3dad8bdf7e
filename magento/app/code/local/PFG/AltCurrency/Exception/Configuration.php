<?php
/**
 * PFG Alt Currency Configuration Exception
 *
 * Exception thrown when configuration validation fails
 * or configuration-related errors occur
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Exception_Configuration extends PFG_AltCurrency_Exception_Base
{
    /**
     * Configuration field that caused the error
     * @var string
     */
    protected $_field = '';
    
    /**
     * Configuration value that caused the error
     * @var mixed
     */
    protected $_value = null;
    
    /**
     * Validation rules that failed
     * @var array
     */
    protected $_failedRules = array();
    
    /**
     * Constructor
     *
     * @param string $message
     * @param string $field
     * @param mixed $value
     * @param array $failedRules
     * @param int $code
     */
    public function __construct($message = '', $field = '', $value = null, $failedRules = array(), $code = 0)
    {
        $this->_field = $field;
        $this->_value = $value;
        $this->_failedRules = $failedRules;
        
        $context = array(
            'field' => $field,
            'value' => $value,
            'failed_rules' => $failedRules
        );
        
        parent::__construct($message, $code, $context, 'error');
    }
    
    /**
     * Get configuration field that caused the error
     *
     * @return string
     */
    public function getField()
    {
        return $this->_field;
    }
    
    /**
     * Get configuration value that caused the error
     *
     * @return mixed
     */
    public function getValue()
    {
        return $this->_value;
    }
    
    /**
     * Get validation rules that failed
     *
     * @return array
     */
    public function getFailedRules()
    {
        return $this->_failedRules;
    }
    
    /**
     * Create exception for invalid currency symbol
     *
     * @param string $symbol
     * @param array $failedRules
     * @return PFG_AltCurrency_Exception_Configuration
     */
    public static function invalidSymbol($symbol, $failedRules = array())
    {
        return new self(
            'Invalid currency symbol provided',
            'symbol',
            $symbol,
            $failedRules
        );
    }
    
    /**
     * Create exception for invalid currency rate
     *
     * @param float $rate
     * @param array $failedRules
     * @return PFG_AltCurrency_Exception_Configuration
     */
    public static function invalidRate($rate, $failedRules = array())
    {
        return new self(
            'Invalid currency rate provided',
            'rate',
            $rate,
            $failedRules
        );
    }
    
    /**
     * Create exception for missing required configuration
     *
     * @param string $field
     * @return PFG_AltCurrency_Exception_Configuration
     */
    public static function missingRequired($field)
    {
        return new self(
            'Required configuration field is missing',
            $field,
            null,
            array('required')
        );
    }
}
