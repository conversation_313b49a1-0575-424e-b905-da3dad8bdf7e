<?php
/**
 * PFG Alt Currency Processing Exception
 *
 * Exception thrown during HTML processing or price conversion operations
 * Includes context about the processing state when the error occurred
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Exception_Processing extends PFG_AltCurrency_Exception_Base
{
    /**
     * HTML content being processed when error occurred
     * @var string
     */
    protected $_htmlContent = '';
    
    /**
     * Processing stage when error occurred
     * @var string
     */
    protected $_processingStage = '';
    
    /**
     * Element being processed when error occurred
     * @var string
     */
    protected $_currentElement = '';
    
    /**
     * Constructor
     *
     * @param string $message
     * @param string $processingStage
     * @param string $htmlContent
     * @param string $currentElement
     * @param int $code
     */
    public function __construct($message = '', $processingStage = '', $htmlContent = '', $currentElement = '', $code = 0)
    {
        $this->_processingStage = $processingStage;
        $this->_htmlContent = $htmlContent;
        $this->_currentElement = $currentElement;
        
        $context = array(
            'processing_stage' => $processingStage,
            'html_length' => strlen($htmlContent),
            'current_element_length' => strlen($currentElement),
            'html_preview' => substr($htmlContent, 0, 200) . (strlen($htmlContent) > 200 ? '...' : ''),
            'element_preview' => substr($currentElement, 0, 100) . (strlen($currentElement) > 100 ? '...' : '')
        );
        
        parent::__construct($message, $code, $context, 'warning');
    }
    
    /**
     * Get processing stage when error occurred
     *
     * @return string
     */
    public function getProcessingStage()
    {
        return $this->_processingStage;
    }
    
    /**
     * Get HTML content being processed
     *
     * @return string
     */
    public function getHtmlContent()
    {
        return $this->_htmlContent;
    }
    
    /**
     * Get current element being processed
     *
     * @return string
     */
    public function getCurrentElement()
    {
        return $this->_currentElement;
    }
    
    /**
     * Create exception for regex processing timeout
     *
     * @param string $pattern
     * @param string $htmlContent
     * @return PFG_AltCurrency_Exception_Processing
     */
    public static function regexTimeout($pattern, $htmlContent)
    {
        return new self(
            'Regex processing timeout - pattern too complex for input size',
            'regex_processing',
            $htmlContent,
            $pattern
        );
    }
    
    /**
     * Create exception for HTML parsing failure
     *
     * @param string $htmlContent
     * @param string $parserError
     * @return PFG_AltCurrency_Exception_Processing
     */
    public static function htmlParsingFailed($htmlContent, $parserError = '')
    {
        return new self(
            'HTML parsing failed: ' . $parserError,
            'html_parsing',
            $htmlContent,
            $parserError
        );
    }
    
    /**
     * Create exception for price conversion failure
     *
     * @param string $priceText
     * @param string $reason
     * @return PFG_AltCurrency_Exception_Processing
     */
    public static function priceConversionFailed($priceText, $reason = '')
    {
        return new self(
            'Price conversion failed: ' . $reason,
            'price_conversion',
            '',
            $priceText
        );
    }
    
    /**
     * Create exception for memory limit exceeded
     *
     * @param string $processingStage
     * @param int $memoryUsage
     * @return PFG_AltCurrency_Exception_Processing
     */
    public static function memoryLimitExceeded($processingStage, $memoryUsage)
    {
        $exception = new self(
            'Memory limit exceeded during processing',
            $processingStage,
            '',
            ''
        );
        
        $exception->setContext(array_merge($exception->getContext(), array(
            'memory_usage' => $memoryUsage,
            'memory_limit' => ini_get('memory_limit')
        )));
        
        return $exception;
    }
}
