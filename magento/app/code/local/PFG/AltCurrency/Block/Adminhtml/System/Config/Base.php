<?php
/**
 * PFG Alt Currency Base Admin Block
 *
 * Base class for all Alt Currency admin configuration blocks
 * Provides common functionality and security features
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
abstract class PFG_AltCurrency_Block_Adminhtml_System_Config_Base extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Helper instance cache
     * @var PFG_AltCurrency_Helper_Data
     */
    protected $_helper = null;
    
    /**
     * Security token for AJAX requests
     * @var string
     */
    protected $_securityToken = null;
    
    /**
     * Get Alt Currency helper
     *
     * @return PFG_AltCurrency_Helper_Data
     */
    protected function _getHelper()
    {
        if ($this->_helper === null) {
            $this->_helper = Mage::helper('altcurrency');
        }
        return $this->_helper;
    }
    
    /**
     * Get security token for AJAX requests
     *
     * @return string
     */
    protected function _getSecurityToken()
    {
        if ($this->_securityToken === null) {
            $this->_securityToken = Mage::getSingleton('core/session')->getFormKey();
        }
        return $this->_securityToken;
    }
    
    /**
     * Get AJAX URL with security token
     *
     * @param string $action
     * @param array $params
     * @return string
     */
    protected function _getAjaxUrl($action, $params = array())
    {
        $params['form_key'] = $this->_getSecurityToken();
        return Mage::helper('adminhtml')->getUrl('adminhtml/pfg_altcurrency/' . $action, $params);
    }
    
    /**
     * Validate admin permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('system/config/pfg_altcurrency');
    }
    
    /**
     * Get current configuration values
     *
     * @return array
     */
    protected function _getCurrentConfig()
    {
        $helper = $this->_getHelper();
        return array(
            'enabled' => $helper->isEnabled(),
            'symbol' => $helper->getCurrencySymbol(),
            'rate' => $helper->getCurrencyRate()
        );
    }
    
    /**
     * Render block with permission check
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        if (!$this->_isAllowed()) {
            return '<div class="notice-msg"><ul><li>You do not have permission to access this feature.</li></ul></div>';
        }
        
        return parent::render($element);
    }
    
    /**
     * Prepare layout with security checks
     *
     * @return PFG_AltCurrency_Block_Adminhtml_System_Config_Base
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        
        // Add CSRF protection meta tag if not already present
        $head = $this->getLayout()->getBlock('head');
        if ($head && !$head->getData('csrf_token_added')) {
            $head->addItem('js_css', 'var FORM_KEY = "' . $this->_getSecurityToken() . '";');
            $head->setData('csrf_token_added', true);
        }
        
        return $this;
    }
    
    /**
     * Get escaped HTML for safe output
     *
     * @param string $data
     * @return string
     */
    protected function _escapeHtml($data)
    {
        return $this->escapeHtml($data);
    }
    
    /**
     * Get JSON-encoded data for JavaScript
     *
     * @param mixed $data
     * @return string
     */
    protected function _jsonEncode($data)
    {
        return Mage::helper('core')->jsonEncode($data);
    }
    
    /**
     * Log block-specific messages
     *
     * @param string $message
     * @param string $level
     * @param array $context
     */
    protected function _log($message, $level = 'info', $context = array())
    {
        $context['block_class'] = get_class($this);
        $this->_getHelper()->log($message, $level, $context);
    }
    
    /**
     * Get common JavaScript for admin blocks
     *
     * @return string
     */
    protected function _getCommonJavaScript()
    {
        return '
        <script type="text/javascript">
        //<![CDATA[
        if (typeof PfgAltCurrency === "undefined") {
            var PfgAltCurrency = {
                showMessage: function(message, type) {
                    var messageDiv = document.createElement("div");
                    messageDiv.className = (type === "error" ? "error-msg" : "success-msg");
                    messageDiv.innerHTML = "<ul><li><span>" + message + "</span></li></ul>";
                    
                    // Find a good place to insert the message
                    var container = document.querySelector(".content-header") || document.body;
                    container.appendChild(messageDiv);
                    
                    // Auto-remove after 5 seconds
                    setTimeout(function() {
                        if (messageDiv.parentNode) {
                            messageDiv.parentNode.removeChild(messageDiv);
                        }
                    }, 5000);
                },
                
                handleAjaxError: function(response) {
                    try {
                        var result = response.responseText.evalJSON();
                        this.showMessage(result.message || "An error occurred", "error");
                    } catch (e) {
                        this.showMessage("Communication error occurred", "error");
                    }
                }
            };
        }
        //]]>
        </script>';
    }
}
