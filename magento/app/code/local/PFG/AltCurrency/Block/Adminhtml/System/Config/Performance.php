<?php
/**
 * PFG Alt Currency Performance Status Block
 *
 * Displays real-time performance metrics and system status
 * for the Alt Currency module
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Block_Adminhtml_System_Config_Performance extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_altcurrency/system/config/performance.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the performance status HTML
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
            'ajax_url' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_altcurrency/getPerformanceMetrics'),
            'refresh_url' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_altcurrency/refreshPerformanceMetrics'),
        ));
        
        return $this->_toHtml();
    }
    
    /**
     * Get current performance metrics
     *
     * @return array
     */
    public function getPerformanceMetrics()
    {
        // Get metrics from cache or default values
        $cache = Mage::app()->getCache();
        $cacheKey = 'pfg_altcurrency_performance_metrics';
        
        $metrics = $cache->load($cacheKey);
        if ($metrics) {
            $metrics = unserialize($metrics);
        } else {
            $metrics = array(
                'total_calls' => 0,
                'processed_elements' => 0,
                'cache_hits' => 0,
                'processing_time' => 0,
                'memory_usage' => 0,
                'last_updated' => time()
            );
        }
        
        return $metrics;
    }
    
    /**
     * Get system status information
     *
     * @return array
     */
    public function getSystemStatus()
    {
        $helper = Mage::helper('altcurrency');
        $config = $helper->validateConfiguration();
        
        $status = array(
            'module_enabled' => $helper->isEnabled(),
            'configuration_valid' => $config['valid'],
            'log_file_exists' => file_exists(Mage::getBaseDir('var') . '/log/' . $helper::LOG_FILE),
            'log_file_writable' => is_writable(Mage::getBaseDir('var') . '/log/'),
            'cache_enabled' => Mage::app()->useCache('config'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time')
        );
        
        if (!$config['valid']) {
            $status['configuration_errors'] = $config['errors'];
        }
        
        return $status;
    }
    
    /**
     * Format performance metrics for display
     *
     * @param array $metrics
     * @return array
     */
    public function formatMetrics($metrics)
    {
        return array(
            'total_calls' => number_format($metrics['total_calls']),
            'processed_elements' => number_format($metrics['processed_elements']),
            'cache_hits' => number_format($metrics['cache_hits']),
            'processing_time' => number_format($metrics['processing_time'] * 1000, 2) . ' ms',
            'memory_usage' => $this->_formatBytes($metrics['memory_usage']),
            'last_updated' => date('Y-m-d H:i:s', $metrics['last_updated'])
        );
    }
    
    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    protected function _formatBytes($bytes)
    {
        $units = array('B', 'KB', 'MB', 'GB');
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
