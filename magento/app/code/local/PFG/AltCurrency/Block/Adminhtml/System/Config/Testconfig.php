<?php
/**
 * PFG Alt Currency Test Configuration Button Block
 *
 * Provides a test button to validate currency conversion configuration
 * and display real-time conversion examples
 *
 * @category   PFG
 * @package    PFG_AltCurrency
 * <AUTHOR> Development Team
 * @version    1.0.0
 */
class PFG_AltCurrency_Block_Adminhtml_System_Config_Testconfig extends PFG_AltCurrency_Block_Adminhtml_System_Config_Base
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_altcurrency/system/config/testconfig.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the button and scripts contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $originalData = $element->getOriginalData();
        $buttonLabel = !empty($originalData['button_label']) ? $originalData['button_label'] : 'Test Configuration';
        
        $this->addData(array(
            'button_label' => $buttonLabel,
            'html_id' => $element->getHtmlId(),
            'ajax_url' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_altcurrency/testConfig'),
        ));
        
        return $this->_toHtml();
    }
    
    /**
     * Get current configuration values for JavaScript
     *
     * @return array
     */
    public function getCurrentConfig()
    {
        $helper = Mage::helper('altcurrency');
        return array(
            'enabled' => $helper->isEnabled(),
            'symbol' => $helper->getCurrencySymbol(),
            'rate' => $helper->getCurrencyRate()
        );
    }
    
    /**
     * Get sample prices for testing
     *
     * @return array
     */
    public function getSamplePrices()
    {
        return array(
            '10.00',
            '25.50',
            '99.99',
            '199.00',
            '1,299.95'
        );
    }
}
