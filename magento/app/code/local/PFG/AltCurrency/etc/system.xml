<?xml version="1.0"?>
<config>
    <tabs>
        <pfg translate="label" module="altcurrency">
            <label>PFG</label>
            <sort_order>200</sort_order>
        </pfg>
    </tabs>

    <sections>
        <pfg_altcurrency translate="label" module="altcurrency">
            <label>Alt Currency</label>
            <tab>pfg</tab>
            <frontend_type>text</frontend_type>
            <sort_order>500</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <settings translate="label">
                    <label>Settings</label>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label comment">
                            <label>Enable Alt Currency</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>5</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Enable or disable alternative currency display</comment>
                        </enabled>
                        <symbol translate="label comment">
                            <label>Currency Symbol</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>pfg_altcurrency/system_config_backend_symbol</backend_model>
                            <validate>required-entry validate-length maximum-length-10</validate>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Currency symbol to display (max 10 characters, HTML entities will be escaped)</comment>
                            <depends><enabled>1</enabled></depends>
                        </symbol>
                        <rate translate="label comment">
                            <label>Currency Rate</label>
                            <frontend_type>text</frontend_type>
                            <backend_model>pfg_altcurrency/system_config_backend_rate</backend_model>
                            <validate>required-entry validate-number validate-greater-than-zero</validate>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment>Conversion rate (must be greater than 0, max 8 decimal places)</comment>
                            <depends><enabled>1</enabled></depends>
                        </rate>
                    </fields>
                </settings>
            </groups>
        </pfg_altcurrency>
    </sections>
</config>

