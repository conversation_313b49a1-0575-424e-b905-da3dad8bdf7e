<?php
/**
 * PFG Core Repository Service
 * 
 * Service layer for repository operations with performance optimization,
 * caching, and batch processing to eliminate N+1 queries.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_Service_Repository extends PFG_Core_Model_Service_Abstract
{
    /**
     * @var PFG_Core_Helper_Bitbucket
     */
    protected $_bitbucketHelper;
    
    /**
     * @var PFG_Core_Helper_Validator
     */
    protected $_validator;

    /**
     * Constructor with dependency injection
     */
    public function __construct()
    {
        parent::__construct();
        $this->_bitbucketHelper = Mage::helper('pfg_core/bitbucket');
        $this->_validator = Mage::helper('pfg_core/validator');
    }
    
    /**
     * Get all repositories with status (optimized version)
     *
     * @param bool $useCache
     * @return array
     */
    public function getRepositoriesWithStatus($useCache = true)
    {
        return $this->_executeWithErrorHandling(function() use ($useCache) {
            $cacheKey = 'repositories_with_status';

            if ($useCache) {
                return $this->_getCachedOrExecute($cacheKey, function() {
                    return $this->_fetchRepositoriesWithStatus();
                });
            }

            return $this->_fetchRepositoriesWithStatus();
        }, 'getRepositoriesWithStatus');
    }
    
    /**
     * Fetch repositories with status (internal optimized method)
     *
     * @return array
     */
    protected function _fetchRepositoriesWithStatus()
    {
        // Get repositories from Bitbucket API
        $response = $this->_bitbucketHelper->getRepositories();

        if (!$response['success']) {
            throw new PFG_Core_Exception_Api($response['error']);
        }

        $repositories = $response['data'];
        $installedModules = $this->_getInstalledModules();

        // Batch fetch all installation records to avoid N+1 queries
        $moduleNames = array();
        foreach ($repositories as $repo) {
            $repositoryIdentifier = isset($repo['slug']) ? $repo['slug'] : $repo['name'];
            $moduleName = $this->_getModuleNameFromRepository($repositoryIdentifier);
            $moduleNames[] = $moduleName;
        }

        $installationRecords = $this->_getInstallationRecordsBatch($moduleNames);
        
        // Batch fetch repository tags to minimize API calls
        $repositoryIdentifiers = array();
        foreach ($repositories as $repo) {
            $repositoryIdentifiers[] = isset($repo['slug']) ? $repo['slug'] : $repo['name'];
        }
        
        $tagsData = $this->_getRepositoryTagsBatch($repositoryIdentifiers);
        
        // Process repositories with cached data
        foreach ($repositories as &$repo) {
            $repositoryIdentifier = isset($repo['slug']) ? $repo['slug'] : $repo['name'];
            $moduleName = $this->_getModuleNameFromRepository($repositoryIdentifier);
            
            $repo['module_name'] = $moduleName;
            $repo['repository_identifier'] = $repositoryIdentifier;
            $repo['is_installed'] = isset($installedModules[$moduleName]);
            $repo['installed_version'] = isset($installedModules[$moduleName]) ? $installedModules[$moduleName] : null;
            
            // Use cached tags data
            if (isset($tagsData[$repositoryIdentifier]) && !empty($tagsData[$repositoryIdentifier])) {
                $repo['latest_version'] = $tagsData[$repositoryIdentifier][0]['name'];
                $repo['has_update'] = $repo['is_installed'] &&
                    version_compare($repo['installed_version'], $repo['latest_version'], '<');
            } else {
                $repo['latest_version'] = 'master';
                $repo['has_update'] = false;
            }
            
            $repo['installation_status'] = $this->_getInstallationStatus($repo);
            $repo['last_installation'] = isset($installationRecords[$moduleName]) ? $installationRecords[$moduleName] : null;
        }
        
        $this->_dispatchEvent('repositories_loaded', array('repositories' => $repositories));
        
        return $repositories;
    }
    
    /**
     * Get installation records in batch to avoid N+1 queries
     *
     * @param array $moduleNames
     * @return array
     */
    protected function _getInstallationRecordsBatch(array $moduleNames)
    {
        if (empty($moduleNames)) {
            return array();
        }
        
        $collection = Mage::getModel('pfg_core/installation')->getCollection()
            ->addFieldToFilter('module_name', array('in' => $moduleNames))
            ->addFieldToFilter('installation_status', array('in' => array('completed', 'failed')))
            ->setOrder('installed_at', 'DESC');
        
        $records = array();
        foreach ($collection as $installation) {
            $moduleName = $installation->getModuleName();
            
            // Only keep the latest installation record per module
            if (!isset($records[$moduleName])) {
                $records[$moduleName] = array(
                    'installation_id' => $installation->getId(),
                    'backup_id' => $installation->getBackupId(),
                    'installation_type' => $installation->getInstallationType(),
                    'version_installed' => $installation->getVersionInstalled(),
                    'installed_at' => $installation->getInstalledAt(),
                    'can_rollback' => $installation->getBackupId() && $installation->getInstallationStatus() === 'completed'
                );
            }
        }
        
        return $records;
    }
    
    /**
     * Get repository tags in batch to minimize API calls
     *
     * @param array $repositoryIdentifiers
     * @return array
     */
    protected function _getRepositoryTagsBatch(array $repositoryIdentifiers)
    {
        $tagsData = array();
        
        foreach ($repositoryIdentifiers as $identifier) {
            $cacheKey = 'repository_tags_' . $identifier;
            
            $tags = $this->_getCachedOrExecute($cacheKey, function() use ($identifier) {
                $response = $this->_bitbucketHelper->getRepositoryTags($identifier);
                return $response['success'] ? $response['data'] : array();
            }, 300); // Cache tags for 5 minutes
            
            $tagsData[$identifier] = $tags;
        }
        
        return $tagsData;
    }
    
    /**
     * Get repository details with caching
     *
     * @param string $repositoryName
     * @param bool $useCache
     * @return array
     */
    public function getRepositoryDetails($repositoryName, $useCache = true)
    {
        return $this->_executeWithErrorHandling(function() use ($repositoryName, $useCache) {
            $this->_validateRequiredParams(array('repositoryName' => $repositoryName), array('repositoryName'));
            
            $cacheKey = 'repository_details_' . $repositoryName;
            
            if ($useCache) {
                return $this->_getCachedOrExecute($cacheKey, function() use ($repositoryName) {
                    return $this->_fetchRepositoryDetails($repositoryName);
                });
            }
            
            return $this->_fetchRepositoryDetails($repositoryName);
        }, 'getRepositoryDetails', array('repository' => $repositoryName));
    }
    
    /**
     * Fetch repository details (internal method)
     *
     * @param string $repositoryName
     * @return array
     * @throws PFG_Core_Exception_Validation
     */
    protected function _fetchRepositoryDetails($repositoryName)
    {
        $repositories = $this->getRepositoriesWithStatus();
        
        if (!$repositories['success']) {
            throw new PFG_Core_Exception_Api($repositories['error']);
        }
        
        foreach ($repositories['data'] as $repo) {
            $repositoryIdentifier = isset($repo['repository_identifier']) ? $repo['repository_identifier'] : $repo['name'];
            if ($repo['name'] === $repositoryName ||
                (isset($repo['slug']) && $repo['slug'] === $repositoryName) ||
                $repositoryIdentifier === $repositoryName) {
                
                // Get additional details like tags using cached data
                $cacheKey = 'repository_tags_' . $repositoryIdentifier;
                $tags = $this->_getCachedOrExecute($cacheKey, function() use ($repositoryIdentifier) {
                    $response = $this->_bitbucketHelper->getRepositoryTags($repositoryIdentifier);
                    return $response['success'] ? $response['data'] : array();
                });
                
                $repo['tags'] = $tags;
                
                return $repo;
            }
        }
        
        throw new PFG_Core_Exception_Validation('Repository not found: ' . $repositoryName);
    }
    
    /**
     * Check if repository can be installed with comprehensive validation
     *
     * @param string $repositoryName
     * @return array
     */
    public function canInstallRepository($repositoryName)
    {
        return $this->_executeWithErrorHandling(function() use ($repositoryName) {
            $this->_validateRequiredParams(array('repositoryName' => $repositoryName), array('repositoryName'));
            
            $repoDetails = $this->getRepositoryDetails($repositoryName);
            
            if (!$repoDetails['success']) {
                throw new PFG_Core_Exception_Api($repoDetails['error']);
            }
            
            $repo = $repoDetails['data'];
            $checks = array();
            $canInstall = true;
            
            // System requirements validation
            $systemChecks = $this->_validator->validateSystemRequirements();
            if (!$systemChecks['can_install']) {
                $canInstall = false;
            }
            $checks = array_merge($checks, $systemChecks['checks']);
            
            // Module compatibility validation
            $compatibilityChecks = $this->_validator->validateModuleCompatibility($repo['module_name']);
            $checks = array_merge($checks, $compatibilityChecks['checks']);
            
            // Additional validation checks
            $additionalChecks = $this->_performAdditionalValidation($repo);
            $checks = array_merge($checks, $additionalChecks['checks']);
            if (!$additionalChecks['is_valid']) {
                $canInstall = false;
            }
            
            return array(
                'can_install' => $canInstall,
                'checks' => $checks,
                'repository' => $repo
            );
            
        }, 'canInstallRepository', array('repository' => $repositoryName));
    }
    
    /**
     * Perform additional validation checks
     *
     * @param array $repo
     * @return array
     */
    protected function _performAdditionalValidation($repo)
    {
        $checks = array();
        $isValid = true;
        
        // Check if already installed
        if ($repo['is_installed']) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->_helper->__('Module is already installed (version %s)', $repo['installed_version'])
            );
        }
        
        // Check module name format
        if (!$this->_helper->isValidModuleName($repo['module_name'])) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->_helper->__('Invalid module name format: %s', $repo['module_name'])
            );
            $isValid = false;
        }
        
        // Security validation
        $securityChecks = $this->_validateModuleSecurity($repo);
        if (!$securityChecks['is_secure']) {
            $isValid = false;
        }
        $checks = array_merge($checks, $securityChecks['checks']);
        
        // Admin user validation
        $adminChecks = $this->_validateAdminPermissions();
        if (!$adminChecks['has_permission']) {
            $isValid = false;
        }
        $checks = array_merge($checks, $adminChecks['checks']);
        
        return array(
            'is_valid' => $isValid,
            'checks' => $checks
        );
    }

    /**
     * Get installed modules (cached)
     *
     * @return array
     */
    protected function _getInstalledModules()
    {
        return $this->_getCachedOrExecute('installed_modules', function() {
            $modules = array();
            $config = Mage::getConfig();

            foreach ($config->getNode('modules')->children() as $moduleName => $moduleConfig) {
                if ($moduleConfig->active == 'true') {
                    $modules[$moduleName] = (string)$moduleConfig->version;
                }
            }

            return $modules;
        }, 600); // Cache for 10 minutes
    }

    /**
     * Get module name from repository name
     *
     * @param string $repositoryName
     * @return string
     */
    protected function _getModuleNameFromRepository($repositoryName)
    {
        // Convert repository name to module name format
        // e.g., "pfg-cloudflare-integration" -> "PFG_CloudflareIntegration"
        $parts = explode('-', $repositoryName);
        $moduleName = '';

        foreach ($parts as $part) {
            $moduleName .= ucfirst(strtolower($part));
        }

        // Ensure it starts with PFG_
        if (strpos($moduleName, 'Pfg') === 0) {
            $moduleName = 'PFG_' . substr($moduleName, 3);
        } elseif (strpos($moduleName, 'PFG') !== 0) {
            $moduleName = 'PFG_' . $moduleName;
        }

        return $moduleName;
    }

    /**
     * Get installation status
     *
     * @param array $repo
     * @return string
     */
    protected function _getInstallationStatus($repo)
    {
        if (!$repo['is_installed']) {
            return 'not_installed';
        }

        if ($repo['has_update']) {
            return 'update_available';
        }

        return 'installed';
    }

    /**
     * Validate module security
     *
     * @param array $repo
     * @return array
     */
    protected function _validateModuleSecurity($repo)
    {
        $checks = array();
        $isSecure = true;

        // Check repository source
        if (!$repo['is_private']) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->_helper->__('Repository is public - ensure it contains trusted code')
            );
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->_helper->__('Repository is private ✓')
            );
        }

        // Check module namespace
        if (strpos($repo['module_name'], 'PFG_') !== 0) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->_helper->__('Module is not in PFG namespace - verify source')
            );
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->_helper->__('Module is in PFG namespace ✓')
            );
        }

        // Check for suspicious patterns in repository name
        $suspiciousPatterns = array('hack', 'exploit', 'backdoor', 'malware', 'virus');
        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($repo['name'], $pattern) !== false) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->_helper->__('Repository name contains suspicious pattern: %s', $pattern)
                );
                $isSecure = false;
            }
        }

        return array(
            'is_secure' => $isSecure,
            'checks' => $checks
        );
    }

    /**
     * Validate admin permissions
     *
     * @return array
     */
    protected function _validateAdminPermissions()
    {
        $checks = array();
        $hasPermission = true;

        $session = Mage::getSingleton('admin/session');

        // Check if admin is logged in
        if (!$session->isLoggedIn()) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->_helper->__('Admin session required')
            );
            $hasPermission = false;
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->_helper->__('Admin session active ✓')
            );

            // Check specific permissions
            if (!$session->isAllowed('pfg/core')) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->_helper->__('Insufficient permissions for PFG Core operations')
                );
                $hasPermission = false;
            } else {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->_helper->__('Admin has required permissions ✓')
                );
            }

            // Check if user is super admin
            $user = $session->getUser();
            if ($user && $user->getRole() && $user->getRole()->getRoleName() === 'Administrators') {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->_helper->__('User has administrator role ✓')
                );
            } else {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->_helper->__('User does not have administrator role - proceed with caution')
                );
            }
        }

        return array(
            'has_permission' => $hasPermission,
            'checks' => $checks
        );
    }

    /**
     * Clear repository cache
     *
     * @param string $repositoryName
     * @return $this
     */
    public function clearRepositoryCache($repositoryName = null)
    {
        if ($repositoryName) {
            $this->_clearCache('repository_details_' . $repositoryName);
            $this->_clearCache('repository_tags_' . $repositoryName);
        } else {
            $this->_clearCache('repositories_with_status');
            $this->_clearCache('installed_modules');
            // Clear all repository-related cache
            $this->_clearCache();
        }

        return $this;
    }
}
