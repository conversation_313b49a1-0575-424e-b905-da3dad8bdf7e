<?php
/**
 * PFG Core Abstract Service Class
 * 
 * Base class for all service layer implementations with dependency injection,
 * standardized error handling, and performance optimization features.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
abstract class PFG_Core_Model_Service_Abstract
{
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * @var PFG_Core_Model_ErrorHandler
     */
    protected $_errorHandler;
    
    /**
     * @var array
     */
    protected $_cache = array();
    
    /**
     * @var int Cache TTL in seconds
     */
    protected $_cacheTtl = 300; // 5 minutes default
    
    /**
     * Constructor with dependency injection
     */
    public function __construct()
    {
        $this->_helper = Mage::helper('pfg_core');

        try {
            $this->_errorHandler = Mage::getSingleton('pfg_core/errorHandler');
        } catch (Exception $e) {
            $this->_errorHandler = null;
        }
    }
    
    /**
     * Execute operation with standardized error handling
     *
     * @param callable $operation
     * @param string $operationName
     * @param array $context
     * @return array
     */
    protected function _executeWithErrorHandling($operation, $operationName, array $context = array())
    {
        try {
            $result = call_user_func($operation);
            return $this->_createSuccessResponse($result);

        } catch (PFG_Core_Exception_Validation $e) {
            return $this->_handleError($e, $operationName, $context, 'validation');
        } catch (PFG_Core_Exception_Api $e) {
            return $this->_handleError($e, $operationName, $context, 'api');
        } catch (PFG_Core_Exception_Database $e) {
            return $this->_handleError($e, $operationName, $context, 'database');
        } catch (Exception $e) {
            return $this->_handleError($e, $operationName, $context, 'generic');
        }
    }
    
    /**
     * Get cached data or execute callback
     *
     * @param string $cacheKey
     * @param callable $callback
     * @param int $ttl
     * @return mixed
     */
    protected function _getCachedOrExecute($cacheKey, $callback, $ttl = null)
    {
        $ttl = $ttl ?: $this->_cacheTtl;
        $cacheData = $this->_getFromCache($cacheKey);
        
        if ($cacheData !== false && (time() - $cacheData['timestamp']) < $ttl) {
            return $cacheData['data'];
        }
        
        $result = call_user_func($callback);
        $this->_setCache($cacheKey, $result);
        
        return $result;
    }
    
    /**
     * Get data from cache
     *
     * @param string $key
     * @return mixed|false
     */
    protected function _getFromCache($key)
    {
        if (isset($this->_cache[$key])) {
            return $this->_cache[$key];
        }
        
        // Try Magento cache
        $cacheKey = $this->_getCacheKey($key);
        $cached = Mage::app()->getCache()->load($cacheKey);
        
        if ($cached) {
            $data = unserialize($cached);
            $this->_cache[$key] = $data;
            return $data;
        }
        
        return false;
    }
    
    /**
     * Set data to cache
     *
     * @param string $key
     * @param mixed $data
     * @return $this
     */
    protected function _setCache($key, $data)
    {
        $cacheData = array(
            'data' => $data,
            'timestamp' => time()
        );
        
        $this->_cache[$key] = $cacheData;
        
        // Store in Magento cache
        $cacheKey = $this->_getCacheKey($key);
        Mage::app()->getCache()->save(
            serialize($cacheData),
            $cacheKey,
            array('PFG_CORE'),
            $this->_cacheTtl
        );
        
        return $this;
    }
    
    /**
     * Clear cache by key or pattern
     *
     * @param string $key
     * @return $this
     */
    protected function _clearCache($key = null)
    {
        if ($key) {
            unset($this->_cache[$key]);
            $cacheKey = $this->_getCacheKey($key);
            Mage::app()->getCache()->remove($cacheKey);
        } else {
            $this->_cache = array();
            Mage::app()->getCache()->clean(array('PFG_CORE'));
        }
        
        return $this;
    }
    
    /**
     * Generate cache key
     *
     * @param string $key
     * @return string
     */
    protected function _getCacheKey($key)
    {
        return 'pfg_core_' . md5(get_class($this) . '_' . $key);
    }
    
    /**
     * Create standardized success response
     *
     * @param mixed $data
     * @param string $message
     * @return array
     */
    protected function _createSuccessResponse($data = null, $message = null)
    {
        return array(
            'success' => true,
            'data' => $data,
            'message' => $message,
            'error_code' => null,
            'timestamp' => time()
        );
    }
    

    
    /**
     * Validate required parameters
     *
     * @param array $params
     * @param array $required
     * @throws PFG_Core_Exception_Validation
     */
    protected function _validateRequiredParams(array $params, array $required)
    {
        $missing = array();
        
        foreach ($required as $param) {
            if (!isset($params[$param]) || empty($params[$param])) {
                $missing[] = $param;
            }
        }
        
        if (!empty($missing)) {
            throw new PFG_Core_Exception_Validation(
                'Missing required parameters: ' . implode(', ', $missing)
            );
        }
    }
    
    /**
     * Handle errors with fallback if error handler is not available
     *
     * @param Exception $exception
     * @param string $operationName
     * @param array $context
     * @param string $errorType
     * @return array
     */
    protected function _handleError(Exception $exception, $operationName, array $context = array(), $errorType = 'generic')
    {
        if ($this->_errorHandler) {
            // Use the proper error handler if available
            switch ($errorType) {
                case 'validation':
                    return $this->_errorHandler->handleValidationError($exception, $operationName, $context);
                case 'api':
                    return $this->_errorHandler->handleApiError($exception, $operationName, $context);
                case 'database':
                    return $this->_errorHandler->handleDatabaseError($exception, $operationName, $context);
                default:
                    return $this->_errorHandler->handleGenericError($exception, $operationName, $context);
            }
        } else {
            // Fallback error handling
            $errorId = uniqid('error_', true);

            // Log the error
            Mage::log(
                sprintf('[%s] %s failed: %s', $errorType, $operationName, $exception->getMessage()),
                Zend_Log::ERR,
                'pfg_core_errors.log'
            );

            return array(
                'success' => false,
                'error' => $exception->getMessage(),
                'error_code' => strtoupper($errorType) . '_ERROR',
                'error_id' => $errorId,
                'error_type' => $errorType,
                'timestamp' => time()
            );
        }
    }

    /**
     * Dispatch event
     *
     * @param string $eventName
     * @param array $data
     */
    protected function _dispatchEvent($eventName, array $data = array())
    {
        Mage::dispatchEvent('pfg_core_service_' . $eventName, $data);
    }
}
