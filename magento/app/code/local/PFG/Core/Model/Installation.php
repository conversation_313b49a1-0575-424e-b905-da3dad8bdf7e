<?php
/**
 * PFG Core Installation Model
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_Installation extends Mage_Core_Model_Abstract
{
    const STATUS_PENDING = 'pending';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_ROLLED_BACK = 'rolled_back';
    
    const TYPE_INSTALL = 'install';
    const TYPE_UPDATE = 'update';
    const TYPE_ROLLBACK = 'rollback';
    const TYPE_UNINSTALL = 'uninstall';
    
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;

    /**
     * @var PFG_Core_Model_Logger
     */
    protected $_logger;

    /**
     * @var PFG_Core_Model_ErrorHandler
     */
    protected $_errorHandler;

    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_init('pfg_core/installation');
        $this->_helper = Mage::helper('pfg_core');
        $this->_logger = Mage::getModel('pfg_core/logger');
        $this->_errorHandler = Mage::getModel('pfg_core/errorHandler');
    }
    
    /**
     * Install module from repository
     *
     * @param string $repositoryName
     * @param string $version
     * @return array
     */
    public function installModule($repositoryName, $version = 'master')
    {
        try {
            $repositoryModel = Mage::getModel('pfg_core/repository');
            $repoDetails = $repositoryModel->getRepositoryDetails($repositoryName);
            
            if (!$repoDetails['success']) {
                throw new Exception($repoDetails['error']);
            }
            
            $repo = $repoDetails['data'];
            $moduleName = $repo['module_name'];
            
            // Check if installation can proceed
            $canInstall = $repositoryModel->canInstallRepository($repositoryName);



            // Handle nested service response format
            $canInstallData = $canInstall;
            if (isset($canInstall['data'])) {
                $canInstallData = $canInstall['data'];
            }

            $canInstallFlag = isset($canInstallData['can_install']) ? $canInstallData['can_install'] : true;

            if (!$canInstall['success'] || !$canInstallFlag) {
                $errorMessages = array();

                // Handle service layer error format (has 'error' field)
                if (!$canInstall['success'] && isset($canInstall['error'])) {
                    $errorMessages[] = $canInstall['error'];
                }

                // Handle validation checks format (has 'checks' field)
                $checksArray = isset($canInstallData['checks']) ? $canInstallData['checks'] : (isset($canInstall['checks']) ? $canInstall['checks'] : array());
                if (is_array($checksArray)) {
                    foreach ($checksArray as $check) {
                        if (isset($check['message']) && isset($check['type']) && $check['type'] === 'error') {
                            $errorMessages[] = $check['message'];
                        }
                    }
                }

                $errorMessage = 'Installation cannot proceed: ' . (!empty($errorMessages) ? implode(', ', $errorMessages) : 'Unknown validation error');
                throw new Exception($errorMessage);
            }
            
            // Create installation record
            $this->setData(array(
                'module_name' => $moduleName,
                'repository_name' => $repositoryName,
                'version_available' => $version,
                'installation_status' => self::STATUS_PENDING,
                'installation_type' => self::TYPE_INSTALL,
                'installed_by' => $this->_getCurrentAdminUser()
            ));
            $this->save();
            
            $this->_logger->logInstallation('started', $moduleName, sprintf('Installing from repository %s (version %s)', $repositoryName, $version));
            
            // Update status to in progress
            $this->setInstallationStatus(self::STATUS_IN_PROGRESS)->save();
            
            // Create backup
            $this->_createBackup($moduleName, 'installation', sprintf('Pre-installation backup for %s', $moduleName));

            // Download repository
            $downloadResult = $this->_downloadRepository($repo, $repositoryName, $version);
            
            // Detect actual module name from ZIP file
            $actualModuleName = $this->_detectModuleNameFromZip($downloadResult['filepath']);
            if (!$actualModuleName) {
                throw new Exception('Could not detect module name from downloaded package');
            }

            $this->_logger->logInstallation('module_detected', $actualModuleName, sprintf('Detected actual module name: %s (expected: %s)', $actualModuleName, $moduleName));

            // Extract and install using the actual module name
            $installResult = $this->_extractAndInstallModule($downloadResult['filepath'], $actualModuleName);

            if (!$installResult['success']) {
                throw new Exception($installResult['error']);
            }

            // Update the installation record with the actual module name
            $this->setModuleName($actualModuleName);
            
            // Update installation record
            $this->setData(array(
                'version_installed' => $version,
                'installation_status' => self::STATUS_COMPLETED,
                'installed_at' => now(),
                'installation_log' => $installResult['log']
            ));
            $this->save();
            
            // Clean up downloaded file
            if (file_exists($downloadResult['filepath'])) {
                unlink($downloadResult['filepath']);
            }
            
            $this->_logger->logInstallation('completed', $moduleName, sprintf('Successfully installed version %s', $version));
            
            return array(
                'success' => true,
                'installation_id' => $this->getId(),
                'message' => sprintf('Module %s installed successfully', $moduleName)
            );
            
        } catch (Exception $e) {
            // Update status to failed
            if ($this->getId()) {
                $this->setInstallationStatus(self::STATUS_FAILED)
                     ->setInstallationLog($e->getMessage())
                     ->save();
            }

            $this->_logger->logInstallation('failed', $moduleName, $e->getMessage(), PFG_Core_Model_Logger::LOG_LEVEL_ERROR);

            return $this->_errorHandler->handleInstallationError($e, $moduleName, 'install');
        }
    }
    
    /**
     * Update module to new version
     *
     * @param string $repositoryName
     * @param string $newVersion
     * @return array
     */
    public function updateModule($repositoryName, $newVersion)
    {
        try {
            $repositoryModel = Mage::getModel('pfg_core/repository');
            $repoDetails = $repositoryModel->getRepositoryDetails($repositoryName);
            
            if (!$repoDetails['success']) {
                throw new Exception($repoDetails['error']);
            }
            
            $repo = $repoDetails['data'];
            $moduleName = $repo['module_name'];
            
            if (!$repo['is_installed']) {
                throw new Exception('Module is not installed');
            }
            
            // Create installation record for update
            $this->setData(array(
                'module_name' => $moduleName,
                'repository_name' => $repositoryName,
                'version_installed' => $repo['installed_version'],
                'version_available' => $newVersion,
                'installation_status' => self::STATUS_PENDING,
                'installation_type' => self::TYPE_UPDATE,
                'installed_by' => $this->_getCurrentAdminUser()
            ));
            $this->save();
            
            $this->_helper->log(sprintf('Starting update of %s from %s to %s', 
                $moduleName, $repo['installed_version'], $newVersion), Zend_Log::INFO);
            
            // Update status to in progress
            $this->setInstallationStatus(self::STATUS_IN_PROGRESS)->save();
            
            // Create backup
            $this->_createBackup($moduleName, 'update', sprintf('Pre-update backup for %s (v%s to v%s)', $moduleName, $repo['installed_version'], $newVersion));

            // Download new version
            $downloadResult = $this->_downloadRepository($repo, $repositoryName, $newVersion);

            // Detect actual module name from ZIP file
            $actualModuleName = $this->_detectModuleNameFromZip($downloadResult['filepath']);
            if (!$actualModuleName) {
                throw new Exception('Could not detect module name from downloaded package');
            }

            $this->_logger->logInstallation('module_detected', $actualModuleName, sprintf('Detected actual module name: %s (expected: %s)', $actualModuleName, $moduleName));

            // Extract and install using the actual module name
            $installResult = $this->_extractAndInstallModule($downloadResult['filepath'], $actualModuleName);

            // Update the installation record with the actual module name
            $this->setModuleName($actualModuleName);
            
            if (!$installResult['success']) {
                throw new Exception($installResult['error']);
            }
            
            // Update installation record
            $this->setData(array(
                'version_installed' => $newVersion,
                'installation_status' => self::STATUS_COMPLETED,
                'installed_at' => now(),
                'installation_log' => $installResult['log']
            ));
            $this->save();
            
            // Clean up downloaded file
            if (file_exists($downloadResult['filepath'])) {
                unlink($downloadResult['filepath']);
            }
            
            $this->_helper->log(sprintf('Successfully updated %s to version %s', $moduleName, $newVersion), Zend_Log::INFO);
            
            return array(
                'success' => true,
                'installation_id' => $this->getId(),
                'message' => sprintf('Module %s updated successfully to version %s', $moduleName, $newVersion)
            );
            
        } catch (Exception $e) {
            // Update status to failed
            if ($this->getId()) {
                $this->setInstallationStatus(self::STATUS_FAILED)
                     ->setInstallationLog($e->getMessage())
                     ->save();
            }
            
            $this->_helper->log('Update failed: ' . $e->getMessage(), Zend_Log::ERR);
            
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Complete module removal - removes everything and reverts to pre-installation state
     * This is the enhanced rollback functionality that completely cleans a module
     *
     * @param string $moduleName
     * @return array
     */
    public function completeModuleRemoval($moduleName)
    {
        try {
            $this->_helper->log(sprintf('Starting complete removal of module: %s', $moduleName), Zend_Log::INFO);

            // Validate module name
            if (empty($moduleName)) {
                throw new Exception('Module name is required');
            }

            // Check if module is actually installed
            if (!$this->_isModuleInstalled($moduleName)) {
                throw new Exception('Module is not installed');
            }

            // Create backup before removal
            $backupModel = Mage::getModel('pfg_core/backup');
            $backupResult = $backupModel->createBackup($moduleName, self::TYPE_ROLLBACK, 'Backup before complete module removal');

            if (!$backupResult['success']) {
                throw new Exception('Failed to create backup: ' . $backupResult['error']);
            }

            $backupId = $backupResult['backup_id'];
            $this->_helper->log(sprintf('Backup created with ID: %s', $backupId), Zend_Log::INFO);

            // Create installation record
            $this->setData(array(
                'module_name' => $moduleName,
                'repository_name' => $this->_getRepositoryNameFromModule($moduleName),
                'version_available' => 'removal',
                'installation_status' => self::STATUS_IN_PROGRESS,
                'installation_type' => self::TYPE_ROLLBACK,
                'backup_id' => $backupId,
                'installed_by' => $this->_getCurrentAdminUser(),
                'installed_at' => now()
            ));
            $this->save();

            // Perform complete module removal
            $removalResult = $this->_performCompleteModuleRemoval($moduleName);

            if (!$removalResult['success']) {
                throw new Exception($removalResult['error']);
            }

            // Update status
            $this->setInstallationStatus(self::STATUS_COMPLETED)->save();

            $this->_helper->log(sprintf('Module %s completely removed successfully', $moduleName), Zend_Log::INFO);

            return array(
                'success' => true,
                'message' => sprintf('Module %s completely removed successfully', $moduleName),
                'backup_id' => $backupId,
                'log' => $removalResult['log']
            );

        } catch (Exception $e) {
            $this->_helper->log('Complete module removal failed: ' . $e->getMessage(), Zend_Log::ERR);

            if ($this->getId()) {
                $this->setInstallationStatus(self::STATUS_FAILED)->save();
            }

            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }


    
    /**
     * Detect actual module name from ZIP file contents
     *
     * @param string $archivePath
     * @return string|false
     */
    protected function _detectModuleNameFromZip($archivePath)
    {
        try {
            $zip = new ZipArchive();
            $result = $zip->open($archivePath);

            if ($result !== TRUE) {
                return false;
            }

            // Look for module declaration files (app/etc/modules/*.xml)
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $filename = $zip->getNameIndex($i);

                // Match pattern: */app/etc/modules/PFG_*.xml
                if (preg_match('#app/etc/modules/(PFG_[^/]+)\.xml$#', $filename, $matches)) {
                    $zip->close();
                    return $matches[1];
                }
            }

            $zip->close();
            return false;

        } catch (Exception $e) {
            $this->_logger->logInstallation('module_detection_error', 'unknown', 'Failed to detect module name: ' . $e->getMessage(), PFG_Core_Model_Logger::LOG_LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Extract and install module files
     *
     * @param string $archivePath
     * @param string $moduleName
     * @return array
     */
    protected function _extractAndInstallModule($archivePath, $moduleName)
    {
        try {
            $tempDir = $this->_helper->getTempDir() . DS . 'install_' . time();
            $log = array();
            
            if (!mkdir($tempDir, 0755, true)) {
                throw new Exception('Failed to create temporary directory');
            }
            
            // Validate archive file first
            if (!file_exists($archivePath)) {
                throw new Exception('Archive file does not exist: ' . $archivePath);
            }

            $fileSize = filesize($archivePath);
            if ($fileSize === false || $fileSize < 100) {
                throw new Exception('Archive file is too small or invalid (size: ' . $fileSize . ' bytes)');
            }

            $log[] = 'Archive file validated (size: ' . number_format($fileSize) . ' bytes)';

            // Extract archive
            $zip = new ZipArchive();
            $zipResult = $zip->open($archivePath);
            if ($zipResult !== TRUE) {
                $zipErrors = array(
                    ZipArchive::ER_OK => 'No error',
                    ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
                    ZipArchive::ER_RENAME => 'Renaming temporary file failed',
                    ZipArchive::ER_CLOSE => 'Closing zip archive failed',
                    ZipArchive::ER_SEEK => 'Seek error',
                    ZipArchive::ER_READ => 'Read error',
                    ZipArchive::ER_WRITE => 'Write error',
                    ZipArchive::ER_CRC => 'CRC error',
                    ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
                    ZipArchive::ER_NOENT => 'No such file',
                    ZipArchive::ER_EXISTS => 'File already exists',
                    ZipArchive::ER_OPEN => 'Can\'t open file',
                    ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
                    ZipArchive::ER_ZLIB => 'Zlib error',
                    ZipArchive::ER_MEMORY => 'Memory allocation failure',
                    ZipArchive::ER_CHANGED => 'Entry has been changed',
                    ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
                    ZipArchive::ER_EOF => 'Premature EOF',
                    ZipArchive::ER_INVAL => 'Invalid argument',
                    ZipArchive::ER_NOZIP => 'Not a zip archive',
                    ZipArchive::ER_INTERNAL => 'Internal error',
                    ZipArchive::ER_INCONS => 'Zip archive inconsistent',
                    ZipArchive::ER_REMOVE => 'Can\'t remove file',
                    ZipArchive::ER_DELETED => 'Entry has been deleted'
                );

                $errorMsg = isset($zipErrors[$zipResult]) ? $zipErrors[$zipResult] : 'Unknown error';
                throw new Exception('Invalid file format. Please check the module package. ZIP Error: ' . $errorMsg . ' (Code: ' . $zipResult . ')');
            }

            $log[] = 'ZIP archive opened successfully (' . $zip->numFiles . ' files)';

            if ($zip->numFiles === 0) {
                $zip->close();
                throw new Exception('Archive is empty');
            }

            // Secure extraction with path validation
            $this->_secureExtractZip($zip, $tempDir, $log);
            $zip->close();

            $log[] = 'Archive extracted successfully';
            
            // Find the extracted directory (usually has a prefix)
            $extractedDirs = glob($tempDir . DS . '*', GLOB_ONLYDIR);
            if (empty($extractedDirs)) {
                throw new Exception('No directories found in extracted archive');
            }
            
            $sourceDir = $extractedDirs[0];
            $log[] = 'Source directory: ' . $sourceDir;
            
            // Install files
            $this->_installModuleFiles($sourceDir, $moduleName, $log);
            
            // Clean up temporary directory
            $this->_removeDirectory($tempDir);
            
            return array(
                'success' => true,
                'log' => implode("\n", $log)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Install module files to Magento
     *
     * @param string $sourceDir
     * @param string $moduleName
     * @param array &$log
     */
    protected function _installModuleFiles($sourceDir, $moduleName, &$log)
    {
        $baseDir = Mage::getBaseDir();
        $filesInstalled = 0;

        // Find and install module code (search for actual module directory)
        $moduleCodeInstalled = false;
        $moduleCodeSearchPaths = array(
            $sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $moduleName),
            $sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . 'PFG' . DS . '*',
            $sourceDir . DS . 'app' . DS . 'code' . DS . 'community' . DS . str_replace('_', DS, $moduleName),
            $sourceDir . DS . 'app' . DS . 'code' . DS . 'community' . DS . 'PFG' . DS . '*'
        );

        foreach ($moduleCodeSearchPaths as $searchPath) {
            if (strpos($searchPath, '*') !== false) {
                // Wildcard search
                $dirs = glob($searchPath, GLOB_ONLYDIR);
                foreach ($dirs as $moduleCodeDir) {
                    if (is_dir($moduleCodeDir)) {
                        $moduleSubName = basename($moduleCodeDir);
                        // Determine code pool from search path
                        $codePool = (strpos($searchPath, 'community') !== false) ? 'community' : 'local';
                        $targetDir = $baseDir . DS . 'app' . DS . 'code' . DS . $codePool . DS . 'PFG' . DS . $moduleSubName;
                        $this->_copyDirectory($moduleCodeDir, $targetDir);
                        $filesInstalled += $this->_countFiles($moduleCodeDir);
                        $log[] = 'Installed module code to: ' . $targetDir;
                        $moduleCodeInstalled = true;
                    }
                }
            } else {
                // Direct path
                if (is_dir($searchPath)) {
                    // Determine code pool from search path
                    $codePool = (strpos($searchPath, 'community') !== false) ? 'community' : 'local';
                    $targetDir = $baseDir . DS . 'app' . DS . 'code' . DS . $codePool . DS . str_replace('_', DS, $moduleName);
                    $this->_copyDirectory($searchPath, $targetDir);
                    $filesInstalled += $this->_countFiles($searchPath);
                    $log[] = 'Installed module code to: ' . $targetDir;
                    $moduleCodeInstalled = true;
                    break;
                }
            }
        }

        if (!$moduleCodeInstalled) {
            $log[] = 'WARNING: No module code directory found';
        }

        // Find and install module declaration (search for actual declaration file)
        $moduleFileInstalled = false;
        $moduleFileSearchPaths = array(
            $sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml',
            $sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . 'PFG_*.xml'
        );

        foreach ($moduleFileSearchPaths as $searchPath) {
            if (strpos($searchPath, '*') !== false) {
                // Wildcard search
                $files = glob($searchPath);
                foreach ($files as $moduleFile) {
                    if (file_exists($moduleFile)) {
                        $filename = basename($moduleFile);
                        $targetFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $filename;
                        copy($moduleFile, $targetFile);
                        $filesInstalled++;
                        $log[] = 'Installed module declaration: ' . $targetFile;
                        $moduleFileInstalled = true;
                    }
                }
            } else {
                // Direct path
                if (file_exists($searchPath)) {
                    $targetFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
                    copy($searchPath, $targetFile);
                    $filesInstalled++;
                    $log[] = 'Installed module declaration: ' . $targetFile;
                    $moduleFileInstalled = true;
                    break;
                }
            }
        }

        if (!$moduleFileInstalled) {
            $log[] = 'WARNING: No module declaration file found';
        }

        // Install other files (layout, locale, etc.)
        $otherDirs = array('app/design', 'app/locale', 'skin', 'js');
        foreach ($otherDirs as $dir) {
            $sourceSubDir = $sourceDir . DS . str_replace('/', DS, $dir);
            if (is_dir($sourceSubDir)) {
                $targetSubDir = $baseDir . DS . str_replace('/', DS, $dir);
                $this->_copyDirectory($sourceSubDir, $targetSubDir);
                $filesInstalled += $this->_countFiles($sourceSubDir);
                $log[] = 'Installed files to: ' . $targetSubDir;
            }
        }

        $log[] = sprintf('Total files installed: %d', $filesInstalled);

        if (!$moduleCodeInstalled || !$moduleFileInstalled) {
            throw new Exception('Module installation incomplete - missing required files');
        }
    }
    
    /**
     * Copy directory recursively
     *
     * @param string $source
     * @param string $target
     */
    protected function _copyDirectory($source, $target)
    {
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $targetPath = $target . DS . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($targetPath)) {
                    mkdir($targetPath, 0755, true);
                }
            } else {
                copy($item, $targetPath);
            }
        }
    }
    
    /**
     * Count files in directory
     *
     * @param string $dir
     * @return int
     */
    protected function _countFiles($dir)
    {
        $count = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * Remove directory recursively
     *
     * @param string $dir
     */
    protected function _removeDirectory($dir)
    {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), array('.', '..'));
            foreach ($files as $file) {
                $path = $dir . DS . $file;
                is_dir($path) ? $this->_removeDirectory($path) : unlink($path);
            }
            rmdir($dir);
        }
    }

    /**
     * Check if module is installed
     *
     * @param string $moduleName
     * @return bool
     */
    protected function _isModuleInstalled($moduleName)
    {
        $config = Mage::getConfig();
        $moduleConfig = $config->getNode('modules/' . $moduleName);

        if (!$moduleConfig) {
            return false;
        }

        return (string)$moduleConfig->active === 'true';
    }

    /**
     * Get repository name from module name
     *
     * @param string $moduleName
     * @return string
     */
    protected function _getRepositoryNameFromModule($moduleName)
    {
        // Convert PFG_ModuleName to module-name format
        $name = str_replace('PFG_', '', $moduleName);
        $name = preg_replace('/([a-z])([A-Z])/', '$1-$2', $name);
        return strtolower($name);
    }

    /**
     * Perform complete module removal - removes everything including database tables and configuration
     *
     * @param string $moduleName
     * @return array
     */
    protected function _performCompleteModuleRemoval($moduleName)
    {
        try {
            $baseDir = Mage::getBaseDir();
            $log = array();
            $filesRemoved = 0;
            $tablesRemoved = 0;
            $configRemoved = 0;

            $log[] = '=== Starting Complete Module Removal ===';
            $log[] = 'Module: ' . $moduleName;

            // 1. Remove all module database tables
            $log[] = '--- Removing Database Tables ---';
            $tablesResult = $this->_removeModuleDatabaseTables($moduleName);
            $tablesRemoved = $tablesResult['tables_removed'];
            $log = array_merge($log, $tablesResult['log']);

            // 2. Remove all module configuration from core_config_data
            $log[] = '--- Removing Configuration Data ---';
            $configResult = $this->_removeModuleConfiguration($moduleName);
            $configRemoved = $configResult['configs_removed'];
            $log = array_merge($log, $configResult['log']);

            // 3. Remove module code directory
            $log[] = '--- Removing Module Files ---';
            $moduleCodeDir = $baseDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $moduleName);
            if (is_dir($moduleCodeDir)) {
                $filesRemoved += $this->_countFiles($moduleCodeDir);
                $this->_removeDirectory($moduleCodeDir);
                $log[] = 'Removed module code directory: ' . $moduleCodeDir;
            } else {
                $log[] = 'Module code directory not found: ' . $moduleCodeDir;
            }

            // 4. Remove module declaration file
            $moduleFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
            if (file_exists($moduleFile)) {
                unlink($moduleFile);
                $filesRemoved++;
                $log[] = 'Removed module declaration: ' . $moduleFile;
            } else {
                $log[] = 'Module declaration file not found: ' . $moduleFile;
            }

            // 5. Remove design files (layouts, templates)
            $log[] = '--- Removing Design Files ---';
            $designDirs = array(
                $baseDir . DS . 'app' . DS . 'design' . DS . 'adminhtml' . DS . 'default' . DS . 'default' . DS . 'layout' . DS . strtolower(str_replace('_', '_', $moduleName)) . '.xml',
                $baseDir . DS . 'app' . DS . 'design' . DS . 'frontend' . DS . 'base' . DS . 'default' . DS . 'layout' . DS . strtolower(str_replace('_', '_', $moduleName)) . '.xml',
                $baseDir . DS . 'app' . DS . 'design' . DS . 'adminhtml' . DS . 'default' . DS . 'default' . DS . 'template' . DS . strtolower(str_replace('_', DS, $moduleName)),
                $baseDir . DS . 'app' . DS . 'design' . DS . 'frontend' . DS . 'base' . DS . 'default' . DS . 'template' . DS . strtolower(str_replace('_', DS, $moduleName))
            );

            foreach ($designDirs as $designPath) {
                if (file_exists($designPath)) {
                    if (is_dir($designPath)) {
                        $filesRemoved += $this->_countFiles($designPath);
                        $this->_removeDirectory($designPath);
                        $log[] = 'Removed design directory: ' . $designPath;
                    } else {
                        unlink($designPath);
                        $filesRemoved++;
                        $log[] = 'Removed design file: ' . $designPath;
                    }
                }
            }

            // 6. Remove skin files
            $log[] = '--- Removing Skin Files ---';
            $skinDirs = array(
                $baseDir . DS . 'skin' . DS . 'adminhtml' . DS . 'default' . DS . 'default' . DS . strtolower(str_replace('_', DS, $moduleName)),
                $baseDir . DS . 'skin' . DS . 'frontend' . DS . 'base' . DS . 'default' . DS . strtolower(str_replace('_', DS, $moduleName))
            );

            foreach ($skinDirs as $skinPath) {
                if (is_dir($skinPath)) {
                    $filesRemoved += $this->_countFiles($skinPath);
                    $this->_removeDirectory($skinPath);
                    $log[] = 'Removed skin directory: ' . $skinPath;
                }
            }

            // 7. Remove locale files
            $log[] = '--- Removing Locale Files ---';
            $localePattern = $baseDir . DS . 'app' . DS . 'locale' . DS . '*' . DS . $moduleName . '.csv';
            $localeFiles = glob($localePattern);
            foreach ($localeFiles as $localeFile) {
                if (file_exists($localeFile)) {
                    unlink($localeFile);
                    $filesRemoved++;
                    $log[] = 'Removed locale file: ' . $localeFile;
                }
            }

            // 8. Clear all caches
            $log[] = '--- Clearing Caches ---';
            $this->_clearAllCaches();
            $log[] = 'All caches cleared';

            // 9. Remove module from installation tracking
            $log[] = '--- Cleaning Installation Records ---';
            $this->_removeModuleFromTracking($moduleName);
            $log[] = 'Removed module from installation tracking';

            $log[] = '=== Complete Module Removal Summary ===';
            $log[] = sprintf('Files/directories removed: %d', $filesRemoved);
            $log[] = sprintf('Database tables removed: %d', $tablesRemoved);
            $log[] = sprintf('Configuration entries removed: %d', $configRemoved);
            $log[] = '=== Module Completely Removed ===';

            return array(
                'success' => true,
                'log' => $log,
                'files_removed' => $filesRemoved,
                'tables_removed' => $tablesRemoved,
                'configs_removed' => $configRemoved
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => 'Failed to perform complete module removal: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Get current admin user
     *
     * @return string
     */
    protected function _getCurrentAdminUser()
    {
        $session = Mage::getSingleton('admin/session');
        if ($session->isLoggedIn()) {
            return $session->getUser()->getUsername();
        }
        return 'system';
    }

    /**
     * Create backup for installation/update
     *
     * @param string $moduleName
     * @param string $type
     * @param string $description
     * @throws Exception
     */
    protected function _createBackup($moduleName, $type, $description)
    {
        $backupModel = Mage::getModel('pfg_core/backup');
        $backupResult = $backupModel->createBackup($moduleName, $type, $description);

        if ($backupResult['success'] && $backupResult['backup_id']) {
            $this->setBackupId($backupResult['backup_id'])->save();
        }
    }

    /**
     * Download repository using correct identifier
     *
     * @param array $repo
     * @param string $repositoryName
     * @param string $version
     * @return array
     * @throws Exception
     */
    protected function _downloadRepository($repo, $repositoryName, $version)
    {
        $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
        $repositoryIdentifier = isset($repo['repository_identifier']) ? $repo['repository_identifier'] : $repositoryName;
        $downloadResult = $bitbucketHelper->downloadRepository($repositoryIdentifier, $version);

        if (!$downloadResult['success']) {
            throw new Exception('Failed to download repository: ' . $downloadResult['error']);
        }

        return $downloadResult;
    }

    /**
     * Remove all database tables related to a module
     *
     * @param string $moduleName
     * @return array
     */
    protected function _removeModuleDatabaseTables($moduleName)
    {
        $log = array();
        $tablesRemoved = 0;

        try {
            $resource = Mage::getSingleton('core/resource');
            $connection = $resource->getConnection('core_write');

            // Get module prefix for table names (e.g., PFG_Analytics -> pfg_analytics)
            $modulePrefix = strtolower(str_replace('_', '_', $moduleName));

            // Get all tables with the module prefix
            $tables = $connection->fetchCol("SHOW TABLES LIKE '{$modulePrefix}_%'");

            foreach ($tables as $table) {
                try {
                    $connection->dropTable($table);
                    $tablesRemoved++;
                    $log[] = "Dropped table: {$table}";
                } catch (Exception $e) {
                    $log[] = "Failed to drop table {$table}: " . $e->getMessage();
                }
            }

            // Also check for tables that might use different naming conventions
            $alternativePatterns = array(
                strtolower($moduleName) . '_%',
                str_replace('_', '', strtolower($moduleName)) . '_%'
            );

            foreach ($alternativePatterns as $pattern) {
                $tables = $connection->fetchCol("SHOW TABLES LIKE '{$pattern}'");
                foreach ($tables as $table) {
                    try {
                        $connection->dropTable($table);
                        $tablesRemoved++;
                        $log[] = "Dropped table: {$table}";
                    } catch (Exception $e) {
                        $log[] = "Failed to drop table {$table}: " . $e->getMessage();
                    }
                }
            }

            if ($tablesRemoved === 0) {
                $log[] = "No database tables found for module {$moduleName}";
            }

        } catch (Exception $e) {
            $log[] = "Error removing database tables: " . $e->getMessage();
        }

        return array(
            'tables_removed' => $tablesRemoved,
            'log' => $log
        );
    }

    /**
     * Remove all configuration entries for a module
     *
     * @param string $moduleName
     * @return array
     */
    protected function _removeModuleConfiguration($moduleName)
    {
        $log = array();
        $configRemoved = 0;

        try {
            $resource = Mage::getSingleton('core/resource');
            $connection = $resource->getConnection('core_write');
            $configTable = $resource->getTableName('core/config_data');

            // Get module config path (e.g., PFG_Analytics -> pfg/analytics)
            $configPath = strtolower(str_replace('_', '/', $moduleName));
            $configPath = str_replace('pfg/', 'pfg/', $configPath); // Ensure pfg prefix

            // Remove all configuration entries for this module
            $patterns = array(
                $configPath . '/%',
                strtolower(str_replace('_', '_', $moduleName)) . '/%'
            );

            foreach ($patterns as $pattern) {
                $result = $connection->delete($configTable, array('path LIKE ?' => $pattern));
                if ($result > 0) {
                    $configRemoved += $result;
                    $log[] = "Removed {$result} configuration entries matching: {$pattern}";
                }
            }

            if ($configRemoved === 0) {
                $log[] = "No configuration entries found for module {$moduleName}";
            }

        } catch (Exception $e) {
            $log[] = "Error removing configuration: " . $e->getMessage();
        }

        return array(
            'configs_removed' => $configRemoved,
            'log' => $log
        );
    }

    /**
     * Clear all Magento caches
     */
    protected function _clearAllCaches()
    {
        try {
            // Clear Magento cache
            Mage::app()->getCacheInstance()->flush();

            // Clear configuration cache
            Mage::getConfig()->reinit();

            // Clear layout cache
            Mage::app()->getLayout()->getUpdate()->resetUpdates();

            // Clear block HTML cache
            if (Mage::app()->useCache('block_html')) {
                Mage::app()->getCacheInstance()->cleanType('block_html');
            }

        } catch (Exception $e) {
            $this->_helper->log('Error clearing caches: ' . $e->getMessage(), Zend_Log::WARN);
        }
    }

    /**
     * Remove module from installation tracking
     *
     * @param string $moduleName
     */
    protected function _removeModuleFromTracking($moduleName)
    {
        try {
            $resource = Mage::getSingleton('core/resource');
            $connection = $resource->getConnection('core_write');
            $installationTable = $resource->getTableName('pfg_core/installation');

            // Remove all installation records for this module
            $connection->delete($installationTable, array('module_name = ?' => $moduleName));

        } catch (Exception $e) {
            $this->_helper->log('Error removing installation tracking: ' . $e->getMessage(), Zend_Log::WARN);
        }
    }

    /**
     * Securely extract ZIP archive with path validation to prevent directory traversal
     *
     * @param ZipArchive $zip
     * @param string $extractPath
     * @param array &$log
     * @throws Exception
     */
    protected function _secureExtractZip($zip, $extractPath, &$log)
    {
        // Normalize the extraction path
        $extractPath = realpath($extractPath);
        if (!$extractPath) {
            throw new Exception('Invalid extraction path');
        }

        $log[] = 'Validating archive contents for security...';

        // Validate each file in the archive before extraction
        for ($i = 0; $i < $zip->numFiles; $i++) {
            $filename = $zip->getNameIndex($i);

            if ($filename === false) {
                throw new Exception('Could not read file name from archive at index ' . $i);
            }

            // Skip directory entries (they end with /)
            if (substr($filename, -1) === '/') {
                continue;
            }

            // Check for directory traversal attempts
            if ($this->_containsDirectoryTraversal($filename)) {
                throw new Exception('Archive contains potentially dangerous file path: ' . $filename);
            }

            // Validate the final extraction path
            $fullPath = $extractPath . DS . $filename;
            $normalizedPath = $this->_normalizePath($fullPath);

            // Ensure the file will be extracted within the allowed directory
            if (strpos($normalizedPath, $extractPath) !== 0) {
                throw new Exception('Archive contains file that would be extracted outside allowed directory: ' . $filename);
            }

            // Check for suspicious file extensions
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            $dangerousExtensions = array('exe', 'bat', 'cmd', 'com', 'scr', 'vbs', 'jar');

            if (in_array($extension, $dangerousExtensions)) {
                throw new Exception('Archive contains potentially dangerous file type: ' . $filename);
            }

            // Check file size to prevent zip bombs
            $fileInfo = $zip->statIndex($i);
            if ($fileInfo && $fileInfo['size'] > 50 * 1024 * 1024) { // 50MB limit per file
                throw new Exception('Archive contains file that is too large: ' . $filename . ' (' . $fileInfo['size'] . ' bytes)');
            }
        }

        $log[] = 'Archive validation passed, extracting files...';

        // Extract files one by one with additional validation
        for ($i = 0; $i < $zip->numFiles; $i++) {
            $filename = $zip->getNameIndex($i);

            // Skip directory entries (they end with /)
            if (substr($filename, -1) === '/') {
                continue;
            }

            $fileContent = $zip->getFromIndex($i);

            if ($fileContent === false) {
                // Could not read file content, skip it
                continue;
            }

            $fullPath = $extractPath . DS . $filename;
            $directory = dirname($fullPath);

            // Create directory if it doesn't exist
            if (!is_dir($directory)) {
                if (!mkdir($directory, 0755, true)) {
                    throw new Exception('Could not create directory: ' . $directory);
                }
            }

            // Write file content
            if (file_put_contents($fullPath, $fileContent) === false) {
                throw new Exception('Could not write file: ' . $fullPath);
            }

            // Set appropriate permissions
            chmod($fullPath, 0644);
        }

        $log[] = 'Secure extraction completed';
    }

    /**
     * Check if filename contains directory traversal patterns
     *
     * @param string $filename
     * @return bool
     */
    protected function _containsDirectoryTraversal($filename)
    {
        // Check for various directory traversal patterns
        $patterns = array(
            '../',      // Unix/Linux traversal
            '..\\',     // Windows traversal
            '..'.DS,    // System-specific traversal
            '//',       // Double slashes
            '\\\\',     // Double backslashes
        );

        foreach ($patterns as $pattern) {
            if (strpos($filename, $pattern) !== false) {
                return true;
            }
        }

        // Check for absolute paths
        if (substr($filename, 0, 1) === '/' || substr($filename, 0, 1) === '\\') {
            return true;
        }

        // Check for Windows drive letters
        if (preg_match('/^[a-zA-Z]:/', $filename)) {
            return true;
        }

        // Check for null bytes (can be used to bypass filters)
        if (strpos($filename, "\0") !== false) {
            return true;
        }

        return false;
    }

    /**
     * Normalize a file path by resolving . and .. components
     *
     * @param string $path
     * @return string
     */
    protected function _normalizePath($path)
    {
        // Convert backslashes to forward slashes
        $path = str_replace('\\', '/', $path);

        // Split path into components
        $parts = explode('/', $path);
        $normalized = array();

        foreach ($parts as $part) {
            if ($part === '' || $part === '.') {
                continue;
            } elseif ($part === '..') {
                if (!empty($normalized)) {
                    array_pop($normalized);
                }
            } else {
                $normalized[] = $part;
            }
        }

        // Rebuild path
        $result = implode('/', $normalized);

        // Preserve leading slash if original path had one
        if (strpos($path, '/') === 0) {
            $result = '/' . $result;
        }

        return $result;
    }
}
