<?php
/**
 * PFG Core Log Level Source Model
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_System_Config_Source_Loglevel
{
    /**
     * Options array
     *
     * @return array
     */
    public function toOptionArray()
    {
        return array(
            array('value' => Zend_Log::EMERG, 'label' => Mage::helper('pfg_core')->__('Emergency')),
            array('value' => Zend_Log::ALERT, 'label' => Mage::helper('pfg_core')->__('Alert')),
            array('value' => Zend_Log::CRIT, 'label' => Mage::helper('pfg_core')->__('Critical')),
            array('value' => Zend_Log::ERR, 'label' => Mage::helper('pfg_core')->__('Error')),
            array('value' => Zend_Log::WARN, 'label' => Mage::helper('pfg_core')->__('Warning')),
            array('value' => Zend_Log::NOTICE, 'label' => Mage::helper('pfg_core')->__('Notice')),
            array('value' => Zend_Log::INFO, 'label' => Mage::helper('pfg_core')->__('Info')),
            array('value' => Zend_Log::DEBUG, 'label' => Mage::helper('pfg_core')->__('Debug')),
        );
    }
}
