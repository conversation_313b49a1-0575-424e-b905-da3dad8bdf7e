<?php
/**
 * PFG Core Post-Installation Actions Model
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_PostInstall extends Mage_Core_Model_Abstract
{
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * @var PFG_Core_Model_Logger
     */
    protected $_logger;
    
    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_helper = Mage::helper('pfg_core');
        $this->_logger = Mage::getModel('pfg_core/logger');
    }
    
    /**
     * Execute all post-installation actions
     *
     * @return array
     */
    public function executePostInstallActions()
    {
        try {
            $actions = array();
            $errors = array();
            
            // Clear Magento caches
            $cacheResult = $this->_clearMagentoCaches();
            $actions[] = array(
                'action' => 'clear_magento_caches',
                'success' => $cacheResult['success'],
                'message' => $cacheResult['message']
            );
            if (!$cacheResult['success']) {
                $errors[] = $cacheResult['message'];
            }
            
            // Clear Redis cache
            $redisResult = $this->_clearRedisCache();
            $actions[] = array(
                'action' => 'clear_redis_cache',
                'success' => $redisResult['success'],
                'message' => $redisResult['message']
            );
            if (!$redisResult['success'] && $redisResult['critical']) {
                $errors[] = $redisResult['message'];
            }
            
            // Refresh configuration
            $configResult = $this->_refreshConfiguration();
            $actions[] = array(
                'action' => 'refresh_configuration',
                'success' => $configResult['success'],
                'message' => $configResult['message']
            );
            if (!$configResult['success']) {
                $errors[] = $configResult['message'];
            }
            
            // Reindex if needed
            $reindexResult = $this->_reindexData();
            $actions[] = array(
                'action' => 'reindex_data',
                'success' => $reindexResult['success'],
                'message' => $reindexResult['message']
            );
            if (!$reindexResult['success'] && $reindexResult['critical']) {
                $errors[] = $reindexResult['message'];
            }
            
            // Logout admin users for security
            $logoutResult = $this->_logoutAdminUsers();
            $actions[] = array(
                'action' => 'logout_admin_users',
                'success' => $logoutResult['success'],
                'message' => $logoutResult['message']
            );
            if (!$logoutResult['success']) {
                $errors[] = $logoutResult['message'];
            }
            
            // Clean up temporary files
            $cleanupResult = $this->_cleanupTempFiles();
            $actions[] = array(
                'action' => 'cleanup_temp_files',
                'success' => $cleanupResult['success'],
                'message' => $cleanupResult['message']
            );
            
            // Validate installation
            $validationResult = $this->_validateInstallation();
            $actions[] = array(
                'action' => 'validate_installation',
                'success' => $validationResult['success'],
                'message' => $validationResult['message']
            );
            if (!$validationResult['success']) {
                $errors[] = $validationResult['message'];
            }
            
            $success = empty($errors);
            $message = $success 
                ? $this->_helper->__('All post-installation actions completed successfully')
                : $this->_helper->__('Post-installation completed with %d error(s): %s', count($errors), implode(', ', $errors));
            
            return array(
                'success' => $success,
                'message' => $message,
                'actions' => $actions,
                'errors' => $errors
            );
            
        } catch (Exception $e) {
            $this->_logger->logException($e, 'Post-installation actions failed');
            
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Clear Magento caches
     *
     * @return array
     */
    protected function _clearMagentoCaches()
    {
        try {
            $cacheInstance = Mage::app()->getCacheInstance();
            
            // Get list of cache types
            $cacheTypes = $cacheInstance->getTypes();
            $clearedTypes = array();
            
            foreach ($cacheTypes as $type => $info) {
                try {
                    $cacheInstance->cleanType($type);
                    $clearedTypes[] = $type;
                } catch (Exception $e) {
                    $this->_logger->log('Failed to clear cache type: ' . $type . ' - ' . $e->getMessage(), 
                        PFG_Core_Model_Logger::LOG_LEVEL_WARNING);
                }
            }
            
            // Also flush all caches
            $cacheInstance->flush();
            
            $message = $this->_helper->__('Cleared %d cache types: %s', count($clearedTypes), implode(', ', $clearedTypes));
            
            return array(
                'success' => true,
                'message' => $message
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Failed to clear Magento caches: %s', $e->getMessage())
            );
        }
    }
    
    /**
     * Clear Redis cache
     *
     * @return array
     */
    protected function _clearRedisCache()
    {
        try {
            // Check if Redis is configured
            $redisConfig = Mage::getConfig()->getNode('global/cache/backend_options');
            if (!$redisConfig) {
                return array(
                    'success' => true,
                    'message' => $this->_helper->__('Redis not configured - skipped'),
                    'critical' => false
                );
            }
            
            // Try to connect to Redis
            if (class_exists('Redis')) {
                $redis = new Redis();
                $host = '127.0.0.1';
                $port = 6379;
                
                // Get Redis configuration if available
                if ($redisConfig->server) {
                    $host = (string)$redisConfig->server;
                }
                if ($redisConfig->port) {
                    $port = (int)$redisConfig->port;
                }
                
                if ($redis->connect($host, $port, 2)) {
                    $redis->flushAll();
                    $redis->close();
                    
                    return array(
                        'success' => true,
                        'message' => $this->_helper->__('Redis cache cleared successfully'),
                        'critical' => false
                    );
                } else {
                    return array(
                        'success' => false,
                        'message' => $this->_helper->__('Could not connect to Redis server'),
                        'critical' => false
                    );
                }
            } else {
                return array(
                    'success' => false,
                    'message' => $this->_helper->__('Redis extension not available'),
                    'critical' => false
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Redis cache clear failed: %s', $e->getMessage()),
                'critical' => false
            );
        }
    }
    
    /**
     * Refresh configuration
     *
     * @return array
     */
    protected function _refreshConfiguration()
    {
        try {
            // Reinitialize configuration
            Mage::getConfig()->reinit();
            
            // Clear configuration cache
            Mage::app()->getCacheInstance()->cleanType('config');
            
            return array(
                'success' => true,
                'message' => $this->_helper->__('Configuration refreshed successfully')
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Failed to refresh configuration: %s', $e->getMessage())
            );
        }
    }
    
    /**
     * Reindex data if needed
     *
     * @return array
     */
    protected function _reindexData()
    {
        try {
            // Only reindex if indexer is available and not running
            if (!class_exists('Mage_Index_Model_Indexer')) {
                return array(
                    'success' => true,
                    'message' => $this->_helper->__('Indexer not available - skipped'),
                    'critical' => false
                );
            }
            
            $indexer = Mage::getSingleton('index/indexer');
            $processes = $indexer->getProcessesCollection();
            
            $reindexed = array();
            foreach ($processes as $process) {
                try {
                    if ($process->getStatus() == Mage_Index_Model_Process::STATUS_REQUIRE_REINDEX) {
                        $process->reindexEverything();
                        $reindexed[] = $process->getIndexerCode();
                    }
                } catch (Exception $e) {
                    $this->_logger->log('Failed to reindex: ' . $process->getIndexerCode() . ' - ' . $e->getMessage(), 
                        PFG_Core_Model_Logger::LOG_LEVEL_WARNING);
                }
            }
            
            $message = empty($reindexed) 
                ? $this->_helper->__('No reindexing required')
                : $this->_helper->__('Reindexed: %s', implode(', ', $reindexed));
            
            return array(
                'success' => true,
                'message' => $message,
                'critical' => false
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Reindexing failed: %s', $e->getMessage()),
                'critical' => false
            );
        }
    }
    
    /**
     * Logout admin users for security
     *
     * @return array
     */
    protected function _logoutAdminUsers()
    {
        try {
            $currentSessionId = session_id();
            $loggedOutCount = 0;
            
            // Get all admin sessions
            $resource = Mage::getSingleton('core/resource');
            $connection = $resource->getConnection('core_write');
            $sessionTable = $resource->getTableName('core/session');
            
            // Delete all admin sessions except current one
            $where = array(
                'session_id != ?' => $currentSessionId,
                'data LIKE ?' => '%admin%'
            );
            
            $loggedOutCount = $connection->delete($sessionTable, $where);
            
            // Also clear admin session storage
            try {
                $adminSessions = Mage::getResourceModel('admin/session_collection');
                foreach ($adminSessions as $session) {
                    if ($session->getSessionId() !== $currentSessionId) {
                        $session->delete();
                    }
                }
            } catch (Exception $e) {
                // Continue if admin session cleanup fails
            }
            
            return array(
                'success' => true,
                'message' => $this->_helper->__('Logged out %d admin sessions for security', $loggedOutCount)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Failed to logout admin users: %s', $e->getMessage())
            );
        }
    }
    
    /**
     * Clean up temporary files
     *
     * @return array
     */
    protected function _cleanupTempFiles()
    {
        try {
            $tempDir = $this->_helper->getTempDir();
            $cleanedFiles = 0;
            $cleanedSize = 0;
            
            if (is_dir($tempDir)) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($tempDir, RecursiveDirectoryIterator::SKIP_DOTS)
                );
                
                foreach ($iterator as $file) {
                    if ($file->isFile() && $file->getMTime() < (time() - 3600)) { // Files older than 1 hour
                        $cleanedSize += $file->getSize();
                        unlink($file->getPathname());
                        $cleanedFiles++;
                    }
                }
            }
            
            return array(
                'success' => true,
                'message' => $this->_helper->__('Cleaned up %d temporary files (%s)', 
                    $cleanedFiles, $this->_helper->formatFileSize($cleanedSize))
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Temporary file cleanup failed: %s', $e->getMessage())
            );
        }
    }
    
    /**
     * Validate installation
     *
     * @return array
     */
    protected function _validateInstallation()
    {
        try {
            $issues = array();
            
            // Check if Magento is still functional
            try {
                $store = Mage::app()->getStore();
                if (!$store->getId()) {
                    $issues[] = 'Store configuration issue';
                }
            } catch (Exception $e) {
                $issues[] = 'Store initialization failed: ' . $e->getMessage();
            }
            
            // Check if admin is accessible
            try {
                $adminConfig = Mage::getConfig()->getNode('admin');
                if (!$adminConfig) {
                    $issues[] = 'Admin configuration missing';
                }
            } catch (Exception $e) {
                $issues[] = 'Admin configuration check failed: ' . $e->getMessage();
            }
            
            // Check critical directories
            $criticalDirs = array(
                Mage::getBaseDir('var'),
                Mage::getBaseDir('media'),
                Mage::getBaseDir('app') . DS . 'etc'
            );
            
            foreach ($criticalDirs as $dir) {
                if (!is_dir($dir) || !is_writable($dir)) {
                    $issues[] = 'Directory issue: ' . $dir;
                }
            }
            
            if (empty($issues)) {
                return array(
                    'success' => true,
                    'message' => $this->_helper->__('Installation validation passed')
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $this->_helper->__('Installation validation failed: %s', implode(', ', $issues))
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $this->_helper->__('Installation validation error: %s', $e->getMessage())
            );
        }
    }
}
