<?php
/**
 * PFG Core Logger Model
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_Logger extends Mage_Core_Model_Abstract
{
    const LOG_LEVEL_EMERGENCY = 0;
    const LOG_LEVEL_ALERT = 1;
    const LOG_LEVEL_CRITICAL = 2;
    const LOG_LEVEL_ERROR = 3;
    const LOG_LEVEL_WARNING = 4;
    const LOG_LEVEL_NOTICE = 5;
    const LOG_LEVEL_INFO = 6;
    const LOG_LEVEL_DEBUG = 7;
    
    const LOG_TYPE_SYSTEM = 'system';
    const LOG_TYPE_SECURITY = 'security';
    const LOG_TYPE_INSTALLATION = 'installation';
    const LOG_TYPE_API = 'api';
    const LOG_TYPE_BACKUP = 'backup';
    
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_helper = Mage::helper('pfg_core');
    }
    
    /**
     * Log message with context
     *
     * @param string $message
     * @param int $level
     * @param string $type
     * @param array $context
     * @param string $file
     */
    public function log($message, $level = self::LOG_LEVEL_INFO, $type = self::LOG_TYPE_SYSTEM, $context = array(), $file = null)
    {
        if (!$this->_helper->isEnabled() && $level > self::LOG_LEVEL_ERROR) {
            return;
        }
        
        if ($level > $this->_helper->getLogLevel()) {
            return;
        }
        
        $logFile = $file ?: $this->_getLogFile($type);
        
        $logEntry = $this->_formatLogEntry($message, $level, $type, $context);
        
        // Write to file
        Mage::log($logEntry, null, $logFile, true);
        
        // For critical errors, also log to system log
        if ($level <= self::LOG_LEVEL_ERROR) {
            Mage::log($logEntry, null, 'system.log', true);
        }
        
        // Store in database for audit trail
        $this->_storeAuditLog($message, $level, $type, $context);
    }
    
    /**
     * Log installation activity
     *
     * @param string $action
     * @param string $moduleName
     * @param string $details
     * @param int $level
     */
    public function logInstallation($action, $moduleName, $details = '', $level = self::LOG_LEVEL_INFO)
    {
        $context = array(
            'action' => $action,
            'module_name' => $moduleName,
            'details' => $details,
            'user' => $this->_getCurrentUser(),
            'ip_address' => $this->_getClientIp()
        );
        
        $message = sprintf('Installation %s: %s - %s', $action, $moduleName, $details);
        $this->log($message, $level, self::LOG_TYPE_INSTALLATION, $context);
    }
    
    /**
     * Log security event
     *
     * @param string $event
     * @param string $details
     * @param int $level
     */
    public function logSecurity($event, $details = '', $level = self::LOG_LEVEL_WARNING)
    {
        $context = array(
            'event' => $event,
            'details' => $details,
            'user' => $this->_getCurrentUser(),
            'ip_address' => $this->_getClientIp(),
            'user_agent' => $this->_getUserAgent(),
            'session_id' => session_id()
        );
        
        $message = sprintf('Security Event: %s - %s', $event, $details);
        $this->log($message, $level, self::LOG_TYPE_SECURITY, $context, 'pfg_core_security.log');
    }
    
    /**
     * Log API activity
     *
     * @param string $endpoint
     * @param string $method
     * @param int $responseCode
     * @param string $details
     */
    public function logApi($endpoint, $method, $responseCode, $details = '')
    {
        $context = array(
            'endpoint' => $endpoint,
            'method' => $method,
            'response_code' => $responseCode,
            'details' => $details,
            'execution_time' => $this->_getExecutionTime()
        );
        
        $level = $responseCode >= 400 ? self::LOG_LEVEL_ERROR : self::LOG_LEVEL_INFO;
        $message = sprintf('API %s %s - Response: %d - %s', $method, $endpoint, $responseCode, $details);
        
        $this->log($message, $level, self::LOG_TYPE_API, $context);
    }
    
    /**
     * Log backup activity
     *
     * @param string $action
     * @param string $backupPath
     * @param string $details
     * @param int $level
     */
    public function logBackup($action, $backupPath, $details = '', $level = self::LOG_LEVEL_INFO)
    {
        $context = array(
            'action' => $action,
            'backup_path' => $backupPath,
            'details' => $details,
            'file_size' => file_exists($backupPath) ? filesize($backupPath) : 0,
            'user' => $this->_getCurrentUser()
        );
        
        $message = sprintf('Backup %s: %s - %s', $action, basename($backupPath), $details);
        $this->log($message, $level, self::LOG_TYPE_BACKUP, $context);
    }
    
    /**
     * Log exception with full context
     *
     * @param Exception $exception
     * @param string $context
     * @param int $level
     */
    public function logException(Exception $exception, $context = '', $level = self::LOG_LEVEL_ERROR)
    {
        $contextData = array(
            'exception_class' => get_class($exception),
            'exception_code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context,
            'user' => $this->_getCurrentUser(),
            'ip_address' => $this->_getClientIp()
        );
        
        $message = sprintf('Exception: %s in %s:%d - %s', 
            $exception->getMessage(), 
            $exception->getFile(), 
            $exception->getLine(),
            $context
        );
        
        $this->log($message, $level, self::LOG_TYPE_SYSTEM, $contextData);
    }
    
    /**
     * Format log entry
     *
     * @param string $message
     * @param int $level
     * @param string $type
     * @param array $context
     * @return string
     */
    protected function _formatLogEntry($message, $level, $type, $context)
    {
        $timestamp = date('Y-m-d H:i:s');
        $levelName = $this->_getLevelName($level);
        $pid = getmypid();
        $memory = memory_get_usage(true);
        
        $logEntry = sprintf(
            '[%s] [%s] [%s] [PID:%d] [MEM:%s] %s',
            $timestamp,
            $levelName,
            strtoupper($type),
            $pid,
            $this->_helper->formatFileSize($memory),
            $message
        );
        
        if (!empty($context)) {
            $logEntry .= ' | Context: ' . json_encode($context);
        }
        
        return $logEntry;
    }
    
    /**
     * Get log file name for type
     *
     * @param string $type
     * @return string
     */
    protected function _getLogFile($type)
    {
        $files = array(
            self::LOG_TYPE_SYSTEM => 'pfg_core.log',
            self::LOG_TYPE_SECURITY => 'pfg_core_security.log',
            self::LOG_TYPE_INSTALLATION => 'pfg_core_installation.log',
            self::LOG_TYPE_API => 'pfg_core_api.log',
            self::LOG_TYPE_BACKUP => 'pfg_core_backup.log'
        );
        
        return isset($files[$type]) ? $files[$type] : 'pfg_core.log';
    }
    
    /**
     * Get level name
     *
     * @param int $level
     * @return string
     */
    protected function _getLevelName($level)
    {
        $levels = array(
            self::LOG_LEVEL_EMERGENCY => 'EMERGENCY',
            self::LOG_LEVEL_ALERT => 'ALERT',
            self::LOG_LEVEL_CRITICAL => 'CRITICAL',
            self::LOG_LEVEL_ERROR => 'ERROR',
            self::LOG_LEVEL_WARNING => 'WARNING',
            self::LOG_LEVEL_NOTICE => 'NOTICE',
            self::LOG_LEVEL_INFO => 'INFO',
            self::LOG_LEVEL_DEBUG => 'DEBUG'
        );
        
        return isset($levels[$level]) ? $levels[$level] : 'UNKNOWN';
    }
    
    /**
     * Store audit log in database
     *
     * @param string $message
     * @param int $level
     * @param string $type
     * @param array $context
     */
    protected function _storeAuditLog($message, $level, $type, $context)
    {
        try {
            // Only store important events in database
            if ($level > self::LOG_LEVEL_WARNING && $type !== self::LOG_TYPE_SECURITY) {
                return;
            }
            
            $auditData = array(
                'message' => $message,
                'level' => $level,
                'type' => $type,
                'context' => json_encode($context),
                'user_id' => $this->_getCurrentUserId(),
                'ip_address' => $this->_getClientIp(),
                'created_at' => now()
            );
            
            // Store in a simple log table (you could create this table in the setup script)
            $write = Mage::getSingleton('core/resource')->getConnection('core_write');
            $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');
            
            // Check if table exists before inserting
            if ($write->isTableExists($table)) {
                $write->insert($table, $auditData);
            }
            
        } catch (Exception $e) {
            // Don't let audit logging break the main functionality
            Mage::log('Audit log storage failed: ' . $e->getMessage(), null, 'pfg_core_error.log', true);
        }
    }
    
    /**
     * Get current admin user
     *
     * @return string
     */
    protected function _getCurrentUser()
    {
        $session = Mage::getSingleton('admin/session');
        if ($session->isLoggedIn()) {
            return $session->getUser()->getUsername();
        }
        return 'system';
    }
    
    /**
     * Get current admin user ID
     *
     * @return int|null
     */
    protected function _getCurrentUserId()
    {
        $session = Mage::getSingleton('admin/session');
        if ($session->isLoggedIn()) {
            return $session->getUser()->getId();
        }
        return null;
    }
    
    /**
     * Get client IP address
     *
     * @return string
     */
    protected function _getClientIp()
    {
        return Mage::helper('core/http')->getRemoteAddr();
    }
    
    /**
     * Get user agent
     *
     * @return string
     */
    protected function _getUserAgent()
    {
        return isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown';
    }
    
    /**
     * Get execution time
     *
     * @return float
     */
    protected function _getExecutionTime()
    {
        return microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'];
    }
}
