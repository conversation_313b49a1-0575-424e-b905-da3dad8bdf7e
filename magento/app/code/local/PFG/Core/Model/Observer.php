<?php
/**
 * PFG Core Observer
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_Observer
{
    /**
     * Validate admin security for PFG Core actions
     *
     * @param Varien_Event_Observer $observer
     */
    public function validateAdminSecurity($observer)
    {
        $controllerAction = $observer->getControllerAction();
        $request = $controllerAction->getRequest();
        
        // Only apply to PFG Core admin actions
        if (strpos($request->getControllerName(), 'pfg_core') === false) {
            return;
        }
        
        // Skip for non-POST requests (except specific actions)
        $postActions = array('testConnection', 'installModule', 'updateModule', 'rollback', 'postInstallActions');
        $actionName = $request->getActionName();
        
        if (!$request->isPost() && !in_array($actionName, $postActions)) {
            return;
        }
        
        // Additional security checks for sensitive actions
        $sensitiveActions = array('installModule', 'updateModule', 'rollback');
        
        if (in_array($actionName, $sensitiveActions)) {
            $session = Mage::getSingleton('admin/session');
            
            // Check if user is logged in
            if (!$session->isLoggedIn()) {
                throw new Exception('Admin session required');
            }
            
            // Check permissions
            if (!$session->isAllowed('pfg/core')) {
                throw new Exception('Insufficient permissions');
            }
            
            // Log the action
            Mage::helper('pfg_core')->log(sprintf(
                'Admin action: %s by user %s from IP %s',
                $actionName,
                $session->getUser()->getUsername(),
                $request->getClientIp()
            ), Zend_Log::INFO);
        }
    }
}
