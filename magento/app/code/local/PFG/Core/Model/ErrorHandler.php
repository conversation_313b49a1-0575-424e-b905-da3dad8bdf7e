<?php
/**
 * PFG Core Error Handler Model
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_ErrorHandler extends Mage_Core_Model_Abstract
{
    /**
     * @var PFG_Core_Model_Logger
     */
    protected $_logger;
    
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_logger = Mage::getModel('pfg_core/logger');
        $this->_helper = Mage::helper('pfg_core');
    }

    /**
     * Handle validation errors with standardized response
     *
     * @param PFG_Core_Exception_Validation $exception
     * @param string $operationName
     * @param array $context
     * @return array
     */
    public function handleValidationError(PFG_Core_Exception_Validation $exception, $operationName, array $context = array())
    {
        return $this->_createStandardizedErrorResponse($exception, $operationName, $context, 'validation');
    }

    /**
     * Handle API errors with standardized response
     *
     * @param PFG_Core_Exception_Api $exception
     * @param string $operationName
     * @param array $context
     * @return array
     */
    public function handleApiError(PFG_Core_Exception_Api $exception, $operationName, array $context = array())
    {
        return $this->_createStandardizedErrorResponse($exception, $operationName, $context, 'api');
    }

    /**
     * Handle database errors with standardized response
     *
     * @param PFG_Core_Exception_Database $exception
     * @param string $operationName
     * @param array $context
     * @return array
     */
    public function handleDatabaseError(PFG_Core_Exception_Database $exception, $operationName, array $context = array())
    {
        return $this->_createStandardizedErrorResponse($exception, $operationName, $context, 'database');
    }

    /**
     * Handle generic errors with standardized response
     *
     * @param Exception $exception
     * @param string $operationName
     * @param array $context
     * @return array
     */
    public function handleGenericError(Exception $exception, $operationName, array $context = array())
    {
        // Convert generic exception to PFG Core exception format
        $pfgException = new PFG_Core_Exception_Generic(
            $exception->getMessage(),
            'GENERIC_ERROR',
            $context,
            $exception->getCode(),
            $exception
        );

        return $this->_createStandardizedErrorResponse($pfgException, $operationName, $context, 'generic');
    }

    /**
     * Create standardized error response
     *
     * @param PFG_Core_Exception_Abstract $exception
     * @param string $operationName
     * @param array $context
     * @param string $errorType
     * @return array
     */
    protected function _createStandardizedErrorResponse(PFG_Core_Exception_Abstract $exception, $operationName, array $context = array(), $errorType = 'generic')
    {
        $errorId = $this->_generateErrorId();

        // Merge contexts
        $fullContext = array_merge($context, $exception->getContext(), array(
            'operation' => $operationName,
            'error_type' => $errorType,
            'error_id' => $errorId,
            'timestamp' => time()
        ));

        // Log the error with full context
        $this->_logger->log(
            sprintf('[%s] %s failed: %s', $errorType, $operationName, $exception->getMessage()),
            $exception->getLogLevel(),
            PFG_Core_Model_Logger::LOG_TYPE_ERROR,
            $fullContext
        );

        // Create user-friendly error message
        $userMessage = $this->_createUserFriendlyMessage($exception, $errorType);

        return array(
            'success' => false,
            'error' => $userMessage,
            'error_code' => $exception->getErrorCode(),
            'error_id' => $errorId,
            'error_type' => $errorType,
            'context' => $this->_sanitizeContextForUser($fullContext),
            'timestamp' => time(),
            'debug_info' => $this->_helper->getLogLevel() >= PFG_Core_Model_Logger::LOG_LEVEL_DEBUG ? array(
                'exception_class' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ) : null
        );
    }

    /**
     * Create user-friendly error message
     *
     * @param PFG_Core_Exception_Abstract $exception
     * @param string $errorType
     * @return string
     */
    protected function _createUserFriendlyMessage(PFG_Core_Exception_Abstract $exception, $errorType)
    {
        $message = $exception->getMessage();

        // Create more user-friendly messages based on error type
        switch ($errorType) {
            case 'validation':
                return $this->_helper->__('Validation Error: %s', $message);
            case 'api':
                return $this->_helper->__('API Error: %s', $message);
            case 'database':
                return $this->_helper->__('Database Error: %s', $message);
            default:
                return $this->_helper->__('System Error: %s', $message);
        }
    }

    /**
     * Sanitize context data for user display
     *
     * @param array $context
     * @return array
     */
    protected function _sanitizeContextForUser(array $context)
    {
        // Remove sensitive information from context
        $sensitiveKeys = array('password', 'token', 'key', 'secret', 'credentials', 'auth');
        $sanitized = array();

        foreach ($context as $key => $value) {
            $keyLower = strtolower($key);
            $isSensitive = false;

            foreach ($sensitiveKeys as $sensitiveKey) {
                if (strpos($keyLower, $sensitiveKey) !== false) {
                    $isSensitive = true;
                    break;
                }
            }

            if ($isSensitive) {
                $sanitized[$key] = '[REDACTED]';
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }
    
    /**
     * Handle installation errors
     *
     * @param Exception $exception
     * @param string $moduleName
     * @param string $action
     * @return array
     */
    public function handleInstallationError(Exception $exception, $moduleName, $action = 'install')
    {
        $errorId = $this->_generateErrorId();
        
        // Log the error
        $this->_logger->logException($exception, sprintf('Installation %s failed for %s', $action, $moduleName));
        
        // Create error report
        $errorReport = $this->_createErrorReport($exception, array(
            'module_name' => $moduleName,
            'action' => $action,
            'error_id' => $errorId
        ));
        
        // Store error report
        $this->_storeErrorReport($errorId, $errorReport);
        
        // Determine user-friendly message
        $userMessage = $this->_getUserFriendlyMessage($exception, $action);
        
        return array(
            'success' => false,
            'error' => $userMessage,
            'error_id' => $errorId,
            'technical_details' => $this->_helper->getLogLevel() >= PFG_Core_Model_Logger::LOG_LEVEL_DEBUG ? $exception->getMessage() : null
        );
    }
    
    /**
     * Handle API errors (legacy method)
     *
     * @param Exception $exception
     * @param string $endpoint
     * @param string $method
     * @return array
     */
    public function handleLegacyApiError(Exception $exception, $endpoint, $method = 'GET')
    {
        $errorId = $this->_generateErrorId();
        
        // Log the error
        $this->_logger->logException($exception, sprintf('API error for %s %s', $method, $endpoint));
        
        // Determine if it's a network issue, authentication issue, or server error
        $errorType = $this->_classifyApiError($exception);
        
        $userMessage = $this->_getApiErrorMessage($errorType, $exception);
        
        return array(
            'success' => false,
            'error' => $userMessage,
            'error_id' => $errorId,
            'error_type' => $errorType
        );
    }
    
    /**
     * Handle backup errors
     *
     * @param Exception $exception
     * @param string $action
     * @param string $backupPath
     * @return array
     */
    public function handleBackupError(Exception $exception, $action, $backupPath = '')
    {
        $errorId = $this->_generateErrorId();
        
        // Log the error
        $this->_logger->logException($exception, sprintf('Backup %s failed: %s', $action, $backupPath));
        
        $userMessage = $this->_getBackupErrorMessage($exception, $action);
        
        return array(
            'success' => false,
            'error' => $userMessage,
            'error_id' => $errorId
        );
    }
    
    /**
     * Handle validation errors
     *
     * @param array $validationErrors
     * @param string $context
     * @return array
     */
    public function handleValidationErrors($validationErrors, $context = '')
    {
        $errorId = $this->_generateErrorId();
        
        // Log validation failures
        $this->_logger->log(
            sprintf('Validation failed in %s: %s', $context, implode(', ', $validationErrors)),
            PFG_Core_Model_Logger::LOG_LEVEL_WARNING,
            PFG_Core_Model_Logger::LOG_TYPE_SYSTEM
        );
        
        return array(
            'success' => false,
            'error' => $this->_helper->__('Validation failed. Please check the requirements and try again.'),
            'validation_errors' => $validationErrors,
            'error_id' => $errorId
        );
    }
    
    /**
     * Create comprehensive error report
     *
     * @param Exception $exception
     * @param array $context
     * @return array
     */
    protected function _createErrorReport(Exception $exception, $context = array())
    {
        return array(
            'timestamp' => date('Y-m-d H:i:s'),
            'exception' => array(
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ),
            'context' => $context,
            'system' => array(
                'php_version' => PHP_VERSION,
                'magento_version' => Mage::getVersion(),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ),
            'request' => array(
                'method' => $_SERVER['REQUEST_METHOD'],
                'uri' => $_SERVER['REQUEST_URI'],
                'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
                'ip_address' => Mage::helper('core/http')->getRemoteAddr()
            ),
            'user' => array(
                'admin_user' => $this->_getCurrentAdminUser(),
                'session_id' => session_id()
            )
        );
    }
    
    /**
     * Store error report to file
     *
     * @param string $errorId
     * @param array $errorReport
     */
    protected function _storeErrorReport($errorId, $errorReport)
    {
        try {
            $reportDir = Mage::getBaseDir('var') . DS . 'pfg_core' . DS . 'error_reports';
            if (!is_dir($reportDir)) {
                mkdir($reportDir, 0755, true);
            }
            
            $reportFile = $reportDir . DS . $errorId . '.json';
            file_put_contents($reportFile, json_encode($errorReport, JSON_PRETTY_PRINT));
            
        } catch (Exception $e) {
            // Don't let error report storage break the main error handling
            Mage::log('Failed to store error report: ' . $e->getMessage(), null, 'pfg_core_error.log', true);
        }
    }
    
    /**
     * Generate unique error ID
     *
     * @return string
     */
    protected function _generateErrorId()
    {
        return 'PFG_' . date('Ymd_His') . '_' . substr(md5(uniqid(mt_rand(), true)), 0, 8);
    }
    
    /**
     * Get user-friendly error message
     *
     * @param Exception $exception
     * @param string $action
     * @return string
     */
    protected function _getUserFriendlyMessage(Exception $exception, $action)
    {
        $message = $exception->getMessage();
        
        // Common error patterns and their user-friendly messages
        $patterns = array(
            '/permission denied/i' => $this->_helper->__('Permission denied. Please check file permissions.'),
            '/disk.*full/i' => $this->_helper->__('Insufficient disk space. Please free up space and try again.'),
            '/connection.*refused/i' => $this->_helper->__('Connection failed. Please check your network connection.'),
            '/timeout/i' => $this->_helper->__('Operation timed out. Please try again later.'),
            '/not found/i' => $this->_helper->__('Required file or resource not found.'),
            '/invalid.*format/i' => $this->_helper->__('Invalid file format. Please check the module package.'),
            '/authentication.*failed/i' => $this->_helper->__('Authentication failed. Please check your credentials.'),
            '/access.*denied/i' => $this->_helper->__('Access denied. Please check your permissions.')
        );
        
        foreach ($patterns as $pattern => $friendlyMessage) {
            if (preg_match($pattern, $message)) {
                return $friendlyMessage;
            }
        }
        
        // Default message based on action
        switch ($action) {
            case 'install':
                return $this->_helper->__('Module installation failed. Please check the logs for details.');
            case 'update':
                return $this->_helper->__('Module update failed. Please check the logs for details.');
            case 'rollback':
                return $this->_helper->__('Rollback failed. Please check the logs for details.');
            default:
                return $this->_helper->__('Operation failed. Please check the logs for details.');
        }
    }
    
    /**
     * Classify API error type
     *
     * @param Exception $exception
     * @return string
     */
    protected function _classifyApiError(Exception $exception)
    {
        $message = strtolower($exception->getMessage());
        
        if (strpos($message, 'connection') !== false || strpos($message, 'network') !== false) {
            return 'network';
        }
        
        if (strpos($message, 'authentication') !== false || strpos($message, 'unauthorized') !== false) {
            return 'authentication';
        }
        
        if (strpos($message, 'timeout') !== false) {
            return 'timeout';
        }
        
        if (strpos($message, 'not found') !== false) {
            return 'not_found';
        }
        
        return 'server_error';
    }
    
    /**
     * Get API error message
     *
     * @param string $errorType
     * @param Exception $exception
     * @return string
     */
    protected function _getApiErrorMessage($errorType, Exception $exception)
    {
        switch ($errorType) {
            case 'network':
                return $this->_helper->__('Network connection failed. Please check your internet connection.');
            case 'authentication':
                return $this->_helper->__('Authentication failed. Please check your Bitbucket credentials.');
            case 'timeout':
                return $this->_helper->__('Request timed out. Please try again later.');
            case 'not_found':
                return $this->_helper->__('Repository or resource not found. Please check the configuration.');
            default:
                return $this->_helper->__('API request failed. Please try again later.');
        }
    }
    
    /**
     * Get backup error message
     *
     * @param Exception $exception
     * @param string $action
     * @return string
     */
    protected function _getBackupErrorMessage(Exception $exception, $action)
    {
        $message = strtolower($exception->getMessage());
        
        if (strpos($message, 'permission') !== false) {
            return $this->_helper->__('Backup failed due to permission issues. Please check file permissions.');
        }
        
        if (strpos($message, 'disk') !== false || strpos($message, 'space') !== false) {
            return $this->_helper->__('Backup failed due to insufficient disk space.');
        }
        
        switch ($action) {
            case 'create':
                return $this->_helper->__('Failed to create backup. Please check the logs for details.');
            case 'restore':
                return $this->_helper->__('Failed to restore from backup. Please check the logs for details.');
            default:
                return $this->_helper->__('Backup operation failed. Please check the logs for details.');
        }
    }
    
    /**
     * Get current admin user
     *
     * @return string
     */
    protected function _getCurrentAdminUser()
    {
        $session = Mage::getSingleton('admin/session');
        if ($session->isLoggedIn()) {
            return $session->getUser()->getUsername();
        }
        return 'system';
    }
}
