<?php
/**
 * PFG Core Repository Model
 *
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Model_Repository extends Mage_Core_Model_Abstract
{
    /**
     * @var PFG_Core_Service_Repository
     */
    protected $_repositoryService;

    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        try {
            $this->_repositoryService = Mage::getSingleton('pfg_core/service_repository');
        } catch (Exception $e) {
            // Service layer not available, will use fallback methods
            $this->_repositoryService = null;
        }

        $this->_helper = Mage::helper('pfg_core');
    }
    
    /**
     * Get all repositories with installation status
     *
     * @param bool $useCache
     * @return array
     */
    public function getRepositoriesWithStatus($useCache = true)
    {
        try {
            if ($this->_repositoryService) {
                return $this->_repositoryService->getRepositoriesWithStatus($useCache);
            }

            return $this->_getRepositoriesWithStatusFallback();

        } catch (Exception $e) {
            $this->_helper->log('Service layer error, using fallback: ' . $e->getMessage(), Zend_Log::WARN);
            return $this->_getRepositoriesWithStatusFallback();
        }
    }

    /**
     * Fallback implementation for getting repositories
     *
     * @return array
     */
    protected function _getRepositoriesWithStatusFallback()
    {
        try {
            $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
            $response = $bitbucketHelper->getRepositories();

            if (!$response['success']) {
                throw new Exception($response['error']);
            }

            $repositories = $response['data'];
            $installedModules = $this->_getInstalledModulesFallback();

            foreach ($repositories as &$repo) {
                $repositoryIdentifier = isset($repo['slug']) ? $repo['slug'] : $repo['name'];
                $moduleName = $this->_getModuleNameFromRepositoryFallback($repositoryIdentifier);
                $repo['module_name'] = $moduleName;
                $repo['repository_identifier'] = $repositoryIdentifier;
                $repo['is_installed'] = isset($installedModules[$moduleName]);
                $repo['installed_version'] = isset($installedModules[$moduleName]) ? $installedModules[$moduleName] : null;

                // Get latest version from tags
                $tagsResponse = $bitbucketHelper->getRepositoryTags($repositoryIdentifier);
                if ($tagsResponse['success'] && !empty($tagsResponse['data'])) {
                    $repo['latest_version'] = $tagsResponse['data'][0]['name'];
                    $repo['has_update'] = $repo['is_installed'] &&
                        version_compare($repo['installed_version'], $repo['latest_version'], '<');
                } else {
                    $repo['latest_version'] = 'master';
                    $repo['has_update'] = false;
                }

                $repo['installation_status'] = $this->_getInstallationStatusFallback($repo);
                $repo['last_installation'] = $this->_getLastInstallationFallback($repo['module_name']);
            }

            return array(
                'success' => true,
                'data' => $repositories
            );

        } catch (Exception $e) {
            $this->_helper->log('Fallback implementation failed: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get repository details by name
     *
     * @param string $repositoryName
     * @param bool $useCache
     * @return array
     */
    public function getRepositoryDetails($repositoryName, $useCache = true)
    {
        try {
            if ($this->_repositoryService) {
                return $this->_repositoryService->getRepositoryDetails($repositoryName, $useCache);
            }

            // Fallback implementation
            $repositories = $this->getRepositoriesWithStatus();

            if (!$repositories['success']) {
                throw new Exception($repositories['error']);
            }

            foreach ($repositories['data'] as $repo) {
                $repositoryIdentifier = isset($repo['repository_identifier']) ? $repo['repository_identifier'] : $repo['name'];
                if ($repo['name'] === $repositoryName ||
                    (isset($repo['slug']) && $repo['slug'] === $repositoryName) ||
                    $repositoryIdentifier === $repositoryName) {

                    $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
                    $tagsResponse = $bitbucketHelper->getRepositoryTags($repositoryIdentifier);
                    if ($tagsResponse['success']) {
                        $repo['tags'] = $tagsResponse['data'];
                    }

                    return array(
                        'success' => true,
                        'data' => $repo
                    );
                }
            }

            throw new Exception('Repository not found');

        } catch (Exception $e) {
            $this->_helper->log('Failed to get repository details: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Check if repository can be installed
     *
     * @param string $repositoryName
     * @return array
     */
    public function canInstallRepository($repositoryName)
    {
        try {
            if ($this->_repositoryService) {
                return $this->_repositoryService->canInstallRepository($repositoryName);
            }

            // Fallback implementation
            $repoDetails = $this->getRepositoryDetails($repositoryName);

            if (!$repoDetails['success']) {
                throw new Exception($repoDetails['error']);
            }

            $repo = $repoDetails['data'];
            $checks = array();
            $canInstall = true;

            $validator = Mage::helper('pfg_core/validator');
            $systemChecks = $validator->validateSystemRequirements();

            if (!$systemChecks['can_install']) {
                $canInstall = false;
            }

            $checks = array_merge($checks, $systemChecks['checks']);

            $compatibilityChecks = $validator->validateModuleCompatibility($repo['module_name']);
            $checks = array_merge($checks, $compatibilityChecks['checks']);

            if ($repo['is_installed']) {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->_helper->__('Module is already installed (version %s)', $repo['installed_version'])
                );
            }

            if (!$this->_helper->isValidModuleName($repo['module_name'])) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->_helper->__('Invalid module name format: %s', $repo['module_name'])
                );
                $canInstall = false;
            }

            return array(
                'success' => true,
                'can_install' => $canInstall,
                'checks' => $checks
            );

        } catch (Exception $e) {
            $this->_helper->log('Failed to check repository installation: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Clear repository cache
     *
     * @param string $repositoryName
     * @return $this
     */
    public function clearCache($repositoryName = null)
    {
        if ($this->_repositoryService) {
            $this->_repositoryService->clearRepositoryCache($repositoryName);
        }
        return $this;
    }

    /**
     * Get installed modules (fallback)
     *
     * @return array
     */
    protected function _getInstalledModulesFallback()
    {
        $modules = array();
        $config = Mage::getConfig();

        foreach ($config->getNode('modules')->children() as $moduleName => $moduleConfig) {
            if ($moduleConfig->active == 'true') {
                $modules[$moduleName] = (string)$moduleConfig->version;
            }
        }

        return $modules;
    }

    /**
     * Get module name from repository name (fallback)
     *
     * @param string $repositoryName
     * @return string
     */
    protected function _getModuleNameFromRepositoryFallback($repositoryName)
    {
        // Clean the repository name - remove spaces and convert to lowercase
        $cleanName = strtolower(trim($repositoryName));

        // Replace spaces with hyphens for consistency
        $cleanName = preg_replace('/\s+/', '-', $cleanName);

        // Split by hyphens and build module name
        $parts = explode('-', $cleanName);
        $moduleName = '';

        foreach ($parts as $part) {
            $part = trim($part);
            if (!empty($part)) {
                $moduleName .= ucfirst($part);
            }
        }

        // Handle PFG prefix
        if (strpos($moduleName, 'Pfg') === 0) {
            $moduleName = 'PFG_' . substr($moduleName, 3);
        } elseif (strpos($moduleName, 'PFG') !== 0) {
            $moduleName = 'PFG_' . $moduleName;
        }



        return $moduleName;
    }

    /**
     * Get installation status (fallback)
     *
     * @param array $repo
     * @return string
     */
    protected function _getInstallationStatusFallback($repo)
    {
        if (!$repo['is_installed']) {
            return 'not_installed';
        }

        if ($repo['has_update']) {
            return 'update_available';
        }

        return 'installed';
    }

    /**
     * Get last installation record for module (fallback)
     *
     * @param string $moduleName
     * @return array|null
     */
    protected function _getLastInstallationFallback($moduleName)
    {
        try {
            $installation = Mage::getModel('pfg_core/installation')->getCollection()
                ->addFieldToFilter('module_name', $moduleName)
                ->addFieldToFilter('installation_status', array('in' => array('completed', 'failed')))
                ->setOrder('installed_at', 'DESC')
                ->getFirstItem();

            if ($installation->getId()) {
                return array(
                    'installation_id' => $installation->getId(),
                    'backup_id' => $installation->getBackupId(),
                    'installation_type' => $installation->getInstallationType(),
                    'version_installed' => $installation->getVersionInstalled(),
                    'installed_at' => $installation->getInstalledAt(),
                    'can_rollback' => $installation->getBackupId() && $installation->getInstallationStatus() === 'completed'
                );
            }

            return null;
        } catch (Exception $e) {
            $this->_helper->log('Failed to get last installation for ' . $moduleName . ': ' . $e->getMessage(), Zend_Log::ERR);
            return null;
        }
    }
}
