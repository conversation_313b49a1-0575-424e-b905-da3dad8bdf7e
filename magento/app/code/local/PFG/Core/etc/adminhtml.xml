<?xml version="1.0"?>
<!--
/**
 * PFG Core Admin Configuration
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<config>
    <!-- PFG Core menu items removed - all functionality accessible through System > Configuration > PFG -->
    
    <acl>
        <resources>
            <all>
                <title>Allow Everything</title>
            </all>
            <admin>
                <children>
                    <!-- PFG Core functionality accessible only through System Configuration -->
                    <system>
                        <children>
                            <config>
                                <children>
                                    <pfg translate="title" module="pfg_core">
                                        <title>PFG Configuration</title>
                                        <children>
                                            <core translate="title" module="pfg_core">
                                                <title>Core Module Management</title>
                                            </core>
                                        </children>
                                    </pfg>
                                </children>
                            </config>
                        </children>
                    </system>
                </children>
            </admin>
        </resources>
    </acl>
</config>
