<?php
/**
 * PFG Core Database Exception
 * 
 * Exception thrown when database operations fail,
 * including connection issues, query errors, and data integrity problems.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Exception_Database extends PFG_Core_Exception_Abstract
{
    /**
     * @var string SQL query that caused the error
     */
    protected $_query;
    
    /**
     * @var array Query parameters
     */
    protected $_queryParams;
    
    /**
     * Constructor
     *
     * @param string $message
     * @param string $errorCode
     * @param array $context
     * @param string $query
     * @param array $queryParams
     * @param int $code
     * @param Exception $previous
     */
    public function __construct($message = '', $errorCode = null, array $context = array(), $query = '', array $queryParams = array(), $code = 0, Exception $previous = null)
    {
        $this->_query = $query;
        $this->_queryParams = $queryParams;
        
        // Add query information to context
        if ($query) {
            $context['query'] = $query;
        }
        if (!empty($queryParams)) {
            $context['query_params'] = $queryParams;
        }
        
        parent::__construct($message, $errorCode, $context, $code, $previous);
    }
    
    /**
     * Get SQL query
     *
     * @return string
     */
    public function getQuery()
    {
        return $this->_query;
    }
    
    /**
     * Get query parameters
     *
     * @return array
     */
    public function getQueryParams()
    {
        return $this->_queryParams;
    }
    
    /**
     * Get default error code for database exceptions
     *
     * @return string
     */
    protected function _getDefaultErrorCode()
    {
        return 'DATABASE_ERROR';
    }
}
