<?php
/**
 * PFG Core Generic Exception
 * 
 * Exception for generic errors that don't fit into specific categories.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Exception_Generic extends PFG_Core_Exception_Abstract
{
    /**
     * Get default error code for generic exceptions
     *
     * @return string
     */
    protected function _getDefaultErrorCode()
    {
        return 'GENERIC_ERROR';
    }
}
