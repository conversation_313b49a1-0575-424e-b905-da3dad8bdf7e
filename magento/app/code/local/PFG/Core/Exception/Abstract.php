<?php
/**
 * PFG Core Abstract Exception
 * 
 * Base exception class for all PFG Core exceptions with standardized
 * error codes, context data, and logging capabilities.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
abstract class PFG_Core_Exception_Abstract extends Exception
{
    /**
     * @var string
     */
    protected $_errorCode;
    
    /**
     * @var array
     */
    protected $_context = array();
    

    
    /**
     * Constructor
     *
     * @param string $message
     * @param string $errorCode
     * @param array $context
     * @param int $code
     * @param Exception $previous
     */
    public function __construct($message = '', $errorCode = null, array $context = array(), $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);

        $this->_errorCode = $errorCode ?: $this->_getDefaultErrorCode();
        $this->_context = $context;
    }
    
    /**
     * Get error code
     *
     * @return string
     */
    public function getErrorCode()
    {
        return $this->_errorCode;
    }
    
    /**
     * Get context data
     *
     * @return array
     */
    public function getContext()
    {
        return $this->_context;
    }
    
    /**
     * Set context data
     *
     * @param array $context
     * @return $this
     */
    public function setContext(array $context)
    {
        $this->_context = $context;
        return $this;
    }
    
    /**
     * Add context data
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function addContext($key, $value)
    {
        $this->_context[$key] = $value;
        return $this;
    }
    

    
    /**
     * Convert exception to array format
     *
     * @return array
     */
    public function toArray()
    {
        return array(
            'success' => false,
            'error' => $this->getMessage(),
            'error_code' => $this->getErrorCode(),
            'context' => $this->getContext(),
            'timestamp' => time(),
            'exception_class' => get_class($this),
            'file' => $this->getFile(),
            'line' => $this->getLine()
        );
    }
    
    /**
     * Get default error code for this exception type
     *
     * @return string
     */
    abstract protected function _getDefaultErrorCode();
    

}
