<?php
/**
 * PFG Core Validation Exception
 * 
 * Exception thrown when validation fails for user input,
 * configuration, or business rules.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Exception_Validation extends PFG_Core_Exception_Abstract
{
    /**
     * Get default error code for validation exceptions
     *
     * @return string
     */
    protected function _getDefaultErrorCode()
    {
        return 'VALIDATION_ERROR';
    }
}
