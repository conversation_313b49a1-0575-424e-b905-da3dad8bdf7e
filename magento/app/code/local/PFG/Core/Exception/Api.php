<?php
/**
 * PFG Core API Exception
 * 
 * Exception thrown when API operations fail, including
 * Bitbucket API calls, HTTP errors, and external service issues.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Exception_Api extends PFG_Core_Exception_Abstract
{
    /**
     * @var int HTTP status code
     */
    protected $_httpStatusCode;
    
    /**
     * @var array API response data
     */
    protected $_responseData;
    
    /**
     * Constructor
     *
     * @param string $message
     * @param string $errorCode
     * @param array $context
     * @param int $httpStatusCode
     * @param array $responseData
     * @param int $code
     * @param Exception $previous
     */
    public function __construct($message = '', $errorCode = null, array $context = array(), $httpStatusCode = 0, array $responseData = array(), $code = 0, Exception $previous = null)
    {
        $this->_httpStatusCode = $httpStatusCode;
        $this->_responseData = $responseData;
        
        // Add HTTP status and response data to context
        $context['http_status_code'] = $httpStatusCode;
        $context['response_data'] = $responseData;
        
        parent::__construct($message, $errorCode, $context, $code, $previous);
    }
    
    /**
     * Get HTTP status code
     *
     * @return int
     */
    public function getHttpStatusCode()
    {
        return $this->_httpStatusCode;
    }
    
    /**
     * Get API response data
     *
     * @return array
     */
    public function getResponseData()
    {
        return $this->_responseData;
    }
    
    /**
     * Get default error code for API exceptions
     *
     * @return string
     */
    protected function _getDefaultErrorCode()
    {
        return 'API_ERROR';
    }
}
