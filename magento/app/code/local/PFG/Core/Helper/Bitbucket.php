<?php
/**
 * PFG Core Bitbucket API Helper
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Helper_Bitbucket extends Mage_Core_Helper_Abstract
{
    const BITBUCKET_API_BASE_URL = 'https://api.bitbucket.org/2.0';
    
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->_helper = Mage::helper('pfg_core');
    }
    
    /**
     * Test Bitbucket API connection
     *
     * @return array
     */
    public function testConnection()
    {
        try {
            if (!$this->_helper->hasCredentials()) {
                return array(
                    'success' => false,
                    'message' => $this->__('Bitbucket credentials not configured')
                );
            }
            
            // Don't cache connection tests
            $response = $this->_makeApiRequest('/user', 'GET', array(), false, 0);
            
            if ($response['success']) {
                $userData = $response['data'];
                return array(
                    'success' => true,
                    'message' => $this->__('Connection successful. Authenticated as: %s', $userData['display_name']),
                    'data' => $userData
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $this->__('Connection failed: %s', $response['error'])
                );
            }
        } catch (Exception $e) {
            $this->_helper->log('Bitbucket connection test failed: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'message' => $this->__('Connection test failed: %s', $e->getMessage())
            );
        }
    }
    
    /**
     * Get repositories from Bitbucket project
     *
     * @return array
     */
    public function getRepositories()
    {
        try {
            if (!$this->_helper->hasCredentials()) {
                throw new Exception('Bitbucket credentials not configured');
            }
            
            $workspace = $this->_helper->getBitbucketWorkspace();
            $project = $this->_helper->getBitbucketProject();
            
            $endpoint = sprintf('/repositories/%s', $workspace);
            $params = array(
                'q' => sprintf('project.key="%s"', $project),
                'sort' => 'name',
                'pagelen' => 100
            );
            
            // Cache repository list for 10 minutes (longer than default)
            $response = $this->_makeApiRequest($endpoint, 'GET', $params, false, 600);
            
            if (!$response['success']) {
                throw new Exception($response['error']);
            }
            
            $repositories = array();
            if (isset($response['data']['values'])) {
                foreach ($response['data']['values'] as $repo) {
                    $repositories[] = $this->_formatRepositoryData($repo);
                }
            }
            
            $this->_helper->log(sprintf('Retrieved %d repositories from Bitbucket', count($repositories)), Zend_Log::INFO);
            
            return array(
                'success' => true,
                'data' => $repositories
            );
            
        } catch (Exception $e) {
            $this->_helper->log('Failed to get repositories: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get repository tags/releases
     *
     * @param string $repositoryName
     * @return array
     */
    public function getRepositoryTags($repositoryName)
    {
        try {
            $workspace = $this->_helper->getBitbucketWorkspace();
            $endpoint = sprintf('/repositories/%s/%s/refs/tags', $workspace, $repositoryName);

            $params = array(
                'sort' => '-name',
                'pagelen' => 50
            );

            // Cache repository tags for 15 minutes (tags don't change frequently)
            $response = $this->_makeApiRequest($endpoint, 'GET', $params, false, 900);

            if (!$response['success']) {
                throw new Exception($response['error']);
            }

            $tags = array();
            if (isset($response['data']['values']) && is_array($response['data']['values'])) {
                foreach ($response['data']['values'] as $tag) {
                    $tags[] = array(
                        'name' => $tag['name'],
                        'date' => isset($tag['date']) ? $tag['date'] : null,
                        'hash' => isset($tag['target']['hash']) ? $tag['target']['hash'] : null
                    );
                }
            }

            $this->_helper->log(sprintf('Retrieved %d tags for repository %s', count($tags), $repositoryName), Zend_Log::INFO);

            return array(
                'success' => true,
                'data' => $tags
            );

        } catch (Exception $e) {
            $this->_helper->log('Failed to get repository tags for ' . $repositoryName . ': ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Download repository archive
     *
     * @param string $repositoryName
     * @param string $ref
     * @return array
     */
    public function downloadRepository($repositoryName, $ref = 'master')
    {
        try {
            $workspace = $this->_helper->getBitbucketWorkspace();

            // Get repository details to determine the correct slug and default branch
            $repositories = $this->getRepositories();
            $repositorySlug = $repositoryName; // Default fallback

            if ($repositories['success']) {
                foreach ($repositories['data'] as $repo) {
                    // Match by name or slug
                    if ($repo['name'] === $repositoryName || $repo['slug'] === $repositoryName) {
                        $repositorySlug = $repo['slug'];
                        break;
                    }
                }
            }

            // Handle branch name fallbacks - try both master and main for compatibility
            $branchesToTry = array($ref);
            if ($ref === 'master') {
                $branchesToTry = array('master', 'main');
            } elseif ($ref === 'main') {
                $branchesToTry = array('main', 'master');
            }

            $lastError = '';
            foreach ($branchesToTry as $branch) {
                $downloadUrl = sprintf('https://bitbucket.org/%s/%s/get/%s.zip', $workspace, $repositorySlug, $branch);

                $this->_helper->log(sprintf('Attempting to download %s from %s branch', $repositorySlug, $branch), Zend_Log::INFO);

                $result = $this->_performDownload($downloadUrl, $repositorySlug, $branch);

                if ($result['success']) {
                    return $result;
                }

                $lastError = $result['error'];
                $this->_helper->log(sprintf('Download failed for %s branch: %s', $branch, $lastError), Zend_Log::WARN);
            }

            // If all branches failed, return the last error
            throw new Exception('Failed to download from any branch. Last error: ' . $lastError);

        } catch (Exception $e) {
            $this->_helper->log('Repository download failed: ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Perform the actual download
     *
     * @param string $downloadUrl
     * @param string $repositorySlug
     * @param string $branch
     * @return array
     */
    protected function _performDownload($downloadUrl, $repositorySlug, $branch)
    {
        try {
            $tempDir = $this->_helper->getTempDir();
            if (!is_dir($tempDir)) {
                if (!mkdir($tempDir, 0755, true)) {
                    throw new Exception('Failed to create temp directory: ' . $tempDir);
                }
            }

            $filename = sprintf('%s-%s-%s.zip', $repositorySlug, $branch, time());
            $filepath = $tempDir . DS . $filename;

            $this->_helper->log(sprintf('Downloading repository %s (%s) from %s', $repositorySlug, $branch, $downloadUrl), Zend_Log::INFO);

            // Use direct cURL download for the ZIP file
            $ch = curl_init();
            curl_setopt_array($ch, array(
                CURLOPT_URL => $downloadUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_TIMEOUT => $this->_helper->getApiTimeout(),
                CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
                CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
                CURLOPT_USERPWD => $this->_helper->getBitbucketUsername() . ':' . $this->_helper->getBitbucketAppPassword(),
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ));

            $zipData = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                throw new Exception('cURL error during download: ' . $error);
            }

            if ($httpCode !== 200) {
                throw new Exception('HTTP error during download: ' . $httpCode);
            }

            if (empty($zipData)) {
                throw new Exception('Downloaded file is empty');
            }

            if (file_put_contents($filepath, $zipData) === false) {
                throw new Exception('Failed to save downloaded file to: ' . $filepath);
            }

            // Verify the downloaded file is a valid ZIP
            $fileSize = filesize($filepath);
            if ($fileSize === false || $fileSize < 100) {
                throw new Exception('Downloaded file is too small or invalid (size: ' . $fileSize . ' bytes)');
            }

            // Test if it's a valid ZIP file
            $zip = new ZipArchive();
            $zipResult = $zip->open($filepath, ZipArchive::CHECKCONS);
            if ($zipResult !== TRUE) {
                throw new Exception('Downloaded file is not a valid ZIP archive (error code: ' . $zipResult . ')');
            }
            $zip->close();

            $this->_helper->log(sprintf('Successfully downloaded repository %s (%s) to %s (%d bytes)', $repositorySlug, $branch, $filepath, $fileSize), Zend_Log::INFO);

            return array(
                'success' => true,
                'filepath' => $filepath,
                'filename' => $filename,
                'size' => $fileSize
            );

        } catch (Exception $e) {
            $this->_helper->log('Failed to download repository ' . $repositorySlug . ' (' . $branch . '): ' . $e->getMessage(), Zend_Log::ERR);
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Make API request to Bitbucket with caching support
     *
     * @param string $endpoint
     * @param string $method
     * @param array $params
     * @param bool $returnRaw
     * @param int $cacheTtl Cache TTL in seconds (default: 300 = 5 minutes)
     * @return array
     */
    protected function _makeApiRequest($endpoint, $method = 'GET', $params = array(), $returnRaw = false, $cacheTtl = 300)
    {
        // Only cache GET requests
        if ($method === 'GET' && $cacheTtl > 0) {
            $cacheKey = $this->_generateCacheKey($endpoint, $params);
            $cachedResponse = $this->_getCachedResponse($cacheKey);

            if ($cachedResponse !== false) {
                $this->_helper->log('Using cached response for: ' . $endpoint, Zend_Log::DEBUG);
                return $cachedResponse;
            }
        }

        try {
            $url = self::BITBUCKET_API_BASE_URL . $endpoint;
            
            if ($method === 'GET' && !empty($params)) {
                $url .= '?' . http_build_query($params);
            }
            
            $ch = curl_init();
            curl_setopt_array($ch, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->_helper->getApiTimeout(),
                CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
                CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
                CURLOPT_USERPWD => $this->_helper->getBitbucketUsername() . ':' . $this->_helper->getBitbucketAppPassword(),
                CURLOPT_HTTPHEADER => array(
                    'Accept: application/json',
                    'Content-Type: application/json'
                ),
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2
            ));
            
            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                if (!empty($params)) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
                }
            }
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);

            if ($error) {
                $this->_helper->log('cURL error for ' . $url . ': ' . $error, Zend_Log::ERR);
                throw new Exception('cURL error: ' . $error);
            }

            if ($response === false || empty($response)) {
                $this->_helper->log('Empty response from ' . $url . ' (HTTP ' . $httpCode . ')', Zend_Log::ERR);
                throw new Exception('Empty reply from server (HTTP ' . $httpCode . ')');
            }
            
            if ($returnRaw) {
                if ($httpCode >= 200 && $httpCode < 300) {
                    $result = array('success' => true, 'data' => $response);

                    // Cache successful GET requests
                    if ($method === 'GET' && $cacheTtl > 0) {
                        $this->_cacheResponse($cacheKey, $result, $cacheTtl);
                    }

                    return $result;
                } else {
                    throw new Exception('HTTP error: ' . $httpCode);
                }
            }

            if ($httpCode >= 200 && $httpCode < 300) {
                $data = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception('Invalid JSON response');
                }

                $result = array('success' => true, 'data' => $data);

                // Cache successful GET requests
                if ($method === 'GET' && $cacheTtl > 0) {
                    $this->_cacheResponse($cacheKey, $result, $cacheTtl);
                }

                return $result;
            } else {
                $errorData = json_decode($response, true);
                $errorMessage = isset($errorData['error']['message']) ? $errorData['error']['message'] : 'HTTP error: ' . $httpCode;
                throw new Exception($errorMessage);
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Format repository data
     *
     * @param array $repo
     * @return array
     */
    protected function _formatRepositoryData($repo)
    {
        // Extract repository slug from full_name (workspace/repository-slug)
        $slug = isset($repo['full_name']) ? explode('/', $repo['full_name'])[1] : $repo['name'];

        return array(
            'name' => $repo['name'], // Display name
            'slug' => $slug, // Repository slug for downloads
            'full_name' => $repo['full_name'],
            'description' => isset($repo['description']) ? $repo['description'] : '',
            'language' => isset($repo['language']) ? $repo['language'] : '',
            'size' => isset($repo['size']) ? $repo['size'] : 0,
            'updated_on' => isset($repo['updated_on']) ? $repo['updated_on'] : null,
            'created_on' => isset($repo['created_on']) ? $repo['created_on'] : null,
            'is_private' => isset($repo['is_private']) ? $repo['is_private'] : true,
            'clone_links' => isset($repo['links']['clone']) ? $repo['links']['clone'] : array(),
            'html_url' => isset($repo['links']['html']['href']) ? $repo['links']['html']['href'] : ''
        );
    }

    /**
     * Generate cache key for API request
     *
     * @param string $endpoint
     * @param array $params
     * @return string
     */
    protected function _generateCacheKey($endpoint, $params = array())
    {
        $keyData = array(
            'endpoint' => $endpoint,
            'params' => $params,
            'workspace' => $this->_helper->getBitbucketWorkspace(),
            'project' => $this->_helper->getBitbucketProject(),
            'username' => $this->_helper->getBitbucketUsername()
        );

        return 'pfg_core_bitbucket_' . md5(serialize($keyData));
    }

    /**
     * Get cached API response
     *
     * @param string $cacheKey
     * @return array|false
     */
    protected function _getCachedResponse($cacheKey)
    {
        try {
            $cached = Mage::app()->getCache()->load($cacheKey);
            if ($cached) {
                $data = unserialize($cached);
                if (is_array($data) && isset($data['success'])) {
                    return $data;
                }
            }
        } catch (Exception $e) {
            $this->_helper->log('Cache read error for key ' . $cacheKey . ': ' . $e->getMessage(), Zend_Log::WARN);
        }

        return false;
    }

    /**
     * Cache API response
     *
     * @param string $cacheKey
     * @param array $response
     * @param int $ttl
     */
    protected function _cacheResponse($cacheKey, $response, $ttl)
    {
        try {
            $tags = array('PFG_CORE_BITBUCKET_API');
            Mage::app()->getCache()->save(
                serialize($response),
                $cacheKey,
                $tags,
                $ttl
            );

            $this->_helper->log('Cached API response for key: ' . $cacheKey . ' (TTL: ' . $ttl . 's)', Zend_Log::DEBUG);
        } catch (Exception $e) {
            $this->_helper->log('Cache write error for key ' . $cacheKey . ': ' . $e->getMessage(), Zend_Log::WARN);
        }
    }

    /**
     * Clear all Bitbucket API cache
     */
    public function clearApiCache()
    {
        try {
            Mage::app()->cleanCache(array('PFG_CORE_BITBUCKET_API'));
            $this->_helper->log('Cleared Bitbucket API cache', Zend_Log::INFO);
        } catch (Exception $e) {
            $this->_helper->log('Error clearing Bitbucket API cache: ' . $e->getMessage(), Zend_Log::WARN);
        }
    }
}
