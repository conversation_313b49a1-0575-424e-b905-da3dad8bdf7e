<?php
/**
 * PFG Core Data Helper
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Helper_Data extends Mage_Core_Helper_Abstract
{
    const XML_PATH_ENABLED = 'pfg/core/enabled';
    const XML_PATH_BITBUCKET_USERNAME = 'pfg/core/bitbucket_username';
    const XML_PATH_BITBUCKET_APP_PASSWORD = 'pfg/core/bitbucket_app_password';
    const XML_PATH_BITBUCKET_WORKSPACE = 'pfg/core/bitbucket_workspace';
    const XML_PATH_BITBUCKET_PROJECT = 'pfg/core/bitbucket_project';
    const XML_PATH_API_TIMEOUT = 'pfg/core/api_timeout';
    const XML_PATH_BACKUP_RETENTION_DAYS = 'pfg/core/backup_retention_days';
    const XML_PATH_LOG_LEVEL = 'pfg/core/log_level';
    
    const LOG_FILE = 'pfg_core.log';
    
    /**
     * Check if PFG Core module is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED);
    }
    
    /**
     * Get Bitbucket username
     *
     * @return string
     */
    public function getBitbucketUsername()
    {
        return Mage::getStoreConfig(self::XML_PATH_BITBUCKET_USERNAME);
    }
    
    /**
     * Get Bitbucket app password (decrypted)
     *
     * @return string
     */
    public function getBitbucketAppPassword()
    {
        return Mage::helper('core')->decrypt(Mage::getStoreConfig(self::XML_PATH_BITBUCKET_APP_PASSWORD));
    }
    
    /**
     * Get Bitbucket workspace
     *
     * @return string
     */
    public function getBitbucketWorkspace()
    {
        return Mage::getStoreConfig(self::XML_PATH_BITBUCKET_WORKSPACE) ?: 'pfg';
    }
    
    /**
     * Get Bitbucket project key
     *
     * @return string
     */
    public function getBitbucketProject()
    {
        return Mage::getStoreConfig(self::XML_PATH_BITBUCKET_PROJECT) ?: 'LABS';
    }
    
    /**
     * Get API timeout in seconds
     *
     * @return int
     */
    public function getApiTimeout()
    {
        return (int)Mage::getStoreConfig(self::XML_PATH_API_TIMEOUT) ?: 30;
    }
    
    /**
     * Get backup retention days
     *
     * @return int
     */
    public function getBackupRetentionDays()
    {
        return (int)Mage::getStoreConfig(self::XML_PATH_BACKUP_RETENTION_DAYS) ?: 30;
    }
    
    /**
     * Get log level
     *
     * @return int
     */
    public function getLogLevel()
    {
        return (int)Mage::getStoreConfig(self::XML_PATH_LOG_LEVEL) ?: 1;
    }
    
    /**
     * Log message to PFG Core log file
     *
     * @param string $message
     * @param int $level
     * @param string $file
     * @param bool $forceLog
     */
    public function log($message, $level = Zend_Log::INFO, $file = null, $forceLog = false)
    {
        // Use the new logger model for enhanced logging
        $logger = Mage::getModel('pfg_core/logger');

        // Convert Zend_Log levels to our logger levels
        $loggerLevel = $this->_convertLogLevel($level);

        $logger->log($message, $loggerLevel, PFG_Core_Model_Logger::LOG_TYPE_SYSTEM, array(), $file);
    }

    /**
     * Convert Zend_Log level to PFG_Core_Model_Logger level
     *
     * @param int $zendLevel
     * @return int
     */
    protected function _convertLogLevel($zendLevel)
    {
        $mapping = array(
            Zend_Log::EMERG => PFG_Core_Model_Logger::LOG_LEVEL_EMERGENCY,
            Zend_Log::ALERT => PFG_Core_Model_Logger::LOG_LEVEL_ALERT,
            Zend_Log::CRIT => PFG_Core_Model_Logger::LOG_LEVEL_CRITICAL,
            Zend_Log::ERR => PFG_Core_Model_Logger::LOG_LEVEL_ERROR,
            Zend_Log::WARN => PFG_Core_Model_Logger::LOG_LEVEL_WARNING,
            Zend_Log::NOTICE => PFG_Core_Model_Logger::LOG_LEVEL_NOTICE,
            Zend_Log::INFO => PFG_Core_Model_Logger::LOG_LEVEL_INFO,
            Zend_Log::DEBUG => PFG_Core_Model_Logger::LOG_LEVEL_DEBUG
        );

        return isset($mapping[$zendLevel]) ? $mapping[$zendLevel] : PFG_Core_Model_Logger::LOG_LEVEL_INFO;
    }


    
    /**
     * Check if credentials are configured
     *
     * @return bool
     */
    public function hasCredentials()
    {
        return !empty($this->getBitbucketUsername()) && !empty($this->getBitbucketAppPassword());
    }
    
    /**
     * Get Magento base directory
     *
     * @return string
     */
    public function getMagentoBaseDir()
    {
        return Mage::getBaseDir();
    }
    
    /**
     * Get backup directory path
     *
     * @return string
     */
    public function getBackupDir()
    {
        $backupDir = Mage::getBaseDir('var') . DS . 'pfg_core' . DS . 'backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        return $backupDir;
    }
    
    /**
     * Get temporary directory path
     *
     * @return string
     */
    public function getTempDir()
    {
        $tempDir = Mage::getBaseDir('var') . DS . 'pfg_core' . DS . 'temp';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        return $tempDir;
    }
    
    /**
     * Format file size
     *
     * @param int $bytes
     * @return string
     */
    public function formatFileSize($bytes)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    /**
     * Validate module name format
     *
     * @param string $moduleName
     * @return bool
     */
    public function isValidModuleName($moduleName)
    {
        return preg_match('/^[A-Z][a-zA-Z0-9]*_[A-Z][a-zA-Z0-9]*$/', $moduleName);
    }
}
