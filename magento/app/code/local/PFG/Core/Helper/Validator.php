<?php
/**
 * PFG Core Validation Helper
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Helper_Validator extends Mage_Core_Helper_Abstract
{
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->_helper = Mage::helper('pfg_core');
    }
    
    /**
     * Validate system requirements for module installation
     *
     * @return array
     */
    public function validateSystemRequirements()
    {
        $checks = array();
        $canInstall = true;
        
        // Check PHP version
        $phpVersion = PHP_VERSION;
        $minPhpVersion = '5.3.0';
        
        if (version_compare($phpVersion, $minPhpVersion, '<')) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->__('PHP version %s or higher is required. Current version: %s', $minPhpVersion, $phpVersion)
            );
            $canInstall = false;
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->__('PHP version: %s ✓', $phpVersion)
            );
        }
        
        // Check required PHP extensions
        $requiredExtensions = array('curl', 'json', 'zip', 'phar');
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->__('Required PHP extension missing: %s', $extension)
                );
                $canInstall = false;
            } else {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->__('PHP extension %s: ✓', $extension)
                );
            }
        }
        
        // Check file permissions
        $writableDirs = array(
            Mage::getBaseDir('app') . DS . 'code' . DS . 'local',
            Mage::getBaseDir('app') . DS . 'etc' . DS . 'modules',
            Mage::getBaseDir('var'),
            $this->_helper->getBackupDir(),
            $this->_helper->getTempDir()
        );
        
        foreach ($writableDirs as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    $checks[] = array(
                        'type' => 'error',
                        'message' => $this->__('Cannot create directory: %s', $dir)
                    );
                    $canInstall = false;
                    continue;
                }
            }
            
            if (!is_writable($dir)) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->__('Directory not writable: %s', $dir)
                );
                $canInstall = false;
            } else {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->__('Directory writable: %s ✓', $dir)
                );
            }
        }
        
        // Check disk space
        $freeSpace = disk_free_space(Mage::getBaseDir());
        $requiredSpace = 100 * 1024 * 1024; // 100MB
        
        if ($freeSpace < $requiredSpace) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->__('Insufficient disk space. Required: %s, Available: %s', 
                    $this->_helper->formatFileSize($requiredSpace),
                    $this->_helper->formatFileSize($freeSpace)
                )
            );
            $canInstall = false;
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->__('Disk space available: %s ✓', $this->_helper->formatFileSize($freeSpace))
            );
        }
        
        // Check Magento version compatibility
        $magentoVersion = Mage::getVersion();
        $minMagentoVersion = '1.9.0.0';
        
        if (version_compare($magentoVersion, $minMagentoVersion, '<')) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->__('Magento version %s or higher is recommended. Current version: %s', $minMagentoVersion, $magentoVersion)
            );
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->__('Magento version: %s ✓', $magentoVersion)
            );
        }
        
        return array(
            'can_install' => $canInstall,
            'checks' => $checks
        );
    }
    
    /**
     * Validate module compatibility
     *
     * @param string $moduleName
     * @return array
     */
    public function validateModuleCompatibility($moduleName)
    {
        $checks = array();
        $canInstall = true;
        
        // Check for existing module
        $moduleConfig = Mage::getConfig()->getModuleConfig($moduleName);
        if ($moduleConfig && $moduleConfig->is('active')) {
            $checks[] = array(
                'type' => 'warning',
                'message' => $this->__('Module %s is already installed and active', $moduleName)
            );
        }
        
        // Check for file conflicts
        $conflicts = $this->_checkFileConflicts($moduleName);
        if (!empty($conflicts)) {
            foreach ($conflicts as $conflict) {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->__('File conflict: %s', $conflict)
                );
            }
        }
        
        // Check for rewrite conflicts
        $rewriteConflicts = $this->_checkRewriteConflicts($moduleName);
        if (!empty($rewriteConflicts)) {
            foreach ($rewriteConflicts as $conflict) {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->__('Potential rewrite conflict: %s', $conflict)
                );
            }
        }
        
        return array(
            'can_install' => $canInstall,
            'checks' => $checks
        );
    }
    
    /**
     * Check for file conflicts
     *
     * @param string $moduleName
     * @return array
     */
    protected function _checkFileConflicts($moduleName)
    {
        $conflicts = array();
        $baseDir = Mage::getBaseDir();
        
        // Check module directory
        $moduleDir = $baseDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $moduleName);
        if (is_dir($moduleDir)) {
            $conflicts[] = $moduleDir;
        }
        
        // Check module declaration file
        $moduleFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
        if (file_exists($moduleFile)) {
            $conflicts[] = $moduleFile;
        }
        
        return $conflicts;
    }
    
    /**
     * Check for rewrite conflicts
     *
     * @param string $moduleName
     * @return array
     */
    protected function _checkRewriteConflicts($moduleName)
    {
        $conflicts = array();
        
        // This is a simplified check - in a real implementation, you would
        // analyze the module's config.xml to detect potential rewrite conflicts
        // with existing modules
        
        $config = Mage::getConfig();
        $modules = $config->getNode('modules')->children();
        
        foreach ($modules as $existingModule => $moduleConfig) {
            if ($existingModule === $moduleName) {
                continue;
            }
            
            if ($moduleConfig->active == 'true') {
                // Check if this module might conflict with the new one
                // This is a basic check - you could make it more sophisticated
                if (strpos($existingModule, 'PFG_') === 0 && $existingModule !== $moduleName) {
                    $conflicts[] = $this->__('Potential conflict with existing PFG module: %s', $existingModule);
                }
            }
        }
        
        return $conflicts;
    }
    
    /**
     * Validate backup integrity
     *
     * @param string $backupPath
     * @return array
     */
    public function validateBackupIntegrity($backupPath)
    {
        $checks = array();
        $isValid = true;
        
        // Check if backup file exists
        if (!file_exists($backupPath)) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->__('Backup file not found: %s', $backupPath)
            );
            $isValid = false;
        } else {
            $checks[] = array(
                'type' => 'success',
                'message' => $this->__('Backup file exists ✓')
            );
            
            // Check if file is readable
            if (!is_readable($backupPath)) {
                $checks[] = array(
                    'type' => 'error',
                    'message' => $this->__('Backup file is not readable')
                );
                $isValid = false;
            } else {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->__('Backup file is readable ✓')
                );
                
                // Check file size
                $fileSize = filesize($backupPath);
                if ($fileSize === 0) {
                    $checks[] = array(
                        'type' => 'error',
                        'message' => $this->__('Backup file is empty')
                    );
                    $isValid = false;
                } else {
                    $checks[] = array(
                        'type' => 'success',
                        'message' => $this->__('Backup file size: %s ✓', $this->_helper->formatFileSize($fileSize))
                    );
                }
                
                // Try to open archive
                try {
                    $phar = new PharData($backupPath);
                    $checks[] = array(
                        'type' => 'success',
                        'message' => $this->__('Backup archive is valid ✓')
                    );
                } catch (Exception $e) {
                    $checks[] = array(
                        'type' => 'error',
                        'message' => $this->__('Backup archive is corrupted: %s', $e->getMessage())
                    );
                    $isValid = false;
                }
            }
        }
        
        return array(
            'is_valid' => $isValid,
            'checks' => $checks
        );
    }
}
