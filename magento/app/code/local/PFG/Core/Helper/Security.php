<?php
/**
 * PFG Core Security Helper
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Helper_Security extends Mage_Core_Helper_Abstract
{
    /**
     * @var PFG_Core_Helper_Data
     */
    protected $_helper;
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->_helper = Mage::helper('pfg_core');
    }
    
    /**
     * Validate file content for security threats
     *
     * @param string $filePath
     * @return array
     */
    public function scanFileForThreats($filePath)
    {
        $threats = array();
        
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return array(
                'safe' => false,
                'threats' => array('File not accessible for security scan')
            );
        }
        
        $content = file_get_contents($filePath);
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        // PHP file specific checks
        if ($extension === 'php') {
            $threats = array_merge($threats, $this->_scanPhpContent($content));
        }
        
        // General file checks
        $threats = array_merge($threats, $this->_scanGeneralContent($content));
        
        return array(
            'safe' => empty($threats),
            'threats' => $threats
        );
    }
    
    /**
     * Scan PHP content for threats
     *
     * @param string $content
     * @return array
     */
    protected function _scanPhpContent($content)
    {
        $threats = array();
        
        // Dangerous PHP functions
        $dangerousFunctions = array(
            'eval', 'exec', 'system', 'shell_exec', 'passthru', 'proc_open',
            'popen', 'file_get_contents', 'file_put_contents', 'fopen', 'fwrite',
            'curl_exec', 'mail', 'base64_decode', 'gzinflate', 'str_rot13'
        );
        
        foreach ($dangerousFunctions as $function) {
            if (preg_match('/\b' . preg_quote($function) . '\s*\(/i', $content)) {
                $threats[] = sprintf('Potentially dangerous function found: %s()', $function);
            }
        }
        
        // Check for obfuscated code
        if (preg_match('/\$[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*["\'][^"\']*["\'];\s*eval\s*\(/i', $content)) {
            $threats[] = 'Obfuscated code detected (eval with string variable)';
        }
        
        // Check for base64 encoded strings (potential obfuscation)
        if (preg_match('/base64_decode\s*\(\s*["\'][A-Za-z0-9+\/=]{50,}["\']/', $content)) {
            $threats[] = 'Base64 encoded string detected (potential obfuscation)';
        }
        
        // Check for suspicious variable names
        $suspiciousVars = array('$_POST', '$_GET', '$_REQUEST', '$_COOKIE', '$_SERVER');
        foreach ($suspiciousVars as $var) {
            if (strpos($content, $var) !== false && preg_match('/eval\s*\(\s*' . preg_quote($var) . '/i', $content)) {
                $threats[] = sprintf('Direct execution of user input detected: %s', $var);
            }
        }
        
        // Check for file operations on user input
        $fileOps = array('file_get_contents', 'file_put_contents', 'fopen', 'include', 'require');
        foreach ($fileOps as $op) {
            if (preg_match('/' . preg_quote($op) . '\s*\(\s*\$_(GET|POST|REQUEST|COOKIE)/i', $content)) {
                $threats[] = sprintf('File operation on user input detected: %s', $op);
            }
        }
        
        return $threats;
    }
    
    /**
     * Scan general content for threats
     *
     * @param string $content
     * @return array
     */
    protected function _scanGeneralContent($content)
    {
        $threats = array();
        
        // Check for suspicious URLs
        $suspiciousDomains = array(
            'bit.ly', 'tinyurl.com', 'goo.gl', 't.co',
            'malware.com', 'phishing.com', 'virus.com'
        );
        
        foreach ($suspiciousDomains as $domain) {
            if (stripos($content, $domain) !== false) {
                $threats[] = sprintf('Suspicious domain found: %s', $domain);
            }
        }
        
        // Check for suspicious keywords
        $suspiciousKeywords = array(
            'backdoor', 'rootkit', 'keylogger', 'trojan', 'malware',
            'exploit', 'vulnerability', 'injection', 'xss', 'csrf'
        );
        
        foreach ($suspiciousKeywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                $threats[] = sprintf('Suspicious keyword found: %s', $keyword);
            }
        }
        
        // Check for encoded content that might hide malicious code
        if (preg_match('/[A-Za-z0-9+\/=]{200,}/', $content)) {
            $threats[] = 'Large encoded string detected (potential obfuscation)';
        }
        
        return $threats;
    }
    
    /**
     * Validate admin session security
     *
     * @return array
     */
    public function validateAdminSession()
    {
        $checks = array();
        $isSecure = true;
        
        $session = Mage::getSingleton('admin/session');
        
        if (!$session->isLoggedIn()) {
            $checks[] = array(
                'type' => 'error',
                'message' => $this->__('No active admin session')
            );
            $isSecure = false;
        } else {
            // Check session timeout
            $sessionLifetime = (int)Mage::getStoreConfig('admin/security/session_lifetime');
            $lastActivity = $session->getUpdatedAt();
            
            if ($lastActivity && (time() - strtotime($lastActivity)) > $sessionLifetime) {
                $checks[] = array(
                    'type' => 'warning',
                    'message' => $this->__('Admin session may have expired')
                );
            }
            
            // Check IP consistency (if enabled)
            $user = $session->getUser();
            if ($user) {
                $checks[] = array(
                    'type' => 'success',
                    'message' => $this->__('Admin user authenticated: %s', $user->getUsername())
                );
                
                // Check user permissions
                if (!$user->getRole() || !$user->getRole()->getRoleId()) {
                    $checks[] = array(
                        'type' => 'error',
                        'message' => $this->__('Admin user has no assigned role')
                    );
                    $isSecure = false;
                }
            }
        }
        
        return array(
            'is_secure' => $isSecure,
            'checks' => $checks
        );
    }
    
    /**
     * Generate secure token for operations
     *
     * @param string $action
     * @param int $expiry
     * @return string
     */
    public function generateSecureToken($action, $expiry = 3600)
    {
        $data = array(
            'action' => $action,
            'timestamp' => time(),
            'expiry' => time() + $expiry,
            'user_id' => $this->_getCurrentUserId(),
            'session_id' => session_id(),
            'random' => mt_rand()
        );
        
        $token = base64_encode(json_encode($data));
        $signature = hash_hmac('sha256', $token, $this->_getSecretKey());
        
        return $token . '.' . $signature;
    }
    
    /**
     * Validate secure token
     *
     * @param string $token
     * @param string $action
     * @return bool
     */
    public function validateSecureToken($token, $action)
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 2) {
                return false;
            }
            
            list($tokenData, $signature) = $parts;
            
            // Verify signature
            $expectedSignature = hash_hmac('sha256', $tokenData, $this->_getSecretKey());
            if (!hash_equals($expectedSignature, $signature)) {
                return false;
            }
            
            // Decode and validate data
            $data = json_decode(base64_decode($tokenData), true);
            if (!$data) {
                return false;
            }
            
            // Check expiry
            if (time() > $data['expiry']) {
                return false;
            }
            
            // Check action
            if ($data['action'] !== $action) {
                return false;
            }
            
            // Check user
            if ($data['user_id'] !== $this->_getCurrentUserId()) {
                return false;
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->_helper->log('Token validation error: ' . $e->getMessage(), Zend_Log::ERR);
            return false;
        }
    }
    
    /**
     * Get current admin user ID
     *
     * @return int|null
     */
    protected function _getCurrentUserId()
    {
        $session = Mage::getSingleton('admin/session');
        if ($session->isLoggedIn()) {
            return $session->getUser()->getId();
        }
        return null;
    }
    
    /**
     * Get secret key for token signing
     *
     * @return string
     */
    protected function _getSecretKey()
    {
        // Use Magento's encryption key as base
        $key = (string)Mage::getConfig()->getNode('global/crypt/key');
        return hash('sha256', $key . 'pfg_core_security');
    }
    
    /**
     * Log security event
     *
     * @param string $event
     * @param string $details
     * @param int $severity
     */
    public function logSecurityEvent($event, $details = '', $severity = Zend_Log::WARN)
    {
        $session = Mage::getSingleton('admin/session');
        $user = $session->isLoggedIn() ? $session->getUser()->getUsername() : 'anonymous';
        $ip = Mage::helper('core/http')->getRemoteAddr();
        
        $message = sprintf(
            'SECURITY EVENT: %s | User: %s | IP: %s | Details: %s',
            $event,
            $user,
            $ip,
            $details
        );
        
        $this->_helper->log($message, $severity, 'pfg_core_security.log', true);
    }
}
