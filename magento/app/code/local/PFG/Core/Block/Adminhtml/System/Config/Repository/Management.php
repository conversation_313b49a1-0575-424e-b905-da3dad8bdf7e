<?php
/**
 * PFG Core Repository Management System Config Block
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Block_Adminhtml_System_Config_Repository_Management extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_core/system/config/repository/management.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the repository management contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $helper = Mage::helper('pfg_core');
        
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
            'is_configured' => $helper->isEnabled() && $helper->hasCredentials(),
            'ajax_urls' => $this->getAjaxUrls(),
            'form_key' => Mage::getSingleton('core/session')->getFormKey()
        ));
        
        return $this->_toHtml();
    }

    /**
     * Get AJAX URLs
     *
     * @return array
     */
    public function getAjaxUrls()
    {
        return array(
            'get_repositories' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/getRepositories'),
            'install_module' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/installModule'),
            'update_module' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/updateModule'),
            'rollback' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/rollback'),
            'post_install_actions' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/postInstallActions'),
            'get_installation_history' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/getInstallationHistory')
        );
    }
}
