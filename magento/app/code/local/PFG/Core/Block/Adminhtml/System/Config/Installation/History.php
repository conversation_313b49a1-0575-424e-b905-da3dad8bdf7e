<?php
/**
 * PFG Core Installation History System Config Block
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Block_Adminhtml_System_Config_Installation_History extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_core/system/config/installation/history.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the installation history contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        // Get recent installations
        $installations = Mage::getModel('pfg_core/installation')->getCollection()
            ->setOrder('created_at', 'DESC')
            ->setPageSize(10);
        
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
            'installations' => $installations,
            'ajax_urls' => array(
                'rollback' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/rollback')
            ),
            'form_key' => Mage::getSingleton('core/session')->getFormKey()
        ));
        
        return $this->_toHtml();
    }
}
