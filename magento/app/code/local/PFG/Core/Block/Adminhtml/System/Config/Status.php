<?php
/**
 * PFG Core Connection Status Block
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Block_Adminhtml_System_Config_Status extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_core/system/config/status.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the status display contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        $helper = Mage::helper('pfg_core');
        
        $status = array(
            'enabled' => $helper->isEnabled(),
            'has_credentials' => $helper->hasCredentials(),
            'workspace' => $helper->getBitbucketWorkspace(),
            'project' => $helper->getBitbucketProject()
        );
        
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
            'status' => $status
        ));
        
        return $this->_toHtml();
    }
}
