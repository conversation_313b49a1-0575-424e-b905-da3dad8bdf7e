<?php
/**
 * PFG Core Backup Management System Config Block
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Block_Adminhtml_System_Config_Backup_Management extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_core/system/config/backup/management.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the backup management contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        // Get recent backups
        $backups = Mage::getModel('pfg_core/backup')->getCollection()
            ->setOrder('created_at', 'DESC')
            ->setPageSize(10);
        
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
            'backups' => $backups,
            'ajax_urls' => array(
                'download_backup' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/downloadBackup'),
                'restore_backup' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/restoreBackup'),
                'cleanup_backups' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/cleanupBackups')
            ),
            'form_key' => Mage::getSingleton('core/session')->getFormKey()
        ));
        
        return $this->_toHtml();
    }
}
