<?php
/**
 * PFG Core System Logs System Config Block
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Block_Adminhtml_System_Config_System_Logs extends Mage_Adminhtml_Block_System_Config_Form_Field
{
    /**
     * Set template to itself
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (!$this->getTemplate()) {
            $this->setTemplate('pfg_core/system/config/system/logs.phtml');
        }
        return $this;
    }
    
    /**
     * Unset some non-related element parameters
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }
    
    /**
     * Get the system logs contents
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    protected function _getElementHtml(Varien_Data_Form_Element_Abstract $element)
    {
        // Get recent log entries
        try {
            $connection = Mage::getSingleton('core/resource')->getConnection('core_read');
            $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');
            
            $logs = array();
            if ($connection->isTableExists($table)) {
                $select = $connection->select()
                    ->from($table)
                    ->order('created_at DESC')
                    ->limit(20);
                
                $logs = $connection->fetchAll($select);
            }
        } catch (Exception $e) {
            $logs = array();
        }
        
        $this->addData(array(
            'html_id' => $element->getHtmlId(),
            'logs' => $logs,
            'ajax_urls' => array(
                'clear_logs' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/clearLogs'),
                'download_logs' => Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/downloadLogs')
            ),
            'form_key' => Mage::getSingleton('core/session')->getFormKey()
        ));
        
        return $this->_toHtml();
    }
}
