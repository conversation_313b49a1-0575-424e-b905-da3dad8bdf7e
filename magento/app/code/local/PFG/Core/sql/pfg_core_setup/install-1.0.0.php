<?php
/**
 * PFG Core Module Installation Script
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

/* @var $installer Mage_Core_Model_Resource_Setup */
$installer = $this;
$installer->startSetup();

/**
 * Create table 'pfg_core_backup'
 * Stores backup information for rollback functionality
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_core/backup'))
    ->addColumn('backup_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Backup ID')
    ->addColumn('module_name', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'Module Name')
    ->addColumn('backup_path', Varien_Db_Ddl_Table::TYPE_TEXT, '64k', array(
        'nullable'  => false,
    ), 'Backup File Path')
    ->addColumn('backup_type', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => false,
        'default'   => 'installation',
    ), 'Backup Type')
    ->addColumn('backup_size', Varien_Db_Ddl_Table::TYPE_BIGINT, null, array(
        'unsigned'  => true,
        'nullable'  => true,
    ), 'Backup Size in Bytes')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addColumn('description', Varien_Db_Ddl_Table::TYPE_TEXT, '2M', array(
        'nullable'  => true,
    ), 'Backup Description')
    ->addColumn('metadata', Varien_Db_Ddl_Table::TYPE_TEXT, '2M', array(
        'nullable'  => true,
    ), 'Backup Metadata (JSON)')
    ->addIndex($installer->getIdxName('pfg_core/backup', array('module_name')),
        array('module_name'))
    ->addIndex($installer->getIdxName('pfg_core/backup', array('backup_type')),
        array('backup_type'))
    ->addIndex($installer->getIdxName('pfg_core/backup', array('created_at')),
        array('created_at'))
    ->setComment('PFG Core Backup Table');

$installer->getConnection()->createTable($table);

/**
 * Create table 'pfg_core_installation'
 * Tracks module installation history and status
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_core/installation'))
    ->addColumn('installation_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Installation ID')
    ->addColumn('module_name', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'Module Name')
    ->addColumn('repository_name', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'Repository Name')
    ->addColumn('version_installed', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => true,
    ), 'Installed Version')
    ->addColumn('version_available', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => true,
    ), 'Available Version')
    ->addColumn('installation_status', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => false,
        'default'   => 'pending',
    ), 'Installation Status')
    ->addColumn('installation_type', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => false,
        'default'   => 'install',
    ), 'Installation Type')
    ->addColumn('backup_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'unsigned'  => true,
        'nullable'  => true,
    ), 'Associated Backup ID')
    ->addColumn('installed_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => true,
    ), 'Installation Date')
    ->addColumn('installed_by', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => true,
    ), 'Installed By User')
    ->addColumn('installation_log', Varien_Db_Ddl_Table::TYPE_TEXT, '2M', array(
        'nullable'  => true,
    ), 'Installation Log')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addColumn('updated_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT_UPDATE,
    ), 'Updated At')
    ->addIndex($installer->getIdxName('pfg_core/installation', array('module_name')),
        array('module_name'))
    ->addIndex($installer->getIdxName('pfg_core/installation', array('repository_name')),
        array('repository_name'))
    ->addIndex($installer->getIdxName('pfg_core/installation', array('installation_status')),
        array('installation_status'))
    ->addIndex($installer->getIdxName('pfg_core/installation', array('installation_type')),
        array('installation_type'))
    ->addForeignKey($installer->getFkName('pfg_core/installation', 'backup_id', 'pfg_core/backup', 'backup_id'),
        'backup_id', $installer->getTable('pfg_core/backup'), 'backup_id',
        Varien_Db_Ddl_Table::ACTION_SET_NULL, Varien_Db_Ddl_Table::ACTION_CASCADE)
    ->setComment('PFG Core Installation History Table');

$installer->getConnection()->createTable($table);

/**
 * Create table 'pfg_core_audit_log'
 * Stores audit trail for important system events
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_core_audit_log'))
    ->addColumn('log_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Log ID')
    ->addColumn('message', Varien_Db_Ddl_Table::TYPE_TEXT, '2M', array(
        'nullable'  => false,
    ), 'Log Message')
    ->addColumn('level', Varien_Db_Ddl_Table::TYPE_SMALLINT, null, array(
        'unsigned'  => true,
        'nullable'  => false,
        'default'   => 6,
    ), 'Log Level')
    ->addColumn('type', Varien_Db_Ddl_Table::TYPE_VARCHAR, 50, array(
        'nullable'  => false,
        'default'   => 'system',
    ), 'Log Type')
    ->addColumn('context', Varien_Db_Ddl_Table::TYPE_TEXT, '2M', array(
        'nullable'  => true,
    ), 'Context Data (JSON)')
    ->addColumn('user_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'unsigned'  => true,
        'nullable'  => true,
    ), 'Admin User ID')
    ->addColumn('ip_address', Varien_Db_Ddl_Table::TYPE_VARCHAR, 45, array(
        'nullable'  => true,
    ), 'IP Address')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addIndex($installer->getIdxName('pfg_core_audit_log', array('level')),
        array('level'))
    ->addIndex($installer->getIdxName('pfg_core_audit_log', array('type')),
        array('type'))
    ->addIndex($installer->getIdxName('pfg_core_audit_log', array('user_id')),
        array('user_id'))
    ->addIndex($installer->getIdxName('pfg_core_audit_log', array('created_at')),
        array('created_at'))
    ->setComment('PFG Core Audit Log Table');

$installer->getConnection()->createTable($table);

$installer->endSetup();
