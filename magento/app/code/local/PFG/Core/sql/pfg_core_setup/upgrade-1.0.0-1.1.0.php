<?php
/**
 * PFG Core Module Upgrade Script - Phase 2 Performance Optimizations
 * 
 * Adds performance indexes, optimizes existing tables, and creates
 * additional tables for enhanced functionality.
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

/* @var $installer Mage_Core_Model_Resource_Setup */
$installer = $this;
$installer->startSetup();

$connection = $installer->getConnection();

/**
 * Add performance indexes to existing tables
 */

// Add composite index for installation queries (module_name + installation_status + installed_at)
$installer->getConnection()->addIndex(
    $installer->getTable('pfg_core/installation'),
    $installer->getIdxName('pfg_core/installation', array('module_name', 'installation_status', 'installed_at')),
    array('module_name', 'installation_status', 'installed_at'),
    Varien_Db_Adapter_Interface::INDEX_TYPE_INDEX
);

// Add index for backup queries by module_name and created_at
$installer->getConnection()->addIndex(
    $installer->getTable('pfg_core/backup'),
    $installer->getIdxName('pfg_core/backup', array('module_name', 'created_at')),
    array('module_name', 'created_at'),
    Varien_Db_Adapter_Interface::INDEX_TYPE_INDEX
);

// Add index for audit log queries by type and created_at
$installer->getConnection()->addIndex(
    $installer->getTable('pfg_core_audit_log'),
    $installer->getIdxName('pfg_core_audit_log', array('type', 'created_at')),
    array('type', 'created_at'),
    Varien_Db_Adapter_Interface::INDEX_TYPE_INDEX
);

/**
 * Create performance metrics table
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_core_performance_metrics'))
    ->addColumn('metric_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Metric ID')
    ->addColumn('operation_name', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'Operation Name')
    ->addColumn('execution_time', Varien_Db_Ddl_Table::TYPE_DECIMAL, '10,4', array(
        'nullable'  => false,
    ), 'Execution Time (seconds)')
    ->addColumn('memory_usage', Varien_Db_Ddl_Table::TYPE_BIGINT, null, array(
        'unsigned'  => true,
        'nullable'  => true,
    ), 'Memory Usage (bytes)')
    ->addColumn('context_data', Varien_Db_Ddl_Table::TYPE_TEXT, '2M', array(
        'nullable'  => true,
    ), 'Context Data (JSON)')
    ->addColumn('user_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'unsigned'  => true,
        'nullable'  => true,
    ), 'Admin User ID')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addIndex($installer->getIdxName('pfg_core_performance_metrics', array('operation_name')),
        array('operation_name'))
    ->addIndex($installer->getIdxName('pfg_core_performance_metrics', array('execution_time')),
        array('execution_time'))
    ->addIndex($installer->getIdxName('pfg_core_performance_metrics', array('created_at')),
        array('created_at'))
    ->addIndex($installer->getIdxName('pfg_core_performance_metrics', array('operation_name', 'created_at')),
        array('operation_name', 'created_at'))
    ->setComment('PFG Core Performance Metrics Table');

$installer->getConnection()->createTable($table);

/**
 * Create cache management table for service layer
 */
$table = $installer->getConnection()
    ->newTable($installer->getTable('pfg_core_cache'))
    ->addColumn('cache_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'identity'  => true,
        'unsigned'  => true,
        'nullable'  => false,
        'primary'   => true,
    ), 'Cache ID')
    ->addColumn('cache_key', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'Cache Key')
    ->addColumn('cache_data', Varien_Db_Ddl_Table::TYPE_TEXT, '16M', array(
        'nullable'  => false,
    ), 'Cached Data (Serialized)')
    ->addColumn('service_class', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(
        'nullable'  => false,
    ), 'Service Class Name')
    ->addColumn('ttl', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
        'unsigned'  => true,
        'nullable'  => false,
        'default'   => 300,
    ), 'Time To Live (seconds)')
    ->addColumn('created_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
        'default'   => Varien_Db_Ddl_Table::TIMESTAMP_INIT,
    ), 'Created At')
    ->addColumn('expires_at', Varien_Db_Ddl_Table::TYPE_TIMESTAMP, null, array(
        'nullable'  => false,
    ), 'Expires At')
    ->addIndex($installer->getIdxName('pfg_core_cache', array('cache_key')),
        array('cache_key'), Varien_Db_Adapter_Interface::INDEX_TYPE_UNIQUE)
    ->addIndex($installer->getIdxName('pfg_core_cache', array('service_class')),
        array('service_class'))
    ->addIndex($installer->getIdxName('pfg_core_cache', array('expires_at')),
        array('expires_at'))
    ->addIndex($installer->getIdxName('pfg_core_cache', array('service_class', 'expires_at')),
        array('service_class', 'expires_at'))
    ->setComment('PFG Core Service Layer Cache Table');

$installer->getConnection()->createTable($table);

/**
 * Add new columns to existing tables for enhanced functionality
 */

// Add performance tracking columns to installation table
$installer->getConnection()->addColumn(
    $installer->getTable('pfg_core/installation'),
    'execution_time',
    array(
        'type'      => Varien_Db_Ddl_Table::TYPE_DECIMAL,
        'length'    => '10,4',
        'nullable'  => true,
        'comment'   => 'Installation Execution Time (seconds)'
    )
);

$installer->getConnection()->addColumn(
    $installer->getTable('pfg_core/installation'),
    'memory_usage',
    array(
        'type'      => Varien_Db_Ddl_Table::TYPE_BIGINT,
        'unsigned'  => true,
        'nullable'  => true,
        'comment'   => 'Memory Usage During Installation (bytes)'
    )
);

// Add error tracking columns to backup table
$installer->getConnection()->addColumn(
    $installer->getTable('pfg_core/backup'),
    'error_count',
    array(
        'type'      => Varien_Db_Ddl_Table::TYPE_INTEGER,
        'unsigned'  => true,
        'nullable'  => false,
        'default'   => 0,
        'comment'   => 'Number of Errors During Backup'
    )
);

$installer->getConnection()->addColumn(
    $installer->getTable('pfg_core/backup'),
    'compression_ratio',
    array(
        'type'      => Varien_Db_Ddl_Table::TYPE_DECIMAL,
        'length'    => '5,2',
        'nullable'  => true,
        'comment'   => 'Backup Compression Ratio'
    )
);

/**
 * Create indexes for better query performance on new columns
 */
$installer->getConnection()->addIndex(
    $installer->getTable('pfg_core/installation'),
    $installer->getIdxName('pfg_core/installation', array('execution_time')),
    array('execution_time'),
    Varien_Db_Adapter_Interface::INDEX_TYPE_INDEX
);

$installer->getConnection()->addIndex(
    $installer->getTable('pfg_core/backup'),
    $installer->getIdxName('pfg_core/backup', array('error_count')),
    array('error_count'),
    Varien_Db_Adapter_Interface::INDEX_TYPE_INDEX
);

$installer->endSetup();
