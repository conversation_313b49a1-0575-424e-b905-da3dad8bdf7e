2025-07-08T20:49:56+00:00 DEBUG (7): [2025-07-08 20:49:56] [ERROR] [SYSTEM] [PID:18] [MEM:2 MB] Failed to get repositories: HTTP error: 401
2025-07-08T20:49:56+00:00 DEBUG (7): [2025-07-08 20:49:56] [ERROR] [SYSTEM] [PID:18] [MEM:2 MB] Failed to get repositories with status: HTTP error: 401
2025-07-08T20:51:51+00:00 DEBUG (7): [2025-07-08 20:51:51] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:51:53+00:00 DEBUG (7): [2025-07-08 20:51:53] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:51:54+00:00 DEBUG (7): [2025-07-08 20:51:54] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:51:55+00:00 DEBUG (7): [2025-07-08 20:51:55] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:51:56+00:00 DEBUG (7): [2025-07-08 20:51:56] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:52:54+00:00 DEBUG (7): [2025-07-08 20:52:54] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:52:56+00:00 DEBUG (7): [2025-07-08 20:52:56] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:52:57+00:00 DEBUG (7): [2025-07-08 20:52:57] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:52:58+00:00 DEBUG (7): [2025-07-08 20:52:58] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:52:59+00:00 DEBUG (7): [2025-07-08 20:52:59] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:57:35+00:00 DEBUG (7): [2025-07-08 20:57:35] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:57:37+00:00 DEBUG (7): [2025-07-08 20:57:37] [ERROR] [SYSTEM] [PID:9] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:57:38+00:00 DEBUG (7): [2025-07-08 20:57:38] [ERROR] [SYSTEM] [PID:9] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:57:39+00:00 DEBUG (7): [2025-07-08 20:57:39] [ERROR] [SYSTEM] [PID:9] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:57:40+00:00 DEBUG (7): [2025-07-08 20:57:40] [ERROR] [SYSTEM] [PID:9] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:57:57+00:00 DEBUG (7): [2025-07-08 20:57:57] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T20:57:57+00:00 DEBUG (7): [2025-07-08 20:57:57] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:57:59+00:00 DEBUG (7): [2025-07-08 20:57:59] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:00+00:00 DEBUG (7): [2025-07-08 20:58:00] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:01+00:00 DEBUG (7): [2025-07-08 20:58:01] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:03+00:00 DEBUG (7): [2025-07-08 20:58:03] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:03+00:00 DEBUG (7): [2025-07-08 20:58:03] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:58:05+00:00 DEBUG (7): [2025-07-08 20:58:05] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:06+00:00 DEBUG (7): [2025-07-08 20:58:06] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:08+00:00 DEBUG (7): [2025-07-08 20:58:08] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:09+00:00 DEBUG (7): [2025-07-08 20:58:09] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:09+00:00 DEBUG (7): [2025-07-08 20:58:09] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] No files to backup for module: PFG_Builder
2025-07-08T20:58:10+00:00 DEBUG (7): [2025-07-08 20:58:10] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Failed to download repository: HTTP error: 404
2025-07-08T20:58:10+00:00 DEBUG (7): [2025-07-08 20:58:10] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Exception: Failed to download repository: HTTP error: 404 in /magento/app/code/local/PFG/Core/Model/Installation.php:106 - Installation install failed for PFG_Builder | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":106,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(138): PFG_Core_Model_Installation->installModule('builder', 'latest')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Builder","user":"martinpfg","ip_address":"**********"}
2025-07-08T20:58:55+00:00 DEBUG (7): [2025-07-08 20:58:55] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T20:58:55+00:00 DEBUG (7): [2025-07-08 20:58:55] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:58:57+00:00 DEBUG (7): [2025-07-08 20:58:57] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:58:58+00:00 DEBUG (7): [2025-07-08 20:58:58] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:00+00:00 DEBUG (7): [2025-07-08 20:59:00] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:01+00:00 DEBUG (7): [2025-07-08 20:59:01] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:02+00:00 DEBUG (7): [2025-07-08 20:59:02] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:02+00:00 DEBUG (7): [2025-07-08 20:59:02] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T20:59:04+00:00 DEBUG (7): [2025-07-08 20:59:04] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:05+00:00 DEBUG (7): [2025-07-08 20:59:05] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:06+00:00 DEBUG (7): [2025-07-08 20:59:06] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:08+00:00 DEBUG (7): [2025-07-08 20:59:08] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:09+00:00 DEBUG (7): [2025-07-08 20:59:09] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Failed to get repository tags: cURL error: Empty reply from server
2025-07-08T20:59:09+00:00 DEBUG (7): [2025-07-08 20:59:09] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Exception: Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_Cloudflare integration, Repository is private ✓, Module is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_Cloudflare integration | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(138): PFG_Core_Model_Installation->installModule('CloudFlare Inte...', 'latest')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Cloudflare integration","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:08:20+00:00 DEBUG (7): [2025-07-08 21:08:20] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:08:21+00:00 DEBUG (7): [2025-07-08 21:08:21] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:08:21+00:00 DEBUG (7): [2025-07-08 21:08:21] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:08:22+00:00 DEBUG (7): [2025-07-08 21:08:22] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/CloudFlare Integration/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:08:22+00:00 DEBUG (7): [2025-07-08 21:08:22] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] Failed to get repository tags for CloudFlare Integration: cURL error: Empty reply from server
2025-07-08T21:08:22+00:00 DEBUG (7): [2025-07-08 21:08:22] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for CloudFlare Integration, using master branch
2025-07-08T21:08:23+00:00 DEBUG (7): [2025-07-08 21:08:23] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:08:23+00:00 DEBUG (7): [2025-07-08 21:08:23] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:08:23+00:00 DEBUG (7): [2025-07-08 21:08:23] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for PFG AltCurrency, using master branch
2025-07-08T21:08:24+00:00 DEBUG (7): [2025-07-08 21:08:24] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:08:24+00:00 DEBUG (7): [2025-07-08 21:08:24] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:08:24+00:00 DEBUG (7): [2025-07-08 21:08:24] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for PFG Analytics, using master branch
2025-07-08T21:08:25+00:00 DEBUG (7): [2025-07-08 21:08:25] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Log Reader/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:08:25+00:00 DEBUG (7): [2025-07-08 21:08:25] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] Failed to get repository tags for PFG Log Reader: cURL error: Empty reply from server
2025-07-08T21:08:25+00:00 DEBUG (7): [2025-07-08 21:08:25] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for PFG Log Reader, using master branch
2025-07-08T21:08:55+00:00 DEBUG (7): [2025-07-08 21:08:55] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T21:08:56+00:00 DEBUG (7): [2025-07-08 21:08:56] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:08:56+00:00 DEBUG (7): [2025-07-08 21:08:56] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:08:56+00:00 DEBUG (7): [2025-07-08 21:08:56] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:08:57+00:00 DEBUG (7): [2025-07-08 21:08:57] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/CloudFlare Integration/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:08:57+00:00 DEBUG (7): [2025-07-08 21:08:57] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for CloudFlare Integration: cURL error: Empty reply from server
2025-07-08T21:08:57+00:00 DEBUG (7): [2025-07-08 21:08:57] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for CloudFlare Integration, using master branch
2025-07-08T21:08:59+00:00 DEBUG (7): [2025-07-08 21:08:59] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:08:59+00:00 DEBUG (7): [2025-07-08 21:08:59] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:08:59+00:00 DEBUG (7): [2025-07-08 21:08:59] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for PFG AltCurrency, using master branch
2025-07-08T21:09:00+00:00 DEBUG (7): [2025-07-08 21:09:00] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:00+00:00 DEBUG (7): [2025-07-08 21:09:00] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:09:00+00:00 DEBUG (7): [2025-07-08 21:09:00] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for PFG Analytics, using master branch
2025-07-08T21:09:01+00:00 DEBUG (7): [2025-07-08 21:09:01] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Log Reader/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:01+00:00 DEBUG (7): [2025-07-08 21:09:01] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG Log Reader: cURL error: Empty reply from server
2025-07-08T21:09:01+00:00 DEBUG (7): [2025-07-08 21:09:01] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for PFG Log Reader, using master branch
2025-07-08T21:09:02+00:00 DEBUG (7): [2025-07-08 21:09:02] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:02+00:00 DEBUG (7): [2025-07-08 21:09:02] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:09:03+00:00 DEBUG (7): [2025-07-08 21:09:03] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:09:03+00:00 DEBUG (7): [2025-07-08 21:09:03] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:09:03+00:00 DEBUG (7): [2025-07-08 21:09:03] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:09:04+00:00 DEBUG (7): [2025-07-08 21:09:04] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/CloudFlare Integration/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:04+00:00 DEBUG (7): [2025-07-08 21:09:04] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for CloudFlare Integration: cURL error: Empty reply from server
2025-07-08T21:09:04+00:00 DEBUG (7): [2025-07-08 21:09:04] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for CloudFlare Integration, using master branch
2025-07-08T21:09:05+00:00 DEBUG (7): [2025-07-08 21:09:05] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:05+00:00 DEBUG (7): [2025-07-08 21:09:05] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:09:05+00:00 DEBUG (7): [2025-07-08 21:09:05] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for PFG AltCurrency, using master branch
2025-07-08T21:09:07+00:00 DEBUG (7): [2025-07-08 21:09:07] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:07+00:00 DEBUG (7): [2025-07-08 21:09:07] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:09:07+00:00 DEBUG (7): [2025-07-08 21:09:07] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for PFG Analytics, using master branch
2025-07-08T21:09:08+00:00 DEBUG (7): [2025-07-08 21:09:08] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Log Reader/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:08+00:00 DEBUG (7): [2025-07-08 21:09:08] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG Log Reader: cURL error: Empty reply from server
2025-07-08T21:09:08+00:00 DEBUG (7): [2025-07-08 21:09:08] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for PFG Log Reader, using master branch
2025-07-08T21:09:09+00:00 DEBUG (7): [2025-07-08 21:09:09] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:09+00:00 DEBUG (7): [2025-07-08 21:09:09] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:09:09+00:00 DEBUG (7): [2025-07-08 21:09:09] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Exception: Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ altcurrency, Repository is private ✓, Module is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_ altcurrency | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(138): PFG_Core_Model_Installation->installModule('PFG AltCurrency', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_ altcurrency","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:09:42+00:00 DEBUG (7): [2025-07-08 21:09:42] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:09:42+00:00 DEBUG (7): [2025-07-08 21:09:42] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:09:42+00:00 DEBUG (7): [2025-07-08 21:09:42] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:09:44+00:00 DEBUG (7): [2025-07-08 21:09:44] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/CloudFlare Integration/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:44+00:00 DEBUG (7): [2025-07-08 21:09:44] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Failed to get repository tags for CloudFlare Integration: cURL error: Empty reply from server
2025-07-08T21:09:44+00:00 DEBUG (7): [2025-07-08 21:09:44] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for CloudFlare Integration, using master branch
2025-07-08T21:09:45+00:00 DEBUG (7): [2025-07-08 21:09:45] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:45+00:00 DEBUG (7): [2025-07-08 21:09:45] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:09:45+00:00 DEBUG (7): [2025-07-08 21:09:45] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for PFG AltCurrency, using master branch
2025-07-08T21:09:46+00:00 DEBUG (7): [2025-07-08 21:09:46] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:46+00:00 DEBUG (7): [2025-07-08 21:09:46] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:09:46+00:00 DEBUG (7): [2025-07-08 21:09:46] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for PFG Analytics, using master branch
2025-07-08T21:09:47+00:00 DEBUG (7): [2025-07-08 21:09:47] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Log Reader/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:09:47+00:00 DEBUG (7): [2025-07-08 21:09:47] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Failed to get repository tags for PFG Log Reader: cURL error: Empty reply from server
2025-07-08T21:09:47+00:00 DEBUG (7): [2025-07-08 21:09:47] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for PFG Log Reader, using master branch
2025-07-08T21:10:14+00:00 DEBUG (7): [2025-07-08 21:10:14] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T21:10:14+00:00 DEBUG (7): [2025-07-08 21:10:14] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:10:15+00:00 DEBUG (7): [2025-07-08 21:10:15] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:10:15+00:00 DEBUG (7): [2025-07-08 21:10:15] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:10:16+00:00 DEBUG (7): [2025-07-08 21:10:16] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/CloudFlare Integration/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:16+00:00 DEBUG (7): [2025-07-08 21:10:16] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for CloudFlare Integration: cURL error: Empty reply from server
2025-07-08T21:10:16+00:00 DEBUG (7): [2025-07-08 21:10:16] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for CloudFlare Integration, using master branch
2025-07-08T21:10:17+00:00 DEBUG (7): [2025-07-08 21:10:17] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:17+00:00 DEBUG (7): [2025-07-08 21:10:17] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:10:17+00:00 DEBUG (7): [2025-07-08 21:10:17] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for PFG AltCurrency, using master branch
2025-07-08T21:10:18+00:00 DEBUG (7): [2025-07-08 21:10:18] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:18+00:00 DEBUG (7): [2025-07-08 21:10:18] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:10:18+00:00 DEBUG (7): [2025-07-08 21:10:18] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for PFG Analytics, using master branch
2025-07-08T21:10:20+00:00 DEBUG (7): [2025-07-08 21:10:20] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Log Reader/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:20+00:00 DEBUG (7): [2025-07-08 21:10:20] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG Log Reader: cURL error: Empty reply from server
2025-07-08T21:10:20+00:00 DEBUG (7): [2025-07-08 21:10:20] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for PFG Log Reader, using master branch
2025-07-08T21:10:21+00:00 DEBUG (7): [2025-07-08 21:10:21] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:21+00:00 DEBUG (7): [2025-07-08 21:10:21] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:10:21+00:00 DEBUG (7): [2025-07-08 21:10:21] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:10:22+00:00 DEBUG (7): [2025-07-08 21:10:22] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:10:22+00:00 DEBUG (7): [2025-07-08 21:10:22] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:10:23+00:00 DEBUG (7): [2025-07-08 21:10:23] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/CloudFlare Integration/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:23+00:00 DEBUG (7): [2025-07-08 21:10:23] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for CloudFlare Integration: cURL error: Empty reply from server
2025-07-08T21:10:23+00:00 DEBUG (7): [2025-07-08 21:10:23] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for CloudFlare Integration, using master branch
2025-07-08T21:10:24+00:00 DEBUG (7): [2025-07-08 21:10:24] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG AltCurrency/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:24+00:00 DEBUG (7): [2025-07-08 21:10:24] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG AltCurrency: cURL error: Empty reply from server
2025-07-08T21:10:24+00:00 DEBUG (7): [2025-07-08 21:10:24] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for PFG AltCurrency, using master branch
2025-07-08T21:10:25+00:00 DEBUG (7): [2025-07-08 21:10:25] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:25+00:00 DEBUG (7): [2025-07-08 21:10:25] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:10:25+00:00 DEBUG (7): [2025-07-08 21:10:25] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for PFG Analytics, using master branch
2025-07-08T21:10:26+00:00 DEBUG (7): [2025-07-08 21:10:26] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Log Reader/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:26+00:00 DEBUG (7): [2025-07-08 21:10:26] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG Log Reader: cURL error: Empty reply from server
2025-07-08T21:10:26+00:00 DEBUG (7): [2025-07-08 21:10:26] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for PFG Log Reader, using master branch
2025-07-08T21:10:28+00:00 DEBUG (7): [2025-07-08 21:10:28] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] cURL error for https://api.bitbucket.org/2.0/repositories/pfg/PFG Analytics/refs/tags\?sort=-name&pagelen=50: Empty reply from server
2025-07-08T21:10:28+00:00 DEBUG (7): [2025-07-08 21:10:28] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Failed to get repository tags for PFG Analytics: cURL error: Empty reply from server
2025-07-08T21:10:28+00:00 DEBUG (7): [2025-07-08 21:10:28] [ERROR] [SYSTEM] [PID:17] [MEM:2 MB] Exception: Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ analytics, Repository is private ✓, Module is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_ analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(138): PFG_Core_Model_Installation->installModule('PFG Analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_ analytics","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:51:18+00:00 DEBUG (7): [2025-07-08 21:51:18] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:18+00:00 DEBUG (7): [2025-07-08 21:51:18] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:51:18+00:00 DEBUG (7): [2025-07-08 21:51:18] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:51:19+00:00 DEBUG (7): [2025-07-08 21:51:19] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:19+00:00 DEBUG (7): [2025-07-08 21:51:19] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:51:19+00:00 DEBUG (7): [2025-07-08 21:51:19] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:51:19+00:00 DEBUG (7): [2025-07-08 21:51:19] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:51:20+00:00 DEBUG (7): [2025-07-08 21:51:20] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:51:20+00:00 DEBUG (7): [2025-07-08 21:51:20] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:51:20+00:00 DEBUG (7): [2025-07-08 21:51:20] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:21+00:00 DEBUG (7): [2025-07-08 21:51:21] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:21+00:00 DEBUG (7): [2025-07-08 21:51:21] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T21:51:21+00:00 DEBUG (7): [2025-07-08 21:51:21] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T21:51:21+00:00 DEBUG (7): [2025-07-08 21:51:21] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752011481.zip (72654 bytes)
2025-07-08T21:51:22+00:00 DEBUG (7): [2025-07-08 21:51:22] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:22+00:00 DEBUG (7): [2025-07-08 21:51:22] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Attempting to download pfg-log-reader from main branch
2025-07-08T21:51:22+00:00 DEBUG (7): [2025-07-08 21:51:22] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Downloading repository pfg-log-reader (main) from https://bitbucket.org/pfg/pfg-log-reader/get/main.zip
2025-07-08T21:51:23+00:00 DEBUG (7): [2025-07-08 21:51:23] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Successfully downloaded repository pfg-log-reader (main) to /magento/var/pfg_core/temp/pfg-log-reader-main-1752011482.zip (32123 bytes)
2025-07-08T21:51:23+00:00 DEBUG (7): [2025-07-08 21:51:23] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:23+00:00 DEBUG (7): [2025-07-08 21:51:23] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Attempting to download pfg-analytics from master branch
2025-07-08T21:51:23+00:00 DEBUG (7): [2025-07-08 21:51:23] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-08T21:51:24+00:00 DEBUG (7): [2025-07-08 21:51:24] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752011483.zip (57135 bytes)
2025-07-08T21:51:25+00:00 DEBUG (7): [2025-07-08 21:51:25] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:25+00:00 DEBUG (7): [2025-07-08 21:51:25] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:51:25+00:00 DEBUG (7): [2025-07-08 21:51:25] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:51:26+00:00 DEBUG (7): [2025-07-08 21:51:26] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:26+00:00 DEBUG (7): [2025-07-08 21:51:26] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:51:26+00:00 DEBUG (7): [2025-07-08 21:51:26] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:51:26+00:00 DEBUG (7): [2025-07-08 21:51:26] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:51:26+00:00 DEBUG (7): [2025-07-08 21:51:26] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:51:26+00:00 DEBUG (7): [2025-07-08 21:51:26] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:51:27+00:00 DEBUG (7): [2025-07-08 21:51:27] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:27+00:00 DEBUG (7): [2025-07-08 21:51:27] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:28+00:00 DEBUG (7): [2025-07-08 21:51:28] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:28+00:00 DEBUG (7): [2025-07-08 21:51:28] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:51:28+00:00 DEBUG (7): [2025-07-08 21:51:28] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:51:29+00:00 DEBUG (7): [2025-07-08 21:51:29] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:29+00:00 DEBUG (7): [2025-07-08 21:51:29] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:51:29+00:00 DEBUG (7): [2025-07-08 21:51:29] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:51:29+00:00 DEBUG (7): [2025-07-08 21:51:29] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:51:30+00:00 DEBUG (7): [2025-07-08 21:51:30] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:51:30+00:00 DEBUG (7): [2025-07-08 21:51:30] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:51:30+00:00 DEBUG (7): [2025-07-08 21:51:30] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:30+00:00 DEBUG (7): [2025-07-08 21:51:30] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:31+00:00 DEBUG (7): [2025-07-08 21:51:31] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:31+00:00 DEBUG (7): [2025-07-08 21:51:31] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:51:31+00:00 DEBUG (7): [2025-07-08 21:51:31] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:51:32+00:00 DEBUG (7): [2025-07-08 21:51:32] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:32+00:00 DEBUG (7): [2025-07-08 21:51:32] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:51:32+00:00 DEBUG (7): [2025-07-08 21:51:32] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:51:32+00:00 DEBUG (7): [2025-07-08 21:51:32] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:51:32+00:00 DEBUG (7): [2025-07-08 21:51:32] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:51:32+00:00 DEBUG (7): [2025-07-08 21:51:32] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:51:33+00:00 DEBUG (7): [2025-07-08 21:51:33] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:33+00:00 DEBUG (7): [2025-07-08 21:51:33] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:34+00:00 DEBUG (7): [2025-07-08 21:51:34] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:34+00:00 DEBUG (7): [2025-07-08 21:51:34] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:51:34+00:00 DEBUG (7): [2025-07-08 21:51:34] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:51:35+00:00 DEBUG (7): [2025-07-08 21:51:35] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:35+00:00 DEBUG (7): [2025-07-08 21:51:35] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:51:35+00:00 DEBUG (7): [2025-07-08 21:51:35] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:51:35+00:00 DEBUG (7): [2025-07-08 21:51:35] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:51:36+00:00 DEBUG (7): [2025-07-08 21:51:36] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:51:36+00:00 DEBUG (7): [2025-07-08 21:51:36] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:51:36+00:00 DEBUG (7): [2025-07-08 21:51:36] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:37+00:00 DEBUG (7): [2025-07-08 21:51:37] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:37+00:00 DEBUG (7): [2025-07-08 21:51:37] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:51:38+00:00 DEBUG (7): [2025-07-08 21:51:38] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:51:38+00:00 DEBUG (7): [2025-07-08 21:51:38] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:51:38+00:00 DEBUG (7): [2025-07-08 21:51:38] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:51:38+00:00 DEBUG (7): [2025-07-08 21:51:38] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:51:38+00:00 DEBUG (7): [2025-07-08 21:51:38] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:51:38+00:00 DEBUG (7): [2025-07-08 21:51:38] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:51:39+00:00 DEBUG (7): [2025-07-08 21:51:39] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:51:39+00:00 DEBUG (7): [2025-07-08 21:51:39] [WARNING] [SYSTEM] [PID:251] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:51:39+00:00 DEBUG (7): [2025-07-08 21:51:39] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:51:40+00:00 DEBUG (7): [2025-07-08 21:51:40] [INFO] [SYSTEM] [PID:251] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:52:50+00:00 DEBUG (7): [2025-07-08 21:52:50] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:52:51+00:00 DEBUG (7): [2025-07-08 21:52:51] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:52:51+00:00 DEBUG (7): [2025-07-08 21:52:51] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:52:51+00:00 DEBUG (7): [2025-07-08 21:52:51] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:52:51+00:00 DEBUG (7): [2025-07-08 21:52:51] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:52:51+00:00 DEBUG (7): [2025-07-08 21:52:51] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:52:51+00:00 DEBUG (7): [2025-07-08 21:52:51] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:52:52+00:00 DEBUG (7): [2025-07-08 21:52:52] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:52:52+00:00 DEBUG (7): [2025-07-08 21:52:52] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:52:52+00:00 DEBUG (7): [2025-07-08 21:52:52] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:53:10+00:00 DEBUG (7): [2025-07-08 21:53:10] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T21:53:11+00:00 DEBUG (7): [2025-07-08 21:53:11] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:53:11+00:00 DEBUG (7): [2025-07-08 21:53:11] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:53:11+00:00 DEBUG (7): [2025-07-08 21:53:11] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:53:12+00:00 DEBUG (7): [2025-07-08 21:53:12] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:53:12+00:00 DEBUG (7): [2025-07-08 21:53:12] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:53:12+00:00 DEBUG (7): [2025-07-08 21:53:12] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:53:12+00:00 DEBUG (7): [2025-07-08 21:53:12] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:53:13+00:00 DEBUG (7): [2025-07-08 21:53:13] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:53:13+00:00 DEBUG (7): [2025-07-08 21:53:13] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:53:13+00:00 DEBUG (7): [2025-07-08 21:53:13] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:53:14+00:00 DEBUG (7): [2025-07-08 21:53:14] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:53:14+00:00 DEBUG (7): [2025-07-08 21:53:14] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:53:14+00:00 DEBUG (7): [2025-07-08 21:53:14] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:53:14+00:00 DEBUG (7): [2025-07-08 21:53:14] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:53:15+00:00 DEBUG (7): [2025-07-08 21:53:15] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:53:15+00:00 DEBUG (7): [2025-07-08 21:53:15] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:53:15+00:00 DEBUG (7): [2025-07-08 21:53:15] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:53:15+00:00 DEBUG (7): [2025-07-08 21:53:15] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:53:16+00:00 DEBUG (7): [2025-07-08 21:53:16] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:53:16+00:00 DEBUG (7): [2025-07-08 21:53:16] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:53:16+00:00 DEBUG (7): [2025-07-08 21:53:16] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:53:17+00:00 DEBUG (7): [2025-07-08 21:53:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:53:17+00:00 DEBUG (7): [2025-07-08 21:53:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] No files to backup for module: PFG_Analytics
2025-07-08T21:53:17+00:00 DEBUG (7): [2025-07-08 21:53:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:53:17+00:00 DEBUG (7): [2025-07-08 21:53:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Attempting to download pfg-analytics from master branch
2025-07-08T21:53:17+00:00 DEBUG (7): [2025-07-08 21:53:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-08T21:53:18+00:00 DEBUG (7): [2025-07-08 21:53:18] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752011597.zip (57135 bytes)
2025-07-08T21:53:20+00:00 DEBUG (7): [2025-07-08 21:53:20] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:53:21+00:00 DEBUG (7): [2025-07-08 21:53:21] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:53:21+00:00 DEBUG (7): [2025-07-08 21:53:21] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:53:21+00:00 DEBUG (7): [2025-07-08 21:53:21] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:53:21+00:00 DEBUG (7): [2025-07-08 21:53:21] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:53:22+00:00 DEBUG (7): [2025-07-08 21:53:22] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:53:22+00:00 DEBUG (7): [2025-07-08 21:53:22] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:53:22+00:00 DEBUG (7): [2025-07-08 21:53:22] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:53:22+00:00 DEBUG (7): [2025-07-08 21:53:22] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:53:22+00:00 DEBUG (7): [2025-07-08 21:53:22] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:53:29+00:00 DEBUG (7): [2025-07-08 21:53:29] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:53:29+00:00 DEBUG (7): [2025-07-08 21:53:29] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:53:29+00:00 DEBUG (7): [2025-07-08 21:53:29] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:53:30+00:00 DEBUG (7): [2025-07-08 21:53:30] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:53:30+00:00 DEBUG (7): [2025-07-08 21:53:30] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:53:30+00:00 DEBUG (7): [2025-07-08 21:53:30] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:53:30+00:00 DEBUG (7): [2025-07-08 21:53:30] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:53:30+00:00 DEBUG (7): [2025-07-08 21:53:30] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:53:30+00:00 DEBUG (7): [2025-07-08 21:53:30] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:53:31+00:00 DEBUG (7): [2025-07-08 21:53:31] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:54:14+00:00 DEBUG (7): [2025-07-08 21:54:14] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:54:14+00:00 DEBUG (7): [2025-07-08 21:54:14] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:54:14+00:00 DEBUG (7): [2025-07-08 21:54:14] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:54:14+00:00 DEBUG (7): [2025-07-08 21:54:14] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:54:14+00:00 DEBUG (7): [2025-07-08 21:54:14] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:54:15+00:00 DEBUG (7): [2025-07-08 21:54:15] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:54:15+00:00 DEBUG (7): [2025-07-08 21:54:15] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:54:15+00:00 DEBUG (7): [2025-07-08 21:54:15] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:54:15+00:00 DEBUG (7): [2025-07-08 21:54:15] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:54:16+00:00 DEBUG (7): [2025-07-08 21:54:16] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:55:20+00:00 DEBUG (7): [2025-07-08 21:55:20] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T21:55:21+00:00 DEBUG (7): [2025-07-08 21:55:21] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:55:21+00:00 DEBUG (7): [2025-07-08 21:55:21] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:55:21+00:00 DEBUG (7): [2025-07-08 21:55:21] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:55:21+00:00 DEBUG (7): [2025-07-08 21:55:21] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:55:21+00:00 DEBUG (7): [2025-07-08 21:55:21] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:55:22+00:00 DEBUG (7): [2025-07-08 21:55:22] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:55:22+00:00 DEBUG (7): [2025-07-08 21:55:22] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:55:22+00:00 DEBUG (7): [2025-07-08 21:55:22] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:55:22+00:00 DEBUG (7): [2025-07-08 21:55:22] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:55:23+00:00 DEBUG (7): [2025-07-08 21:55:23] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:55:23+00:00 DEBUG (7): [2025-07-08 21:55:23] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:55:23+00:00 DEBUG (7): [2025-07-08 21:55:23] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:55:24+00:00 DEBUG (7): [2025-07-08 21:55:24] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:55:24+00:00 DEBUG (7): [2025-07-08 21:55:24] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:55:24+00:00 DEBUG (7): [2025-07-08 21:55:24] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:55:24+00:00 DEBUG (7): [2025-07-08 21:55:24] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:55:25+00:00 DEBUG (7): [2025-07-08 21:55:25] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:55:25+00:00 DEBUG (7): [2025-07-08 21:55:25] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:55:25+00:00 DEBUG (7): [2025-07-08 21:55:25] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:55:25+00:00 DEBUG (7): [2025-07-08 21:55:25] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:55:25+00:00 DEBUG (7): [2025-07-08 21:55:25] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:55:26+00:00 DEBUG (7): [2025-07-08 21:55:26] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:55:26+00:00 DEBUG (7): [2025-07-08 21:55:26] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] No files to backup for module: PFG_CloudflareIntegration
2025-07-08T21:55:26+00:00 DEBUG (7): [2025-07-08 21:55:26] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:55:26+00:00 DEBUG (7): [2025-07-08 21:55:26] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T21:55:26+00:00 DEBUG (7): [2025-07-08 21:55:26] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T21:55:27+00:00 DEBUG (7): [2025-07-08 21:55:27] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752011726.zip (72654 bytes)
2025-07-08T21:55:30+00:00 DEBUG (7): [2025-07-08 21:55:30] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:55:30+00:00 DEBUG (7): [2025-07-08 21:55:30] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T21:55:30+00:00 DEBUG (7): [2025-07-08 21:55:30] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T21:55:31+00:00 DEBUG (7): [2025-07-08 21:55:31] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:55:31+00:00 DEBUG (7): [2025-07-08 21:55:31] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:55:31+00:00 DEBUG (7): [2025-07-08 21:55:31] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:55:31+00:00 DEBUG (7): [2025-07-08 21:55:31] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:55:32+00:00 DEBUG (7): [2025-07-08 21:55:32] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:55:32+00:00 DEBUG (7): [2025-07-08 21:55:32] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:55:32+00:00 DEBUG (7): [2025-07-08 21:55:32] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:58:15+00:00 DEBUG (7): [2025-07-08 21:58:15] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:58:16+00:00 DEBUG (7): [2025-07-08 21:58:16] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T21:58:16+00:00 DEBUG (7): [2025-07-08 21:58:16] [WARNING] [SYSTEM] [PID:377] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T21:58:16+00:00 DEBUG (7): [2025-07-08 21:58:16] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:58:16+00:00 DEBUG (7): [2025-07-08 21:58:16] [WARNING] [SYSTEM] [PID:377] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T21:58:16+00:00 DEBUG (7): [2025-07-08 21:58:16] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T21:58:16+00:00 DEBUG (7): [2025-07-08 21:58:16] [WARNING] [SYSTEM] [PID:377] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T21:58:17+00:00 DEBUG (7): [2025-07-08 21:58:17] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T21:58:17+00:00 DEBUG (7): [2025-07-08 21:58:17] [WARNING] [SYSTEM] [PID:377] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T21:58:17+00:00 DEBUG (7): [2025-07-08 21:58:17] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T21:58:18+00:00 DEBUG (7): [2025-07-08 21:58:18] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T21:58:18+00:00 DEBUG (7): [2025-07-08 21:58:18] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T21:58:18+00:00 DEBUG (7): [2025-07-08 21:58:18] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T21:58:18+00:00 DEBUG (7): [2025-07-08 21:58:18] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T21:58:19+00:00 DEBUG (7): [2025-07-08 21:58:19] [INFO] [SYSTEM] [PID:377] [MEM:8 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752011898.zip (72654 bytes)
2025-07-08T22:07:58+00:00 DEBUG (7): [2025-07-08 22:07:58] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:07:59+00:00 DEBUG (7): [2025-07-08 22:07:59] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:07:59+00:00 DEBUG (7): [2025-07-08 22:07:59] [WARNING] [SYSTEM] [PID:392] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:07:59+00:00 DEBUG (7): [2025-07-08 22:07:59] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:07:59+00:00 DEBUG (7): [2025-07-08 22:07:59] [WARNING] [SYSTEM] [PID:392] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:08:00+00:00 DEBUG (7): [2025-07-08 22:08:00] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:08:00+00:00 DEBUG (7): [2025-07-08 22:08:00] [WARNING] [SYSTEM] [PID:392] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:08:00+00:00 DEBUG (7): [2025-07-08 22:08:00] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:08:00+00:00 DEBUG (7): [2025-07-08 22:08:00] [WARNING] [SYSTEM] [PID:392] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:08:01+00:00 DEBUG (7): [2025-07-08 22:08:01] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:08:01+00:00 DEBUG (7): [2025-07-08 22:08:01] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:08:01+00:00 DEBUG (7): [2025-07-08 22:08:01] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:08:01+00:00 DEBUG (7): [2025-07-08 22:08:01] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T22:08:01+00:00 DEBUG (7): [2025-07-08 22:08:01] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T22:08:03+00:00 DEBUG (7): [2025-07-08 22:08:03] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752012481.zip (72654 bytes)
2025-07-08T22:08:03+00:00 DEBUG (7): [2025-07-08 22:08:03] [INFO] [SYSTEM] [PID:392] [MEM:8 MB] No files to backup for module: PFG_Cloudflare
2025-07-08T22:08:37+00:00 DEBUG (7): [2025-07-08 22:08:37] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:08:38+00:00 DEBUG (7): [2025-07-08 22:08:38] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:08:38+00:00 DEBUG (7): [2025-07-08 22:08:38] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:08:38+00:00 DEBUG (7): [2025-07-08 22:08:38] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:08:38+00:00 DEBUG (7): [2025-07-08 22:08:38] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:08:39+00:00 DEBUG (7): [2025-07-08 22:08:39] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:08:39+00:00 DEBUG (7): [2025-07-08 22:08:39] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:08:39+00:00 DEBUG (7): [2025-07-08 22:08:39] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:08:39+00:00 DEBUG (7): [2025-07-08 22:08:39] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:08:40+00:00 DEBUG (7): [2025-07-08 22:08:40] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:08:40+00:00 DEBUG (7): [2025-07-08 22:08:40] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:08:41+00:00 DEBUG (7): [2025-07-08 22:08:41] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:08:41+00:00 DEBUG (7): [2025-07-08 22:08:41] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T22:08:41+00:00 DEBUG (7): [2025-07-08 22:08:41] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T22:08:42+00:00 DEBUG (7): [2025-07-08 22:08:42] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752012521.zip (72654 bytes)
2025-07-08T22:08:42+00:00 DEBUG (7): [2025-07-08 22:08:42] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:08:43+00:00 DEBUG (7): [2025-07-08 22:08:43] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:08:43+00:00 DEBUG (7): [2025-07-08 22:08:43] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:08:43+00:00 DEBUG (7): [2025-07-08 22:08:43] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:08:43+00:00 DEBUG (7): [2025-07-08 22:08:43] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:08:43+00:00 DEBUG (7): [2025-07-08 22:08:43] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:08:43+00:00 DEBUG (7): [2025-07-08 22:08:43] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:08:44+00:00 DEBUG (7): [2025-07-08 22:08:44] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:08:44+00:00 DEBUG (7): [2025-07-08 22:08:44] [WARNING] [SYSTEM] [PID:407] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:08:44+00:00 DEBUG (7): [2025-07-08 22:08:44] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:08:45+00:00 DEBUG (7): [2025-07-08 22:08:45] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:08:45+00:00 DEBUG (7): [2025-07-08 22:08:45] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:08:45+00:00 DEBUG (7): [2025-07-08 22:08:45] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Attempting to download pfg-log-reader from main branch
2025-07-08T22:08:45+00:00 DEBUG (7): [2025-07-08 22:08:45] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Downloading repository pfg-log-reader (main) from https://bitbucket.org/pfg/pfg-log-reader/get/main.zip
2025-07-08T22:08:46+00:00 DEBUG (7): [2025-07-08 22:08:46] [INFO] [SYSTEM] [PID:407] [MEM:8 MB] Successfully downloaded repository pfg-log-reader (main) to /magento/var/pfg_core/temp/pfg-log-reader-main-1752012525.zip (32123 bytes)
2025-07-08T22:09:40+00:00 DEBUG (7): [2025-07-08 22:09:40] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:09:41+00:00 DEBUG (7): [2025-07-08 22:09:41] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:09:41+00:00 DEBUG (7): [2025-07-08 22:09:41] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:09:41+00:00 DEBUG (7): [2025-07-08 22:09:41] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:09:41+00:00 DEBUG (7): [2025-07-08 22:09:41] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:09:41+00:00 DEBUG (7): [2025-07-08 22:09:41] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:09:41+00:00 DEBUG (7): [2025-07-08 22:09:41] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:09:42+00:00 DEBUG (7): [2025-07-08 22:09:42] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:09:42+00:00 DEBUG (7): [2025-07-08 22:09:42] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:09:42+00:00 DEBUG (7): [2025-07-08 22:09:42] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:09:43+00:00 DEBUG (7): [2025-07-08 22:09:43] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:09:43+00:00 DEBUG (7): [2025-07-08 22:09:43] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:09:44+00:00 DEBUG (7): [2025-07-08 22:09:44] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:09:44+00:00 DEBUG (7): [2025-07-08 22:09:44] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:09:44+00:00 DEBUG (7): [2025-07-08 22:09:44] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:09:44+00:00 DEBUG (7): [2025-07-08 22:09:44] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:09:44+00:00 DEBUG (7): [2025-07-08 22:09:44] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:09:44+00:00 DEBUG (7): [2025-07-08 22:09:44] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:09:45+00:00 DEBUG (7): [2025-07-08 22:09:45] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:09:45+00:00 DEBUG (7): [2025-07-08 22:09:45] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:09:45+00:00 DEBUG (7): [2025-07-08 22:09:45] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:09:46+00:00 DEBUG (7): [2025-07-08 22:09:46] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:09:46+00:00 DEBUG (7): [2025-07-08 22:09:46] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:09:46+00:00 DEBUG (7): [2025-07-08 22:09:46] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Attempting to download cloudflare-integration from 1.0.0 branch
2025-07-08T22:09:46+00:00 DEBUG (7): [2025-07-08 22:09:46] [INFO] [SYSTEM] [PID:431] [MEM:8 MB] Downloading repository cloudflare-integration (1.0.0) from https://bitbucket.org/pfg/cloudflare-integration/get/1.0.0.zip
2025-07-08T22:09:47+00:00 DEBUG (7): [2025-07-08 22:09:47] [ERROR] [SYSTEM] [PID:431] [MEM:8 MB] Failed to download repository cloudflare-integration (1.0.0): HTTP error during download: 404
2025-07-08T22:09:47+00:00 DEBUG (7): [2025-07-08 22:09:47] [WARNING] [SYSTEM] [PID:431] [MEM:8 MB] Download failed for 1.0.0 branch: HTTP error during download: 404
2025-07-08T22:09:47+00:00 DEBUG (7): [2025-07-08 22:09:47] [ERROR] [SYSTEM] [PID:431] [MEM:8 MB] Repository download failed: Failed to download from any branch. Last error: HTTP error during download: 404
2025-07-08T22:10:47+00:00 DEBUG (7): [2025-07-08 22:10:47] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:10:48+00:00 DEBUG (7): [2025-07-08 22:10:48] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:10:48+00:00 DEBUG (7): [2025-07-08 22:10:48] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:10:48+00:00 DEBUG (7): [2025-07-08 22:10:48] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:10:48+00:00 DEBUG (7): [2025-07-08 22:10:48] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:10:48+00:00 DEBUG (7): [2025-07-08 22:10:48] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:10:48+00:00 DEBUG (7): [2025-07-08 22:10:48] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:10:49+00:00 DEBUG (7): [2025-07-08 22:10:49] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:10:49+00:00 DEBUG (7): [2025-07-08 22:10:49] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:10:49+00:00 DEBUG (7): [2025-07-08 22:10:49] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:11:17+00:00 DEBUG (7): [2025-07-08 22:11:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:11:18+00:00 DEBUG (7): [2025-07-08 22:11:18] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:11:18+00:00 DEBUG (7): [2025-07-08 22:11:18] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:11:18+00:00 DEBUG (7): [2025-07-08 22:11:18] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:11:18+00:00 DEBUG (7): [2025-07-08 22:11:18] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:11:18+00:00 DEBUG (7): [2025-07-08 22:11:18] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:11:18+00:00 DEBUG (7): [2025-07-08 22:11:18] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:11:19+00:00 DEBUG (7): [2025-07-08 22:11:19] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:11:19+00:00 DEBUG (7): [2025-07-08 22:11:19] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:11:19+00:00 DEBUG (7): [2025-07-08 22:11:19] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:11:32+00:00 DEBUG (7): [2025-07-08 22:11:32] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T22:11:32+00:00 DEBUG (7): [2025-07-08 22:11:32] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:11:33+00:00 DEBUG (7): [2025-07-08 22:11:33] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:11:33+00:00 DEBUG (7): [2025-07-08 22:11:33] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:11:33+00:00 DEBUG (7): [2025-07-08 22:11:33] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:11:33+00:00 DEBUG (7): [2025-07-08 22:11:33] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:11:34+00:00 DEBUG (7): [2025-07-08 22:11:34] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:11:34+00:00 DEBUG (7): [2025-07-08 22:11:34] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:11:34+00:00 DEBUG (7): [2025-07-08 22:11:34] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:11:34+00:00 DEBUG (7): [2025-07-08 22:11:34] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:11:34+00:00 DEBUG (7): [2025-07-08 22:11:34] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:11:35+00:00 DEBUG (7): [2025-07-08 22:11:35] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:11:35+00:00 DEBUG (7): [2025-07-08 22:11:35] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:11:36+00:00 DEBUG (7): [2025-07-08 22:11:36] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:11:36+00:00 DEBUG (7): [2025-07-08 22:11:36] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:11:36+00:00 DEBUG (7): [2025-07-08 22:11:36] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:11:36+00:00 DEBUG (7): [2025-07-08 22:11:36] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:11:37+00:00 DEBUG (7): [2025-07-08 22:11:37] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:11:37+00:00 DEBUG (7): [2025-07-08 22:11:37] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:11:37+00:00 DEBUG (7): [2025-07-08 22:11:37] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:11:37+00:00 DEBUG (7): [2025-07-08 22:11:37] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] No files to backup for module: PFG_LogReader
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-08T22:11:39+00:00 DEBUG (7): [2025-07-08 22:11:39] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752012698.zip (32123 bytes)
2025-07-08T22:11:42+00:00 DEBUG (7): [2025-07-08 22:11:42] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:11:42+00:00 DEBUG (7): [2025-07-08 22:11:42] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:11:42+00:00 DEBUG (7): [2025-07-08 22:11:42] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:11:43+00:00 DEBUG (7): [2025-07-08 22:11:43] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:11:43+00:00 DEBUG (7): [2025-07-08 22:11:43] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:11:43+00:00 DEBUG (7): [2025-07-08 22:11:43] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:11:43+00:00 DEBUG (7): [2025-07-08 22:11:43] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:11:43+00:00 DEBUG (7): [2025-07-08 22:11:43] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:11:43+00:00 DEBUG (7): [2025-07-08 22:11:43] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:11:44+00:00 DEBUG (7): [2025-07-08 22:11:44] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:12:28+00:00 DEBUG (7): [2025-07-08 22:12:28] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:12:28+00:00 DEBUG (7): [2025-07-08 22:12:28] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:12:28+00:00 DEBUG (7): [2025-07-08 22:12:28] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:12:29+00:00 DEBUG (7): [2025-07-08 22:12:29] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:12:29+00:00 DEBUG (7): [2025-07-08 22:12:29] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:12:29+00:00 DEBUG (7): [2025-07-08 22:12:29] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:12:29+00:00 DEBUG (7): [2025-07-08 22:12:29] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:12:30+00:00 DEBUG (7): [2025-07-08 22:12:30] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:12:30+00:00 DEBUG (7): [2025-07-08 22:12:30] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:12:30+00:00 DEBUG (7): [2025-07-08 22:12:30] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:12:46+00:00 DEBUG (7): [2025-07-08 22:12:46] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:12:46+00:00 DEBUG (7): [2025-07-08 22:12:46] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:12:46+00:00 DEBUG (7): [2025-07-08 22:12:46] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:12:47+00:00 DEBUG (7): [2025-07-08 22:12:47] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:12:47+00:00 DEBUG (7): [2025-07-08 22:12:47] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:12:47+00:00 DEBUG (7): [2025-07-08 22:12:47] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:12:47+00:00 DEBUG (7): [2025-07-08 22:12:47] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:12:47+00:00 DEBUG (7): [2025-07-08 22:12:47] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:12:47+00:00 DEBUG (7): [2025-07-08 22:12:47] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:12:48+00:00 DEBUG (7): [2025-07-08 22:12:48] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:20:40+00:00 DEBUG (7): [2025-07-08 22:20:40] [INFO] [SYSTEM] [PID:519] [MEM:8 MB] Created backup for PFG_Analytics: PFG_Analytics_test_uninstall_2025-07-08_22-20-40.tar.gz (6 KB)
2025-07-08T22:22:31+00:00 DEBUG (7): [2025-07-08 22:22:31] [INFO] [SYSTEM] [PID:531] [MEM:8 MB] No files to backup for module: PFG_TestModule
2025-07-08T22:23:21+00:00 DEBUG (7): [2025-07-08 22:23:21] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:23:21+00:00 DEBUG (7): [2025-07-08 22:23:21] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:23:21+00:00 DEBUG (7): [2025-07-08 22:23:21] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:23:22+00:00 DEBUG (7): [2025-07-08 22:23:22] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:23:22+00:00 DEBUG (7): [2025-07-08 22:23:22] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:23:22+00:00 DEBUG (7): [2025-07-08 22:23:22] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:23:22+00:00 DEBUG (7): [2025-07-08 22:23:22] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:23:23+00:00 DEBUG (7): [2025-07-08 22:23:23] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:23:23+00:00 DEBUG (7): [2025-07-08 22:23:23] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:23:23+00:00 DEBUG (7): [2025-07-08 22:23:23] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:23:58+00:00 DEBUG (7): [2025-07-08 22:23:58] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:23:58+00:00 DEBUG (7): [2025-07-08 22:23:58] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:23:58+00:00 DEBUG (7): [2025-07-08 22:23:58] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:23:59+00:00 DEBUG (7): [2025-07-08 22:23:59] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:23:59+00:00 DEBUG (7): [2025-07-08 22:23:59] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:23:59+00:00 DEBUG (7): [2025-07-08 22:23:59] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:23:59+00:00 DEBUG (7): [2025-07-08 22:23:59] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:23:59+00:00 DEBUG (7): [2025-07-08 22:23:59] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:23:59+00:00 DEBUG (7): [2025-07-08 22:23:59] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:24:00+00:00 DEBUG (7): [2025-07-08 22:24:00] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:24:20+00:00 DEBUG (7): [2025-07-08 22:24:20] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Restored backup: /magento/var/pfg_core/backups/PFG_Analytics_test_uninstall_2025-07-08_22-20-40.tar.gz
2025-07-08T22:35:25+00:00 DEBUG (7): [2025-07-08 22:35:25] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:35:25+00:00 DEBUG (7): [2025-07-08 22:35:25] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:35:25+00:00 DEBUG (7): [2025-07-08 22:35:25] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:35:26+00:00 DEBUG (7): [2025-07-08 22:35:26] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:35:26+00:00 DEBUG (7): [2025-07-08 22:35:26] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:35:26+00:00 DEBUG (7): [2025-07-08 22:35:26] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:35:26+00:00 DEBUG (7): [2025-07-08 22:35:26] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:35:26+00:00 DEBUG (7): [2025-07-08 22:35:26] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:35:26+00:00 DEBUG (7): [2025-07-08 22:35:26] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:35:27+00:00 DEBUG (7): [2025-07-08 22:35:27] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:40:52+00:00 DEBUG (7): [2025-07-08 22:40:52] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:40:52+00:00 DEBUG (7): [2025-07-08 22:40:52] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:40:52+00:00 DEBUG (7): [2025-07-08 22:40:52] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:40:52+00:00 DEBUG (7): [2025-07-08 22:40:52] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:40:52+00:00 DEBUG (7): [2025-07-08 22:40:52] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:40:53+00:00 DEBUG (7): [2025-07-08 22:40:53] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:40:53+00:00 DEBUG (7): [2025-07-08 22:40:53] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:40:53+00:00 DEBUG (7): [2025-07-08 22:40:53] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:40:53+00:00 DEBUG (7): [2025-07-08 22:40:53] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:40:54+00:00 DEBUG (7): [2025-07-08 22:40:54] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:41:10+00:00 DEBUG (7): [2025-07-08 22:41:10] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:41:10+00:00 DEBUG (7): [2025-07-08 22:41:10] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:41:10+00:00 DEBUG (7): [2025-07-08 22:41:10] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:41:11+00:00 DEBUG (7): [2025-07-08 22:41:11] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:41:11+00:00 DEBUG (7): [2025-07-08 22:41:11] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:41:11+00:00 DEBUG (7): [2025-07-08 22:41:11] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:41:11+00:00 DEBUG (7): [2025-07-08 22:41:11] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:41:12+00:00 DEBUG (7): [2025-07-08 22:41:12] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:41:12+00:00 DEBUG (7): [2025-07-08 22:41:12] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:41:12+00:00 DEBUG (7): [2025-07-08 22:41:12] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:45:05+00:00 DEBUG (7): [2025-07-08 22:45:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:45:05+00:00 DEBUG (7): [2025-07-08 22:45:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:45:05+00:00 DEBUG (7): [2025-07-08 22:45:05] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:45:06+00:00 DEBUG (7): [2025-07-08 22:45:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:45:06+00:00 DEBUG (7): [2025-07-08 22:45:06] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:45:06+00:00 DEBUG (7): [2025-07-08 22:45:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:45:06+00:00 DEBUG (7): [2025-07-08 22:45:06] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:45:07+00:00 DEBUG (7): [2025-07-08 22:45:07] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:45:07+00:00 DEBUG (7): [2025-07-08 22:45:07] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:45:07+00:00 DEBUG (7): [2025-07-08 22:45:07] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:48:42+00:00 DEBUG (7): [2025-07-08 22:48:42] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:48:43+00:00 DEBUG (7): [2025-07-08 22:48:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:48:43+00:00 DEBUG (7): [2025-07-08 22:48:43] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:48:43+00:00 DEBUG (7): [2025-07-08 22:48:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:48:43+00:00 DEBUG (7): [2025-07-08 22:48:43] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:48:44+00:00 DEBUG (7): [2025-07-08 22:48:44] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:48:44+00:00 DEBUG (7): [2025-07-08 22:48:44] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:48:44+00:00 DEBUG (7): [2025-07-08 22:48:44] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:48:44+00:00 DEBUG (7): [2025-07-08 22:48:44] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:48:45+00:00 DEBUG (7): [2025-07-08 22:48:45] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:48:53+00:00 DEBUG (7): [2025-07-08 22:48:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Starting uninstall of module: PFG_Analytics
2025-07-08T22:48:53+00:00 DEBUG (7): [2025-07-08 22:48:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Created backup for PFG_Analytics: PFG_Analytics_uninstall_2025-07-08_22-48-53.tar.gz (6 KB)
2025-07-08T22:48:53+00:00 DEBUG (7): [2025-07-08 22:48:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Backup created with ID: 2
2025-07-08T22:49:12+00:00 DEBUG (7): [2025-07-08 22:49:12] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Starting uninstall of module: PFG_LogReader
2025-07-08T22:49:12+00:00 DEBUG (7): [2025-07-08 22:49:12] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Created backup for PFG_LogReader: PFG_LogReader_uninstall_2025-07-08_22-49-12.tar.gz (83 KB)
2025-07-08T22:49:12+00:00 DEBUG (7): [2025-07-08 22:49:12] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Backup created with ID: 3
2025-07-08T22:49:17+00:00 DEBUG (7): [2025-07-08 22:49:17] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:49:17+00:00 DEBUG (7): [2025-07-08 22:49:17] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:49:17+00:00 DEBUG (7): [2025-07-08 22:49:17] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:49:18+00:00 DEBUG (7): [2025-07-08 22:49:18] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:49:18+00:00 DEBUG (7): [2025-07-08 22:49:18] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:49:18+00:00 DEBUG (7): [2025-07-08 22:49:18] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:49:18+00:00 DEBUG (7): [2025-07-08 22:49:18] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:49:19+00:00 DEBUG (7): [2025-07-08 22:49:19] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:49:19+00:00 DEBUG (7): [2025-07-08 22:49:19] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:49:19+00:00 DEBUG (7): [2025-07-08 22:49:19] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:49:39+00:00 DEBUG (7): [2025-07-08 22:49:39] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Restored backup: /magento/var/pfg_core/backups/PFG_Analytics_uninstall_2025-07-08_22-48-53.tar.gz
2025-07-08T22:51:39+00:00 DEBUG (7): [2025-07-08 22:51:39] [INFO] [SYSTEM] [PID:604] [MEM:6 MB] Starting uninstall of module: PFG_LogReader
2025-07-08T22:51:39+00:00 DEBUG (7): [2025-07-08 22:51:39] [INFO] [SYSTEM] [PID:604] [MEM:8 MB] Created backup for PFG_LogReader: PFG_LogReader_uninstall_2025-07-08_22-51-39.tar.gz (83 KB)
2025-07-08T22:51:39+00:00 DEBUG (7): [2025-07-08 22:51:39] [INFO] [SYSTEM] [PID:604] [MEM:8 MB] Backup created with ID: 4
2025-07-08T22:52:23+00:00 DEBUG (7): [2025-07-08 22:52:23] [INFO] [SYSTEM] [PID:610] [MEM:6 MB] Starting uninstall of module: PFG_LogReader
2025-07-08T22:52:23+00:00 DEBUG (7): [2025-07-08 22:52:23] [INFO] [SYSTEM] [PID:610] [MEM:8 MB] Created backup for PFG_LogReader: PFG_LogReader_uninstall_2025-07-08_22-52-23.tar.gz (83 KB)
2025-07-08T22:52:23+00:00 DEBUG (7): [2025-07-08 22:52:23] [INFO] [SYSTEM] [PID:610] [MEM:8 MB] Backup created with ID: 5
2025-07-08T22:52:23+00:00 DEBUG (7): [2025-07-08 22:52:23] [INFO] [SYSTEM] [PID:610] [MEM:8 MB] Module PFG_LogReader uninstalled successfully
2025-07-08T22:52:53+00:00 DEBUG (7): [2025-07-08 22:52:53] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:52:53+00:00 DEBUG (7): [2025-07-08 22:52:53] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:52:53+00:00 DEBUG (7): [2025-07-08 22:52:53] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:52:54+00:00 DEBUG (7): [2025-07-08 22:52:54] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:52:54+00:00 DEBUG (7): [2025-07-08 22:52:54] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:52:54+00:00 DEBUG (7): [2025-07-08 22:52:54] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:52:54+00:00 DEBUG (7): [2025-07-08 22:52:54] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:52:55+00:00 DEBUG (7): [2025-07-08 22:52:55] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:52:55+00:00 DEBUG (7): [2025-07-08 22:52:55] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:52:55+00:00 DEBUG (7): [2025-07-08 22:52:55] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:02+00:00 DEBUG (7): [2025-07-08 22:53:02] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Starting uninstall of module: PFG_Analytics
2025-07-08T22:53:02+00:00 DEBUG (7): [2025-07-08 22:53:02] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Created backup for PFG_Analytics: PFG_Analytics_uninstall_2025-07-08_22-53-02.tar.gz (6 KB)
2025-07-08T22:53:02+00:00 DEBUG (7): [2025-07-08 22:53:02] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Backup created with ID: 6
2025-07-08T22:53:02+00:00 DEBUG (7): [2025-07-08 22:53:02] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Module PFG_Analytics uninstalled successfully
2025-07-08T22:53:04+00:00 DEBUG (7): [2025-07-08 22:53:04] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:05+00:00 DEBUG (7): [2025-07-08 22:53:05] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:53:05+00:00 DEBUG (7): [2025-07-08 22:53:05] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:53:05+00:00 DEBUG (7): [2025-07-08 22:53:05] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:53:05+00:00 DEBUG (7): [2025-07-08 22:53:05] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:53:05+00:00 DEBUG (7): [2025-07-08 22:53:05] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:53:05+00:00 DEBUG (7): [2025-07-08 22:53:05] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:53:06+00:00 DEBUG (7): [2025-07-08 22:53:06] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:06+00:00 DEBUG (7): [2025-07-08 22:53:06] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:53:06+00:00 DEBUG (7): [2025-07-08 22:53:06] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:16+00:00 DEBUG (7): [2025-07-08 22:53:16] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T22:53:16+00:00 DEBUG (7): [2025-07-08 22:53:16] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:17+00:00 DEBUG (7): [2025-07-08 22:53:17] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:53:17+00:00 DEBUG (7): [2025-07-08 22:53:17] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:53:17+00:00 DEBUG (7): [2025-07-08 22:53:17] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:53:17+00:00 DEBUG (7): [2025-07-08 22:53:17] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:53:18+00:00 DEBUG (7): [2025-07-08 22:53:18] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:53:18+00:00 DEBUG (7): [2025-07-08 22:53:18] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:53:18+00:00 DEBUG (7): [2025-07-08 22:53:18] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:18+00:00 DEBUG (7): [2025-07-08 22:53:18] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:53:19+00:00 DEBUG (7): [2025-07-08 22:53:19] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:19+00:00 DEBUG (7): [2025-07-08 22:53:19] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:20+00:00 DEBUG (7): [2025-07-08 22:53:20] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:20+00:00 DEBUG (7): [2025-07-08 22:53:20] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:53:20+00:00 DEBUG (7): [2025-07-08 22:53:20] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:53:20+00:00 DEBUG (7): [2025-07-08 22:53:20] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:53:20+00:00 DEBUG (7): [2025-07-08 22:53:20] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:53:21+00:00 DEBUG (7): [2025-07-08 22:53:21] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:53:21+00:00 DEBUG (7): [2025-07-08 22:53:21] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:53:21+00:00 DEBUG (7): [2025-07-08 22:53:21] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:21+00:00 DEBUG (7): [2025-07-08 22:53:21] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] No files to backup for module: PFG_LogReader
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-08T22:53:23+00:00 DEBUG (7): [2025-07-08 22:53:23] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752015202.zip (32123 bytes)
2025-07-08T22:53:26+00:00 DEBUG (7): [2025-07-08 22:53:26] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:26+00:00 DEBUG (7): [2025-07-08 22:53:26] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:53:26+00:00 DEBUG (7): [2025-07-08 22:53:26] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:53:27+00:00 DEBUG (7): [2025-07-08 22:53:27] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:53:27+00:00 DEBUG (7): [2025-07-08 22:53:27] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:53:27+00:00 DEBUG (7): [2025-07-08 22:53:27] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:53:27+00:00 DEBUG (7): [2025-07-08 22:53:27] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:53:27+00:00 DEBUG (7): [2025-07-08 22:53:27] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:27+00:00 DEBUG (7): [2025-07-08 22:53:27] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:53:28+00:00 DEBUG (7): [2025-07-08 22:53:28] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:38+00:00 DEBUG (7): [2025-07-08 22:53:38] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T22:53:38+00:00 DEBUG (7): [2025-07-08 22:53:38] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:39+00:00 DEBUG (7): [2025-07-08 22:53:39] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:53:39+00:00 DEBUG (7): [2025-07-08 22:53:39] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:53:39+00:00 DEBUG (7): [2025-07-08 22:53:39] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:53:39+00:00 DEBUG (7): [2025-07-08 22:53:39] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:53:39+00:00 DEBUG (7): [2025-07-08 22:53:39] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:53:39+00:00 DEBUG (7): [2025-07-08 22:53:39] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:53:40+00:00 DEBUG (7): [2025-07-08 22:53:40] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:40+00:00 DEBUG (7): [2025-07-08 22:53:40] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:53:40+00:00 DEBUG (7): [2025-07-08 22:53:40] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:41+00:00 DEBUG (7): [2025-07-08 22:53:41] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:41+00:00 DEBUG (7): [2025-07-08 22:53:41] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:42+00:00 DEBUG (7): [2025-07-08 22:53:42] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:53:42+00:00 DEBUG (7): [2025-07-08 22:53:42] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:53:42+00:00 DEBUG (7): [2025-07-08 22:53:42] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:53:42+00:00 DEBUG (7): [2025-07-08 22:53:42] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:53:42+00:00 DEBUG (7): [2025-07-08 22:53:42] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:53:42+00:00 DEBUG (7): [2025-07-08 22:53:42] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:53:43+00:00 DEBUG (7): [2025-07-08 22:53:43] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:43+00:00 DEBUG (7): [2025-07-08 22:53:43] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:53:43+00:00 DEBUG (7): [2025-07-08 22:53:43] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:53:44+00:00 DEBUG (7): [2025-07-08 22:53:44] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:53:44+00:00 DEBUG (7): [2025-07-08 22:53:44] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] No files to backup for module: PFG_Analytics
2025-07-08T22:53:44+00:00 DEBUG (7): [2025-07-08 22:53:44] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:53:44+00:00 DEBUG (7): [2025-07-08 22:53:44] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Attempting to download pfg-analytics from master branch
2025-07-08T22:53:44+00:00 DEBUG (7): [2025-07-08 22:53:44] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-08T22:53:45+00:00 DEBUG (7): [2025-07-08 22:53:45] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752015224.zip (57135 bytes)
2025-07-08T22:53:45+00:00 DEBUG (7): [2025-07-08 22:53:45] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Exception: Module installation incomplete - missing required files in /magento/app/code/local/PFG/Core/Model/Installation.php:111 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":111,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(138): PFG_Core_Model_Installation->installModule('PFG Analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:56:14+00:00 DEBUG (7): [2025-07-08 22:56:14] [INFO] [SYSTEM] [PID:671] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:56:14+00:00 DEBUG (7): [2025-07-08 22:56:14] [INFO] [SYSTEM] [PID:671] [MEM:8 MB] Attempting to download pfg-analytics from master branch
2025-07-08T22:56:14+00:00 DEBUG (7): [2025-07-08 22:56:14] [INFO] [SYSTEM] [PID:671] [MEM:8 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-08T22:56:14+00:00 DEBUG (7): [2025-07-08 22:56:14] [INFO] [SYSTEM] [PID:671] [MEM:8 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752015374.zip (57135 bytes)
2025-07-08T22:57:45+00:00 DEBUG (7): [2025-07-08 22:57:45] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:57:45+00:00 DEBUG (7): [2025-07-08 22:57:45] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:57:45+00:00 DEBUG (7): [2025-07-08 22:57:45] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:57:46+00:00 DEBUG (7): [2025-07-08 22:57:46] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:57:46+00:00 DEBUG (7): [2025-07-08 22:57:46] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:57:46+00:00 DEBUG (7): [2025-07-08 22:57:46] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:57:46+00:00 DEBUG (7): [2025-07-08 22:57:46] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:57:46+00:00 DEBUG (7): [2025-07-08 22:57:46] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:57:46+00:00 DEBUG (7): [2025-07-08 22:57:46] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:57:47+00:00 DEBUG (7): [2025-07-08 22:57:47] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:57:47+00:00 DEBUG (7): [2025-07-08 22:57:47] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:57:48+00:00 DEBUG (7): [2025-07-08 22:57:48] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:57:48+00:00 DEBUG (7): [2025-07-08 22:57:48] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-08T22:57:48+00:00 DEBUG (7): [2025-07-08 22:57:48] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for builder, using master branch
2025-07-08T22:57:49+00:00 DEBUG (7): [2025-07-08 22:57:49] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:57:49+00:00 DEBUG (7): [2025-07-08 22:57:49] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:57:49+00:00 DEBUG (7): [2025-07-08 22:57:49] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:57:49+00:00 DEBUG (7): [2025-07-08 22:57:49] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:57:49+00:00 DEBUG (7): [2025-07-08 22:57:49] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:57:49+00:00 DEBUG (7): [2025-07-08 22:57:49] [WARNING] [SYSTEM] [PID:681] [MEM:8 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:57:50+00:00 DEBUG (7): [2025-07-08 22:57:50] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:57:50+00:00 DEBUG (7): [2025-07-08 22:57:50] [INFO] [SYSTEM] [PID:681] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:57:50+00:00 DEBUG (7): [2025-07-08 22:57:50] [ERROR] [SYSTEM] [PID:681] [MEM:8 MB] Exception: Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.5 GB ✓, Magento version: ******* ✓, Module PFG_Analytics is already installed and active, File conflict: /magento/app/etc/modules/PFG_Analytics.xml, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_LogReader, Module is already installed (version ), Repository is private ✓, Module is in PFG namespace ✓, Admin session required in /magento/app/code/local/PFG/Core/Model/Installation.php:73 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":73,"trace":"#0 \/magento\/test_analytics_install_fix.php(13): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 {main}","context":"Installation install failed for PFG_Analytics","user":"system","ip_address":false}
2025-07-08T22:59:21+00:00 DEBUG (7): [2025-07-08 22:59:21] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:59:21+00:00 DEBUG (7): [2025-07-08 22:59:21] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:59:21+00:00 DEBUG (7): [2025-07-08 22:59:21] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:59:21+00:00 DEBUG (7): [2025-07-08 22:59:21] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:59:21+00:00 DEBUG (7): [2025-07-08 22:59:21] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:59:22+00:00 DEBUG (7): [2025-07-08 22:59:22] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:59:22+00:00 DEBUG (7): [2025-07-08 22:59:22] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:59:22+00:00 DEBUG (7): [2025-07-08 22:59:22] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:59:22+00:00 DEBUG (7): [2025-07-08 22:59:22] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:59:23+00:00 DEBUG (7): [2025-07-08 22:59:23] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:59:42+00:00 DEBUG (7): [2025-07-08 22:59:42] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T22:59:42+00:00 DEBUG (7): [2025-07-08 22:59:42] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:59:43+00:00 DEBUG (7): [2025-07-08 22:59:43] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:59:43+00:00 DEBUG (7): [2025-07-08 22:59:43] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:59:43+00:00 DEBUG (7): [2025-07-08 22:59:43] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:59:43+00:00 DEBUG (7): [2025-07-08 22:59:43] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:59:43+00:00 DEBUG (7): [2025-07-08 22:59:43] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:59:43+00:00 DEBUG (7): [2025-07-08 22:59:43] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:59:44+00:00 DEBUG (7): [2025-07-08 22:59:44] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:59:44+00:00 DEBUG (7): [2025-07-08 22:59:44] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:59:44+00:00 DEBUG (7): [2025-07-08 22:59:44] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:59:45+00:00 DEBUG (7): [2025-07-08 22:59:45] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:59:45+00:00 DEBUG (7): [2025-07-08 22:59:45] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:59:46+00:00 DEBUG (7): [2025-07-08 22:59:46] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:59:46+00:00 DEBUG (7): [2025-07-08 22:59:46] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:59:46+00:00 DEBUG (7): [2025-07-08 22:59:46] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:59:46+00:00 DEBUG (7): [2025-07-08 22:59:46] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:59:46+00:00 DEBUG (7): [2025-07-08 22:59:46] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:59:46+00:00 DEBUG (7): [2025-07-08 22:59:46] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:59:47+00:00 DEBUG (7): [2025-07-08 22:59:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:59:47+00:00 DEBUG (7): [2025-07-08 22:59:47] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:59:47+00:00 DEBUG (7): [2025-07-08 22:59:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T22:59:48+00:00 DEBUG (7): [2025-07-08 22:59:48] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:59:48+00:00 DEBUG (7): [2025-07-08 22:59:48] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] No files to backup for module: PFG_CloudflareIntegration
2025-07-08T22:59:48+00:00 DEBUG (7): [2025-07-08 22:59:48] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:59:48+00:00 DEBUG (7): [2025-07-08 22:59:48] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T22:59:48+00:00 DEBUG (7): [2025-07-08 22:59:48] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T22:59:49+00:00 DEBUG (7): [2025-07-08 22:59:49] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752015588.zip (72654 bytes)
2025-07-08T22:59:52+00:00 DEBUG (7): [2025-07-08 22:59:52] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T22:59:52+00:00 DEBUG (7): [2025-07-08 22:59:52] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T22:59:52+00:00 DEBUG (7): [2025-07-08 22:59:52] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T22:59:52+00:00 DEBUG (7): [2025-07-08 22:59:52] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T22:59:52+00:00 DEBUG (7): [2025-07-08 22:59:52] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T22:59:53+00:00 DEBUG (7): [2025-07-08 22:59:53] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T22:59:53+00:00 DEBUG (7): [2025-07-08 22:59:53] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T22:59:53+00:00 DEBUG (7): [2025-07-08 22:59:53] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T22:59:53+00:00 DEBUG (7): [2025-07-08 22:59:53] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T22:59:54+00:00 DEBUG (7): [2025-07-08 22:59:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:05+00:00 DEBUG (7): [2025-07-08 23:00:05] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:05+00:00 DEBUG (7): [2025-07-08 23:00:05] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:05+00:00 DEBUG (7): [2025-07-08 23:00:05] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:06+00:00 DEBUG (7): [2025-07-08 23:00:06] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:06+00:00 DEBUG (7): [2025-07-08 23:00:06] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:06+00:00 DEBUG (7): [2025-07-08 23:00:06] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:06+00:00 DEBUG (7): [2025-07-08 23:00:06] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:07+00:00 DEBUG (7): [2025-07-08 23:00:07] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:07+00:00 DEBUG (7): [2025-07-08 23:00:07] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:07+00:00 DEBUG (7): [2025-07-08 23:00:07] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:14+00:00 DEBUG (7): [2025-07-08 23:00:14] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T23:00:15+00:00 DEBUG (7): [2025-07-08 23:00:15] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:15+00:00 DEBUG (7): [2025-07-08 23:00:15] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:15+00:00 DEBUG (7): [2025-07-08 23:00:15] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:16+00:00 DEBUG (7): [2025-07-08 23:00:16] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:16+00:00 DEBUG (7): [2025-07-08 23:00:16] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:16+00:00 DEBUG (7): [2025-07-08 23:00:16] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:16+00:00 DEBUG (7): [2025-07-08 23:00:16] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:17+00:00 DEBUG (7): [2025-07-08 23:00:17] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:17+00:00 DEBUG (7): [2025-07-08 23:00:17] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:17+00:00 DEBUG (7): [2025-07-08 23:00:17] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:17+00:00 DEBUG (7): [2025-07-08 23:00:17] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:18+00:00 DEBUG (7): [2025-07-08 23:00:18] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:18+00:00 DEBUG (7): [2025-07-08 23:00:18] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:18+00:00 DEBUG (7): [2025-07-08 23:00:18] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:19+00:00 DEBUG (7): [2025-07-08 23:00:19] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:19+00:00 DEBUG (7): [2025-07-08 23:00:19] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:19+00:00 DEBUG (7): [2025-07-08 23:00:19] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:19+00:00 DEBUG (7): [2025-07-08 23:00:19] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:20+00:00 DEBUG (7): [2025-07-08 23:00:20] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:20+00:00 DEBUG (7): [2025-07-08 23:00:20] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:20+00:00 DEBUG (7): [2025-07-08 23:00:20] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:21+00:00 DEBUG (7): [2025-07-08 23:00:21] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:21+00:00 DEBUG (7): [2025-07-08 23:00:21] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Created backup for PFG_Analytics: PFG_Analytics_installation_2025-07-08_23-00-21.tar.gz (5 KB)
2025-07-08T23:00:21+00:00 DEBUG (7): [2025-07-08 23:00:21] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:21+00:00 DEBUG (7): [2025-07-08 23:00:21] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Attempting to download pfg-analytics from master branch
2025-07-08T23:00:21+00:00 DEBUG (7): [2025-07-08 23:00:21] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-08T23:00:22+00:00 DEBUG (7): [2025-07-08 23:00:22] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752015621.zip (57135 bytes)
2025-07-08T23:00:25+00:00 DEBUG (7): [2025-07-08 23:00:25] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:25+00:00 DEBUG (7): [2025-07-08 23:00:25] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:25+00:00 DEBUG (7): [2025-07-08 23:00:25] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:26+00:00 DEBUG (7): [2025-07-08 23:00:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:26+00:00 DEBUG (7): [2025-07-08 23:00:26] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:26+00:00 DEBUG (7): [2025-07-08 23:00:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:26+00:00 DEBUG (7): [2025-07-08 23:00:26] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:26+00:00 DEBUG (7): [2025-07-08 23:00:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:26+00:00 DEBUG (7): [2025-07-08 23:00:26] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:27+00:00 DEBUG (7): [2025-07-08 23:00:27] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:34+00:00 DEBUG (7): [2025-07-08 23:00:34] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T23:00:34+00:00 DEBUG (7): [2025-07-08 23:00:34] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:35+00:00 DEBUG (7): [2025-07-08 23:00:35] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:35+00:00 DEBUG (7): [2025-07-08 23:00:35] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:35+00:00 DEBUG (7): [2025-07-08 23:00:35] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:35+00:00 DEBUG (7): [2025-07-08 23:00:35] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:35+00:00 DEBUG (7): [2025-07-08 23:00:35] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:35+00:00 DEBUG (7): [2025-07-08 23:00:35] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:36+00:00 DEBUG (7): [2025-07-08 23:00:36] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:36+00:00 DEBUG (7): [2025-07-08 23:00:36] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:36+00:00 DEBUG (7): [2025-07-08 23:00:36] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:37+00:00 DEBUG (7): [2025-07-08 23:00:37] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:37+00:00 DEBUG (7): [2025-07-08 23:00:37] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:37+00:00 DEBUG (7): [2025-07-08 23:00:37] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:37+00:00 DEBUG (7): [2025-07-08 23:00:37] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:38+00:00 DEBUG (7): [2025-07-08 23:00:38] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:38+00:00 DEBUG (7): [2025-07-08 23:00:38] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:38+00:00 DEBUG (7): [2025-07-08 23:00:38] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:38+00:00 DEBUG (7): [2025-07-08 23:00:38] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:39+00:00 DEBUG (7): [2025-07-08 23:00:39] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:39+00:00 DEBUG (7): [2025-07-08 23:00:39] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:39+00:00 DEBUG (7): [2025-07-08 23:00:39] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:00:40+00:00 DEBUG (7): [2025-07-08 23:00:40] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:40+00:00 DEBUG (7): [2025-07-08 23:00:40] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] No files to backup for module: PFG_CloudflareIntegration
2025-07-08T23:00:40+00:00 DEBUG (7): [2025-07-08 23:00:40] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:40+00:00 DEBUG (7): [2025-07-08 23:00:40] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Attempting to download cloudflare-integration from master branch
2025-07-08T23:00:40+00:00 DEBUG (7): [2025-07-08 23:00:40] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-08T23:00:41+00:00 DEBUG (7): [2025-07-08 23:00:41] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752015640.zip (72654 bytes)
2025-07-08T23:00:44+00:00 DEBUG (7): [2025-07-08 23:00:44] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:00:44+00:00 DEBUG (7): [2025-07-08 23:00:44] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:00:44+00:00 DEBUG (7): [2025-07-08 23:00:44] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:00:45+00:00 DEBUG (7): [2025-07-08 23:00:45] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:00:45+00:00 DEBUG (7): [2025-07-08 23:00:45] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:00:45+00:00 DEBUG (7): [2025-07-08 23:00:45] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:00:45+00:00 DEBUG (7): [2025-07-08 23:00:45] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:00:45+00:00 DEBUG (7): [2025-07-08 23:00:45] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:00:45+00:00 DEBUG (7): [2025-07-08 23:00:45] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:00:46+00:00 DEBUG (7): [2025-07-08 23:00:46] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:02:54+00:00 DEBUG (7): [2025-07-08 23:02:54] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:02:54+00:00 DEBUG (7): [2025-07-08 23:02:54] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:02:54+00:00 DEBUG (7): [2025-07-08 23:02:54] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:02:55+00:00 DEBUG (7): [2025-07-08 23:02:55] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:02:55+00:00 DEBUG (7): [2025-07-08 23:02:55] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:02:55+00:00 DEBUG (7): [2025-07-08 23:02:55] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:02:55+00:00 DEBUG (7): [2025-07-08 23:02:55] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:02:56+00:00 DEBUG (7): [2025-07-08 23:02:56] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:02:56+00:00 DEBUG (7): [2025-07-08 23:02:56] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:02:56+00:00 DEBUG (7): [2025-07-08 23:02:56] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:03:05+00:00 DEBUG (7): [2025-07-08 23:03:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Starting uninstall of module: PFG_LogReader
2025-07-08T23:03:05+00:00 DEBUG (7): [2025-07-08 23:03:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Created backup for PFG_LogReader: PFG_LogReader_uninstall_2025-07-08_23-03-05.tar.gz (83 KB)
2025-07-08T23:03:05+00:00 DEBUG (7): [2025-07-08 23:03:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Backup created with ID: 8
2025-07-08T23:03:05+00:00 DEBUG (7): [2025-07-08 23:03:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Module PFG_LogReader uninstalled successfully
2025-07-08T23:03:08+00:00 DEBUG (7): [2025-07-08 23:03:08] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:03:08+00:00 DEBUG (7): [2025-07-08 23:03:08] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:03:08+00:00 DEBUG (7): [2025-07-08 23:03:08] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:03:09+00:00 DEBUG (7): [2025-07-08 23:03:09] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:03:09+00:00 DEBUG (7): [2025-07-08 23:03:09] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:03:09+00:00 DEBUG (7): [2025-07-08 23:03:09] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:03:09+00:00 DEBUG (7): [2025-07-08 23:03:09] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:03:10+00:00 DEBUG (7): [2025-07-08 23:03:10] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:03:10+00:00 DEBUG (7): [2025-07-08 23:03:10] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:03:10+00:00 DEBUG (7): [2025-07-08 23:03:10] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:03:21+00:00 DEBUG (7): [2025-07-08 23:03:21] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:03:21+00:00 DEBUG (7): [2025-07-08 23:03:21] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:03:21+00:00 DEBUG (7): [2025-07-08 23:03:21] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:03:21+00:00 DEBUG (7): [2025-07-08 23:03:21] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:03:21+00:00 DEBUG (7): [2025-07-08 23:03:21] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:03:22+00:00 DEBUG (7): [2025-07-08 23:03:22] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:03:22+00:00 DEBUG (7): [2025-07-08 23:03:22] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:03:22+00:00 DEBUG (7): [2025-07-08 23:03:22] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:03:22+00:00 DEBUG (7): [2025-07-08 23:03:22] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:03:23+00:00 DEBUG (7): [2025-07-08 23:03:23] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:03:44+00:00 DEBUG (7): [2025-07-08 23:03:44] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Admin action: rollback by user martinpfg from IP **********
2025-07-08T23:03:44+00:00 DEBUG (7): [2025-07-08 23:03:44] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Restored backup: /magento/var/pfg_core/backups/PFG_Analytics_uninstall_2025-07-08_22-53-02.tar.gz
2025-07-08T23:03:44+00:00 DEBUG (7): [2025-07-08 23:03:44] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Successfully rolled back installation 11
2025-07-08T23:04:09+00:00 DEBUG (7): [2025-07-08 23:04:09] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Cleaned up 0 old backups, freed 0 B
2025-07-08T23:05:37+00:00 DEBUG (7): [2025-07-08 23:05:37] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T23:05:37+00:00 DEBUG (7): [2025-07-08 23:05:37] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:05:37+00:00 DEBUG (7): [2025-07-08 23:05:37] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:05:37+00:00 DEBUG (7): [2025-07-08 23:05:37] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:05:38+00:00 DEBUG (7): [2025-07-08 23:05:38] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:05:38+00:00 DEBUG (7): [2025-07-08 23:05:38] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:05:38+00:00 DEBUG (7): [2025-07-08 23:05:38] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:05:38+00:00 DEBUG (7): [2025-07-08 23:05:38] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:05:39+00:00 DEBUG (7): [2025-07-08 23:05:39] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:05:39+00:00 DEBUG (7): [2025-07-08 23:05:39] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:05:39+00:00 DEBUG (7): [2025-07-08 23:05:39] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:05:40+00:00 DEBUG (7): [2025-07-08 23:05:40] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:05:40+00:00 DEBUG (7): [2025-07-08 23:05:40] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:05:40+00:00 DEBUG (7): [2025-07-08 23:05:40] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:05:40+00:00 DEBUG (7): [2025-07-08 23:05:40] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:05:41+00:00 DEBUG (7): [2025-07-08 23:05:41] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:05:41+00:00 DEBUG (7): [2025-07-08 23:05:41] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:05:41+00:00 DEBUG (7): [2025-07-08 23:05:41] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:05:41+00:00 DEBUG (7): [2025-07-08 23:05:41] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:05:42+00:00 DEBUG (7): [2025-07-08 23:05:42] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:05:42+00:00 DEBUG (7): [2025-07-08 23:05:42] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:05:42+00:00 DEBUG (7): [2025-07-08 23:05:42] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:05:43+00:00 DEBUG (7): [2025-07-08 23:05:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:05:43+00:00 DEBUG (7): [2025-07-08 23:05:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] No files to backup for module: PFG_Altcurrency
2025-07-08T23:05:43+00:00 DEBUG (7): [2025-07-08 23:05:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:05:43+00:00 DEBUG (7): [2025-07-08 23:05:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Attempting to download pfg-altcurrency from master branch
2025-07-08T23:05:43+00:00 DEBUG (7): [2025-07-08 23:05:43] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Downloading repository pfg-altcurrency (master) from https://bitbucket.org/pfg/pfg-altcurrency/get/master.zip
2025-07-08T23:05:44+00:00 DEBUG (7): [2025-07-08 23:05:44] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Successfully downloaded repository pfg-altcurrency (master) to /magento/var/pfg_core/temp/pfg-altcurrency-master-1752015943.zip (10875 bytes)
2025-07-08T23:05:46+00:00 DEBUG (7): [2025-07-08 23:05:46] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:05:47+00:00 DEBUG (7): [2025-07-08 23:05:47] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:05:47+00:00 DEBUG (7): [2025-07-08 23:05:47] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:05:47+00:00 DEBUG (7): [2025-07-08 23:05:47] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:05:47+00:00 DEBUG (7): [2025-07-08 23:05:47] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:05:48+00:00 DEBUG (7): [2025-07-08 23:05:48] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:05:48+00:00 DEBUG (7): [2025-07-08 23:05:48] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:05:48+00:00 DEBUG (7): [2025-07-08 23:05:48] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:05:48+00:00 DEBUG (7): [2025-07-08 23:05:48] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:05:48+00:00 DEBUG (7): [2025-07-08 23:05:48] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:05:59+00:00 DEBUG (7): [2025-07-08 23:05:59] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:05:59+00:00 DEBUG (7): [2025-07-08 23:05:59] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:05:59+00:00 DEBUG (7): [2025-07-08 23:05:59] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:06:00+00:00 DEBUG (7): [2025-07-08 23:06:00] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:06:00+00:00 DEBUG (7): [2025-07-08 23:06:00] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:06:00+00:00 DEBUG (7): [2025-07-08 23:06:00] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:06:00+00:00 DEBUG (7): [2025-07-08 23:06:00] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:06:00+00:00 DEBUG (7): [2025-07-08 23:06:00] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:06:00+00:00 DEBUG (7): [2025-07-08 23:06:00] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:06:01+00:00 DEBUG (7): [2025-07-08 23:06:01] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:28+00:00 DEBUG (7): [2025-07-08 23:27:28] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:27:28+00:00 DEBUG (7): [2025-07-08 23:27:28] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:27:28+00:00 DEBUG (7): [2025-07-08 23:27:28] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:27:28+00:00 DEBUG (7): [2025-07-08 23:27:28] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:27:28+00:00 DEBUG (7): [2025-07-08 23:27:28] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:27:29+00:00 DEBUG (7): [2025-07-08 23:27:29] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:27:29+00:00 DEBUG (7): [2025-07-08 23:27:29] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:27:29+00:00 DEBUG (7): [2025-07-08 23:27:29] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:27:29+00:00 DEBUG (7): [2025-07-08 23:27:29] [WARNING] [SYSTEM] [PID:16] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:27:30+00:00 DEBUG (7): [2025-07-08 23:27:30] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:37+00:00 DEBUG (7): [2025-07-08 23:27:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Admin action: rollback by user martinpfg from IP **********
2025-07-08T23:27:37+00:00 DEBUG (7): [2025-07-08 23:27:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Starting complete removal of module: PFG_Analytics
2025-07-08T23:27:37+00:00 DEBUG (7): [2025-07-08 23:27:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Created backup for PFG_Analytics: PFG_Analytics_rollback_2025-07-08_23-27-37.tar.gz (6 KB)
2025-07-08T23:27:37+00:00 DEBUG (7): [2025-07-08 23:27:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Backup created with ID: 9
2025-07-08T23:27:37+00:00 DEBUG (7): [2025-07-08 23:27:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Module PFG_Analytics completely removed successfully
2025-07-08T23:27:40+00:00 DEBUG (7): [2025-07-08 23:27:40] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:27:40+00:00 DEBUG (7): [2025-07-08 23:27:40] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:27:40+00:00 DEBUG (7): [2025-07-08 23:27:40] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:27:41+00:00 DEBUG (7): [2025-07-08 23:27:41] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:27:41+00:00 DEBUG (7): [2025-07-08 23:27:41] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:27:41+00:00 DEBUG (7): [2025-07-08 23:27:41] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:27:41+00:00 DEBUG (7): [2025-07-08 23:27:41] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:27:42+00:00 DEBUG (7): [2025-07-08 23:27:42] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:27:42+00:00 DEBUG (7): [2025-07-08 23:27:42] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:27:42+00:00 DEBUG (7): [2025-07-08 23:27:42] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:52+00:00 DEBUG (7): [2025-07-08 23:27:52] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-08T23:27:52+00:00 DEBUG (7): [2025-07-08 23:27:52] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:27:53+00:00 DEBUG (7): [2025-07-08 23:27:53] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:27:53+00:00 DEBUG (7): [2025-07-08 23:27:53] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:27:53+00:00 DEBUG (7): [2025-07-08 23:27:53] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:27:53+00:00 DEBUG (7): [2025-07-08 23:27:53] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:27:53+00:00 DEBUG (7): [2025-07-08 23:27:53] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:27:53+00:00 DEBUG (7): [2025-07-08 23:27:53] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:27:54+00:00 DEBUG (7): [2025-07-08 23:27:54] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:27:54+00:00 DEBUG (7): [2025-07-08 23:27:54] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:27:54+00:00 DEBUG (7): [2025-07-08 23:27:54] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:55+00:00 DEBUG (7): [2025-07-08 23:27:55] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:55+00:00 DEBUG (7): [2025-07-08 23:27:55] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:27:56+00:00 DEBUG (7): [2025-07-08 23:27:56] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:27:56+00:00 DEBUG (7): [2025-07-08 23:27:56] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:27:56+00:00 DEBUG (7): [2025-07-08 23:27:56] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:27:56+00:00 DEBUG (7): [2025-07-08 23:27:56] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:27:56+00:00 DEBUG (7): [2025-07-08 23:27:56] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:27:56+00:00 DEBUG (7): [2025-07-08 23:27:56] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:27:57+00:00 DEBUG (7): [2025-07-08 23:27:57] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:27:57+00:00 DEBUG (7): [2025-07-08 23:27:57] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:27:57+00:00 DEBUG (7): [2025-07-08 23:27:57] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:58+00:00 DEBUG (7): [2025-07-08 23:27:58] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:27:58+00:00 DEBUG (7): [2025-07-08 23:27:58] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] No files to backup for module: PFG_LogReader
2025-07-08T23:27:58+00:00 DEBUG (7): [2025-07-08 23:27:58] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:27:58+00:00 DEBUG (7): [2025-07-08 23:27:58] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-08T23:27:58+00:00 DEBUG (7): [2025-07-08 23:27:58] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-08T23:27:59+00:00 DEBUG (7): [2025-07-08 23:27:59] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752017278.zip (32123 bytes)
2025-07-08T23:28:02+00:00 DEBUG (7): [2025-07-08 23:28:02] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:28:02+00:00 DEBUG (7): [2025-07-08 23:28:02] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:28:02+00:00 DEBUG (7): [2025-07-08 23:28:02] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:28:02+00:00 DEBUG (7): [2025-07-08 23:28:02] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:28:02+00:00 DEBUG (7): [2025-07-08 23:28:02] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:28:03+00:00 DEBUG (7): [2025-07-08 23:28:03] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:28:03+00:00 DEBUG (7): [2025-07-08 23:28:03] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:28:03+00:00 DEBUG (7): [2025-07-08 23:28:03] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:28:03+00:00 DEBUG (7): [2025-07-08 23:28:03] [WARNING] [SYSTEM] [PID:12] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:28:04+00:00 DEBUG (7): [2025-07-08 23:28:04] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:28:20+00:00 DEBUG (7): [2025-07-08 23:28:20] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:28:20+00:00 DEBUG (7): [2025-07-08 23:28:20] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:28:20+00:00 DEBUG (7): [2025-07-08 23:28:20] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:28:21+00:00 DEBUG (7): [2025-07-08 23:28:21] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:28:21+00:00 DEBUG (7): [2025-07-08 23:28:21] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:28:21+00:00 DEBUG (7): [2025-07-08 23:28:21] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:28:21+00:00 DEBUG (7): [2025-07-08 23:28:21] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:28:22+00:00 DEBUG (7): [2025-07-08 23:28:22] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:28:22+00:00 DEBUG (7): [2025-07-08 23:28:22] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:28:22+00:00 DEBUG (7): [2025-07-08 23:28:22] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Admin action: rollback by user martinpfg from IP **********
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Starting complete removal of module: PFG_LogReader
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Created backup for PFG_LogReader: PFG_LogReader_rollback_2025-07-08_23-28-28.tar.gz (83 KB)
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Backup created with ID: 10
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Module PFG_LogReader completely removed successfully
2025-07-08T23:28:31+00:00 DEBUG (7): [2025-07-08 23:28:31] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:28:31+00:00 DEBUG (7): [2025-07-08 23:28:31] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:28:31+00:00 DEBUG (7): [2025-07-08 23:28:31] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:28:31+00:00 DEBUG (7): [2025-07-08 23:28:31] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:28:31+00:00 DEBUG (7): [2025-07-08 23:28:31] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:28:32+00:00 DEBUG (7): [2025-07-08 23:28:32] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:28:32+00:00 DEBUG (7): [2025-07-08 23:28:32] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:28:32+00:00 DEBUG (7): [2025-07-08 23:28:32] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:28:32+00:00 DEBUG (7): [2025-07-08 23:28:32] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:28:33+00:00 DEBUG (7): [2025-07-08 23:28:33] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:28:37+00:00 DEBUG (7): [2025-07-08 23:28:37] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:28:37+00:00 DEBUG (7): [2025-07-08 23:28:37] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:28:37+00:00 DEBUG (7): [2025-07-08 23:28:37] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:28:37+00:00 DEBUG (7): [2025-07-08 23:28:37] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:28:37+00:00 DEBUG (7): [2025-07-08 23:28:37] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:28:38+00:00 DEBUG (7): [2025-07-08 23:28:38] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:28:38+00:00 DEBUG (7): [2025-07-08 23:28:38] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:28:38+00:00 DEBUG (7): [2025-07-08 23:28:38] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:28:38+00:00 DEBUG (7): [2025-07-08 23:28:38] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:28:39+00:00 DEBUG (7): [2025-07-08 23:28:39] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:45:57+00:00 DEBUG (7): [2025-07-08 23:45:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:45:57+00:00 DEBUG (7): [2025-07-08 23:45:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:45:57+00:00 DEBUG (7): [2025-07-08 23:45:57] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:45:58+00:00 DEBUG (7): [2025-07-08 23:45:58] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:45:58+00:00 DEBUG (7): [2025-07-08 23:45:58] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:45:58+00:00 DEBUG (7): [2025-07-08 23:45:58] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:45:58+00:00 DEBUG (7): [2025-07-08 23:45:58] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:45:59+00:00 DEBUG (7): [2025-07-08 23:45:59] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:45:59+00:00 DEBUG (7): [2025-07-08 23:45:59] [WARNING] [SYSTEM] [PID:11] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:45:59+00:00 DEBUG (7): [2025-07-08 23:45:59] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:47:24+00:00 DEBUG (7): [2025-07-08 23:47:24] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:47:25+00:00 DEBUG (7): [2025-07-08 23:47:25] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:47:25+00:00 DEBUG (7): [2025-07-08 23:47:25] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:47:25+00:00 DEBUG (7): [2025-07-08 23:47:25] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:47:25+00:00 DEBUG (7): [2025-07-08 23:47:25] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:47:26+00:00 DEBUG (7): [2025-07-08 23:47:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:47:26+00:00 DEBUG (7): [2025-07-08 23:47:26] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:47:26+00:00 DEBUG (7): [2025-07-08 23:47:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:47:26+00:00 DEBUG (7): [2025-07-08 23:47:26] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:47:27+00:00 DEBUG (7): [2025-07-08 23:47:27] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:48:38+00:00 DEBUG (7): [2025-07-08 23:48:38] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:48:39+00:00 DEBUG (7): [2025-07-08 23:48:39] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:48:39+00:00 DEBUG (7): [2025-07-08 23:48:39] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:48:39+00:00 DEBUG (7): [2025-07-08 23:48:39] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:48:39+00:00 DEBUG (7): [2025-07-08 23:48:39] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:48:39+00:00 DEBUG (7): [2025-07-08 23:48:39] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:48:39+00:00 DEBUG (7): [2025-07-08 23:48:39] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:48:40+00:00 DEBUG (7): [2025-07-08 23:48:40] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:48:40+00:00 DEBUG (7): [2025-07-08 23:48:40] [WARNING] [SYSTEM] [PID:15] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:48:40+00:00 DEBUG (7): [2025-07-08 23:48:40] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:49:36+00:00 DEBUG (7): [2025-07-08 23:49:36] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:49:36+00:00 DEBUG (7): [2025-07-08 23:49:36] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:49:36+00:00 DEBUG (7): [2025-07-08 23:49:36] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:49:37+00:00 DEBUG (7): [2025-07-08 23:49:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:49:37+00:00 DEBUG (7): [2025-07-08 23:49:37] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:49:37+00:00 DEBUG (7): [2025-07-08 23:49:37] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:49:37+00:00 DEBUG (7): [2025-07-08 23:49:37] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:49:38+00:00 DEBUG (7): [2025-07-08 23:49:38] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:49:38+00:00 DEBUG (7): [2025-07-08 23:49:38] [WARNING] [SYSTEM] [PID:17] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:49:38+00:00 DEBUG (7): [2025-07-08 23:49:38] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:51:35+00:00 DEBUG (7): [2025-07-08 23:51:35] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:51:35+00:00 DEBUG (7): [2025-07-08 23:51:35] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:51:35+00:00 DEBUG (7): [2025-07-08 23:51:35] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:51:36+00:00 DEBUG (7): [2025-07-08 23:51:36] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:51:36+00:00 DEBUG (7): [2025-07-08 23:51:36] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:51:36+00:00 DEBUG (7): [2025-07-08 23:51:36] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:51:36+00:00 DEBUG (7): [2025-07-08 23:51:36] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:51:37+00:00 DEBUG (7): [2025-07-08 23:51:37] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:51:37+00:00 DEBUG (7): [2025-07-08 23:51:37] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:51:37+00:00 DEBUG (7): [2025-07-08 23:51:37] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-08T23:52:00+00:00 DEBUG (7): [2025-07-08 23:52:00] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-08T23:52:00+00:00 DEBUG (7): [2025-07-08 23:52:00] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-08T23:52:00+00:00 DEBUG (7): [2025-07-08 23:52:00] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-08T23:52:01+00:00 DEBUG (7): [2025-07-08 23:52:01] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-08T23:52:01+00:00 DEBUG (7): [2025-07-08 23:52:01] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-08T23:52:01+00:00 DEBUG (7): [2025-07-08 23:52:01] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-08T23:52:01+00:00 DEBUG (7): [2025-07-08 23:52:01] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-08T23:52:02+00:00 DEBUG (7): [2025-07-08 23:52:02] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-08T23:52:02+00:00 DEBUG (7): [2025-07-08 23:52:02] [WARNING] [SYSTEM] [PID:18] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-08T23:52:02+00:00 DEBUG (7): [2025-07-08 23:52:02] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:14:42+00:00 DEBUG (7): [2025-07-09 00:14:42] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:14:43+00:00 DEBUG (7): [2025-07-09 00:14:43] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:14:43+00:00 DEBUG (7): [2025-07-09 00:14:43] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-09T00:14:43+00:00 DEBUG (7): [2025-07-09 00:14:43] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:14:43+00:00 DEBUG (7): [2025-07-09 00:14:43] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-09T00:14:44+00:00 DEBUG (7): [2025-07-09 00:14:44] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:14:44+00:00 DEBUG (7): [2025-07-09 00:14:44] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-09T00:14:44+00:00 DEBUG (7): [2025-07-09 00:14:44] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:14:44+00:00 DEBUG (7): [2025-07-09 00:14:44] [WARNING] [SYSTEM] [PID:10] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-09T00:14:45+00:00 DEBUG (7): [2025-07-09 00:14:45] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:14:50+00:00 DEBUG (7): [2025-07-09 00:14:50] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Connection test error: Bitbucket App Password appears to be invalid (too short)
2025-07-09T00:17:12+00:00 DEBUG (7): [2025-07-09 00:17:12] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:17:13+00:00 DEBUG (7): [2025-07-09 00:17:13] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:17:13+00:00 DEBUG (7): [2025-07-09 00:17:13] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-09T00:17:13+00:00 DEBUG (7): [2025-07-09 00:17:13] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:17:13+00:00 DEBUG (7): [2025-07-09 00:17:13] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-09T00:17:14+00:00 DEBUG (7): [2025-07-09 00:17:14] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:17:14+00:00 DEBUG (7): [2025-07-09 00:17:14] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-09T00:17:14+00:00 DEBUG (7): [2025-07-09 00:17:14] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:17:14+00:00 DEBUG (7): [2025-07-09 00:17:14] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-09T00:17:14+00:00 DEBUG (7): [2025-07-09 00:17:14] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [WARNING] [SYSTEM] [PID:9] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-09T00:18:54+00:00 DEBUG (7): [2025-07-09 00:18:54] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [WARNING] [SYSTEM] [PID:13] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-09T00:19:03+00:00 DEBUG (7): [2025-07-09 00:19:03] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for builder, using master branch
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for cloudflare-integration, using master branch
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-altcurrency, using master branch
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [WARNING] [SYSTEM] [PID:14] [MEM:2 MB] Tags not available for pfg-analytics, using master branch
2025-07-09T00:19:06+00:00 DEBUG (7): [2025-07-09 00:19:06] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:34:17+00:00 DEBUG (7): [2025-07-09 00:34:17] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:34:18+00:00 DEBUG (7): [2025-07-09 00:34:18] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:34:18+00:00 DEBUG (7): [2025-07-09 00:34:18] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:34:18+00:00 DEBUG (7): [2025-07-09 00:34:18] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:34:19+00:00 DEBUG (7): [2025-07-09 00:34:19] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:34:19+00:00 DEBUG (7): [2025-07-09 00:34:19] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:40:26+00:00 DEBUG (7): [2025-07-09 00:40:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:40:26+00:00 DEBUG (7): [2025-07-09 00:40:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:40:26+00:00 DEBUG (7): [2025-07-09 00:40:26] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:40:27+00:00 DEBUG (7): [2025-07-09 00:40:27] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:40:27+00:00 DEBUG (7): [2025-07-09 00:40:27] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:40:28+00:00 DEBUG (7): [2025-07-09 00:40:28] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:42:19+00:00 DEBUG (7): [2025-07-09 00:42:19] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:42:19+00:00 DEBUG (7): [2025-07-09 00:42:19] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:42:19+00:00 DEBUG (7): [2025-07-09 00:42:19] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:42:20+00:00 DEBUG (7): [2025-07-09 00:42:20] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:42:20+00:00 DEBUG (7): [2025-07-09 00:42:20] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:42:21+00:00 DEBUG (7): [2025-07-09 00:42:21] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:46:53+00:00 DEBUG (7): [2025-07-09 00:46:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:46:53+00:00 DEBUG (7): [2025-07-09 00:46:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:46:53+00:00 DEBUG (7): [2025-07-09 00:46:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:46:53+00:00 DEBUG (7): [2025-07-09 00:46:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:46:53+00:00 DEBUG (7): [2025-07-09 00:46:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:46:53+00:00 DEBUG (7): [2025-07-09 00:46:53] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:46:56+00:00 DEBUG (7): [2025-07-09 00:46:56] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:46:56+00:00 DEBUG (7): [2025-07-09 00:46:56] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:46:56+00:00 DEBUG (7): [2025-07-09 00:46:56] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:46:56+00:00 DEBUG (7): [2025-07-09 00:46:56] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:46:56+00:00 DEBUG (7): [2025-07-09 00:46:56] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:46:56+00:00 DEBUG (7): [2025-07-09 00:46:56] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:51:25+00:00 DEBUG (7): [2025-07-09 00:51:25] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:51:25+00:00 DEBUG (7): [2025-07-09 00:51:25] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:51:25+00:00 DEBUG (7): [2025-07-09 00:51:25] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:51:25+00:00 DEBUG (7): [2025-07-09 00:51:25] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:51:25+00:00 DEBUG (7): [2025-07-09 00:51:25] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:51:25+00:00 DEBUG (7): [2025-07-09 00:51:25] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:51:47+00:00 DEBUG (7): [2025-07-09 00:51:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:51:47+00:00 DEBUG (7): [2025-07-09 00:51:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:51:47+00:00 DEBUG (7): [2025-07-09 00:51:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:51:47+00:00 DEBUG (7): [2025-07-09 00:51:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:51:47+00:00 DEBUG (7): [2025-07-09 00:51:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:51:47+00:00 DEBUG (7): [2025-07-09 00:51:47] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:51:49+00:00 DEBUG (7): [2025-07-09 00:51:49] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:51:49+00:00 DEBUG (7): [2025-07-09 00:51:49] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:51:49+00:00 DEBUG (7): [2025-07-09 00:51:49] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:51:49+00:00 DEBUG (7): [2025-07-09 00:51:49] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:51:49+00:00 DEBUG (7): [2025-07-09 00:51:49] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:51:49+00:00 DEBUG (7): [2025-07-09 00:51:49] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:53:05+00:00 DEBUG (7): [2025-07-09 00:53:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:53:05+00:00 DEBUG (7): [2025-07-09 00:53:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:53:05+00:00 DEBUG (7): [2025-07-09 00:53:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:53:05+00:00 DEBUG (7): [2025-07-09 00:53:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:53:05+00:00 DEBUG (7): [2025-07-09 00:53:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:53:05+00:00 DEBUG (7): [2025-07-09 00:53:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:53:51+00:00 DEBUG (7): [2025-07-09 00:53:51] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:53:51+00:00 DEBUG (7): [2025-07-09 00:53:51] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:53:51+00:00 DEBUG (7): [2025-07-09 00:53:51] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:53:51+00:00 DEBUG (7): [2025-07-09 00:53:51] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:53:51+00:00 DEBUG (7): [2025-07-09 00:53:51] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:53:51+00:00 DEBUG (7): [2025-07-09 00:53:51] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T00:55:57+00:00 DEBUG (7): [2025-07-09 00:55:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T00:55:57+00:00 DEBUG (7): [2025-07-09 00:55:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T00:55:57+00:00 DEBUG (7): [2025-07-09 00:55:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T00:55:57+00:00 DEBUG (7): [2025-07-09 00:55:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T00:55:57+00:00 DEBUG (7): [2025-07-09 00:55:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T00:55:57+00:00 DEBUG (7): [2025-07-09 00:55:57] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T01:06:41+00:00 DEBUG (7): [2025-07-09 01:06:41] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T01:06:41+00:00 DEBUG (7): [2025-07-09 01:06:41] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T01:06:41+00:00 DEBUG (7): [2025-07-09 01:06:41] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T01:06:42+00:00 DEBUG (7): [2025-07-09 01:06:42] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T01:06:42+00:00 DEBUG (7): [2025-07-09 01:06:42] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T01:06:43+00:00 DEBUG (7): [2025-07-09 01:06:43] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:09:42+00:00 DEBUG (7): [2025-07-09 06:09:42] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:09:42+00:00 DEBUG (7): [2025-07-09 06:09:42] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T06:09:42+00:00 DEBUG (7): [2025-07-09 06:09:42] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:09:43+00:00 DEBUG (7): [2025-07-09 06:09:43] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:09:43+00:00 DEBUG (7): [2025-07-09 06:09:43] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:09:44+00:00 DEBUG (7): [2025-07-09 06:09:44] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:10:16+00:00 DEBUG (7): [2025-07-09 06:10:16] [INFO] [SYSTEM] [PID:9] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:10:16+00:00 DEBUG (7): [2025-07-09 06:10:16] [ERROR] [SYSTEM] [PID:9] [MEM:2 MB] Install module error: Repository name can only contain letters, numbers, hyphens, and underscores
2025-07-09T06:10:23+00:00 DEBUG (7): [2025-07-09 06:10:23] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:10:23+00:00 DEBUG (7): [2025-07-09 06:10:23] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Install module error: Repository name can only contain letters, numbers, hyphens, and underscores
2025-07-09T06:15:18+00:00 DEBUG (7): [2025-07-09 06:15:18] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:15:19+00:00 DEBUG (7): [2025-07-09 06:15:19] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T06:15:19+00:00 DEBUG (7): [2025-07-09 06:15:19] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:15:19+00:00 DEBUG (7): [2025-07-09 06:15:19] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:15:20+00:00 DEBUG (7): [2025-07-09 06:15:20] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:15:20+00:00 DEBUG (7): [2025-07-09 06:15:20] [INFO] [SYSTEM] [PID:17] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:15:43+00:00 DEBUG (7): [2025-07-09 06:15:43] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:15:43+00:00 DEBUG (7): [2025-07-09 06:15:43] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Exception: Installation cannot proceed:  in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:20:14+00:00 DEBUG (7): [2025-07-09 06:20:14] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:20:14+00:00 DEBUG (7): [2025-07-09 06:20:14] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T06:20:14+00:00 DEBUG (7): [2025-07-09 06:20:14] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:20:15+00:00 DEBUG (7): [2025-07-09 06:20:15] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:20:15+00:00 DEBUG (7): [2025-07-09 06:20:15] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:20:16+00:00 DEBUG (7): [2025-07-09 06:20:16] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:21:07+00:00 DEBUG (7): [2025-07-09 06:21:07] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:21:07+00:00 DEBUG (7): [2025-07-09 06:21:07] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Exception: Installation cannot proceed:  in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:26:03+00:00 DEBUG (7): [2025-07-09 06:26:03] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:26:03+00:00 DEBUG (7): [2025-07-09 06:26:03] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T06:26:04+00:00 DEBUG (7): [2025-07-09 06:26:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:26:04+00:00 DEBUG (7): [2025-07-09 06:26:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:26:05+00:00 DEBUG (7): [2025-07-09 06:26:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:26:05+00:00 DEBUG (7): [2025-07-09 06:26:05] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:26:16+00:00 DEBUG (7): [2025-07-09 06:26:16] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:26:16+00:00 DEBUG (7): [2025-07-09 06:26:16] [ERROR] [SYSTEM] [PID:18] [MEM:2 MB] Exception: Installation cannot proceed:  in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:26:49+00:00 DEBUG (7): [2025-07-09 06:26:49] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:26:49+00:00 DEBUG (7): [2025-07-09 06:26:49] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Exception: Installation cannot proceed:  in /magento/app/code/local/PFG/Core/Model/Installation.php:72 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":72,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:28:31+00:00 DEBUG (7): [2025-07-09 06:28:31] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:28:31+00:00 DEBUG (7): [2025-07-09 06:28:31] [ERROR] [SYSTEM] [PID:10] [MEM:2 MB] Exception: Installation cannot proceed: Unknown validation error in /magento/app/code/local/PFG/Core/Model/Installation.php:86 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":86,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:28:36+00:00 DEBUG (7): [2025-07-09 06:28:36] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:28:36+00:00 DEBUG (7): [2025-07-09 06:28:36] [ERROR] [SYSTEM] [PID:13] [MEM:2 MB] Exception: Installation cannot proceed: Unknown validation error in /magento/app/code/local/PFG/Core/Model/Installation.php:86 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":86,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:37:41+00:00 DEBUG (7): [2025-07-09 06:37:41] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:37:42+00:00 DEBUG (7): [2025-07-09 06:37:42] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T06:37:42+00:00 DEBUG (7): [2025-07-09 06:37:42] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:37:43+00:00 DEBUG (7): [2025-07-09 06:37:43] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:37:43+00:00 DEBUG (7): [2025-07-09 06:37:43] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:37:43+00:00 DEBUG (7): [2025-07-09 06:37:43] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:37:51+00:00 DEBUG (7): [2025-07-09 06:37:51] [INFO] [SYSTEM] [PID:15] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:37:51+00:00 DEBUG (7): [2025-07-09 06:37:51] [ERROR] [SYSTEM] [PID:15] [MEM:2 MB] Exception: Installation cannot proceed: Unknown validation error in /magento/app/code/local/PFG/Core/Model/Installation.php:93 - Installation install failed for PFG_Altcurrency | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":93,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-altcurrency', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Altcurrency","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:37:56+00:00 DEBUG (7): [2025-07-09 06:37:56] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:37:56+00:00 DEBUG (7): [2025-07-09 06:37:56] [ERROR] [SYSTEM] [PID:16] [MEM:2 MB] Exception: Installation cannot proceed: Unknown validation error in /magento/app/code/local/PFG/Core/Model/Installation.php:93 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":93,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:38:03+00:00 DEBUG (7): [2025-07-09 06:38:03] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:38:03+00:00 DEBUG (7): [2025-07-09 06:38:03] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] canInstallRepository result for pfg-log-reader: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.45 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Log Reader","slug":"pfg-log-reader","full_name":"pfg\/pfg-log-reader","description":"","language":"","size":234202,"updated_on":"2025-07-06T22:08:36.553494+00:00","created_on":"2025-07-06T21:43:59.243721+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-log-reader.git"},{"name":"ssh","href":"*****************:pfg\/pfg-log-reader.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-log-reader","module_name":"PFG_LogReader","repository_identifier":"pfg-log-reader","is_installed":false,"installed_version":null,"latest_version":"v1.0.0","has_update":false,"installation_status":"not_installed","last_installation":null,"tags":[{"name":"v1.0.0","date":"2025-07-06T22:08:15+00:00","hash":"70d05dbb414eb7ab37664a405c13563a0e0492fc"}]}},"message":null,"error_code":null,"timestamp":1752043083}
2025-07-09T06:38:03+00:00 DEBUG (7): [2025-07-09 06:38:03] [ERROR] [SYSTEM] [PID:12] [MEM:2 MB] Exception: Installation cannot proceed: Unknown validation error in /magento/app/code/local/PFG/Core/Model/Installation.php:93 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":93,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] canInstallRepository result for pfg-log-reader: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.44 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Log Reader","slug":"pfg-log-reader","full_name":"pfg\/pfg-log-reader","description":"","language":"","size":234202,"updated_on":"2025-07-06T22:08:36.553494+00:00","created_on":"2025-07-06T21:43:59.243721+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-log-reader.git"},{"name":"ssh","href":"*****************:pfg\/pfg-log-reader.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-log-reader","module_name":"PFG_LogReader","repository_identifier":"pfg-log-reader","is_installed":false,"installed_version":null,"latest_version":"v1.0.0","has_update":false,"installation_status":"not_installed","last_installation":null,"tags":[{"name":"v1.0.0","date":"2025-07-06T22:08:15+00:00","hash":"70d05dbb414eb7ab37664a405c13563a0e0492fc"}]}},"message":null,"error_code":null,"timestamp":1752043152}
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] No files to backup for module: PFG_LogReader
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-09T06:39:13+00:00 DEBUG (7): [2025-07-09 06:39:13] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752043152.zip (32123 bytes)
2025-07-09T06:39:13+00:00 DEBUG (7): [2025-07-09 06:39:13] [ERROR] [SYSTEM] [PID:11] [MEM:2 MB] Exception: Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e/.gitignore in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] canInstallRepository result for pfg-analytics: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.44 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Analytics","slug":"pfg-analytics","full_name":"pfg\/pfg-analytics","description":"","language":"","size":480999,"updated_on":"2025-07-03T22:25:52.162660+00:00","created_on":"2025-07-02T07:24:11.709232+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-analytics.git"},{"name":"ssh","href":"*****************:pfg\/pfg-analytics.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-analytics","module_name":"PFG_Analytics","repository_identifier":"pfg-analytics","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":null,"tags":[]}},"message":null,"error_code":null,"timestamp":1752043157}
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] No files to backup for module: PFG_Analytics
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Attempting to download pfg-analytics from master branch
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-09T06:39:18+00:00 DEBUG (7): [2025-07-09 06:39:18] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752043157.zip (57135 bytes)
2025-07-09T06:39:18+00:00 DEBUG (7): [2025-07-09 06:39:18] [ERROR] [SYSTEM] [PID:14] [MEM:2 MB] Exception: Archive contains file that would be extracted outside allowed directory: pfg-pfg-analytics-164a06ec854b/app/ in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:41:00+00:00 DEBUG (7): [2025-07-09 06:41:00] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:41:00+00:00 DEBUG (7): [2025-07-09 06:41:00] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T06:41:00+00:00 DEBUG (7): [2025-07-09 06:41:00] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:41:01+00:00 DEBUG (7): [2025-07-09 06:41:01] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:41:01+00:00 DEBUG (7): [2025-07-09 06:41:01] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:41:02+00:00 DEBUG (7): [2025-07-09 06:41:02] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:56:42+00:00 DEBUG (7): [2025-07-09 06:56:42] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:56:43+00:00 DEBUG (7): [2025-07-09 06:56:43] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 0 tags for repository builder
2025-07-09T06:56:43+00:00 DEBUG (7): [2025-07-09 06:56:43] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T06:56:44+00:00 DEBUG (7): [2025-07-09 06:56:44] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T06:56:44+00:00 DEBUG (7): [2025-07-09 06:56:44] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T06:56:45+00:00 DEBUG (7): [2025-07-09 06:56:45] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T06:56:50+00:00 DEBUG (7): [2025-07-09 06:56:50] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T06:56:50+00:00 DEBUG (7): [2025-07-09 06:56:50] [ERROR] [SYSTEM] [PID:11] [MEM:16 MB] canInstallRepository result for pfg-analytics: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.44 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Analytics","slug":"pfg-analytics","full_name":"pfg\/pfg-analytics","description":"","language":"","size":480999,"updated_on":"2025-07-03T22:25:52.162660+00:00","created_on":"2025-07-02T07:24:11.709232+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-analytics.git"},{"name":"ssh","href":"*****************:pfg\/pfg-analytics.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-analytics","module_name":"PFG_Analytics","repository_identifier":"pfg-analytics","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"29","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[]}},"message":null,"error_code":null,"timestamp":1752044210}
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] No files to backup for module: PFG_Analytics
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Attempting to download pfg-analytics from master branch
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752044211.zip (57135 bytes)
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [ERROR] [SYSTEM] [PID:11] [MEM:16 MB] Exception: Archive contains potentially dangerous file type: pfg-pfg-analytics-164a06ec854b/js/pfg/analytics.js in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:04:32+00:00 DEBUG (7): [2025-07-09 07:04:32] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:04:33+00:00 DEBUG (7): [2025-07-09 07:04:33] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository builder
2025-07-09T07:04:33+00:00 DEBUG (7): [2025-07-09 07:04:33] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:04:34+00:00 DEBUG (7): [2025-07-09 07:04:34] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:04:34+00:00 DEBUG (7): [2025-07-09 07:04:34] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:04:35+00:00 DEBUG (7): [2025-07-09 07:04:35] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [SYSTEM] [PID:15] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [ERROR] [SYSTEM] [PID:15] [MEM:16 MB] canInstallRepository result for pfg-analytics: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.43 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Analytics","slug":"pfg-analytics","full_name":"pfg\/pfg-analytics","description":"","language":"","size":480999,"updated_on":"2025-07-03T22:25:52.162660+00:00","created_on":"2025-07-02T07:24:11.709232+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-analytics.git"},{"name":"ssh","href":"*****************:pfg\/pfg-analytics.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-analytics","module_name":"PFG_Analytics","repository_identifier":"pfg-analytics","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"29","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[]}},"message":null,"error_code":null,"timestamp":1752044689}
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [SYSTEM] [PID:15] [MEM:16 MB] No files to backup for module: PFG_Analytics
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [SYSTEM] [PID:15] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [SYSTEM] [PID:15] [MEM:16 MB] Attempting to download pfg-analytics from master branch
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [SYSTEM] [PID:15] [MEM:16 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [SYSTEM] [PID:15] [MEM:16 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752044689.zip (57135 bytes)
2025-07-09T07:04:50+00:00 DEBUG (7): [2025-07-09 07:04:50] [ERROR] [SYSTEM] [PID:15] [MEM:16 MB] Exception: Could not write file: /magento/var/pfg_core/temp/install_1752044689/pfg-pfg-analytics-164a06ec854b/ in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_Analytics | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_Analytics","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [INFO] [SYSTEM] [PID:9] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [ERROR] [SYSTEM] [PID:9] [MEM:16 MB] canInstallRepository result for cloudflare-integration: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.43 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"CloudFlare Integration","slug":"cloudflare-integration","full_name":"pfg\/cloudflare-integration","description":"","language":"php","size":419156,"updated_on":"2025-07-06T20:06:23.198843+00:00","created_on":"2025-07-06T19:50:44.688613+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/cloudflare-integration.git"},{"name":"ssh","href":"*****************:pfg\/cloudflare-integration.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/cloudflare-integration","module_name":"PFG_CloudflareIntegration","repository_identifier":"cloudflare-integration","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":null,"tags":[]}},"message":null,"error_code":null,"timestamp":1752044895}
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [INFO] [SYSTEM] [PID:9] [MEM:16 MB] No files to backup for module: PFG_CloudflareIntegration
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [INFO] [SYSTEM] [PID:9] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [INFO] [SYSTEM] [PID:9] [MEM:16 MB] Attempting to download cloudflare-integration from master branch
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [INFO] [SYSTEM] [PID:9] [MEM:16 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-09T07:08:17+00:00 DEBUG (7): [2025-07-09 07:08:17] [INFO] [SYSTEM] [PID:9] [MEM:16 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752044895.zip (72654 bytes)
2025-07-09T07:08:17+00:00 DEBUG (7): [2025-07-09 07:08:17] [ERROR] [SYSTEM] [PID:9] [MEM:16 MB] Exception: Archive contains potentially dangerous file path: pfg-cloudflare-integration-47f7c20578db/.gitignore in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_CloudflareIntegration | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('cloudflare-inte...', 'master')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_CloudflareIntegration","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [ERROR] [SYSTEM] [PID:10] [MEM:16 MB] canInstallRepository result for pfg-analytics: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.43 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Analytics","slug":"pfg-analytics","full_name":"pfg\/pfg-analytics","description":"","language":"","size":480999,"updated_on":"2025-07-03T22:25:52.162660+00:00","created_on":"2025-07-02T07:24:11.709232+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-analytics.git"},{"name":"ssh","href":"*****************:pfg\/pfg-analytics.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-analytics","module_name":"PFG_Analytics","repository_identifier":"pfg-analytics","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"29","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[]}},"message":null,"error_code":null,"timestamp":1752044902}
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] No files to backup for module: PFG_Analytics
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Attempting to download pfg-analytics from master branch
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Downloading repository pfg-analytics (master) from https://bitbucket.org/pfg/pfg-analytics/get/master.zip
2025-07-09T07:08:23+00:00 DEBUG (7): [2025-07-09 07:08:23] [INFO] [SYSTEM] [PID:10] [MEM:16 MB] Successfully downloaded repository pfg-analytics (master) to /magento/var/pfg_core/temp/pfg-analytics-master-1752044902.zip (57135 bytes)
2025-07-09T07:08:26+00:00 DEBUG (7): [2025-07-09 07:08:26] [INFO] [SYSTEM] [PID:12] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:08:26+00:00 DEBUG (7): [2025-07-09 07:08:26] [INFO] [SYSTEM] [PID:12] [MEM:16 MB] Retrieved 0 tags for repository builder
2025-07-09T07:08:26+00:00 DEBUG (7): [2025-07-09 07:08:26] [INFO] [SYSTEM] [PID:12] [MEM:16 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:08:27+00:00 DEBUG (7): [2025-07-09 07:08:27] [INFO] [SYSTEM] [PID:12] [MEM:16 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:08:27+00:00 DEBUG (7): [2025-07-09 07:08:27] [INFO] [SYSTEM] [PID:12] [MEM:16 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:08:28+00:00 DEBUG (7): [2025-07-09 07:08:28] [INFO] [SYSTEM] [PID:12] [MEM:16 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [ERROR] [SYSTEM] [PID:13] [MEM:16 MB] canInstallRepository result for pfg-log-reader: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.42 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Analytics"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Log Reader","slug":"pfg-log-reader","full_name":"pfg\/pfg-log-reader","description":"","language":"","size":234202,"updated_on":"2025-07-06T22:08:36.553494+00:00","created_on":"2025-07-06T21:43:59.243721+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-log-reader.git"},{"name":"ssh","href":"*****************:pfg\/pfg-log-reader.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-log-reader","module_name":"PFG_LogReader","repository_identifier":"pfg-log-reader","is_installed":false,"installed_version":null,"latest_version":"v1.0.0","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"28","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[{"name":"v1.0.0","date":"2025-07-06T22:08:15+00:00","hash":"70d05dbb414eb7ab37664a405c13563a0e0492fc"}]}},"message":null,"error_code":null,"timestamp":1752044912}
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] No files to backup for module: PFG_LogReader
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-09T07:08:33+00:00 DEBUG (7): [2025-07-09 07:08:33] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752044912.zip (32123 bytes)
2025-07-09T07:08:33+00:00 DEBUG (7): [2025-07-09 07:08:33] [ERROR] [SYSTEM] [PID:13] [MEM:16 MB] Exception: Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e/.gitignore in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [ERROR] [SYSTEM] [PID:17] [MEM:16 MB] canInstallRepository result for pfg-log-reader: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.42 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Analytics"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Log Reader","slug":"pfg-log-reader","full_name":"pfg\/pfg-log-reader","description":"","language":"","size":234202,"updated_on":"2025-07-06T22:08:36.553494+00:00","created_on":"2025-07-06T21:43:59.243721+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-log-reader.git"},{"name":"ssh","href":"*****************:pfg\/pfg-log-reader.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-log-reader","module_name":"PFG_LogReader","repository_identifier":"pfg-log-reader","is_installed":false,"installed_version":null,"latest_version":"v1.0.0","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"28","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[{"name":"v1.0.0","date":"2025-07-06T22:08:15+00:00","hash":"70d05dbb414eb7ab37664a405c13563a0e0492fc"}]}},"message":null,"error_code":null,"timestamp":1752044962}
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] No files to backup for module: PFG_LogReader
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-09T07:09:23+00:00 DEBUG (7): [2025-07-09 07:09:23] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752044962.zip (32123 bytes)
2025-07-09T07:09:23+00:00 DEBUG (7): [2025-07-09 07:09:23] [ERROR] [SYSTEM] [PID:17] [MEM:16 MB] Exception: Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e/.gitignore in /magento/app/code/local/PFG/Core/Model/Installation.php:139 - Installation install failed for PFG_LogReader | Context: {"exception_class":"Exception","exception_code":0,"file":"\/magento\/app\/code\/local\/PFG\/Core\/Model\/Installation.php","line":139,"trace":"#0 \/magento\/app\/code\/local\/PFG\/Core\/controllers\/Adminhtml\/Pfg\/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-log-reader', 'v1.0.0')\n#1 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Router\/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 \/magento\/app\/code\/core\/Mage\/Core\/Controller\/Varien\/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 \/magento\/app\/code\/core\/Mage\/Core\/Model\/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 \/magento\/app\/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 \/magento\/index.php(86): Mage::run('default', 'store')\n#7 {main}","context":"Installation install failed for PFG_LogReader","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [ERROR] [SYSTEM] [PID:11] [MEM:16 MB] canInstallRepository result for pfg-log-reader: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.42 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Analytics"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG Log Reader","slug":"pfg-log-reader","full_name":"pfg\/pfg-log-reader","description":"","language":"","size":234202,"updated_on":"2025-07-06T22:08:36.553494+00:00","created_on":"2025-07-06T21:43:59.243721+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-log-reader.git"},{"name":"ssh","href":"*****************:pfg\/pfg-log-reader.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-log-reader","module_name":"PFG_LogReader","repository_identifier":"pfg-log-reader","is_installed":false,"installed_version":null,"latest_version":"v1.0.0","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"28","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[{"name":"v1.0.0","date":"2025-07-06T22:08:15+00:00","hash":"70d05dbb414eb7ab37664a405c13563a0e0492fc"}]}},"message":null,"error_code":null,"timestamp":1752045029}
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] No files to backup for module: PFG_LogReader
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Attempting to download pfg-log-reader from v1.0.0 branch
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Downloading repository pfg-log-reader (v1.0.0) from https://bitbucket.org/pfg/pfg-log-reader/get/v1.0.0.zip
2025-07-09T07:10:30+00:00 DEBUG (7): [2025-07-09 07:10:30] [INFO] [SYSTEM] [PID:11] [MEM:16 MB] Successfully downloaded repository pfg-log-reader (v1.0.0) to /magento/var/pfg_core/temp/pfg-log-reader-v1.0.0-1752045029.zip (32123 bytes)
2025-07-09T07:10:33+00:00 DEBUG (7): [2025-07-09 07:10:33] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:10:33+00:00 DEBUG (7): [2025-07-09 07:10:33] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository builder
2025-07-09T07:10:33+00:00 DEBUG (7): [2025-07-09 07:10:33] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:10:34+00:00 DEBUG (7): [2025-07-09 07:10:34] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:10:34+00:00 DEBUG (7): [2025-07-09 07:10:34] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:10:35+00:00 DEBUG (7): [2025-07-09 07:10:35] [INFO] [SYSTEM] [PID:13] [MEM:16 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [INFO] [SYSTEM] [PID:14] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [ERROR] [SYSTEM] [PID:14] [MEM:16 MB] canInstallRepository result for pfg-altcurrency: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.42 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Analytics"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_LogReader"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"PFG AltCurrency","slug":"pfg-altcurrency","full_name":"pfg\/pfg-altcurrency","description":"An incubator for ideas","language":"","size":132460,"updated_on":"2025-07-04T13:56:06.448288+00:00","created_on":"2025-07-02T13:12:07.047311+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/pfg-altcurrency.git"},{"name":"ssh","href":"*****************:pfg\/pfg-altcurrency.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/pfg-altcurrency","module_name":"PFG_Altcurrency","repository_identifier":"pfg-altcurrency","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":null,"tags":[]}},"message":null,"error_code":null,"timestamp":1752045040}
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [INFO] [SYSTEM] [PID:14] [MEM:16 MB] No files to backup for module: PFG_Altcurrency
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [INFO] [SYSTEM] [PID:14] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [INFO] [SYSTEM] [PID:14] [MEM:16 MB] Attempting to download pfg-altcurrency from master branch
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [INFO] [SYSTEM] [PID:14] [MEM:16 MB] Downloading repository pfg-altcurrency (master) from https://bitbucket.org/pfg/pfg-altcurrency/get/master.zip
2025-07-09T07:10:41+00:00 DEBUG (7): [2025-07-09 07:10:41] [INFO] [SYSTEM] [PID:14] [MEM:16 MB] Successfully downloaded repository pfg-altcurrency (master) to /magento/var/pfg_core/temp/pfg-altcurrency-master-1752045040.zip (10875 bytes)
2025-07-09T07:10:44+00:00 DEBUG (7): [2025-07-09 07:10:44] [INFO] [SYSTEM] [PID:16] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:10:44+00:00 DEBUG (7): [2025-07-09 07:10:44] [INFO] [SYSTEM] [PID:16] [MEM:16 MB] Retrieved 0 tags for repository builder
2025-07-09T07:10:45+00:00 DEBUG (7): [2025-07-09 07:10:45] [INFO] [SYSTEM] [PID:16] [MEM:16 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:10:45+00:00 DEBUG (7): [2025-07-09 07:10:45] [INFO] [SYSTEM] [PID:16] [MEM:16 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:10:46+00:00 DEBUG (7): [2025-07-09 07:10:46] [INFO] [SYSTEM] [PID:16] [MEM:16 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:10:46+00:00 DEBUG (7): [2025-07-09 07:10:46] [INFO] [SYSTEM] [PID:16] [MEM:16 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [ERROR] [SYSTEM] [PID:17] [MEM:16 MB] canInstallRepository result for cloudflare-integration: {"success":true,"data":{"can_install":true,"checks":[{"type":"success","message":"PHP version: 7.4.33 \u2713"},{"type":"success","message":"PHP extension curl: \u2713"},{"type":"success","message":"PHP extension json: \u2713"},{"type":"success","message":"PHP extension zip: \u2713"},{"type":"success","message":"PHP extension phar: \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/code\/local \u2713"},{"type":"success","message":"Directory writable: \/magento\/app\/etc\/modules \u2713"},{"type":"success","message":"Directory writable: \/magento\/var \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/backups \u2713"},{"type":"success","message":"Directory writable: \/magento\/var\/pfg_core\/temp \u2713"},{"type":"success","message":"Disk space available: 389.42 GB \u2713"},{"type":"success","message":"Magento version: ******* \u2713"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_AltCurrency"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Analytics"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core"},{"type":"warning","message":"Potential rewrite conflict: Potential conflict with existing PFG module: PFG_LogReader"},{"type":"success","message":"Repository is private \u2713"},{"type":"success","message":"Module is in PFG namespace \u2713"},{"type":"success","message":"Admin session active \u2713"},{"type":"success","message":"Admin has required permissions \u2713"},{"type":"warning","message":"User does not have administrator role - proceed with caution"}],"repository":{"name":"CloudFlare Integration","slug":"cloudflare-integration","full_name":"pfg\/cloudflare-integration","description":"","language":"php","size":419156,"updated_on":"2025-07-06T20:06:23.198843+00:00","created_on":"2025-07-06T19:50:44.688613+00:00","is_private":true,"clone_links":[{"name":"https","href":"https:\/\/<EMAIL>\/pfg\/cloudflare-integration.git"},{"name":"ssh","href":"*****************:pfg\/cloudflare-integration.git"}],"html_url":"https:\/\/bitbucket.org\/pfg\/cloudflare-integration","module_name":"PFG_CloudflareIntegration","repository_identifier":"cloudflare-integration","is_installed":false,"installed_version":null,"latest_version":"master","has_update":false,"installation_status":"not_installed","last_installation":{"installation_id":"32","backup_id":null,"installation_type":"install","version_installed":null,"installed_at":null,"can_rollback":false},"tags":[]}},"message":null,"error_code":null,"timestamp":1752045052}
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] No files to backup for module: PFG_CloudflareIntegration
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Attempting to download cloudflare-integration from master branch
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-09T07:10:53+00:00 DEBUG (7): [2025-07-09 07:10:53] [INFO] [SYSTEM] [PID:17] [MEM:16 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752045052.zip (72654 bytes)
2025-07-09T07:10:55+00:00 DEBUG (7): [2025-07-09 07:10:55] [INFO] [SYSTEM] [PID:9] [MEM:6 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:10:56+00:00 DEBUG (7): [2025-07-09 07:10:56] [INFO] [SYSTEM] [PID:9] [MEM:6 MB] Retrieved 0 tags for repository builder
2025-07-09T07:10:56+00:00 DEBUG (7): [2025-07-09 07:10:56] [INFO] [SYSTEM] [PID:9] [MEM:6 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:10:56+00:00 DEBUG (7): [2025-07-09 07:10:56] [INFO] [SYSTEM] [PID:9] [MEM:6 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:10:57+00:00 DEBUG (7): [2025-07-09 07:10:57] [INFO] [SYSTEM] [PID:9] [MEM:6 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:10:57+00:00 DEBUG (7): [2025-07-09 07:10:57] [INFO] [SYSTEM] [PID:9] [MEM:6 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:15:02+00:00 DEBUG (7): [2025-07-09 07:15:02] [INFO] [SYSTEM] [PID:16] [MEM:8 MB] Admin action: rollback by user martinpfg from IP **********
2025-07-09T07:15:02+00:00 DEBUG (7): [2025-07-09 07:15:02] [INFO] [SYSTEM] [PID:16] [MEM:8 MB] Starting complete removal of module: PFG_Analytics
2025-07-09T07:15:02+00:00 DEBUG (7): [2025-07-09 07:15:02] [INFO] [SYSTEM] [PID:16] [MEM:8 MB] Created backup for PFG_Analytics: PFG_Analytics_rollback_2025-07-09_07-15-02.tar.gz (6 KB)
2025-07-09T07:15:02+00:00 DEBUG (7): [2025-07-09 07:15:02] [INFO] [SYSTEM] [PID:16] [MEM:8 MB] Backup created with ID: 11
2025-07-09T07:15:02+00:00 DEBUG (7): [2025-07-09 07:15:02] [INFO] [SYSTEM] [PID:16] [MEM:10 MB] Module PFG_Analytics completely removed successfully
2025-07-09T07:15:05+00:00 DEBUG (7): [2025-07-09 07:15:05] [INFO] [SYSTEM] [PID:18] [MEM:8 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:15:06+00:00 DEBUG (7): [2025-07-09 07:15:06] [INFO] [SYSTEM] [PID:18] [MEM:8 MB] Retrieved 0 tags for repository builder
2025-07-09T07:15:06+00:00 DEBUG (7): [2025-07-09 07:15:06] [INFO] [SYSTEM] [PID:18] [MEM:8 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:15:06+00:00 DEBUG (7): [2025-07-09 07:15:06] [INFO] [SYSTEM] [PID:18] [MEM:8 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:15:07+00:00 DEBUG (7): [2025-07-09 07:15:07] [INFO] [SYSTEM] [PID:18] [MEM:8 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:15:07+00:00 DEBUG (7): [2025-07-09 07:15:07] [INFO] [SYSTEM] [PID:18] [MEM:8 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:17:17+00:00 DEBUG (7): [2025-07-09 07:17:17] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:17:18+00:00 DEBUG (7): [2025-07-09 07:17:18] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T07:17:18+00:00 DEBUG (7): [2025-07-09 07:17:18] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:17:19+00:00 DEBUG (7): [2025-07-09 07:17:19] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:17:19+00:00 DEBUG (7): [2025-07-09 07:17:19] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:17:19+00:00 DEBUG (7): [2025-07-09 07:17:19] [INFO] [SYSTEM] [PID:11] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:27:45+00:00 DEBUG (7): [2025-07-09 07:27:45] [INFO] [SYSTEM] [PID:15] [MEM:4 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:27:45+00:00 DEBUG (7): [2025-07-09 07:27:45] [INFO] [SYSTEM] [PID:15] [MEM:4 MB] Retrieved 0 tags for repository builder
2025-07-09T07:27:46+00:00 DEBUG (7): [2025-07-09 07:27:46] [INFO] [SYSTEM] [PID:15] [MEM:4 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:27:46+00:00 DEBUG (7): [2025-07-09 07:27:46] [INFO] [SYSTEM] [PID:15] [MEM:4 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:27:47+00:00 DEBUG (7): [2025-07-09 07:27:47] [INFO] [SYSTEM] [PID:15] [MEM:4 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:27:47+00:00 DEBUG (7): [2025-07-09 07:27:47] [INFO] [SYSTEM] [PID:15] [MEM:4 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:28:05+00:00 DEBUG (7): [2025-07-09 07:28:05] [INFO] [SYSTEM] [PID:18] [MEM:4 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T07:28:05+00:00 DEBUG (7): [2025-07-09 07:28:05] [INFO] [SYSTEM] [PID:18] [MEM:4 MB] No files to backup for module: PFG_CloudflareIntegration
2025-07-09T07:28:05+00:00 DEBUG (7): [2025-07-09 07:28:05] [INFO] [SYSTEM] [PID:18] [MEM:4 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:28:05+00:00 DEBUG (7): [2025-07-09 07:28:05] [INFO] [SYSTEM] [PID:18] [MEM:4 MB] Attempting to download cloudflare-integration from master branch
2025-07-09T07:28:05+00:00 DEBUG (7): [2025-07-09 07:28:05] [INFO] [SYSTEM] [PID:18] [MEM:4 MB] Downloading repository cloudflare-integration (master) from https://bitbucket.org/pfg/cloudflare-integration/get/master.zip
2025-07-09T07:28:06+00:00 DEBUG (7): [2025-07-09 07:28:06] [INFO] [SYSTEM] [PID:18] [MEM:4 MB] Successfully downloaded repository cloudflare-integration (master) to /magento/var/pfg_core/temp/cloudflare-integration-master-1752046085.zip (72654 bytes)
2025-07-09T07:28:08+00:00 DEBUG (7): [2025-07-09 07:28:08] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:28:09+00:00 DEBUG (7): [2025-07-09 07:28:09] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T07:28:09+00:00 DEBUG (7): [2025-07-09 07:28:09] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:28:10+00:00 DEBUG (7): [2025-07-09 07:28:10] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:28:10+00:00 DEBUG (7): [2025-07-09 07:28:10] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:28:11+00:00 DEBUG (7): [2025-07-09 07:28:11] [INFO] [SYSTEM] [PID:10] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T07:29:04+00:00 DEBUG (7): [2025-07-09 07:29:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Admin action: rollback by user martinpfg from IP **********
2025-07-09T07:29:04+00:00 DEBUG (7): [2025-07-09 07:29:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Starting complete removal of module: PFG_LogReader
2025-07-09T07:29:04+00:00 DEBUG (7): [2025-07-09 07:29:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Created backup for PFG_LogReader: PFG_LogReader_rollback_2025-07-09_07-29-04.tar.gz (83 KB)
2025-07-09T07:29:04+00:00 DEBUG (7): [2025-07-09 07:29:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Backup created with ID: 12
2025-07-09T07:29:04+00:00 DEBUG (7): [2025-07-09 07:29:04] [INFO] [SYSTEM] [PID:14] [MEM:2 MB] Module PFG_LogReader completely removed successfully
2025-07-09T07:29:07+00:00 DEBUG (7): [2025-07-09 07:29:07] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 5 repositories from Bitbucket
2025-07-09T07:29:07+00:00 DEBUG (7): [2025-07-09 07:29:07] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T07:29:08+00:00 DEBUG (7): [2025-07-09 07:29:08] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T07:29:08+00:00 DEBUG (7): [2025-07-09 07:29:08] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T07:29:08+00:00 DEBUG (7): [2025-07-09 07:29:08] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T07:29:09+00:00 DEBUG (7): [2025-07-09 07:29:09] [INFO] [SYSTEM] [PID:16] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T09:13:20+00:00 DEBUG (7): [2025-07-09 09:13:20] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 6 repositories from Bitbucket
2025-07-09T09:13:20+00:00 DEBUG (7): [2025-07-09 09:13:20] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T09:13:21+00:00 DEBUG (7): [2025-07-09 09:13:21] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T09:13:21+00:00 DEBUG (7): [2025-07-09 09:13:21] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T09:13:22+00:00 DEBUG (7): [2025-07-09 09:13:22] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T09:13:22+00:00 DEBUG (7): [2025-07-09 09:13:22] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 0 tags for repository pfg-core
2025-07-09T09:13:23+00:00 DEBUG (7): [2025-07-09 09:13:23] [INFO] [SYSTEM] [PID:18] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Admin action: installModule by user martinpfg from IP **********
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] No files to backup for module: PFG_Altcurrency
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Retrieved 6 repositories from Bitbucket
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Attempting to download pfg-altcurrency from master branch
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Downloading repository pfg-altcurrency (master) from https://bitbucket.org/pfg/pfg-altcurrency/get/master.zip
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [SYSTEM] [PID:12] [MEM:2 MB] Successfully downloaded repository pfg-altcurrency (master) to /magento/var/pfg_core/temp/pfg-altcurrency-master-1752052410.zip (10875 bytes)
2025-07-09T09:13:33+00:00 DEBUG (7): [2025-07-09 09:13:33] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 6 repositories from Bitbucket
2025-07-09T09:13:33+00:00 DEBUG (7): [2025-07-09 09:13:33] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository builder
2025-07-09T09:13:34+00:00 DEBUG (7): [2025-07-09 09:13:34] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T09:13:34+00:00 DEBUG (7): [2025-07-09 09:13:34] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T09:13:35+00:00 DEBUG (7): [2025-07-09 09:13:35] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T09:13:35+00:00 DEBUG (7): [2025-07-09 09:13:35] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 0 tags for repository pfg-core
2025-07-09T09:13:35+00:00 DEBUG (7): [2025-07-09 09:13:35] [INFO] [SYSTEM] [PID:13] [MEM:2 MB] Retrieved 1 tags for repository pfg-log-reader
2025-07-09T13:43:35+00:00 DEBUG (7): [2025-07-09 13:43:35] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 6 repositories from Bitbucket
2025-07-09T13:43:35+00:00 DEBUG (7): [2025-07-09 13:43:35] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 0 tags for repository builder
2025-07-09T13:43:36+00:00 DEBUG (7): [2025-07-09 13:43:36] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 0 tags for repository cloudflare-integration
2025-07-09T13:43:36+00:00 DEBUG (7): [2025-07-09 13:43:36] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 0 tags for repository pfg-altcurrency
2025-07-09T13:43:37+00:00 DEBUG (7): [2025-07-09 13:43:37] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 0 tags for repository pfg-analytics
2025-07-09T13:43:37+00:00 DEBUG (7): [2025-07-09 13:43:37] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 0 tags for repository pfg-core
2025-07-09T13:43:38+00:00 DEBUG (7): [2025-07-09 13:43:38] [INFO] [SYSTEM] [PID:9] [MEM:12 MB] Retrieved 1 tags for repository pfg-log-reader
