2025-07-08T20:58:09+00:00 DEBUG (7): [2025-07-08 20:58:09] [INFO] [INSTALLATION] [PID:10] [MEM:2 MB] Installation started: PFG_Builder - Installing from repository builder (version latest) | Context: {"action":"started","module_name":"PFG_Builder","details":"Installing from repository builder (version latest)","user":"martinpfg","ip_address":"**********"}
2025-07-08T20:58:10+00:00 DEBUG (7): [2025-07-08 20:58:10] [ERROR] [INSTALLATION] [PID:10] [MEM:2 MB] Installation failed: PFG_Builder - Failed to download repository: HTTP error: 404 | Context: {"action":"failed","module_name":"PFG_Builder","details":"Failed to download repository: HTTP error: 404","user":"martinpfg","ip_address":"**********"}
2025-07-08T20:59:09+00:00 DEBUG (7): [2025-07-08 20:59:09] [ERROR] [INSTALLATION] [PID:11] [MEM:2 MB] Installation failed: PFG_Cloudflare integration - Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_Cloudflare integration, Repository is private ✓, Module is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution | Context: {"action":"failed","module_name":"PFG_Cloudflare integration","details":"Installation cannot proceed: PHP version: 7.4.33 \u2713, PHP extension curl: \u2713, PHP extension json: \u2713, PHP extension zip: \u2713, PHP extension phar: \u2713, Directory writable: \/magento\/app\/code\/local \u2713, Directory writable: \/magento\/app\/etc\/modules \u2713, Directory writable: \/magento\/var \u2713, Directory writable: \/magento\/var\/pfg_core\/backups \u2713, Directory writable: \/magento\/var\/pfg_core\/temp \u2713, Disk space available: 389.63 GB \u2713, Magento version: ******* \u2713, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_Cloudflare integration, Repository is private \u2713, Module is in PFG namespace \u2713, Admin session active \u2713, Admin has required permissions \u2713, User does not have administrator role - proceed with caution","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:09:09+00:00 DEBUG (7): [2025-07-08 21:09:09] [ERROR] [INSTALLATION] [PID:14] [MEM:2 MB] Installation failed: PFG_ altcurrency - Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ altcurrency, Repository is private ✓, Module is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution | Context: {"action":"failed","module_name":"PFG_ altcurrency","details":"Installation cannot proceed: PHP version: 7.4.33 \u2713, PHP extension curl: \u2713, PHP extension json: \u2713, PHP extension zip: \u2713, PHP extension phar: \u2713, Directory writable: \/magento\/app\/code\/local \u2713, Directory writable: \/magento\/app\/etc\/modules \u2713, Directory writable: \/magento\/var \u2713, Directory writable: \/magento\/var\/pfg_core\/backups \u2713, Directory writable: \/magento\/var\/pfg_core\/temp \u2713, Disk space available: 389.63 GB \u2713, Magento version: ******* \u2713, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ altcurrency, Repository is private \u2713, Module is in PFG namespace \u2713, Admin session active \u2713, Admin has required permissions \u2713, User does not have administrator role - proceed with caution","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:10:28+00:00 DEBUG (7): [2025-07-08 21:10:28] [ERROR] [INSTALLATION] [PID:17] [MEM:2 MB] Installation failed: PFG_ analytics - Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ analytics, Repository is private ✓, Module is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution | Context: {"action":"failed","module_name":"PFG_ analytics","details":"Installation cannot proceed: PHP version: 7.4.33 \u2713, PHP extension curl: \u2713, PHP extension json: \u2713, PHP extension zip: \u2713, PHP extension phar: \u2713, Directory writable: \/magento\/app\/code\/local \u2713, Directory writable: \/magento\/app\/etc\/modules \u2713, Directory writable: \/magento\/var \u2713, Directory writable: \/magento\/var\/pfg_core\/backups \u2713, Directory writable: \/magento\/var\/pfg_core\/temp \u2713, Disk space available: 389.63 GB \u2713, Magento version: ******* \u2713, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ analytics, Repository is private \u2713, Module is in PFG namespace \u2713, Admin session active \u2713, Admin has required permissions \u2713, User does not have administrator role - proceed with caution","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:53:17+00:00 DEBUG (7): [2025-07-08 21:53:17] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation started: PFG_Analytics - Installing from repository PFG Analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository PFG Analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:53:18+00:00 DEBUG (7): [2025-07-08 21:53:18] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation completed: PFG_Analytics - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_Analytics","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:53:18+00:00 DEBUG (7): [2025-07-08 21:53:18] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:53:18+00:00 DEBUG (7): [2025-07-08 21:53:18] [ERROR] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T21:55:26+00:00 DEBUG (7): [2025-07-08 21:55:26] [INFO] [INSTALLATION] [PID:18] [MEM:2 MB] Installation started: PFG_CloudflareIntegration - Installing from repository CloudFlare Integration (version master) | Context: {"action":"started","module_name":"PFG_CloudflareIntegration","details":"Installing from repository CloudFlare Integration (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:55:27+00:00 DEBUG (7): [2025-07-08 21:55:27] [INFO] [INSTALLATION] [PID:18] [MEM:2 MB] Installation completed: PFG_CloudflareIntegration - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_CloudflareIntegration","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:55:28+00:00 DEBUG (7): [2025-07-08 21:55:28] [INFO] [INSTALLATION] [PID:9] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T21:55:28+00:00 DEBUG (7): [2025-07-08 21:55:28] [ERROR] [INSTALLATION] [PID:9] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T22:11:38+00:00 DEBUG (7): [2025-07-08 22:11:38] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation started: PFG_LogReader - Installing from repository PFG Log Reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository PFG Log Reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:11:39+00:00 DEBUG (7): [2025-07-08 22:11:39] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:11:39+00:00 DEBUG (7): [2025-07-08 22:11:39] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation completed: PFG_LogReader - Successfully installed version v1.0.0 | Context: {"action":"completed","module_name":"PFG_LogReader","details":"Successfully installed version v1.0.0","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:11:40+00:00 DEBUG (7): [2025-07-08 22:11:40] [INFO] [INSTALLATION] [PID:16] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:11:40+00:00 DEBUG (7): [2025-07-08 22:11:40] [ERROR] [INSTALLATION] [PID:16] [MEM:4 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:02+00:00 DEBUG (7): [2025-07-08 22:53:02] [INFO] [INSTALLATION] [PID:17] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:02+00:00 DEBUG (7): [2025-07-08 22:53:02] [ERROR] [INSTALLATION] [PID:17] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:22+00:00 DEBUG (7): [2025-07-08 22:53:22] [INFO] [INSTALLATION] [PID:9] [MEM:2 MB] Installation started: PFG_LogReader - Installing from repository PFG Log Reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository PFG Log Reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:23+00:00 DEBUG (7): [2025-07-08 22:53:23] [INFO] [INSTALLATION] [PID:9] [MEM:2 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:23+00:00 DEBUG (7): [2025-07-08 22:53:23] [INFO] [INSTALLATION] [PID:9] [MEM:2 MB] Installation completed: PFG_LogReader - Successfully installed version v1.0.0 | Context: {"action":"completed","module_name":"PFG_LogReader","details":"Successfully installed version v1.0.0","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:24+00:00 DEBUG (7): [2025-07-08 22:53:24] [INFO] [INSTALLATION] [PID:10] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:24+00:00 DEBUG (7): [2025-07-08 22:53:24] [ERROR] [INSTALLATION] [PID:10] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:44+00:00 DEBUG (7): [2025-07-08 22:53:44] [INFO] [INSTALLATION] [PID:12] [MEM:2 MB] Installation started: PFG_Analytics - Installing from repository PFG Analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository PFG Analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:45+00:00 DEBUG (7): [2025-07-08 22:53:45] [INFO] [INSTALLATION] [PID:12] [MEM:2 MB] Installation module_detected: PFG_Analytics - Detected actual module name: PFG_Analytics (expected: PFG_Analytics) | Context: {"action":"module_detected","module_name":"PFG_Analytics","details":"Detected actual module name: PFG_Analytics (expected: PFG_Analytics)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:53:45+00:00 DEBUG (7): [2025-07-08 22:53:45] [ERROR] [INSTALLATION] [PID:12] [MEM:2 MB] Installation failed: PFG_Analytics - Module installation incomplete - missing required files | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Module installation incomplete - missing required files","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:57:50+00:00 DEBUG (7): [2025-07-08 22:57:50] [ERROR] [INSTALLATION] [PID:681] [MEM:8 MB] Installation failed: PFG_Analytics - Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.5 GB ✓, Magento version: ******* ✓, Module PFG_Analytics is already installed and active, File conflict: /magento/app/etc/modules/PFG_Analytics.xml, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_LogReader, Module is already installed (version ), Repository is private ✓, Module is in PFG namespace ✓, Admin session required | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Installation cannot proceed: PHP version: 7.4.33 \u2713, PHP extension curl: \u2713, PHP extension json: \u2713, PHP extension zip: \u2713, PHP extension phar: \u2713, Directory writable: \/magento\/app\/code\/local \u2713, Directory writable: \/magento\/app\/etc\/modules \u2713, Directory writable: \/magento\/var \u2713, Directory writable: \/magento\/var\/pfg_core\/backups \u2713, Directory writable: \/magento\/var\/pfg_core\/temp \u2713, Disk space available: 389.5 GB \u2713, Magento version: ******* \u2713, Module PFG_Analytics is already installed and active, File conflict: \/magento\/app\/etc\/modules\/PFG_Analytics.xml, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_LogReader, Module is already installed (version ), Repository is private \u2713, Module is in PFG namespace \u2713, Admin session required","user":"system","ip_address":false}
2025-07-08T22:59:48+00:00 DEBUG (7): [2025-07-08 22:59:48] [INFO] [INSTALLATION] [PID:17] [MEM:2 MB] Installation started: PFG_CloudflareIntegration - Installing from repository CloudFlare Integration (version master) | Context: {"action":"started","module_name":"PFG_CloudflareIntegration","details":"Installing from repository CloudFlare Integration (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:59:49+00:00 DEBUG (7): [2025-07-08 22:59:49] [INFO] [INSTALLATION] [PID:17] [MEM:2 MB] Installation module_detected: PFG_Cloudflare - Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration) | Context: {"action":"module_detected","module_name":"PFG_Cloudflare","details":"Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration)","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:59:49+00:00 DEBUG (7): [2025-07-08 22:59:49] [INFO] [INSTALLATION] [PID:17] [MEM:2 MB] Installation completed: PFG_CloudflareIntegration - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_CloudflareIntegration","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:59:49+00:00 DEBUG (7): [2025-07-08 22:59:49] [INFO] [INSTALLATION] [PID:18] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T22:59:49+00:00 DEBUG (7): [2025-07-08 22:59:49] [ERROR] [INSTALLATION] [PID:18] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:21+00:00 DEBUG (7): [2025-07-08 23:00:21] [INFO] [INSTALLATION] [PID:11] [MEM:2 MB] Installation started: PFG_Analytics - Installing from repository PFG Analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository PFG Analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:22+00:00 DEBUG (7): [2025-07-08 23:00:22] [INFO] [INSTALLATION] [PID:11] [MEM:2 MB] Installation module_detected: PFG_Analytics - Detected actual module name: PFG_Analytics (expected: PFG_Analytics) | Context: {"action":"module_detected","module_name":"PFG_Analytics","details":"Detected actual module name: PFG_Analytics (expected: PFG_Analytics)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:22+00:00 DEBUG (7): [2025-07-08 23:00:22] [INFO] [INSTALLATION] [PID:11] [MEM:2 MB] Installation completed: PFG_Analytics - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_Analytics","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:23+00:00 DEBUG (7): [2025-07-08 23:00:23] [INFO] [INSTALLATION] [PID:12] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:23+00:00 DEBUG (7): [2025-07-08 23:00:23] [ERROR] [INSTALLATION] [PID:12] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:40+00:00 DEBUG (7): [2025-07-08 23:00:40] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation started: PFG_CloudflareIntegration - Installing from repository CloudFlare Integration (version master) | Context: {"action":"started","module_name":"PFG_CloudflareIntegration","details":"Installing from repository CloudFlare Integration (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:41+00:00 DEBUG (7): [2025-07-08 23:00:41] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation module_detected: PFG_Cloudflare - Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration) | Context: {"action":"module_detected","module_name":"PFG_Cloudflare","details":"Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:41+00:00 DEBUG (7): [2025-07-08 23:00:41] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation completed: PFG_CloudflareIntegration - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_CloudflareIntegration","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:41+00:00 DEBUG (7): [2025-07-08 23:00:41] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:00:42+00:00 DEBUG (7): [2025-07-08 23:00:42] [ERROR] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:03:06+00:00 DEBUG (7): [2025-07-08 23:03:06] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:03:06+00:00 DEBUG (7): [2025-07-08 23:03:06] [ERROR] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:05:43+00:00 DEBUG (7): [2025-07-08 23:05:43] [INFO] [INSTALLATION] [PID:16] [MEM:2 MB] Installation started: PFG_Altcurrency - Installing from repository PFG AltCurrency (version master) | Context: {"action":"started","module_name":"PFG_Altcurrency","details":"Installing from repository PFG AltCurrency (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:05:44+00:00 DEBUG (7): [2025-07-08 23:05:44] [INFO] [INSTALLATION] [PID:16] [MEM:2 MB] Installation module_detected: PFG_AltCurrency - Detected actual module name: PFG_AltCurrency (expected: PFG_Altcurrency) | Context: {"action":"module_detected","module_name":"PFG_AltCurrency","details":"Detected actual module name: PFG_AltCurrency (expected: PFG_Altcurrency)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:05:44+00:00 DEBUG (7): [2025-07-08 23:05:44] [INFO] [INSTALLATION] [PID:16] [MEM:2 MB] Installation completed: PFG_Altcurrency - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_Altcurrency","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:05:44+00:00 DEBUG (7): [2025-07-08 23:05:44] [INFO] [INSTALLATION] [PID:17] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:05:44+00:00 DEBUG (7): [2025-07-08 23:05:44] [ERROR] [INSTALLATION] [PID:17] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:38+00:00 DEBUG (7): [2025-07-08 23:27:38] [INFO] [INSTALLATION] [PID:18] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:38+00:00 DEBUG (7): [2025-07-08 23:27:38] [ERROR] [INSTALLATION] [PID:18] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:58+00:00 DEBUG (7): [2025-07-08 23:27:58] [INFO] [INSTALLATION] [PID:10] [MEM:2 MB] Installation started: PFG_LogReader - Installing from repository PFG Log Reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository PFG Log Reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:59+00:00 DEBUG (7): [2025-07-08 23:27:59] [INFO] [INSTALLATION] [PID:10] [MEM:2 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:59+00:00 DEBUG (7): [2025-07-08 23:27:59] [INFO] [INSTALLATION] [PID:10] [MEM:2 MB] Installation completed: PFG_LogReader - Successfully installed version v1.0.0 | Context: {"action":"completed","module_name":"PFG_LogReader","details":"Successfully installed version v1.0.0","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:59+00:00 DEBUG (7): [2025-07-08 23:27:59] [INFO] [INSTALLATION] [PID:11] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:27:59+00:00 DEBUG (7): [2025-07-08 23:27:59] [ERROR] [INSTALLATION] [PID:11] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [INFO] [INSTALLATION] [PID:16] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-08T23:28:28+00:00 DEBUG (7): [2025-07-08 23:28:28] [ERROR] [INSTALLATION] [PID:16] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T06:15:43+00:00 DEBUG (7): [2025-07-09 06:15:43] [ERROR] [INSTALLATION] [PID:12] [MEM:2 MB] Installation failed: PFG_LogReader - Installation cannot proceed:  | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Installation cannot proceed: ","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:21:07+00:00 DEBUG (7): [2025-07-09 06:21:07] [ERROR] [INSTALLATION] [PID:16] [MEM:2 MB] Installation failed: PFG_LogReader - Installation cannot proceed:  | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Installation cannot proceed: ","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:26:16+00:00 DEBUG (7): [2025-07-09 06:26:16] [ERROR] [INSTALLATION] [PID:18] [MEM:2 MB] Installation failed: PFG_LogReader - Installation cannot proceed:  | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Installation cannot proceed: ","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:26:49+00:00 DEBUG (7): [2025-07-09 06:26:49] [ERROR] [INSTALLATION] [PID:16] [MEM:2 MB] Installation failed: PFG_Analytics - Installation cannot proceed:  | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Installation cannot proceed: ","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:28:31+00:00 DEBUG (7): [2025-07-09 06:28:31] [ERROR] [INSTALLATION] [PID:10] [MEM:2 MB] Installation failed: PFG_LogReader - Installation cannot proceed: Unknown validation error | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Installation cannot proceed: Unknown validation error","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:28:36+00:00 DEBUG (7): [2025-07-09 06:28:36] [ERROR] [INSTALLATION] [PID:13] [MEM:2 MB] Installation failed: PFG_Analytics - Installation cannot proceed: Unknown validation error | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Installation cannot proceed: Unknown validation error","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:37:51+00:00 DEBUG (7): [2025-07-09 06:37:51] [ERROR] [INSTALLATION] [PID:15] [MEM:2 MB] Installation failed: PFG_Altcurrency - Installation cannot proceed: Unknown validation error | Context: {"action":"failed","module_name":"PFG_Altcurrency","details":"Installation cannot proceed: Unknown validation error","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:37:56+00:00 DEBUG (7): [2025-07-09 06:37:56] [ERROR] [INSTALLATION] [PID:16] [MEM:2 MB] Installation failed: PFG_Analytics - Installation cannot proceed: Unknown validation error | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Installation cannot proceed: Unknown validation error","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:38:03+00:00 DEBUG (7): [2025-07-09 06:38:03] [ERROR] [INSTALLATION] [PID:12] [MEM:2 MB] Installation failed: PFG_LogReader - Installation cannot proceed: Unknown validation error | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Installation cannot proceed: Unknown validation error","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:12+00:00 DEBUG (7): [2025-07-09 06:39:12] [INFO] [INSTALLATION] [PID:11] [MEM:2 MB] Installation started: PFG_LogReader - Installing from repository pfg-log-reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository pfg-log-reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:13+00:00 DEBUG (7): [2025-07-09 06:39:13] [INFO] [INSTALLATION] [PID:11] [MEM:2 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:13+00:00 DEBUG (7): [2025-07-09 06:39:13] [ERROR] [INSTALLATION] [PID:11] [MEM:2 MB] Installation failed: PFG_LogReader - Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e/.gitignore | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e\/.gitignore","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:17+00:00 DEBUG (7): [2025-07-09 06:39:17] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation started: PFG_Analytics - Installing from repository pfg-analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository pfg-analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:18+00:00 DEBUG (7): [2025-07-09 06:39:18] [INFO] [INSTALLATION] [PID:14] [MEM:2 MB] Installation module_detected: PFG_Analytics - Detected actual module name: PFG_Analytics (expected: PFG_Analytics) | Context: {"action":"module_detected","module_name":"PFG_Analytics","details":"Detected actual module name: PFG_Analytics (expected: PFG_Analytics)","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:39:18+00:00 DEBUG (7): [2025-07-09 06:39:18] [ERROR] [INSTALLATION] [PID:14] [MEM:2 MB] Installation failed: PFG_Analytics - Archive contains file that would be extracted outside allowed directory: pfg-pfg-analytics-164a06ec854b/app/ | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Archive contains file that would be extracted outside allowed directory: pfg-pfg-analytics-164a06ec854b\/app\/","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [INSTALLATION] [PID:11] [MEM:16 MB] Installation started: PFG_Analytics - Installing from repository pfg-analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository pfg-analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [INFO] [INSTALLATION] [PID:11] [MEM:16 MB] Installation module_detected: PFG_Analytics - Detected actual module name: PFG_Analytics (expected: PFG_Analytics) | Context: {"action":"module_detected","module_name":"PFG_Analytics","details":"Detected actual module name: PFG_Analytics (expected: PFG_Analytics)","user":"martinpfg","ip_address":"**********"}
2025-07-09T06:56:51+00:00 DEBUG (7): [2025-07-09 06:56:51] [ERROR] [INSTALLATION] [PID:11] [MEM:16 MB] Installation failed: PFG_Analytics - Archive contains potentially dangerous file type: pfg-pfg-analytics-164a06ec854b/js/pfg/analytics.js | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Archive contains potentially dangerous file type: pfg-pfg-analytics-164a06ec854b\/js\/pfg\/analytics.js","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [INSTALLATION] [PID:15] [MEM:16 MB] Installation started: PFG_Analytics - Installing from repository pfg-analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository pfg-analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:04:49+00:00 DEBUG (7): [2025-07-09 07:04:49] [INFO] [INSTALLATION] [PID:15] [MEM:16 MB] Installation module_detected: PFG_Analytics - Detected actual module name: PFG_Analytics (expected: PFG_Analytics) | Context: {"action":"module_detected","module_name":"PFG_Analytics","details":"Detected actual module name: PFG_Analytics (expected: PFG_Analytics)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:04:50+00:00 DEBUG (7): [2025-07-09 07:04:50] [ERROR] [INSTALLATION] [PID:15] [MEM:16 MB] Installation failed: PFG_Analytics - Could not write file: /magento/var/pfg_core/temp/install_1752044689/pfg-pfg-analytics-164a06ec854b/ | Context: {"action":"failed","module_name":"PFG_Analytics","details":"Could not write file: \/magento\/var\/pfg_core\/temp\/install_1752044689\/pfg-pfg-analytics-164a06ec854b\/","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:15+00:00 DEBUG (7): [2025-07-09 07:08:15] [INFO] [INSTALLATION] [PID:9] [MEM:16 MB] Installation started: PFG_CloudflareIntegration - Installing from repository cloudflare-integration (version master) | Context: {"action":"started","module_name":"PFG_CloudflareIntegration","details":"Installing from repository cloudflare-integration (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:17+00:00 DEBUG (7): [2025-07-09 07:08:17] [INFO] [INSTALLATION] [PID:9] [MEM:16 MB] Installation module_detected: PFG_Cloudflare - Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration) | Context: {"action":"module_detected","module_name":"PFG_Cloudflare","details":"Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:17+00:00 DEBUG (7): [2025-07-09 07:08:17] [ERROR] [INSTALLATION] [PID:9] [MEM:16 MB] Installation failed: PFG_CloudflareIntegration - Archive contains potentially dangerous file path: pfg-cloudflare-integration-47f7c20578db/.gitignore | Context: {"action":"failed","module_name":"PFG_CloudflareIntegration","details":"Archive contains potentially dangerous file path: pfg-cloudflare-integration-47f7c20578db\/.gitignore","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:22+00:00 DEBUG (7): [2025-07-09 07:08:22] [INFO] [INSTALLATION] [PID:10] [MEM:16 MB] Installation started: PFG_Analytics - Installing from repository pfg-analytics (version master) | Context: {"action":"started","module_name":"PFG_Analytics","details":"Installing from repository pfg-analytics (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:23+00:00 DEBUG (7): [2025-07-09 07:08:23] [INFO] [INSTALLATION] [PID:10] [MEM:16 MB] Installation module_detected: PFG_Analytics - Detected actual module name: PFG_Analytics (expected: PFG_Analytics) | Context: {"action":"module_detected","module_name":"PFG_Analytics","details":"Detected actual module name: PFG_Analytics (expected: PFG_Analytics)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:23+00:00 DEBUG (7): [2025-07-09 07:08:23] [INFO] [INSTALLATION] [PID:10] [MEM:16 MB] Installation completed: PFG_Analytics - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_Analytics","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:23+00:00 DEBUG (7): [2025-07-09 07:08:23] [INFO] [INSTALLATION] [PID:11] [MEM:16 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:23+00:00 DEBUG (7): [2025-07-09 07:08:23] [ERROR] [INSTALLATION] [PID:11] [MEM:16 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:32+00:00 DEBUG (7): [2025-07-09 07:08:32] [INFO] [INSTALLATION] [PID:13] [MEM:16 MB] Installation started: PFG_LogReader - Installing from repository pfg-log-reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository pfg-log-reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:33+00:00 DEBUG (7): [2025-07-09 07:08:33] [INFO] [INSTALLATION] [PID:13] [MEM:16 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:08:33+00:00 DEBUG (7): [2025-07-09 07:08:33] [ERROR] [INSTALLATION] [PID:13] [MEM:16 MB] Installation failed: PFG_LogReader - Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e/.gitignore | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e\/.gitignore","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:09:22+00:00 DEBUG (7): [2025-07-09 07:09:22] [INFO] [INSTALLATION] [PID:17] [MEM:16 MB] Installation started: PFG_LogReader - Installing from repository pfg-log-reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository pfg-log-reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:09:23+00:00 DEBUG (7): [2025-07-09 07:09:23] [INFO] [INSTALLATION] [PID:17] [MEM:16 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:09:23+00:00 DEBUG (7): [2025-07-09 07:09:23] [ERROR] [INSTALLATION] [PID:17] [MEM:16 MB] Installation failed: PFG_LogReader - Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e/.gitignore | Context: {"action":"failed","module_name":"PFG_LogReader","details":"Archive contains potentially dangerous file path: pfg-pfg-log-reader-70d05dbb414e\/.gitignore","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:29+00:00 DEBUG (7): [2025-07-09 07:10:29] [INFO] [INSTALLATION] [PID:11] [MEM:16 MB] Installation started: PFG_LogReader - Installing from repository pfg-log-reader (version v1.0.0) | Context: {"action":"started","module_name":"PFG_LogReader","details":"Installing from repository pfg-log-reader (version v1.0.0)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:30+00:00 DEBUG (7): [2025-07-09 07:10:30] [INFO] [INSTALLATION] [PID:11] [MEM:16 MB] Installation module_detected: PFG_LogReader - Detected actual module name: PFG_LogReader (expected: PFG_LogReader) | Context: {"action":"module_detected","module_name":"PFG_LogReader","details":"Detected actual module name: PFG_LogReader (expected: PFG_LogReader)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:30+00:00 DEBUG (7): [2025-07-09 07:10:30] [INFO] [INSTALLATION] [PID:11] [MEM:16 MB] Installation completed: PFG_LogReader - Successfully installed version v1.0.0 | Context: {"action":"completed","module_name":"PFG_LogReader","details":"Successfully installed version v1.0.0","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:30+00:00 DEBUG (7): [2025-07-09 07:10:30] [INFO] [INSTALLATION] [PID:12] [MEM:16 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:30+00:00 DEBUG (7): [2025-07-09 07:10:30] [ERROR] [INSTALLATION] [PID:12] [MEM:16 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:40+00:00 DEBUG (7): [2025-07-09 07:10:40] [INFO] [INSTALLATION] [PID:14] [MEM:16 MB] Installation started: PFG_Altcurrency - Installing from repository pfg-altcurrency (version master) | Context: {"action":"started","module_name":"PFG_Altcurrency","details":"Installing from repository pfg-altcurrency (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:41+00:00 DEBUG (7): [2025-07-09 07:10:41] [INFO] [INSTALLATION] [PID:14] [MEM:16 MB] Installation module_detected: PFG_AltCurrency - Detected actual module name: PFG_AltCurrency (expected: PFG_Altcurrency) | Context: {"action":"module_detected","module_name":"PFG_AltCurrency","details":"Detected actual module name: PFG_AltCurrency (expected: PFG_Altcurrency)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:41+00:00 DEBUG (7): [2025-07-09 07:10:41] [INFO] [INSTALLATION] [PID:14] [MEM:16 MB] Installation completed: PFG_Altcurrency - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_Altcurrency","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:42+00:00 DEBUG (7): [2025-07-09 07:10:42] [INFO] [INSTALLATION] [PID:15] [MEM:16 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:42+00:00 DEBUG (7): [2025-07-09 07:10:42] [ERROR] [INSTALLATION] [PID:15] [MEM:16 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:52+00:00 DEBUG (7): [2025-07-09 07:10:52] [INFO] [INSTALLATION] [PID:17] [MEM:16 MB] Installation started: PFG_CloudflareIntegration - Installing from repository cloudflare-integration (version master) | Context: {"action":"started","module_name":"PFG_CloudflareIntegration","details":"Installing from repository cloudflare-integration (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:53+00:00 DEBUG (7): [2025-07-09 07:10:53] [INFO] [INSTALLATION] [PID:17] [MEM:16 MB] Installation module_detected: PFG_Cloudflare - Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration) | Context: {"action":"module_detected","module_name":"PFG_Cloudflare","details":"Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:53+00:00 DEBUG (7): [2025-07-09 07:10:53] [INFO] [INSTALLATION] [PID:17] [MEM:16 MB] Installation completed: PFG_CloudflareIntegration - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_CloudflareIntegration","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:53+00:00 DEBUG (7): [2025-07-09 07:10:53] [INFO] [INSTALLATION] [PID:18] [MEM:16 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:10:53+00:00 DEBUG (7): [2025-07-09 07:10:53] [ERROR] [INSTALLATION] [PID:18] [MEM:16 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T07:15:03+00:00 DEBUG (7): [2025-07-09 07:15:03] [INFO] [INSTALLATION] [PID:17] [MEM:8 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:15:03+00:00 DEBUG (7): [2025-07-09 07:15:03] [ERROR] [INSTALLATION] [PID:17] [MEM:8 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T07:28:05+00:00 DEBUG (7): [2025-07-09 07:28:05] [INFO] [INSTALLATION] [PID:18] [MEM:4 MB] Installation started: PFG_CloudflareIntegration - Installing from repository cloudflare-integration (version master) | Context: {"action":"started","module_name":"PFG_CloudflareIntegration","details":"Installing from repository cloudflare-integration (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:28:06+00:00 DEBUG (7): [2025-07-09 07:28:06] [INFO] [INSTALLATION] [PID:18] [MEM:4 MB] Installation module_detected: PFG_Cloudflare - Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration) | Context: {"action":"module_detected","module_name":"PFG_Cloudflare","details":"Detected actual module name: PFG_Cloudflare (expected: PFG_CloudflareIntegration)","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:28:06+00:00 DEBUG (7): [2025-07-09 07:28:06] [INFO] [INSTALLATION] [PID:18] [MEM:4 MB] Installation completed: PFG_CloudflareIntegration - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_CloudflareIntegration","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:28:06+00:00 DEBUG (7): [2025-07-09 07:28:06] [INFO] [INSTALLATION] [PID:9] [MEM:4 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:28:06+00:00 DEBUG (7): [2025-07-09 07:28:06] [ERROR] [INSTALLATION] [PID:9] [MEM:4 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T07:29:04+00:00 DEBUG (7): [2025-07-09 07:29:04] [INFO] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T07:29:05+00:00 DEBUG (7): [2025-07-09 07:29:05] [ERROR] [INSTALLATION] [PID:15] [MEM:2 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [INSTALLATION] [PID:12] [MEM:2 MB] Installation started: PFG_Altcurrency - Installing from repository pfg-altcurrency (version master) | Context: {"action":"started","module_name":"PFG_Altcurrency","details":"Installing from repository pfg-altcurrency (version master)","user":"martinpfg","ip_address":"**********"}
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [INSTALLATION] [PID:12] [MEM:2 MB] Installation module_detected: PFG_AltCurrency - Detected actual module name: PFG_AltCurrency (expected: PFG_Altcurrency) | Context: {"action":"module_detected","module_name":"PFG_AltCurrency","details":"Detected actual module name: PFG_AltCurrency (expected: PFG_Altcurrency)","user":"martinpfg","ip_address":"**********"}
2025-07-09T09:13:30+00:00 DEBUG (7): [2025-07-09 09:13:30] [INFO] [INSTALLATION] [PID:12] [MEM:2 MB] Installation completed: PFG_Altcurrency - Successfully installed version master | Context: {"action":"completed","module_name":"PFG_Altcurrency","details":"Successfully installed version master","user":"martinpfg","ip_address":"**********"}
2025-07-09T09:13:31+00:00 DEBUG (7): [2025-07-09 09:13:31] [INFO] [INSTALLATION] [PID:15] [MEM:4 MB] Installation post_install_started: system - Starting post-installation cleanup | Context: {"action":"post_install_started","module_name":"system","details":"Starting post-installation cleanup","user":"martinpfg","ip_address":"**********"}
2025-07-09T09:13:31+00:00 DEBUG (7): [2025-07-09 09:13:31] [ERROR] [INSTALLATION] [PID:15] [MEM:4 MB] Installation post_install_failed: system -  | Context: {"action":"post_install_failed","module_name":"system","details":null,"user":"martinpfg","ip_address":"**********"}
