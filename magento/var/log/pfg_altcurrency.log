2025-07-09T10:00:44+00:00 DEBUG (7): 2025-07-09 10:00:44 [WARNING] PFG_AltCurrency: Currency symbol required sanitization on load | Context: {"original":{"0":"\u20ac"},"sanitized":"\u20ac"}
2025-07-09T10:06:24+00:00 DEBUG (7): 2025-07-09 10:06:24 [WARNING] PFG_AltCurrency: Currency symbol required sanitization on load | Context: {"original":{"0":"\u20ac"},"sanitized":"\u20ac"}
2025-07-09T10:06:49+00:00 DEBUG (7): 2025-07-09 10:06:49 [INFO] PFG_AltCurrency: Currency symbol updated successfully | Context: {"old_value":"\u20ac","new_value":"\u20ac","scope":"default","scope_id":0}
2025-07-09T10:06:49+00:00 DEBUG (7): 2025-07-09 10:06:49 [INFO] PFG_AltCurrency: Currency rate updated successfully | Context: {"old_value":"0.51129","new_value":0.51129,"scope":"default","scope_id":0}
2025-07-09T10:07:32+00:00 DEBUG (7): 2025-07-09 10:07:32 [INFO] PFG_AltCurrency: Performance metrics refreshed
2025-07-09T10:07:36+00:00 DEBUG (7): 2025-07-09 10:07:36 [INFO] PFG_AltCurrency: Configuration test completed successfully | Context: {"symbol":"\u20ac","rate":0.51129,"examples_count":5}
2025-07-09T10:19:45+00:00 DEBUG (7): 2025-07-09 10:19:45 [INFO] PFG_AltCurrency: Configuration test completed successfully | Context: {"symbol":"\u20ac","rate":0.51129,"examples_count":5}
2025-07-09T10:20:03+00:00 DEBUG (7): 2025-07-09 10:20:03 [INFO] PFG_AltCurrency: Performance metrics refreshed
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3601,"contains_dollar":false,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER',\n    content_ids: ['PRJ-B001-1ER'],\n    content_type: 'product',\n    value: '286',\n","html_length":234,"contains_dollar":false,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'PRJ-B001-1ER',\n        value:'286',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER","html_length":1203,"contains_dollar":true,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1920,"contains_dollar":true,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":2500,"contains_dollar":false,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8135,"contains_dollar":true,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLWNhc2lvLXByby10cmVrLXByai1iMDAxLTFlcg,,/pr","html_length":4149,"contains_dollar":true,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e43fcb018b\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752056828.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1143,"contains_dollar":true,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '114689';\n\tvar product_price \t= '286';\n\tvar c","html_length":547,"contains_dollar":false,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"114689\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Casio","html_length":1140,"contains_dollar":false,"contains_product":true}
2025-07-09T10:27:08+00:00 DEBUG (7): 2025-07-09 10:27:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4277,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3601,"contains_dollar":false,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER',\n    content_ids: ['PRJ-B001-1ER'],\n    content_type: 'product',\n    value: '286',\n","html_length":234,"contains_dollar":false,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'PRJ-B001-1ER',\n        value:'286',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER","html_length":1203,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1920,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":2500,"contains_dollar":false,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8135,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLWNhc2lvLXByby10cmVrLXByai1iMDAxLTFlcg,,/pr","html_length":4149,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:08+00:00 DEBUG (7): 2025-07-09 10:32:08 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e4528f28c8\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:09+00:00 DEBUG (7): 2025-07-09 10:32:09 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057128.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1143,"contains_dollar":true,"contains_product":true}
2025-07-09T10:32:09+00:00 DEBUG (7): 2025-07-09 10:32:09 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '114689';\n\tvar product_price \t= '286';\n\tvar c","html_length":547,"contains_dollar":false,"contains_product":true}
2025-07-09T10:32:09+00:00 DEBUG (7): 2025-07-09 10:32:09 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"114689\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Casio","html_length":1140,"contains_dollar":false,"contains_product":true}
2025-07-09T10:32:09+00:00 DEBUG (7): 2025-07-09 10:32:09 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4277,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3601,"contains_dollar":false,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER',\n    content_ids: ['PRJ-B001-1ER'],\n    content_type: 'product',\n    value: '286',\n","html_length":234,"contains_dollar":false,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'PRJ-B001-1ER',\n        value:'286',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER","html_length":1203,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1920,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":2500,"contains_dollar":false,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8135,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLWNhc2lvLXByby10cmVrLXByai1iMDAxLTFlcg,,/pr","html_length":4149,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e46ac863ec\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057516.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1143,"contains_dollar":true,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '114689';\n\tvar product_price \t= '286';\n\tvar c","html_length":547,"contains_dollar":false,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"114689\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Casio","html_length":1140,"contains_dollar":false,"contains_product":true}
2025-07-09T10:38:36+00:00 DEBUG (7): 2025-07-09 10:38:36 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4277,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:05+00:00 DEBUG (7): 2025-07-09 10:41:05 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3601,"contains_dollar":false,"contains_product":true}
2025-07-09T10:41:05+00:00 DEBUG (7): 2025-07-09 10:41:05 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER',\n    content_ids: ['PRJ-B001-1ER'],\n    content_type: 'product',\n    value: '286',\n","html_length":234,"contains_dollar":false,"contains_product":true}
2025-07-09T10:41:05+00:00 DEBUG (7): 2025-07-09 10:41:05 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'PRJ-B001-1ER',\n        value:'286',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER","html_length":1203,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:05+00:00 DEBUG (7): 2025-07-09 10:41:05 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1920,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":2500,"contains_dollar":false,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8135,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLWNhc2lvLXByby10cmVrLXByai1iMDAxLTFlcg,,/pr","html_length":4149,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e474248b98\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057665.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1143,"contains_dollar":true,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '114689';\n\tvar product_price \t= '286';\n\tvar c","html_length":547,"contains_dollar":false,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"114689\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Casio","html_length":1140,"contains_dollar":false,"contains_product":true}
2025-07-09T10:41:06+00:00 DEBUG (7): 2025-07-09 10:41:06 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4277,"contains_dollar":true,"contains_product":true}
2025-07-09T10:43:25+00:00 DEBUG (7): 2025-07-09 10:43:25 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"nav-bar\">\n\<!-- menu start -->\n\<ul id=\"nav\">\n\<li  class=\"  level0\"> \<a href=\"http://localhost:8580/index.php/vipadmin/dashboard/index/key/1a7816ac138eae4b60d7521c520760e9/\"   class=\"\">\<spa","html_length":36634,"contains_dollar":true,"contains_product":true}
2025-07-09T10:43:25+00:00 DEBUG (7): 2025-07-09 10:43:25 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<h3>\u041a\u043e\u043d\u0444\u0438\u0433\u0443\u0440\u0430\u0446\u0438\u044f\</h3>\n\<ul id=\"system_config_tabs\" class=\"tabs config-tabs\">\n        \<li >\n        \<dl>\n        \<dt class=\"label\" style=\"\">Stenik\</dt>\n                            \<dd>\n ","html_length":28008,"contains_dollar":true,"contains_product":true}
2025-07-09T10:43:25+00:00 DEBUG (7): 2025-07-09 10:43:25 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div class=\"switcher\">\n    \<label for=\"store_switcher\">\u0422\u0435\u043a\u0443\u0449 \u043e\u0431\u0445\u0432\u0430\u0442 \u043d\u0430 \u043a\u043e\u043d\u0444\u0438\u0433\u0443\u0440\u0438\u0440\u0430\u043d\u0435:\</label>\n    \<a href=\"http://merch.docs.magento.com/ce/user_guide/configuration/scope.htm","html_length":29714,"contains_dollar":true,"contains_product":true}
2025-07-09T10:43:25+00:00 DEBUG (7): 2025-07-09 10:43:25 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div class=\"entry-edit\">\n    \<div class=\"section-config\">\<div class=\"entry-edit-head collapseable\" >\<a id=\"pfg_altcurrency_settings-head\" href=\"#\" onclick=\"Fieldset.toggleCollapse('pfg_altcurrency_set","html_length":15828,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:25+00:00 DEBUG (7): 2025-07-09 10:43:25 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"content-header\">\n    \<table cellspacing=\"0\">\n        \<tr>\n            \<td>\n                \<h3>Alt Currency\</h3>\n            \</td>\n            \<td class=\"form-buttons\">\<button  id=\"id_c87","html_length":29466,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:25+00:00 DEBUG (7): 2025-07-09 10:43:25 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<style>\n        .amasty-extensions-tab:after {\n            content: '2';\n        }\n    \</style>\n\n\<div class=\"content-header\">\n    \<table cellspacing=\"0\">\n        \<tr>\n            \<td>\n            ","html_length":29566,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p class=\"bug-report\">\n    \<a href=\"http://www.magentocommerce.com/bug-tracking\" id=\"footer_bug_tracking\">\u041f\u043e\u043c\u043e\u0433\u043d\u0438 \u0437\u0430 \u0441\u0442\u0430\u0431\u0438\u043b\u043d\u043e\u0441\u0442\u0442\u0430 \u043d\u0430 Magento - \u0414\u043e\u043a\u043b\u0430\u0434\u0432\u0430\u0439 \u0432\u0441\u0438\u0447\u043a\u0438 \u043f","html_length":9041,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n//\<![CDATA[\n    function hideRowArrayElements(arr)\n    {\n        for (var a = 0; a \< arr.length; a++) {\n            $(arr[a]).up(1).hide();\n        }\n    }\n\n    functio","html_length":8464,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n//\<![CDATA[\nvar freeModel = Class.create();\nfreeModel.prototype = {\n    initialize : function()\n    {\n        this.reload = false;\n        this.bindFreeMethodCutoffCost","html_length":9288,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n//\<![CDATA[\nvar CountryModel = Class.create();\nCountryModel.prototype = {\n    initialize : function()\n    {\n        this.reload = false;\n        this.bindSpecificCountr","html_length":3845,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n//\<![CDATA[\n    function hideRowArrayElements(arr)\n    {\n        for (var a = 0; a \< arr.length; a++) {\n            $(arr[a]).up(1).hide();\n        }\n    }\n\n    functio","html_length":21597,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":866,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":4237,"contains_dollar":true,"contains_product":false}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057665.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1120,"contains_dollar":true,"contains_product":true}
2025-07-09T10:43:26+00:00 DEBUG (7): 2025-07-09 10:43:26 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2567,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3601,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER',\n    content_ids: ['PRJ-B001-1ER'],\n    content_type: 'product',\n    value: '286',\n","html_length":234,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'PRJ-B001-1ER',\n        value:'286',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER","html_length":1203,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1920,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":2500,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8135,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLWNhc2lvLXByby10cmVrLXByai1iMDAxLTFlcg,,/pr","html_length":4149,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e48497d935\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057929.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1143,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '114689';\n\tvar product_price \t= '286';\n\tvar c","html_length":547,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"114689\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Casio","html_length":1140,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:29+00:00 DEBUG (7): 2025-07-09 10:45:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4277,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3601,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER',\n    content_ids: ['PRJ-B001-1ER'],\n    content_type: 'product',\n    value: '286',\n","html_length":234,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'PRJ-B001-1ER',\n        value:'286',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Casio Pro Trek - PRJ-B001-1ER","html_length":1203,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1920,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":2500,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8135,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLWNhc2lvLXByby10cmVrLXByai1iMDAxLTFlcg,,/pr","html_length":4149,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e484d95a15\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057929.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1143,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '114689';\n\tvar product_price \t= '286';\n\tvar c","html_length":547,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"114689\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Casio","html_length":1140,"contains_dollar":false,"contains_product":true}
2025-07-09T10:45:33+00:00 DEBUG (7): 2025-07-09 10:45:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4277,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id =\"category-container-111961\" class=\"onsale-category-container\" onclick=\"document.location='http://localhost:8580/mazhki-chasovnik-private-label-vip143'\" style=\"width: 80px; height: 80px; backg","html_length":810,"contains_dollar":true,"contains_product":false}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id =\"category-container-62650\" class=\"onsale-category-container\" onclick=\"document.location='http://localhost:8580/certina-c033-407-22-031-00'\" style=\"width: 80px; height: 80px; background-image:","html_length":792,"contains_dollar":true,"contains_product":false}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id =\"category-container-62677\" class=\"onsale-category-container\" onclick=\"document.location='http://localhost:8580/certina-ds-powermatic-c026-407-11-057-00'\" style=\"width: 80px; height: 80px; bac","html_length":806,"contains_dollar":true,"contains_product":false}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id =\"category-container-62678\" class=\"onsale-category-container\" onclick=\"document.location='http://localhost:8580/certina-c026-407-16-087-00'\" style=\"width: 80px; height: 80px; background-image:","html_length":792,"contains_dollar":true,"contains_product":false}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Performance metrics update | Context: {"total_calls":100,"processed_elements":20,"cache_hits":71,"processing_time":0.0017802715301513672,"memory_usage":2616,"last_updated":1752057958}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057929.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1115,"contains_dollar":true,"contains_product":true}
2025-07-09T10:45:58+00:00 DEBUG (7): 2025-07-09 10:45:58 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2562,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3419,"contains_dollar":false,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script>\nfbq('track', 'ViewContent', {\n    content_name: '\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Private Label - VIP143',\n    content_ids: ['VIP143'],\n    content_type: 'product',\n    value: '1704',\n    currency","html_length":222,"contains_dollar":false,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n    var productData = {\n        ids:'VIP143',\n        value:'1704',\n        currency:'BGN',\n        name:'\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Private Label - VIP143'\n    };\n\n  ","html_length":1191,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod\?\n    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\n    n.push=n;n.loaded=!0;n.ver","html_length":1896,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<nav class=\"breadcrumbs\">\n        \<ul itemscope itemtype=\"http://schema.org/BreadcrumbList\">\n                                                                                            \<li class=","html_length":1637,"contains_dollar":false,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div style=\"display: none\" class=\"hiddenGallery\">\n                \<a href=\"http://localhost:8580/media/catalog/product/cache/1/image/9df78eab33525d08d6e5fb8d27136e95/images/catalog/product/placehold","html_length":8921,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id =\"product-container-111961\" class=\"onsale-product-container-inside\" onclick=\"document.location='http://localhost:8580/mazhki-chasovnik-private-label-vip143'\" style=\"width: 80px; height: 80px; ","html_length":810,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_dollar":false,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<div class=\"section personalized-label-section\">\n    \<form action=\"http://localhost:8580/checkout/cart/add/uenc/aHR0cDovL2xvY2FsaG9zdC9tYXpoa2ktY2hhc292bmlrLXByaXZhdGUtbGFiZWwtdmlwMTQz/product/111961","html_length":4137,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n\<div class=\"unicreditLeasing leasing-calculator\" id=\"lease_calculator_686e486c41472\">\n    \n    \<input type=\"hidden\" name=\"config_json\" value=\"[]\">\n\n    \<script>\n        jQuery(function(){\n          ","html_length":1012,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752057929.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1136,"contains_dollar":true,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '111961';\n\tvar product_price \t= '1704';\n\tvar ","html_length":530,"contains_dollar":false,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script text=\"text/javascript\">\n  var _learnq = _learnq || [];\n\n  var item = {\n    ProductID: \"111961\",\n    Name: \"\\u041c\\u044a\\u0436\\u043a\\u0438 \\u0447\\u0430\\u0441\\u043e\\u0432\\u043d\\u0438\\u043a Priva","html_length":963,"contains_dollar":false,"contains_product":true}
2025-07-09T10:46:04+00:00 DEBUG (7): 2025-07-09 10:46:04 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4076,"contains_dollar":true,"contains_product":true}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3419,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Private Label - VIP143 - \u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</title>\n\<meta name=\"description\" content","html_length":8774}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":8774,"contains_price_box":true}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:21+00:00 DEBUG (7): 2025-07-09 10:49:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-111961\">\n                          ","html_length":346}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6afa4c3e"}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n \<style>    \n\t.newpay-popup\n\t{\n\t\tdisplay: none;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: absolute;\n   \t\tz-index: 9999999;\n\t\ttop: 0;\n\t\tpadding: 10%;\n\t\tbackground: #00000080;\n\t    padding-left: 20%;\n","html_length":3016,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p style=\"text-align: justify;\">\<strong>\<a title=\"\u0413\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u0438 \u0431\u0438\u0436\u0443\u0442\u0430\" href=\"/gravirane-na-chasovnici\">\u041b\u0430\u0437\u0435\u0440\u043d\u043e \u0433\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u0437\u0430 17.90\u043b\u0432\</a> \</str","html_length":223,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":2570,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752058161.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1136,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '111961';\n\tvar product_price \t= '1704';\n\tvar ","html_length":530,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4076,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26123,"contains_price_box":false}
2025-07-09T10:49:22+00:00 DEBUG (7): 2025-07-09 10:49:22 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:49:23+00:00 DEBUG (7): 2025-07-09 10:49:23 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26545,"contains_price_box":false}
2025-07-09T10:49:23+00:00 DEBUG (7): 2025-07-09 10:49:23 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3419,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Private Label - VIP143 - \u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</title>\n\<meta name=\"description\" content","html_length":8774}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":8774,"contains_price_box":true}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-111961\">\n                          ","html_length":346}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0704,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0704,00\u00a0\u043b\u0432."}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6afa4c3e"}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n \<style>    \n\t.newpay-popup\n\t{\n\t\tdisplay: none;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: absolute;\n   \t\tz-index: 9999999;\n\t\ttop: 0;\n\t\tpadding: 10%;\n\t\tbackground: #00000080;\n\t    padding-left: 20%;\n","html_length":3016,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p style=\"text-align: justify;\">\<strong>\<a title=\"\u0413\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u0438 \u0431\u0438\u0436\u0443\u0442\u0430\" href=\"/gravirane-na-chasovnici\">\u041b\u0430\u0437\u0435\u0440\u043d\u043e \u0433\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u0437\u0430 17.90\u043b\u0432\</a> \</str","html_length":223,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":2570,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752058370.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1136,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '111961';\n\tvar product_price \t= '1704';\n\tvar ","html_length":530,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:50+00:00 DEBUG (7): 2025-07-09 10:52:50 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4076,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:51+00:00 DEBUG (7): 2025-07-09 10:52:51 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26123,"contains_price_box":false}
2025-07-09T10:52:51+00:00 DEBUG (7): 2025-07-09 10:52:51 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:51+00:00 DEBUG (7): 2025-07-09 10:52:51 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26545,"contains_price_box":false}
2025-07-09T10:52:51+00:00 DEBUG (7): 2025-07-09 10:52:51 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3419,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Private Label - VIP143 - \u041c\u044a\u0436\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</title>\n\<meta name=\"description\" content","html_length":8774}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":8774,"contains_price_box":true}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-111961\">\n                          ","html_length":346}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0704,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0704,00\u00a0\u043b\u0432."}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6afa4c3e"}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n \<style>    \n\t.newpay-popup\n\t{\n\t\tdisplay: none;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: absolute;\n   \t\tz-index: 9999999;\n\t\ttop: 0;\n\t\tpadding: 10%;\n\t\tbackground: #00000080;\n\t    padding-left: 20%;\n","html_length":3016,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p style=\"text-align: justify;\">\<strong>\<a title=\"\u0413\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u0438 \u0431\u0438\u0436\u0443\u0442\u0430\" href=\"/gravirane-na-chasovnici\">\u041b\u0430\u0437\u0435\u0440\u043d\u043e \u0433\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u0437\u0430 17.90\u043b\u0432\</a> \</str","html_length":223,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":2570,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752058370.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1136,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '111961';\n\tvar product_price \t= '1704';\n\tvar ","html_length":530,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:57+00:00 DEBUG (7): 2025-07-09 10:52:57 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4076,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:52:58+00:00 DEBUG (7): 2025-07-09 10:52:58 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26123,"contains_price_box":false}
2025-07-09T10:52:58+00:00 DEBUG (7): 2025-07-09 10:52:58 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:52:58+00:00 DEBUG (7): 2025-07-09 10:52:58 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26545,"contains_price_box":false}
2025-07-09T10:52:58+00:00 DEBUG (7): 2025-07-09 10:52:58 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u2013 \u041b\u0443\u043a\u0441\u043e\u0437\u043d\u0438 \u0438 \u0434\u043e\u0441\u0442\u044a\u043f\u043d\u0438 | Vip-Watches.net\</title>\n\<meta name=\"description\"","html_length":4204}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":4204,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":91181,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \n    \<div class=\"toolbarRightPosition\">        \n        \<div class=\"dropDownfilter showBy\">\n                                                \<a href=\"javascript:;\" class=\"openFilters\">\u041f\u043e\u043a\u0430\u0436\u0438 ","html_length":1660,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100361\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            136,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"136,00","original":"\n                                            136,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"136,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"136.00","parsed_float":136,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":69.53544000000001,"formatted_result":"\u20ac 69,54"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"517d86dd"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100488\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            765,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"765,00","original":"\n                                            765,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"765,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"765.00","parsed_float":765,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":391.13685000000004,"formatted_result":"\u20ac 391,14"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7cb67f18"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100491\">\n                          ","html_length":346}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0073,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0073,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7ff719da"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100522\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b7addf58"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100524\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"3e5e5267"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100525\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"83f38161"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100526\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6dda011b"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100537\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            889,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"889,00","original":"\n                                            889,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"889,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"889.00","parsed_float":889,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":454.53681,"formatted_result":"\u20ac 454,54"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"bea0faef"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100538\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            889,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"889,00","original":"\n                                            889,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"889,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"889.00","parsed_float":889,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":454.53681,"formatted_result":"\u20ac 454,54"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"61ac21d4"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100573\">\n                          ","html_length":342}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":342,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            58,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"58,00","original":"\n                                            58,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"58,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"58.00","parsed_float":58,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":29.65482,"formatted_result":"\u20ac 29,65"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b202c1fb"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100576\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            795,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"795,00","original":"\n                                            795,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"795,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"795.00","parsed_float":795,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":406.47555,"formatted_result":"\u20ac 406,48"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a392819b"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100578\">\n                          ","html_length":346}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0965,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0965,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"389211f6"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100662\">\n                          ","html_length":346}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0039,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0039,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"c9d3e00e"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100741\">\n                          ","html_length":343}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            345,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"345,00","original":"\n                                            345,00\u00a0\u043b\u0432."}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"345,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"345.00","parsed_float":345,"will_convert":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":176.39505,"formatted_result":"\u20ac 176,40"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"ac2a3fd1"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752058370.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1116,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:00+00:00 DEBUG (7): 2025-07-09 10:53:00 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2563,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3444,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a Lee Cooper Elegance - LC07342.390 - \u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</title>\n\<meta name=\"descr","html_length":8841}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":8841,"contains_price_box":true}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100361\">\n                          ","html_length":343}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            136,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"136,00","original":"\n                                            136,00\u00a0\u043b\u0432."}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"136,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"136.00","parsed_float":136,"will_convert":true}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":69.53544000000001,"formatted_result":"\u20ac 69,54"}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"517d86dd"}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n \<style>    \n\t.newpay-popup\n\t{\n\t\tdisplay: none;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: absolute;\n   \t\tz-index: 9999999;\n\t\ttop: 0;\n\t\tpadding: 10%;\n\t\tbackground: #00000080;\n\t    padding-left: 20%;\n","html_length":3016,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p style=\"text-align: justify;\">\<strong>\<a title=\"\u0413\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u0438 \u0431\u0438\u0436\u0443\u0442\u0430\" href=\"/gravirane-na-chasovnici\">\u041b\u0430\u0437\u0435\u0440\u043d\u043e \u0433\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u0437\u0430 17.90\u043b\u0432\</a> \</str","html_length":223,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":2567,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752058370.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1147,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '100361';\n\tvar product_price \t= '136';\n\tvar c","html_length":526,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:14+00:00 DEBUG (7): 2025-07-09 10:53:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4081,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:15+00:00 DEBUG (7): 2025-07-09 10:53:15 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26497,"contains_price_box":false}
2025-07-09T10:53:15+00:00 DEBUG (7): 2025-07-09 10:53:15 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u2013 \u041b\u0443\u043a\u0441\u043e\u0437\u043d\u0438 \u0438 \u0434\u043e\u0441\u0442\u044a\u043f\u043d\u0438 | Vip-Watches.net\</title>\n\<meta name=\"description\"","html_length":4204}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":4204,"contains_price_box":true}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:18+00:00 DEBUG (7): 2025-07-09 10:53:18 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":91181,"contains_price_box":false}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \n    \<div class=\"toolbarRightPosition\">        \n        \<div class=\"dropDownfilter showBy\">\n                                                \<a href=\"javascript:;\" class=\"openFilters\">\u041f\u043e\u043a\u0430\u0436\u0438 ","html_length":1660,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100361\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            136,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"136,00","original":"\n                                            136,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"136,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"136.00","parsed_float":136,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":69.53544000000001,"formatted_result":"\u20ac 69,54"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"517d86dd"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100488\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            765,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"765,00","original":"\n                                            765,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"765,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"765.00","parsed_float":765,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":391.13685000000004,"formatted_result":"\u20ac 391,14"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7cb67f18"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100491\">\n                          ","html_length":346}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0073,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0073,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7ff719da"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100522\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b7addf58"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100524\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"3e5e5267"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100525\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"83f38161"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100526\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"299,00","original":"\n                                            299,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"299,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6dda011b"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100537\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            889,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"889,00","original":"\n                                            889,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"889,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"889.00","parsed_float":889,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":454.53681,"formatted_result":"\u20ac 454,54"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"bea0faef"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100538\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            889,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"889,00","original":"\n                                            889,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"889,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"889.00","parsed_float":889,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":454.53681,"formatted_result":"\u20ac 454,54"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"61ac21d4"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100573\">\n                          ","html_length":342}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":342,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            58,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"58,00","original":"\n                                            58,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"58,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"58.00","parsed_float":58,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":29.65482,"formatted_result":"\u20ac 29,65"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b202c1fb"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100576\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            795,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"795,00","original":"\n                                            795,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"795,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"795.00","parsed_float":795,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":406.47555,"formatted_result":"\u20ac 406,48"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a392819b"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100578\">\n                          ","html_length":346}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0965,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0965,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"389211f6"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100662\">\n                          ","html_length":346}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0039,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"1","original":"\n                                            1\u00a0039,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"1","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1","parsed_float":1,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":0.51129,"formatted_result":"\u20ac 0,51"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"c9d3e00e"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100741\">\n                          ","html_length":343}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            345,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"345,00","original":"\n                                            345,00\u00a0\u043b\u0432."}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Converting price | Context: {"original_text":"345,00","rate":0.51129,"symbol":"\u20ac"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"345.00","parsed_float":345,"will_convert":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":176.39505,"formatted_result":"\u20ac 176,40"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"ac2a3fd1"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Price extraction result | Context: {"extracted":"","original":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:"}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752058370.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1116,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T10:53:19+00:00 DEBUG (7): 2025-07-09 10:53:19 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2563,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u2013 \u041b\u0443\u043a\u0441\u043e\u0437\u043d\u0438 \u0438 \u0434\u043e\u0441\u0442\u044a\u043f\u043d\u0438 | Vip-Watches.net\</title>\n\<meta name=\"description\"","html_length":4204}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":4204,"contains_price_box":true}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:35:20+00:00 DEBUG (7): 2025-07-09 13:35:20 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":91181,"contains_price_box":false}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \n    \<div class=\"toolbarRightPosition\">        \n        \<div class=\"dropDownfilter showBy\">\n                                                \<a href=\"javascript:;\" class=\"openFilters\">\u041f\u043e\u043a\u0430\u0436\u0438 ","html_length":1660,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100361\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            136,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"136.00","parsed_float":136,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":69.53544000000001,"formatted_result":"\u20ac 69,54"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"517d86dd"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100488\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            765,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"765.00","parsed_float":765,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":391.13685000000004,"formatted_result":"\u20ac 391,14"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7cb67f18"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100491\">\n                          ","html_length":346}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0073,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1073.00","parsed_float":1073,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":548.6141700000001,"formatted_result":"\u20ac 548,61"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7ff719da"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100522\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b7addf58"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100524\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"3e5e5267"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100525\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"83f38161"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100526\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            299,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"299.00","parsed_float":299,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":152.87571,"formatted_result":"\u20ac 152,88"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6dda011b"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100537\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            889,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"889.00","parsed_float":889,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":454.53681,"formatted_result":"\u20ac 454,54"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"bea0faef"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100538\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            889,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"889.00","parsed_float":889,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":454.53681,"formatted_result":"\u20ac 454,54"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"61ac21d4"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100573\">\n                          ","html_length":342}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":342,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            58,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"58.00","parsed_float":58,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":29.65482,"formatted_result":"\u20ac 29,65"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b202c1fb"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100576\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            795,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"795.00","parsed_float":795,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":406.47555,"formatted_result":"\u20ac 406,48"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a392819b"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100578\">\n                          ","html_length":346}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0965,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1965.00","parsed_float":1965,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1004.6848500000001,"formatted_result":"\u20ac 1 004,68"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"389211f6"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100662\">\n                          ","html_length":346}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0039,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1039.00","parsed_float":1039,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":531.23031,"formatted_result":"\u20ac 531,23"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"c9d3e00e"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-100741\">\n                          ","html_length":343}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            345,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"345.00","parsed_float":345,"will_convert":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":176.39505,"formatted_result":"\u20ac 176,40"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"ac2a3fd1"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1116,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:35:21+00:00 DEBUG (7): 2025-07-09 13:35:21 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2563,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3428,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a FOSSIL Harwell - ES5266 - \u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</title>\n\<meta name=\"description\" co","html_length":8800}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":8800,"contains_price_box":true}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n \<style>    \n\t.newpay-popup\n\t{\n\t\tdisplay: none;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: absolute;\n   \t\tz-index: 9999999;\n\t\ttop: 0;\n\t\tpadding: 10%;\n\t\tbackground: #00000080;\n\t    padding-left: 20%;\n","html_length":3016,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p style=\"text-align: justify;\">\<strong>\<a title=\"\u0413\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u0438 \u0431\u0438\u0436\u0443\u0442\u0430\" href=\"/gravirane-na-chasovnici\">\u041b\u0430\u0437\u0435\u0440\u043d\u043e \u0433\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u0437\u0430 17.90\u043b\u0432\</a> \</str","html_length":223,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":2567,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\<script>\n    var optionsPrice = new Product.OptionsPrice([]);\n\</script>\n\<!--\nStart of Floodlight Tag on behalf of AdTradr Corporation: Please do not remove\nActivity name of this tag: VIP Watches Dyna","html_length":57572}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":57572,"contains_price_box":true}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\<script>\n    var optionsPrice = new Product.OptionsPrice([]);\n\</script>\n\<!--\nStart of Floodlight Tag on behalf of AdTradr Corporation: Please do not remove\nActivity name of this tag: VIP Watches Dyna","html_length":57862}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":57862,"contains_price_box":true}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1137,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '100740';\n\tvar product_price \t= '309.6';\n\tvar","html_length":527,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4083,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\<!DOCTYPE html>\n\<html lang=\"bg\" class=\"langClass-bg\">\n    \<head>\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0414\u0430\u043c\u0441\u043a\u0438 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u043a FOSSIL Harwell - ES5266 - \u0414","html_length":110031}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":110031,"contains_price_box":true}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:36:35+00:00 DEBUG (7): 2025-07-09 13:36:35 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:36+00:00 DEBUG (7): 2025-07-09 13:36:36 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26081,"contains_price_box":false}
2025-07-09T13:36:36+00:00 DEBUG (7): 2025-07-09 13:36:36 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:36:36+00:00 DEBUG (7): 2025-07-09 13:36:36 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26502,"contains_price_box":false}
2025-07-09T13:36:36+00:00 DEBUG (7): 2025-07-09 13:36:36 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u041c\u0430\u0433\u0430\u0437\u0438\u043d \u0437\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</title>\n\<meta name=\"description\" content=\"\u041f\u043e\u0432\u0435\u0447\u0435 \u043e\u0442 10,000 \u043c\u043e\u0434\u0435\u043b\u0430 \u0447\u0430","html_length":3663}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":3663,"contains_price_box":true}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:43:01+00:00 DEBUG (7): 2025-07-09 13:43:01 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114690\">\n                          ","html_length":343}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            286,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"286.00","parsed_float":286,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":146.22894,"formatted_result":"\u20ac 146,23"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"423a8c1c"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114689\">\n                          ","html_length":343}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            286,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"286.00","parsed_float":286,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":146.22894,"formatted_result":"\u20ac 146,23"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"7cf41075"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114397\">\n                          ","html_length":343}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            502,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"502.00","parsed_float":502,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":256.66758,"formatted_result":"\u20ac 256,67"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"261eacd7"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114390\">\n                          ","html_length":346}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0479,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1479.00","parsed_float":1479,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":756.19791,"formatted_result":"\u20ac 756,20"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"94c40ef4"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114387\">\n                          ","html_length":346}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0479,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1479.00","parsed_float":1479,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":756.19791,"formatted_result":"\u20ac 756,20"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"9e63d241"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114384\">\n                          ","html_length":346}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0479,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1479.00","parsed_float":1479,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":756.19791,"formatted_result":"\u20ac 756,20"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"529faf1f"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114379\">\n                          ","html_length":343}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            304,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"304.00","parsed_float":304,"will_convert":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":155.43216,"formatted_result":"\u20ac 155,43"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"cacb0357"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1099,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:02+00:00 DEBUG (7): 2025-07-09 13:43:02 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2546,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":21926,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[955][renderer_type]\" id=\"blcgCRS955c4dbfd7c98ebb1b9e396cb82eb813fbb-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[956][renderer_type]\" id=\"blcgCRS956d59d334482b03d8f9374566ff3594791-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[957][renderer_type]\" id=\"blcgCRS9577eda04b3802216883f7b6f1a65b6794d-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[958][renderer_type]\" id=\"blcgCRS95833d4235747d83461c00f77b4e57256c0-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[959][renderer_type]\" id=\"blcgCRS959abad7c7f7bb1fcd71d26b96d39db95e5-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[960][renderer_type]\" id=\"blcgCRS960c09b4e07c440a80bf588f02fa9cf0de1-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[961][renderer_type]\" id=\"blcgCRS96142fc625df755d2ba5cb8475c536e947e-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[962][renderer_type]\" id=\"blcgCRS962935836603d445a87cf7b5ee0e4558eae-select\" class=\"select\">\n                    \<option value=\"\" selected=\"selec","html_length":2915,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[1556][renderer_type]\" id=\"blcgCRS15566fb9c57693433981bb2d6e1490e436fe-select\" class=\"select\">\n                    \<option value=\"\" selected=\"sel","html_length":2922,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[1557][renderer_type]\" id=\"blcgCRS155794fadaf1fcc10e3a11d2c98e96ae14fb-select\" class=\"select\">\n                    \<option value=\"\" selected=\"sel","html_length":2922,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[1558][renderer_type]\" id=\"blcgCRS155826b564169dfd37d513cf8d765b3ebc9f-select\" class=\"select\">\n                    \<option value=\"\" selected=\"sel","html_length":2922,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[2087][renderer_type]\" id=\"blcgCRS2087d4deff79604bda7ef8fbf97d5bd5e92e-select\" class=\"select\">\n                    \<option value=\"\" selected=\"sel","html_length":2922,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"nobr\">    \n        \<select name=\"config[2172][renderer_type]\" id=\"blcgCRS217202238c56470929c2a93cc0d5d3f24d3f-select\" class=\"select\">\n                    \<option value=\"\" selected=\"sel","html_length":2922,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"customGridConfig_1b4f94366dc790e578a1ba32f2689e2b6-grid\" class=\"grid\">\n    \<div class=\"hor-scroll\">\n        \<table id=\"customGridConfig_1b4f94366dc790e578a1ba32f2689e2b6-table\" class=\"data\" c","html_length":122393,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:13+00:00 DEBUG (7): 2025-07-09 13:43:13 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \<div class=\"blcg-custom-grid-config-switch\">\n        \<button  id=\"id_2c96a5ab752cd90b7ab3a0584c0126e8\" title=\"Grid Customization\" type=\"button\" class=\"scalable scalable blcg-customize\" onclick=\"$(","html_length":209181,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"            \n        \<div id=\"sales_order_grid\">\n                \<div class=\"blcg-custom-grid-config-switch\">\n        \<button  id=\"id_2c96a5ab752cd90b7ab3a0584c0126e8\" title=\"Grid Customization\" type=","html_length":285630,"contains_currency":true,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div class=\"content-header\">\n    \<table cellspacing=\"0\">\n        \<tr>\n            \<td style=\"width:50%;\">\<h3 class=\"icon-head head-sales-order\">\u041f\u043e\u0440\u044a\u0447\u043a\u0438\</h3>\</td>\n            \<td class=\"form-but","html_length":286245,"contains_currency":true,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div class=\"content-header\">\n    \<table cellspacing=\"0\">\n        \<tr>\n            \<td style=\"width:50%;\">\<h3 class=\"icon-head head-sales-order\">\u041f\u043e\u0440\u044a\u0447\u043a\u0438\</h3>\</td>\n            \<td class=\"form-but","html_length":286245,"contains_currency":true,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"        \<div id=\"blcg-collection-renderers-descriptions\" style=\"display: none;\">\n        \<div class=\"blcg-content-header\">\n            \<h3 class=\"icon-head head-adminhtml\">Columns Types Descriptions\</","html_length":1494,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":4237,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":361535,"contains_price_box":false}
2025-07-09T13:43:14+00:00 DEBUG (7): 2025-07-09 13:43:14 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:18+00:00 DEBUG (7): 2025-07-09 13:43:18 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":23061,"contains_price_box":false}
2025-07-09T13:43:18+00:00 DEBUG (7): 2025-07-09 13:43:18 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:19+00:00 DEBUG (7): 2025-07-09 13:43:19 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"        \<div id=\"blcg-collection-renderers-descriptions\" style=\"display: none;\">\n        \<div class=\"blcg-content-header\">\n            \<h3 class=\"icon-head head-adminhtml\">Columns Types Descriptions\</","html_length":1494,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:19+00:00 DEBUG (7): 2025-07-09 13:43:19 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":4237,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:19+00:00 DEBUG (7): 2025-07-09 13:43:19 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":147019,"contains_price_box":false}
2025-07-09T13:43:19+00:00 DEBUG (7): 2025-07-09 13:43:19 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:29+00:00 DEBUG (7): 2025-07-09 13:43:29 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":23061,"contains_price_box":false}
2025-07-09T13:43:29+00:00 DEBUG (7): 2025-07-09 13:43:29 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:29+00:00 DEBUG (7): 2025-07-09 13:43:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"        \<div id=\"blcg-collection-renderers-descriptions\" style=\"display: none;\">\n        \<div class=\"blcg-content-header\">\n            \<h3 class=\"icon-head head-adminhtml\">Columns Types Descriptions\</","html_length":1494,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:29+00:00 DEBUG (7): 2025-07-09 13:43:29 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":4237,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:29+00:00 DEBUG (7): 2025-07-09 13:43:29 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":178361,"contains_price_box":false}
2025-07-09T13:43:29+00:00 DEBUG (7): 2025-07-09 13:43:29 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:32+00:00 DEBUG (7): 2025-07-09 13:43:32 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":23061,"contains_price_box":false}
2025-07-09T13:43:32+00:00 DEBUG (7): 2025-07-09 13:43:32 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:32+00:00 DEBUG (7): 2025-07-09 13:43:32 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"        \<div id=\"blcg-collection-renderers-descriptions\" style=\"display: none;\">\n        \<div class=\"blcg-content-header\">\n            \<h3 class=\"icon-head head-adminhtml\">Columns Types Descriptions\</","html_length":1494,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:32+00:00 DEBUG (7): 2025-07-09 13:43:32 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":4237,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:32+00:00 DEBUG (7): 2025-07-09 13:43:32 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":184780,"contains_price_box":false}
2025-07-09T13:43:32+00:00 DEBUG (7): 2025-07-09 13:43:32 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:33+00:00 DEBUG (7): 2025-07-09 13:43:33 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":23061,"contains_price_box":false}
2025-07-09T13:43:33+00:00 DEBUG (7): 2025-07-09 13:43:33 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:43:33+00:00 DEBUG (7): 2025-07-09 13:43:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"        \<div id=\"blcg-collection-renderers-descriptions\" style=\"display: none;\">\n        \<div class=\"blcg-content-header\">\n            \<h3 class=\"icon-head head-adminhtml\">Columns Types Descriptions\</","html_length":1494,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:33+00:00 DEBUG (7): 2025-07-09 13:43:33 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<script>\n    document.observe(\"dom:loaded\", function() {\n        if ($$('.amasty-info-block').length !== 0\n            && $$('.content-header table tbody tr td h3').length !== 0)\n        {\n          ","html_length":4237,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:43:33+00:00 DEBUG (7): 2025-07-09 13:43:33 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":245352,"contains_price_box":false}
2025-07-09T13:43:33+00:00 DEBUG (7): 2025-07-09 13:43:33 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: 
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":4054,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":31948,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \n    \<div class=\"toolbarRightPosition\">        \n        \<div class=\"dropDownfilter showBy\">\n                                                \<a href=\"javascript:;\" class=\"openFilters\">\u041f\u043e\u043a\u0430\u0436\u0438 ","html_length":1600,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":655}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":655,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":655}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":655,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":654}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":654,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":654}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":654,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-101828\">\n                          ","html_length":343}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            256,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"256.00","parsed_float":256,"will_convert":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":130.89024,"formatted_result":"\u20ac 130,89"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"10728ca8"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":655}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":655,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":654}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":654,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":654}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":654,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1106,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:51:44+00:00 DEBUG (7): 2025-07-09 13:51:44 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2553,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0421\u043c\u0430\u0440\u0442 \u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u2013 \u041c\u044a\u0436\u043a\u0438, \u0434\u0430\u043c\u0441\u043a\u0438 \u0438 \u0434\u0435\u0442\u0441\u043a\u0438 | Vip-Watches\</title>\n\<meta name=\"description\" c","html_length":4192}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":4192,"contains_price_box":true}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":33312,"contains_price_box":false}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:46+00:00 DEBUG (7): 2025-07-09 13:51:46 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \n    \<div class=\"toolbarRightPosition\">        \n        \<div class=\"dropDownfilter showBy\">\n                                                \<a href=\"javascript:;\" class=\"openFilters\">\u041f\u043e\u043a\u0430\u0436\u0438 ","html_length":1660,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-109376\">\n                          ","html_length":343}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            539,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"539.00","parsed_float":539,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":275.58531,"formatted_result":"\u20ac 275,59"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a41a5b1f"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110002\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0965,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1965.00","parsed_float":1965,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1004.6848500000001,"formatted_result":"\u20ac 1 004,68"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"3fa9958f"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110003\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0965,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1965.00","parsed_float":1965,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1004.6848500000001,"formatted_result":"\u20ac 1 004,68"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"81374ed1"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110005\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0159,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2159.00","parsed_float":2159,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1103.87511,"formatted_result":"\u20ac 1 103,88"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"3aa2eafc"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110006\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0159,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2159.00","parsed_float":2159,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1103.87511,"formatted_result":"\u20ac 1 103,88"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"72670413"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110007\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0159,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2159.00","parsed_float":2159,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1103.87511,"formatted_result":"\u20ac 1 103,88"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"bed83b2c"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110008\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0359,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2359.00","parsed_float":2359,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1206.13311,"formatted_result":"\u20ac 1 206,13"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"1d121411"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110009\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0555,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2555.00","parsed_float":2555,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1306.3459500000001,"formatted_result":"\u20ac 1 306,35"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"f766958d"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110010\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0555,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2555.00","parsed_float":2555,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1306.3459500000001,"formatted_result":"\u20ac 1 306,35"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b4d73d73"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110011\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0359,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2359.00","parsed_float":2359,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1206.13311,"formatted_result":"\u20ac 1 206,13"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"11052ad6"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110012\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0359,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2359.00","parsed_float":2359,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1206.13311,"formatted_result":"\u20ac 1 206,13"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b3e1ca23"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110013\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0159,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2159.00","parsed_float":2159,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1103.87511,"formatted_result":"\u20ac 1 103,88"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"9b15cf62"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110014\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0159,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2159.00","parsed_float":2159,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1103.87511,"formatted_result":"\u20ac 1 103,88"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"b842f485"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110015\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0359,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2359.00","parsed_float":2359,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1206.13311,"formatted_result":"\u20ac 1 206,13"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"bf266b8d"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-110016\">\n                          ","html_length":346}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            2\u00a0159,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"2159.00","parsed_float":2159,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":1103.87511,"formatted_result":"\u20ac 1 103,88"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"3c753e12"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Performance metrics update | Context: {"total_calls":100,"processed_elements":15,"cache_hits":76,"processing_time":0.00426936149597168,"memory_usage":78336,"last_updated":1752069107}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-112313\">\n                          ","html_length":343}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            779,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"779.00","parsed_float":779,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":398.29491,"formatted_result":"\u20ac 398,29"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"94b6928e"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-112350\">\n                          ","html_length":343}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            449,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"449.00","parsed_float":449,"will_convert":true}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":229.56921,"formatted_result":"\u20ac 229,57"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a3cfed5d"}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1116,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T13:51:47+00:00 DEBUG (7): 2025-07-09 13:51:47 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2563,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n    \<script>\n        // Retargeting JS helpers\n        function _ra_helper_addLoadEvent(func) {\n            var oldonload = window.onload;\n            if (typeof window.onload != 'function') {\n      ","html_length":3518,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: 
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":9015,"contains_price_box":true}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-109376\">\n                          ","html_length":343}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            539,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"539.00","parsed_float":539,"will_convert":true}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":275.58531,"formatted_result":"\u20ac 275,59"}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a41a5b1f"}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n \<style>    \n\t.newpay-popup\n\t{\n\t\tdisplay: none;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: absolute;\n   \t\tz-index: 9999999;\n\t\ttop: 0;\n\t\tpadding: 10%;\n\t\tbackground: #00000080;\n\t    padding-left: 20%;\n","html_length":3016,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\n            \<div class=\"addToCart\">\n\n            \<input type=\"text\" name=\"qty\" id=\"qty\" value=\"1\" title=\"\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e\" class=\"qty amount\" readonly />\n\n            \<a href=\"javascript:;\" clas","html_length":3467,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p style=\"text-align: justify;\">\<strong>\<a title=\"\u0413\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0447\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u0438 \u0431\u0438\u0436\u0443\u0442\u0430\" href=\"/gravirane-na-chasovnici\">\u041b\u0430\u0437\u0435\u0440\u043d\u043e \u0433\u0440\u0430\u0432\u0438\u0440\u0430\u043d\u0435 \u0437\u0430 17.90\u043b\u0432\</a> \</str","html_length":223,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":2567,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1180,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\tvar adv_code \t\t= 'db1162c56479b9bad8e53a254f3c2fcc14859c75e695163de88e6f1ecaa8ac19f7a7e0c3d04b0fa8';\n\tvar product_code \t= '109376';\n\tvar product_price \t= '539';\n\tvar c","html_length":548,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:41+00:00 DEBUG (7): 2025-07-09 14:17:41 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":4211,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:42+00:00 DEBUG (7): 2025-07-09 14:17:42 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26083,"contains_price_box":false}
2025-07-09T14:17:42+00:00 DEBUG (7): 2025-07-09 14:17:42 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:42+00:00 DEBUG (7): 2025-07-09 14:17:42 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":26503,"contains_price_box":false}
2025-07-09T14:17:42+00:00 DEBUG (7): 2025-07-09 14:17:42 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<script type=\"text/javascript\">\n\t\t\t(function(){\n\tra_key = \"7BX8TXVI5IGCH2\";\n \tra_params = {\n\t    add_to_cart_button_id: \"add-to-cart-buttons\",\n\t    price_label_id: \"price-box\",\n\t};\n\tvar ra = document.","html_length":504}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":504,"contains_price_box":true}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n\<title>\u0421\u043c\u0430\u0440\u0442 \u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438 \u2013 \u041c\u044a\u0436\u043a\u0438, \u0434\u0430\u043c\u0441\u043a\u0438 \u0438 \u0434\u0435\u0442\u0441\u043a\u0438 | Vip-Watches\</title>\n\<meta name=\"description\" c","html_length":4211}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":4211,"contains_price_box":true}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=0-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/mujki-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99 \u043b","html_length":556,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=32-150\">\u0434\u043e 149.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/damski-chasovnici\?tsena=150-300\">150.00 \u043b\u0432. - 299.99","html_length":563,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:47+00:00 DEBUG (7): 2025-07-09 14:17:47 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<p>\<span class=\"colTitle\">\u041f\u043e \u0426\u0435\u043d\u0430\</span>\</p>\r\n\<ul>\r\n\<li>\<a href=\"/bizhuta/\?tsena=57-100\">\u0434\u043e 99.99 \u043b\u0432.\</a>\</li>\r\n\<li>\<a href=\"/bizhuta\?tsena=100-200\">100.00 \u043b\u0432. - 199.99 \u043b\u0432.\</a>\</li>\r\n\<li","html_length":504,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<nav class=\"mainNav\">\n    \<ul>\n        \<li class=\"hasSub\">\n            \<a href=\"http://localhost:8580/chasovnici/\">\u0427\u0430\u0441\u043e\u0432\u043d\u0438\u0446\u0438\</a>\n            \<div class=\"subMenu\">\n                           ","html_length":19804,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\n\<header>\n    \<!--wb7-->\n    \<div class=\"headerTop\">\n        \<div class=\"wrapper\">\n                            \<div class=\"headerPhone\">\n                    \<p>\<a href=\"/contacts\">0878 700 515\</a>\</p>","html_length":25771,"contains_currency":true,"contains_price":false,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":28667,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"    \n    \<div class=\"toolbarRightPosition\">        \n        \<div class=\"dropDownfilter showBy\">\n                                                \<a href=\"javascript:;\" class=\"openFilters\">\u041f\u043e\u043a\u0430\u0436\u0438 ","html_length":1750,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-109376\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            539,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"539.00","parsed_float":539,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":275.58531,"formatted_result":"\u20ac 275,59"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a41a5b1f"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-112313\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            779,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"779.00","parsed_float":779,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":398.29491,"formatted_result":"\u20ac 398,29"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"94b6928e"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                            \n                    \<p class=\"old-price\">\n                \<span class=\"price-label\">\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430","html_length":656}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":656,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\u041d\u043e\u0440\u043c\u0430\u043b\u043d\u0430 \u0446\u0435\u043d\u0430:","class":"price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":0}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-112354\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0379,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1379.00","parsed_float":1379,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":705.0689100000001,"formatted_result":"\u20ac 705,07"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6e178209"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-112453\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0379,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1379.00","parsed_float":1379,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":705.0689100000001,"formatted_result":"\u20ac 705,07"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"83885abd"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-112459\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0379,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1379.00","parsed_float":1379,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":705.0689100000001,"formatted_result":"\u20ac 705,07"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"9b798a89"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-113338\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0369,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1369.00","parsed_float":1369,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":699.95601,"formatted_result":"\u20ac 699,96"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"f24eb8fe"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-113557\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            655,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"655.00","parsed_float":655,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":334.89495,"formatted_result":"\u20ac 334,89"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"1896c864"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-113559\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            655,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"655.00","parsed_float":655,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":334.89495,"formatted_result":"\u20ac 334,89"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a77ed81e"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-113560\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            655,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"655.00","parsed_float":655,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":334.89495,"formatted_result":"\u20ac 334,89"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"2e97477e"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-113562\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            655,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"655.00","parsed_float":655,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":334.89495,"formatted_result":"\u20ac 334,89"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"6644527f"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114281\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0185,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1185.00","parsed_float":1185,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":605.87865,"formatted_result":"\u20ac 605,88"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a16ef39b"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114282\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            989,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"989.00","parsed_float":989,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":505.66581,"formatted_result":"\u20ac 505,67"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"277ab593"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114384\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0479,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1479.00","parsed_float":1479,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":756.19791,"formatted_result":"\u20ac 756,20"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"529faf1f"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114387\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0479,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1479.00","parsed_float":1479,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":756.19791,"formatted_result":"\u20ac 756,20"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"9e63d241"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114390\">\n                          ","html_length":346}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":346,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            1\u00a0479,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"1479.00","parsed_float":1479,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":756.19791,"formatted_result":"\u20ac 756,20"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"94c40ef4"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-114397\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            502,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"502.00","parsed_float":502,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":256.66758,"formatted_result":"\u20ac 256,67"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"261eacd7"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processing HTML with price elements | Context: {"html_preview":"\n\n                        \n    \<div class=\"price-box\">\n                                                                \<span class=\"regular-price\" id=\"product-price-109375\">\n                          ","html_length":343}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Starting price box processing | Context: {"html_length":343,"contains_price_box":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Extracted price text | Context: {"raw_price":"\n                                            539,00\u00a0\u043b\u0432.","class":"regular-price"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price extraction result | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Converting price | Context: 
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion details | Context: {"cleaned_price":"539.00","parsed_float":539,"will_convert":true}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Price conversion result | Context: {"converted_amount":275.58531,"formatted_result":"\u20ac 275,59"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Processed price box successfully | Context: {"processed_count":1,"hash":"a7d41587"}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Finished price box processing | Context: {"total_processed":1}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<div id=\"bubble-layer-overlay\" style=\"display:none;\">\n    \<img id=\"bubble-layer-loader\" src=\"//localhost:8580/skin/frontend/base/default/images/bubble/layer/loader.1752068120.gif\" alt=\"img\">\n\</div>\n\<s","html_length":1131,"contains_currency":false,"contains_price":true,"contains_price_box":false}
2025-07-09T14:17:48+00:00 DEBUG (7): 2025-07-09 14:17:48 [DEBUG] PFG_AltCurrency: Skipped HTML processing - no price elements detected | Context: {"html_preview":"\<script type=\"text/javascript\">\n\n    var ps_click_id = 0;\n\n    var queryString = window.location.search.substring(1);\n\n    if (queryString.length > 0)\n    {\n        var pairs = queryString.split('&');","html_length":2578,"contains_currency":false,"contains_price":true,"contains_price_box":false}
