app/etc/modules/PFG_Analytics.xml                                                                   0000666                 00000000671 15033320516 0012722 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?xml version="1.0"?>
<!--
/**
 * PFG Analytics Module Declaration
 * 
 * This file declares the PFG Analytics module to Magento.
 * It tells Magento that this module exists and should be loaded.
 * 
 * @package PFG_Analytics
 * <AUTHOR> Development Team
 */
-->
<config>
    <modules>
        <PFG_Analytics>
            <active>true</active>
            <codePool>community</codePool>
        </PFG_Analytics>
    </modules>
</config>
                                                                       app/locale/en_US/PFG_Analytics.csv                                                                  0000666                 00000006746 15033320516 0012753 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       "PFG Analytics","PFG Analytics"
"PFG","PFG"
"Analytics","Analytics"
"Sales Analytics Dashboard","Sales Analytics Dashboard"
"Date Range","Date Range"
"From:","From:"
"To:","To:"
"Order Statuses:","Order Statuses:"
"All Statuses","All Statuses"
"Quick Filters:","Quick Filters:"
"Last 30 Days","Last 30 Days"
"Last 90 Days","Last 90 Days"
"Year to Date","Year to Date"
"Q1 (Jan-Mar)","Q1 (Jan-Mar)"
"Q2 (Apr-Jun)","Q2 (Apr-Jun)"
"Q3 (Jul-Sep)","Q3 (Jul-Sep)"
"Q4 (Oct-Dec)","Q4 (Oct-Dec)"
"Apply Filters","Apply Filters"
"Comparison:","Comparison:"
"No Comparison","No Comparison"
"vs Previous 30 Days","vs Previous 30 Days"
"vs Previous Quarter","vs Previous Quarter"
"vs Previous Year","vs Previous Year"
"vs Previous Year to Date","vs Previous Year to Date"
"vs Custom Date Range","vs Custom Date Range"
"Comparison From:","Comparison From:"
"Comparison To:","Comparison To:"
"Sales Statistics","Sales Statistics"
"Total Orders","Total Orders"
"Total Amount","Total Amount"
"Average Order Amount","Average Order Amount"
"Previous Period","Previous Period"
"New!","New!"
"Daily Purchases Chart","Daily Purchases Chart"
"Date range limited to 1 year maximum. Showing data for 1 year from the start date.","Date range limited to 1 year maximum. Showing data for 1 year from the start date."
"Order Status Breakdown","Order Status Breakdown"
"Order Status","Order Status"
"Count","Count"
"Percentage","Percentage"
"No orders found for the selected date range.","No orders found for the selected date range."
"Status Configuration","Status Configuration"
"Configure Status Mapping","Configure Status Mapping"
"Hide Configuration","Hide Configuration"
"Map real order statuses to custom display names. Multiple real statuses can be grouped under one custom status.","Map real order statuses to custom display names. Multiple real statuses can be grouped under one custom status."
"Custom Status Name:","Custom Status Name:"
"Real Statuses:","Real Statuses:"
"e.g., Completed Orders","e.g., Completed Orders"
"Remove","Remove"
"Add Status Mapping","Add Status Mapping"
"Save Configuration","Save Configuration"
"Status mappings saved successfully!","Status mappings saved successfully!"
"Error saving status mappings: ","Error saving status mappings: "
"Error saving status mappings. Please try again.","Error saving status mappings. Please try again."
"Current Period","Current Period"
"Comparison Period","Comparison Period"
"Comparison View","Comparison View"
"Enable Analytics Module","Enable Analytics Module"
"Enable or disable the PFG Analytics module","Enable or disable the PFG Analytics module"
"Status Mapping Configuration","Status Mapping Configuration"
"Custom Status Mappings","Custom Status Mappings"
"Configure custom status names and map them to real order statuses. Use the Analytics interface for easier configuration.","Configure custom status names and map them to real order statuses. Use the Analytics interface for easier configuration."
"Configuration Help","Configuration Help"
"PFG Analytics module is disabled. Please enable it in System > Configuration > PFG > Analytics.","PFG Analytics module is disabled. Please enable it in System > Configuration > PFG > Analytics."
"Invalid request","Invalid request"
"No mappings data provided","No mappings data provided"
"Invalid mappings data format","Invalid mappings data format"
"Invalid mapping data: custom name and real statuses are required","Invalid mapping data: custom name and real statuses are required"
"Status mappings saved successfully","Status mappings saved successfully"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          