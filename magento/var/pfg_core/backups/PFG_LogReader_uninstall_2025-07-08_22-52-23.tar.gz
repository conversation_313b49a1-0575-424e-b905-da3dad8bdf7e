app/code/local/PFG/LogReader/Helper/Data.php                                                        0000666                 00000007533 15033320447 0014426 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?php
/**
 * PFG Log Reader Helper
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Reader Data Helper
 *
 * Provides utility methods for log file operations, configuration access,
 * and error logging functionality.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Log file name for this module
     */
    const LOG_FILE = 'pfg_logreader.log';

    /**
     * Configuration paths
     */
    const XML_PATH_ENABLED = 'pfg_logreader/general/enabled';
    const XML_PATH_FILES_PER_PAGE = 'pfg_logreader/general/files_per_page';
    const XML_PATH_LINES_TO_SHOW = 'pfg_logreader/general/lines_to_show';
    const XML_PATH_MAX_FILE_SIZE = 'pfg_logreader/general/max_file_size';

    /**
     * Check if module is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_ENABLED);
    }

    /**
     * Get number of files to display per page
     *
     * @return int
     */
    public function getFilesPerPage()
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_FILES_PER_PAGE) ?: 20;
    }

    /**
     * Get number of lines to show from file
     *
     * @return int
     */
    public function getLinesToShow()
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_LINES_TO_SHOW) ?: 50;
    }

    /**
     * Get maximum file size to read
     *
     * @return int
     */
    public function getMaxFileSize()
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_MAX_FILE_SIZE) ?: 10485760; // 10MB
    }

    /**
     * Get log directory path
     *
     * @return string
     */
    public function getLogDirectory()
    {
        return Mage::getBaseDir('var') . DS . 'log';
    }

    /**
     * Get report directory path
     *
     * @return string
     */
    public function getReportDirectory()
    {
        return Mage::getBaseDir('var') . DS . 'report';
    }

    /**
     * Log message to module log file
     *
     * @param string $message
     * @param int $level
     * @param bool $forceLog
     * @return $this
     */
    public function log($message, $level = null, $forceLog = false)
    {
        Mage::log($message, $level, self::LOG_FILE, $forceLog);
        return $this;
    }

    /**
     * Log error message
     *
     * @param string $message
     * @param Exception $exception
     * @return $this
     */
    public function logError($message, $exception = null)
    {
        $logMessage = $message;
        if ($exception) {
            $logMessage .= ' - Exception: ' . $exception->getMessage();
            $logMessage .= ' - Trace: ' . $exception->getTraceAsString();
        }
        $this->log($logMessage, Zend_Log::ERR, true);
        return $this;
    }

    /**
     * Format file size for display
     *
     * @param int $bytes
     * @return string
     */
    public function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Check if file is readable and within size limits
     *
     * @param string $filePath
     * @return bool
     */
    public function isFileReadable($filePath)
    {
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return false;
        }

        $fileSize = filesize($filePath);
        if ($fileSize === false || $fileSize > $this->getMaxFileSize()) {
            return false;
        }

        return true;
    }
}
                                                                                                                                                                     app/code/local/PFG/LogReader/etc/system.xml                                                         0000666                 00000010504 15033320447 0014436 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?xml version="1.0"?>
<!--
/**
 * PFG Log Reader System Configuration
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */
-->
<config>
    <tabs>
        <pfg translate="label">
            <label>PFG</label>
            <sort_order>200</sort_order>
        </pfg>
    </tabs>

    <sections>
        <pfg_logreader translate="label" module="pfg_logreader">
            <label>Log Reader</label>
            <tab>pfg</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>0</show_in_website>
            <show_in_store>0</show_in_store>
            <groups>
                <general translate="label">
                    <label>General Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <expanded>1</expanded>
                    <fields>
                        <enabled translate="label">
                            <label>Enable Log Reader</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </enabled>
                        <files_per_page translate="label comment">
                            <label>Files Per Page</label>
                            <comment>Number of files to display per page (default: 20)</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-digits validate-greater-than-zero</validate>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </files_per_page>
                        <lines_to_show translate="label comment">
                            <label>Lines to Show</label>
                            <comment>Number of lines to display from end of file (default: 50)</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-digits validate-greater-than-zero</validate>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </lines_to_show>
                        <max_file_size translate="label comment">
                            <label>Maximum File Size (bytes)</label>
                            <comment>Maximum file size to read (default: 10MB = 10485760 bytes)</comment>
                            <frontend_type>text</frontend_type>
                            <validate>validate-digits validate-greater-than-zero</validate>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </max_file_size>
                    </fields>
                </general>
                <log_viewer translate="label">
                    <label>Log File Viewer</label>
                    <frontend_type>text</frontend_type>
                    <frontend_model>pfg_logreader/adminhtml_system_config_form_fieldset_logviewer</frontend_model>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <expanded>1</expanded>
                </log_viewer>
            </groups>
        </pfg_logreader>
    </sections>
</config>
                                                                                                                                                                                            app/code/local/PFG/LogReader/etc/config.xml                                                         0000666                 00000004570 15033320447 0014365 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?xml version="1.0"?>
<!--
/**
 * PFG Log Reader Module Configuration
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */
-->
<config>
    <modules>
        <PFG_LogReader>
            <version>1.0.0</version>
        </PFG_LogReader>
    </modules>

    <global>
        <blocks>
            <pfg_logreader>
                <class>PFG_LogReader_Block</class>
            </pfg_logreader>
        </blocks>

        <helpers>
            <pfg_logreader>
                <class>PFG_LogReader_Helper</class>
            </pfg_logreader>
        </helpers>

        <models>
            <pfg_logreader>
                <class>PFG_LogReader_Model</class>
            </pfg_logreader>
        </models>
    </global>

    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <PFG_LogReader after="Mage_Adminhtml">PFG_LogReader_Adminhtml</PFG_LogReader>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <adminhtml>
        <translate>
            <modules>
                <PFG_LogReader>
                    <files>
                        <default>PFG_LogReader.csv</default>
                    </files>
                </PFG_LogReader>
            </modules>
        </translate>
        <acl>
            <resources>
                <admin>
                    <children>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <pfg_logreader translate="title">
                                            <title>PFG Log Reader</title>
                                        </pfg_logreader>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
    </adminhtml>

    <default>
        <pfg_logreader>
            <general>
                <enabled>1</enabled>
                <files_per_page>20</files_per_page>
                <lines_to_show>50</lines_to_show>
                <max_file_size>10485760</max_file_size>
            </general>
        </pfg_logreader>
    </default>
</config>
                                                                                                                                        app/code/local/PFG/LogReader/Block/Adminhtml/System/Config/Form/Fieldset/Logviewer.php              0000666                 00000064727 15033320447 0024413 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?php
/**
 * PFG Log Reader Admin Configuration Fieldset
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Viewer Configuration Fieldset Block
 *
 * Creates the custom log viewer interface within the system configuration.
 * Displays file list on the left and content viewer on the right.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Block_Adminhtml_System_Config_Form_Fieldset_Logviewer 
    extends Mage_Adminhtml_Block_System_Config_Form_Fieldset
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_helper = Mage::helper('pfg_logreader');
    }

    /**
     * Render fieldset html
     *
     * @param Varien_Data_Form_Element_Abstract $element
     * @return string
     */
    public function render(Varien_Data_Form_Element_Abstract $element)
    {
        if (!$this->_helper->isEnabled()) {
            return '<div class="pfg-logreader-disabled">
                <p><strong>' . $this->__('Log Reader is disabled.') . '</strong> ' . $this->__('Please enable it in General Settings above.') . '</p>
            </div>';
        }

        $html = $this->_getHeaderHtml($element);
        $html .= $this->_getLogViewerHtml();
        $html .= $this->_getFooterHtml($element);
        $html .= $this->_getJavaScript();
        $html .= $this->_getCss();

        return $html;
    }

    /**
     * Get log viewer HTML
     *
     * @return string
     */
    protected function _getLogViewerHtml()
    {
        $ajaxUrl = $this->getUrl('adminhtml/pfg_logreader/ajax');
        $formKey = Mage::getSingleton('core/session')->getFormKey();

        return '
        <div class="pfg-logreader-container">
            <div class="pfg-logreader-left">
                <div class="pfg-logreader-tabs">
                    <button type="button" id="pfg-logreader-tab-logs" class="pfg-tab-button active" onclick="PfgLogReader.switchTab(\'logs\')">
                        Log Files
                    </button>
                    <button type="button" id="pfg-logreader-tab-exceptions" class="pfg-tab-button" onclick="PfgLogReader.switchTab(\'exceptions\')">
                        Exception Files
                    </button>
                </div>
                <div class="pfg-logreader-header">
                    <h4 id="pfg-logreader-section-title">Log Files</h4>
                    <div class="pfg-logreader-actions">
                        <button type="button" id="pfg-logreader-refresh" class="scalable">
                            <span>Refresh</span>
                        </button>
                        <button type="button" id="pfg-logreader-delete-all" class="scalable delete-all-btn">
                            <span>Delete All</span>
                        </button>
                    </div>
                </div>
                <div id="pfg-logreader-files" class="pfg-logreader-files">
                    <div class="loading">Loading files...</div>
                </div>
                <div id="pfg-logreader-pagination" class="pfg-logreader-pagination"></div>
            </div>
            <div class="pfg-logreader-right">
                <div class="pfg-logreader-header">
                    <h4 id="pfg-logreader-file-title">File Content</h4>
                    <div class="pfg-logreader-file-info" id="pfg-logreader-file-info"></div>
                </div>
                <div class="pfg-logreader-content">
                    <textarea id="pfg-logreader-content" readonly placeholder="Select a file to view its content..."></textarea>
                </div>
            </div>
        </div>
        <input type="hidden" id="pfg-logreader-ajax-url" value="' . $ajaxUrl . '" />
        <input type="hidden" id="pfg-logreader-form-key" value="' . $formKey . '" />
        ';
    }

    /**
     * Get JavaScript for log viewer functionality
     *
     * @return string
     */
    protected function _getJavaScript()
    {
        return '
        <script type="text/javascript">
        //<![CDATA[
        var PfgLogReader = {
            currentPage: 1,
            currentTab: "logs",
            ajaxUrl: null,
            formKey: null,

            init: function() {
                this.ajaxUrl = document.getElementById("pfg-logreader-ajax-url").value;
                this.formKey = document.getElementById("pfg-logreader-form-key").value;

                // Bind events
                document.getElementById("pfg-logreader-refresh").onclick = this.refreshFiles.bind(this);
                document.getElementById("pfg-logreader-delete-all").onclick = this.confirmDeleteAll.bind(this);

                // Load initial files
                this.loadFiles(1);
            },

            switchTab: function(tab) {
                this.currentTab = tab;
                this.currentPage = 1;

                // Update tab buttons
                document.getElementById("pfg-logreader-tab-logs").className =
                    tab === "logs" ? "pfg-tab-button active" : "pfg-tab-button";
                document.getElementById("pfg-logreader-tab-exceptions").className =
                    tab === "exceptions" ? "pfg-tab-button active" : "pfg-tab-button";

                // Update section title
                document.getElementById("pfg-logreader-section-title").innerHTML =
                    tab === "logs" ? "Log Files" : "Exception Files";

                // Clear content area
                document.getElementById("pfg-logreader-content").value = "";
                document.getElementById("pfg-logreader-file-title").innerHTML = "File Content";
                document.getElementById("pfg-logreader-file-info").innerHTML = "";

                // Load files for the selected tab
                this.loadFiles(1);
            },
            
            loadFiles: function(page) {
                this.currentPage = page;
                var filesContainer = document.getElementById("pfg-logreader-files");
                filesContainer.innerHTML = "<div class=\"loading\">Loading files...</div>";

                var action = this.currentTab === "logs" ? "list_log_files" : "list_exception_files";

                new Ajax.Request(this.ajaxUrl, {
                    method: "post",
                    parameters: {
                        action: action,
                        page: page,
                        form_key: this.formKey
                    },
                    timeout: 30, // 30 second timeout
                    onSuccess: this.onFilesLoaded.bind(this),
                    onFailure: this.onError.bind(this),
                    onTimeout: this.onTimeout.bind(this)
                });
            },
            
            onFilesLoaded: function(response) {
                try {
                    var data = response.responseJSON || response.responseText.evalJSON();
                    
                    if (data.success) {
                        this.renderFiles(data.files);
                        this.renderPagination(data.pagination);
                    } else {
                        this.showError(data.error || "Failed to load files");
                    }
                } catch (e) {
                    this.showError("Invalid response from server");
                }
            },
            
            renderFiles: function(files) {
                var html = "";
                
                if (files.length === 0) {
                    html = "<div class=\"no-files\">No log files found</div>";
                } else {
                    files.each(function(file) {
                        var cssClass = file.readable ? "file-item" : "file-item disabled";

                        html += "<div class=\"" + cssClass + "\">";
                        html += "<div class=\"file-content\" onclick=\"" + (file.readable ? "PfgLogReader.loadFileContent(\'" + file.path + "\', \'" + file.name + "\')" : "") + "\">";
                        html += "<div class=\"file-name\">" + file.name + "</div>";
                        if (file.directory) {
                            html += "<div class=\"file-directory\">" + file.directory + "</div>";
                        }
                        html += "<div class=\"file-details\">";
                        html += "<span class=\"file-size\">" + file.size_formatted + "</span>";
                        html += "<span class=\"file-modified\">" + file.modified_formatted + "</span>";
                        html += "<span class=\"file-type\">" + file.type + "</span>";
                        html += "</div>";
                        if (!file.readable) {
                            html += "<div class=\"file-error\">File too large or not readable</div>";
                        }
                        html += "</div>";
                        html += "<div class=\"file-actions\">";
                        html += "<button type=\"button\" class=\"delete-btn\" onclick=\"PfgLogReader.confirmDeleteFile(\'" + file.path + "\', \'" + file.name + "\'); event.stopPropagation();\" title=\"Delete file\">";
                        html += "Delete";
                        html += "</button>";
                        html += "</div>";
                        html += "</div>";
                    });
                }
                
                document.getElementById("pfg-logreader-files").innerHTML = html;
            },
            
            renderPagination: function(pagination) {
                var html = "";
                
                if (pagination.total_pages > 1) {
                    html += "<div class=\"pagination-info\">";
                    html += "Page " + pagination.current_page + " of " + pagination.total_pages;
                    html += " (" + pagination.total_files + " files total)";
                    html += "</div>";
                    
                    html += "<div class=\"pagination-controls\">";
                    
                    if (pagination.has_previous) {
                        html += "<button type=\"button\" onclick=\"PfgLogReader.loadFiles(" + (pagination.current_page - 1) + ")\">Previous</button>";
                    }
                    
                    if (pagination.has_next) {
                        html += "<button type=\"button\" onclick=\"PfgLogReader.loadFiles(" + (pagination.current_page + 1) + ")\">Next</button>";
                    }
                    
                    html += "</div>";
                }
                
                document.getElementById("pfg-logreader-pagination").innerHTML = html;
            },
            
            loadFileContent: function(filePath, fileName) {
                var contentArea = document.getElementById("pfg-logreader-content");
                var fileTitle = document.getElementById("pfg-logreader-file-title");
                var fileInfo = document.getElementById("pfg-logreader-file-info");
                
                contentArea.value = "Loading file content...";
                fileTitle.innerHTML = "Loading: " + fileName;
                fileInfo.innerHTML = "";
                
                new Ajax.Request(this.ajaxUrl, {
                    method: "post",
                    parameters: {
                        action: "read_file",
                        file_path: filePath,
                        form_key: this.formKey
                    },
                    onSuccess: this.onFileContentLoaded.bind(this, fileName),
                    onFailure: this.onError.bind(this)
                });
            },
            
            onFileContentLoaded: function(fileName, response) {
                try {
                    var data = response.responseJSON || response.responseText.evalJSON();
                    
                    if (data.success) {
                        document.getElementById("pfg-logreader-content").value = data.lines.join("\\n");
                        document.getElementById("pfg-logreader-file-title").innerHTML = fileName;
                        document.getElementById("pfg-logreader-file-info").innerHTML = 
                            "Showing last " + data.total_lines + " lines | File size: " + data.file_size_formatted;
                    } else {
                        this.showError(data.error || "Failed to load file content");
                        document.getElementById("pfg-logreader-content").value = "Error: " + (data.error || "Failed to load file");
                    }
                } catch (e) {
                    this.showError("Invalid response from server");
                }
            },
            
            refreshFiles: function() {
                this.loadFiles(this.currentPage);
            },
            
            onError: function(response) {
                this.showError("Network error occurred");
            },

            onTimeout: function() {
                this.showError("Request timed out. Please try again.");
            },
            
            showError: function(message) {
                document.getElementById("pfg-logreader-files").innerHTML =
                    "<div class=\"error\">Error: " + message + "</div>";
            },

            confirmDeleteFile: function(filePath, fileName) {
                if (confirm("Are you sure you want to delete the file: " + fileName + "?\\n\\nThis action cannot be undone.")) {
                    this.deleteFile(filePath, fileName);
                }
            },

            deleteFile: function(filePath, fileName) {
                new Ajax.Request(this.ajaxUrl, {
                    method: "post",
                    parameters: {
                        action: "delete_file",
                        file_path: filePath,
                        form_key: this.formKey
                    },
                    onSuccess: this.onFileDeleted.bind(this, fileName),
                    onFailure: this.onError.bind(this)
                });
            },

            onFileDeleted: function(fileName, response) {
                try {
                    var data = response.responseJSON || response.responseText.evalJSON();

                    if (data.success) {
                        // Show success message briefly
                        var filesContainer = document.getElementById("pfg-logreader-files");
                        filesContainer.innerHTML = "<div class=\"success\">File \"" + fileName + "\" deleted successfully</div>";

                        // Refresh the file list after a short delay
                        setTimeout(function() {
                            PfgLogReader.refreshFiles();
                        }, 1500);

                        // Clear content area if the deleted file was being viewed
                        var currentTitle = document.getElementById("pfg-logreader-file-title").innerHTML;
                        if (currentTitle === fileName) {
                            document.getElementById("pfg-logreader-content").value = "";
                            document.getElementById("pfg-logreader-file-title").innerHTML = "File Content";
                            document.getElementById("pfg-logreader-file-info").innerHTML = "";
                        }
                    } else {
                        this.showError(data.error || "Failed to delete file");
                    }
                } catch (e) {
                    this.showError("Invalid response from server");
                }
            },

            confirmDeleteAll: function() {
                var fileType = this.currentTab === "logs" ? "log files" : "exception files";
                var fileCount = document.querySelectorAll("#pfg-logreader-files .file-item").length;

                if (fileCount === 0) {
                    alert("No " + fileType + " found to delete.");
                    return;
                }

                var message = "WARNING: You are about to delete ALL " + fileCount + " " + fileType + ".\\n\\n";
                message += "This action is IRREVERSIBLE and cannot be undone.\\n\\n";
                message += "To confirm this action, please type DELETE in the text box below:";

                var confirmation = prompt(message, "");

                if (confirmation === null) {
                    return; // User cancelled
                }

                if (confirmation !== "DELETE") {
                    alert("Confirmation failed. You must type exactly: DELETE");
                    return;
                }

                // Double confirmation
                var doubleConfirm = confirm("FINAL CONFIRMATION:\\n\\nAre you absolutely sure you want to delete ALL " + fileCount + " " + fileType + "?\\n\\nClick OK to proceed or Cancel to abort.");

                if (doubleConfirm) {
                    this.deleteAllFiles();
                }
            },

            deleteAllFiles: function() {
                var action = this.currentTab === "logs" ? "delete_all_log_files" : "delete_all_exception_files";
                var fileType = this.currentTab === "logs" ? "log files" : "exception files";

                // Show progress message
                var filesContainer = document.getElementById("pfg-logreader-files");
                filesContainer.innerHTML = "<div class=\"loading\">Deleting all " + fileType + "... Please wait.</div>";

                // Disable buttons during operation
                document.getElementById("pfg-logreader-refresh").disabled = true;
                document.getElementById("pfg-logreader-delete-all").disabled = true;

                new Ajax.Request(this.ajaxUrl, {
                    method: "post",
                    parameters: {
                        action: action,
                        confirmation: "DELETE",
                        form_key: this.formKey
                    },
                    onSuccess: this.onDeleteAllComplete.bind(this),
                    onFailure: this.onDeleteAllError.bind(this)
                });
            },

            onDeleteAllComplete: function(response) {
                // Re-enable buttons
                document.getElementById("pfg-logreader-refresh").disabled = false;
                document.getElementById("pfg-logreader-delete-all").disabled = false;

                try {
                    var data = response.responseJSON || response.responseText.evalJSON();

                    if (data.success) {
                        var message = data.message;
                        if (data.failed_count > 0 && data.failed_files) {
                            message += "\\n\\nFailed files:\\n" + data.failed_files.join("\\n");
                        }

                        // Show success message
                        var filesContainer = document.getElementById("pfg-logreader-files");
                        filesContainer.innerHTML = "<div class=\"success\">" + data.message + "</div>";

                        // Clear content area
                        document.getElementById("pfg-logreader-content").value = "";
                        document.getElementById("pfg-logreader-file-title").innerHTML = "File Content";
                        document.getElementById("pfg-logreader-file-info").innerHTML = "";

                        // Show detailed results if there were failures
                        if (data.failed_count > 0) {
                            alert("Bulk deletion completed with some failures:\\n\\n" + message);
                        }

                        // Refresh the file list after a short delay
                        setTimeout(function() {
                            PfgLogReader.refreshFiles();
                        }, 2000);

                    } else {
                        this.showError(data.error || "Failed to delete files");
                    }
                } catch (e) {
                    this.showError("Invalid response from server");
                }
            },

            onDeleteAllError: function() {
                // Re-enable buttons
                document.getElementById("pfg-logreader-refresh").disabled = false;
                document.getElementById("pfg-logreader-delete-all").disabled = false;

                this.showError("Network error occurred during bulk deletion");
            }
        };
        
        // Initialize when DOM is ready
        document.observe("dom:loaded", function() {
            PfgLogReader.init();
        });
        //]]>
        </script>';
    }

    /**
     * Get CSS styles for log viewer
     *
     * @return string
     */
    protected function _getCss()
    {
        return '
        <style type="text/css">
        .pfg-logreader-container {
            display: flex;
            gap: 20px;
            min-height: 600px;
            margin: 20px 0;
        }
        
        .pfg-logreader-left {
            width: 20%;
            min-width: 250px;
        }

        .pfg-logreader-right {
            width: 80%;
            min-width: 400px;
        }

        .pfg-logreader-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .pfg-tab-button {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-bottom: none;
            padding: 8px 16px;
            cursor: pointer;
            margin-right: 2px;
            font-size: 12px;
            color: #666;
        }

        .pfg-tab-button:hover {
            background: #e9e9e9;
        }

        .pfg-tab-button.active {
            background: #fff;
            color: #333;
            font-weight: bold;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }
        
        .pfg-logreader-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .pfg-logreader-header h4 {
            margin: 0;
            color: #333;
        }

        .pfg-logreader-actions {
            display: flex;
            gap: 10px;
        }

        .delete-all-btn {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: white !important;
        }

        .delete-all-btn:hover {
            background-color: #c82333 !important;
            border-color: #bd2130 !important;
        }

        .delete-all-btn:disabled {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            cursor: not-allowed;
            opacity: 0.65;
        }
        
        .pfg-logreader-files {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            background: #fff;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 10px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .file-item:hover {
            background-color: #f5f5f5;
        }

        .file-content {
            flex: 1;
            cursor: pointer;
        }

        .file-actions {
            margin-left: 10px;
            flex-shrink: 0;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .delete-btn:hover {
            background: #c82333;
        }

        .success {
            color: #28a745;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .file-item.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .file-directory {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .file-details {
            font-size: 11px;
            color: #888;
        }
        
        .file-details span {
            margin-right: 15px;
        }
        
        .file-error {
            font-size: 11px;
            color: #d40707;
            margin-top: 5px;
        }
        
        .pfg-logreader-content textarea {
            width: 100%;
            height: 400px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            padding: 10px;
            resize: vertical;
        }
        
        .pfg-logreader-pagination {
            margin-top: 10px;
            text-align: center;
        }
        
        .pagination-info {
            margin-bottom: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .pagination-controls button {
            margin: 0 5px;
        }
        
        .pfg-logreader-file-info {
            font-size: 12px;
            color: #666;
        }
        
        .loading, .error, .no-files {
            padding: 20px;
            text-align: center;
            color: #666;
        }
        
        .error {
            color: #d40707;
        }
        
        .pfg-logreader-disabled {
            padding: 20px;
            background: #f5f5f5;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        
        /* Responsive design for smaller screens */
        @media (max-width: 1024px) {
            .pfg-logreader-left {
                width: 25%;
                min-width: 200px;
            }

            .pfg-logreader-right {
                width: 75%;
            }
        }

        @media (max-width: 768px) {
            .pfg-logreader-container {
                flex-direction: column;
                gap: 15px;
            }

            .pfg-logreader-left,
            .pfg-logreader-right {
                width: 100%;
                min-width: auto;
            }

            .pfg-logreader-files {
                height: 300px;
            }
        }

        @media (max-width: 480px) {
            .pfg-logreader-container {
                margin: 10px 0;
                gap: 10px;
            }

            .pfg-logreader-files {
                height: 250px;
            }

            .pfg-tab-button {
                padding: 6px 12px;
                font-size: 11px;
            }
        }
        </style>';
    }
}
                                         app/code/local/PFG/LogReader/Model/File.php                                                         0000666                 00000036740 15033320447 0014257 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?php
/**
 * PFG Log Reader File Model
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log File Model
 *
 * Handles file discovery, scanning, and content reading operations
 * for log files in var/log and var/report directories.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Model_File extends Mage_Core_Model_Abstract
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_helper = Mage::helper('pfg_logreader');
    }

    /**
     * Discover all log files from both directories
     *
     * @param int $page Current page number (1-based)
     * @param int $limit Files per page
     * @return array
     */
    public function discoverFiles($page = 1, $limit = null)
    {
        if ($limit === null) {
            $limit = $this->_helper->getFilesPerPage();
        }

        try {
            $allFiles = array();
            
            // Scan log directory
            $logFiles = $this->_scanDirectory($this->_helper->getLogDirectory(), false);
            foreach ($logFiles as $file) {
                $file['type'] = 'log';
                $allFiles[] = $file;
            }

            // Scan report directory recursively
            $reportFiles = $this->_scanDirectory($this->_helper->getReportDirectory(), true);
            foreach ($reportFiles as $file) {
                $file['type'] = 'report';
                $allFiles[] = $file;
            }

            // Sort files by modification time (newest first)
            usort($allFiles, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });

            // Calculate pagination
            $totalFiles = count($allFiles);
            $totalPages = ceil($totalFiles / $limit);
            $offset = ($page - 1) * $limit;
            $files = array_slice($allFiles, $offset, $limit);

            return array(
                'files' => $files,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_files' => $totalFiles,
                    'files_per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                )
            );

        } catch (Exception $e) {
            $this->_helper->logError('Error discovering files', $e);
            return array(
                'files' => array(),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_files' => 0,
                    'files_per_page' => $limit,
                    'has_previous' => false,
                    'has_next' => false
                ),
                'error' => 'Failed to discover log files: ' . $e->getMessage()
            );
        }
    }

    /**
     * Discover log files only from var/log directory
     *
     * @param int $page Current page number (1-based)
     * @param int $limit Files per page
     * @return array
     */
    public function discoverLogFiles($page = 1, $limit = null)
    {
        if ($limit === null) {
            $limit = $this->_helper->getFilesPerPage();
        }

        try {
            // Use optimized file discovery with early pagination
            $result = $this->_scanDirectoryOptimized($this->_helper->getLogDirectory(), false, $page, $limit, 'log');

            $logFiles = $result['files'];
            $totalFiles = $result['total_count'];
            $totalPages = ceil($totalFiles / $limit);
            $files = $logFiles;

            return array(
                'files' => $files,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_files' => $totalFiles,
                    'files_per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                )
            );

        } catch (Exception $e) {
            $this->_helper->logError('Error discovering log files', $e);
            return array(
                'files' => array(),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_files' => 0,
                    'files_per_page' => $limit,
                    'has_previous' => false,
                    'has_next' => false
                ),
                'error' => 'Failed to discover log files: ' . $e->getMessage()
            );
        }
    }

    /**
     * Discover exception files only from var/report directory
     *
     * @param int $page Current page number (1-based)
     * @param int $limit Files per page
     * @return array
     */
    public function discoverExceptionFiles($page = 1, $limit = null)
    {
        if ($limit === null) {
            $limit = $this->_helper->getFilesPerPage();
        }

        try {
            // Use optimized file discovery with early pagination
            $result = $this->_scanDirectoryOptimized($this->_helper->getReportDirectory(), true, $page, $limit, 'exception');

            $reportFiles = $result['files'];
            $totalFiles = $result['total_count'];
            $totalPages = ceil($totalFiles / $limit);
            $files = $reportFiles;

            return array(
                'files' => $files,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_files' => $totalFiles,
                    'files_per_page' => $limit,
                    'has_previous' => $page > 1,
                    'has_next' => $page < $totalPages
                )
            );

        } catch (Exception $e) {
            $this->_helper->logError('Error discovering exception files', $e);
            return array(
                'files' => array(),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_files' => 0,
                    'files_per_page' => $limit,
                    'has_previous' => false,
                    'has_next' => false
                ),
                'error' => 'Failed to discover exception files: ' . $e->getMessage()
            );
        }
    }

    /**
     * Scan directory for files
     *
     * @param string $directory
     * @param bool $recursive
     * @return array
     */
    protected function _scanDirectory($directory, $recursive = false)
    {
        $files = array();

        if (!is_dir($directory) || !is_readable($directory)) {
            $this->_helper->log("Directory not accessible: {$directory}");
            return $files;
        }

        try {
            if ($recursive) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
                    RecursiveIteratorIterator::LEAVES_ONLY
                );
            } else {
                $iterator = new DirectoryIterator($directory);
            }

            foreach ($iterator as $fileInfo) {
                if ($fileInfo->isFile() && $this->_isLogFile($fileInfo)) {
                    $filePath = $fileInfo->getPathname();
                    $relativePath = str_replace($directory . DS, '', $filePath);
                    
                    $files[] = array(
                        'name' => $fileInfo->getFilename(),
                        'path' => $filePath,
                        'relative_path' => $relativePath,
                        'size' => $fileInfo->getSize(),
                        'size_formatted' => $this->_helper->formatFileSize($fileInfo->getSize()),
                        'modified' => $fileInfo->getMTime(),
                        'modified_formatted' => date('Y-m-d H:i:s', $fileInfo->getMTime()),
                        'readable' => $this->_helper->isFileReadable($filePath),
                        'directory' => dirname($relativePath) !== '.' ? dirname($relativePath) : ''
                    );
                }
            }

        } catch (Exception $e) {
            $this->_helper->logError("Error scanning directory: {$directory}", $e);
        }

        return $files;
    }

    /**
     * Check if file is a log file
     *
     * @param SplFileInfo $fileInfo
     * @return bool
     */
    protected function _isLogFile($fileInfo)
    {
        $filename = $fileInfo->getFilename();
        
        // Skip hidden files and directories
        if (substr($filename, 0, 1) === '.') {
            return false;
        }

        // Common log file extensions
        $logExtensions = array('log', 'txt', 'out', 'err');
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        if (in_array($extension, $logExtensions)) {
            return true;
        }

        // Files without extension that might be logs
        if (empty($extension)) {
            $logPatterns = array('error', 'access', 'debug', 'system', 'exception');
            foreach ($logPatterns as $pattern) {
                if (stripos($filename, $pattern) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Read last N lines from a file efficiently
     *
     * @param string $filePath
     * @param int $lines
     * @return array
     */
    public function readLastLines($filePath, $lines = null)
    {
        if ($lines === null) {
            $lines = $this->_helper->getLinesToShow();
        }

        try {
            if (!$this->_helper->isFileReadable($filePath)) {
                throw new Exception('File is not readable or exceeds size limit');
            }

            $fileSize = filesize($filePath);
            if ($fileSize === 0) {
                return array(
                    'success' => true,
                    'lines' => array(),
                    'total_lines' => 0,
                    'file_size' => 0
                );
            }

            $handle = fopen($filePath, 'r');
            if (!$handle) {
                throw new Exception('Unable to open file for reading');
            }

            $result = $this->_readLastLinesFromHandle($handle, $lines, $fileSize);
            fclose($handle);

            return array(
                'success' => true,
                'lines' => $result['lines'],
                'total_lines' => $result['total_lines'],
                'file_size' => $fileSize,
                'file_size_formatted' => $this->_helper->formatFileSize($fileSize)
            );

        } catch (Exception $e) {
            $this->_helper->logError("Error reading file: {$filePath}", $e);
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'lines' => array(),
                'total_lines' => 0,
                'file_size' => 0
            );
        }
    }

    /**
     * Read last lines from file handle efficiently
     *
     * @param resource $handle
     * @param int $lines
     * @param int $fileSize
     * @return array
     */
    protected function _readLastLinesFromHandle($handle, $lines, $fileSize)
    {
        $buffer = '';
        $linesFound = array();
        $pos = $fileSize;
        $chunkSize = 4096;

        while (count($linesFound) < $lines && $pos > 0) {
            $readSize = min($chunkSize, $pos);
            $pos -= $readSize;
            
            fseek($handle, $pos);
            $chunk = fread($handle, $readSize);
            $buffer = $chunk . $buffer;
            
            $linesInBuffer = explode("\n", $buffer);
            
            if (count($linesInBuffer) > 1) {
                // Keep the first partial line for next iteration
                $buffer = array_shift($linesInBuffer);
                
                // Add lines to our result (in reverse order since we're reading backwards)
                $linesFound = array_merge($linesInBuffer, $linesFound);
                
                // Trim to requested number of lines
                if (count($linesFound) > $lines) {
                    $linesFound = array_slice($linesFound, -$lines);
                }
            }
        }

        // If we have a remaining buffer and need more lines, add it
        if (!empty($buffer) && count($linesFound) < $lines) {
            array_unshift($linesFound, $buffer);
        }

        // Remove empty last line if present
        if (end($linesFound) === '') {
            array_pop($linesFound);
        }

        return array(
            'lines' => $linesFound,
            'total_lines' => count($linesFound)
        );
    }

    /**
     * Optimized directory scanning with pagination support
     *
     * Efficiently scans directories with early pagination to avoid loading
     * all files into memory. Sorts by modification time and applies pagination.
     *
     * @param string $directory Directory path to scan
     * @param bool $recursive Whether to scan recursively
     * @param int $page Current page number (1-based)
     * @param int $limit Number of files per page
     * @param string $fileType File type identifier for results
     * @return array Array containing 'files' and 'total_count' keys
     */
    protected function _scanDirectoryOptimized($directory, $recursive = false, $page = 1, $limit = 20, $fileType = 'log')
    {
        // Input validation for edge cases
        $page = max(1, (int)$page); // Ensure page is at least 1
        $limit = max(1, min(1000, (int)$limit)); // Limit between 1 and 1000

        $files = array();
        $totalCount = 0;
        $offset = ($page - 1) * $limit;

        if (!is_dir($directory)) {
            return array('files' => array(), 'total_count' => 0);
        }

        // Use DirectoryIterator for better performance
        $iterator = $recursive ?
            new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)) :
            new DirectoryIterator($directory);

        $tempFiles = array();

        foreach ($iterator as $fileInfo) {
            if ($fileInfo->isFile() && $this->_isLogFile($fileInfo)) {
                $totalCount++;

                // Collect file info for sorting
                $tempFiles[] = array(
                    'name' => $fileInfo->getFilename(),
                    'path' => $fileInfo->getPathname(),
                    'size' => $fileInfo->getSize(),
                    'modified' => $fileInfo->getMTime(),
                    'size_formatted' => $this->_helper->formatFileSize($fileInfo->getSize()),
                    'modified_formatted' => date('Y-m-d H:i:s', $fileInfo->getMTime()),
                    'type' => $fileType,
                    'readable' => $this->_helper->isFileReadable($fileInfo->getPathname()),
                    'directory' => $recursive ? dirname(str_replace($directory . DS, '', $fileInfo->getPathname())) : ''
                );
            }
        }

        // Sort by modification time (newest first)
        usort($tempFiles, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });

        // Apply pagination
        $files = array_slice($tempFiles, $offset, $limit);

        return array(
            'files' => $files,
            'total_count' => $totalCount
        );
    }
}
                                app/code/local/PFG/LogReader/Model/Security.php                                                     0000666                 00000016354 15033320447 0015206 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?php
/**
 * PFG Log Reader Security Model
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Security Model
 *
 * Provides additional security validation and monitoring
 * for log file access operations.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Model_Security extends Mage_Core_Model_Abstract
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * Constructor
     */
    protected function _construct()
    {
        $this->_helper = Mage::helper('pfg_logreader');
    }

    /**
     * Validate file access permissions
     *
     * @param string $filePath
     * @return array
     */
    public function validateFileAccess($filePath)
    {
        $result = array(
            'allowed' => false,
            'reason' => '',
            'file_info' => array()
        );

        try {
            // Check if file exists
            if (!file_exists($filePath)) {
                $result['reason'] = 'File does not exist';
                return $result;
            }

            // Get real path to prevent directory traversal
            $realPath = realpath($filePath);
            if ($realPath === false) {
                $result['reason'] = 'Invalid file path';
                return $result;
            }

            // Check if path is within allowed directories
            if (!$this->_isPathInAllowedDirectory($realPath)) {
                $result['reason'] = 'File is outside allowed directories';
                $this->_logSecurityViolation('Path traversal attempt', $filePath, $realPath);
                return $result;
            }

            // Check file permissions
            if (!is_readable($realPath)) {
                $result['reason'] = 'File is not readable';
                return $result;
            }

            // Check file size
            $fileSize = filesize($realPath);
            if ($fileSize === false) {
                $result['reason'] = 'Cannot determine file size';
                return $result;
            }

            if ($fileSize > $this->_helper->getMaxFileSize()) {
                $result['reason'] = 'File exceeds maximum size limit';
                return $result;
            }

            // Check if file is a valid log file
            if (!$this->_isValidLogFile($realPath)) {
                $result['reason'] = 'File type not allowed';
                $this->_logSecurityViolation('Invalid file type access attempt', $filePath, $realPath);
                return $result;
            }

            // All checks passed
            $result['allowed'] = true;
            $result['file_info'] = array(
                'path' => $realPath,
                'size' => $fileSize,
                'modified' => filemtime($realPath),
                'type' => $this->_getFileType($realPath)
            );

        } catch (Exception $e) {
            $result['reason'] = 'Security validation error: ' . $e->getMessage();
            $this->_helper->logError('Security validation failed for file: ' . $filePath, $e);
        }

        return $result;
    }

    /**
     * Check if path is within allowed directories
     *
     * @param string $realPath
     * @return bool
     */
    protected function _isPathInAllowedDirectory($realPath)
    {
        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        if ($logDir === false || $reportDir === false) {
            return false;
        }

        return (strpos($realPath, $logDir . DIRECTORY_SEPARATOR) === 0) || 
               (strpos($realPath, $reportDir . DIRECTORY_SEPARATOR) === 0);
    }

    /**
     * Check if file is a valid log file
     *
     * @param string $filePath
     * @return bool
     */
    protected function _isValidLogFile($filePath)
    {
        $filename = basename($filePath);
        
        // Skip hidden files
        if (substr($filename, 0, 1) === '.') {
            return false;
        }

        // Check for dangerous file extensions
        $dangerousExtensions = array('php', 'phtml', 'exe', 'sh', 'bat', 'cmd');
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        if (in_array($extension, $dangerousExtensions)) {
            return false;
        }

        // Allow common log file extensions
        $allowedExtensions = array('log', 'txt', 'out', 'err', '');
        if (in_array($extension, $allowedExtensions)) {
            return true;
        }

        // Check for log-like patterns in filename
        $logPatterns = array('error', 'access', 'debug', 'system', 'exception', 'trace');
        foreach ($logPatterns as $pattern) {
            if (stripos($filename, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get file type based on location and name
     *
     * @param string $filePath
     * @return string
     */
    protected function _getFileType($filePath)
    {
        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        if (strpos($filePath, $logDir) === 0) {
            return 'log';
        } elseif (strpos($filePath, $reportDir) === 0) {
            return 'report';
        }

        return 'unknown';
    }

    /**
     * Log security violation
     *
     * @param string $violation
     * @param string $requestedPath
     * @param string $realPath
     */
    protected function _logSecurityViolation($violation, $requestedPath, $realPath = null)
    {
        $adminUser = Mage::getSingleton('admin/session')->getUser();
        $username = $adminUser ? $adminUser->getUsername() : 'unknown';
        $ip = Mage::helper('core/http')->getRemoteAddr();

        $message = sprintf(
            'SECURITY VIOLATION: %s | User: %s | IP: %s | Requested: %s | Real: %s',
            $violation,
            $username,
            $ip,
            $requestedPath,
            $realPath ?: 'N/A'
        );

        $this->_helper->log($message, Zend_Log::WARN, true);
    }

    /**
     * Rate limiting check for file access
     *
     * @param string $identifier
     * @return bool
     */
    public function checkRateLimit($identifier = null)
    {
        if ($identifier === null) {
            $adminUser = Mage::getSingleton('admin/session')->getUser();
            $identifier = $adminUser ? $adminUser->getId() : Mage::helper('core/http')->getRemoteAddr();
        }

        $cacheKey = 'pfg_logreader_rate_limit_' . md5($identifier);
        $cache = Mage::app()->getCache();
        
        $requests = $cache->load($cacheKey);
        if ($requests === false) {
            $requests = 0;
        } else {
            $requests = (int)$requests; // Convert from string back to integer
        }

        $maxRequests = 100; // Max requests per minute
        $timeWindow = 60; // 1 minute

        if ($requests >= $maxRequests) {
            $this->_helper->log("Rate limit exceeded for identifier: {$identifier}");
            return false;
        }

        $cache->save((string)($requests + 1), $cacheKey, array(), $timeWindow);
        return true;
    }
}
                                                                                                                                                                                                                                                                                    app/code/local/PFG/LogReader/controllers/Adminhtml/Pfg/LogreaderController.php                      0000666                 00000041521 15033320447 0023300 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?php
/**
 * PFG Log Reader Admin Controller
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Reader Admin Controller
 *
 * Handles AJAX requests for file listing and content reading.
 * Includes CSRF protection and comprehensive error handling.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Adminhtml_Pfg_LogreaderController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * File model instance
     *
     * @var PFG_LogReader_Model_File
     */
    protected $_fileModel;

    /**
     * Security model instance
     *
     * @var PFG_LogReader_Model_Security
     */
    protected $_securityModel;

    /**
     * Constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_helper = Mage::helper('pfg_logreader');
        $this->_fileModel = Mage::getModel('pfg_logreader/file');
        $this->_securityModel = Mage::getModel('pfg_logreader/security');
    }

    /**
     * AJAX endpoint for file operations
     */
    public function ajaxAction()
    {
        // Verify CSRF token
        if (!$this->_validateFormKey()) {
            $this->_returnJsonError('Invalid form key');
            return;
        }

        // Check rate limiting
        if (!$this->_securityModel->checkRateLimit()) {
            $this->_returnJsonError('Rate limit exceeded. Please try again later.');
            return;
        }

        // Check if module is enabled
        if (!$this->_helper->isEnabled()) {
            $this->_returnJsonError('Log Reader module is disabled');
            return;
        }

        // Check admin permissions
        if (!$this->_isAllowed()) {
            $this->_returnJsonError('Access denied');
            return;
        }

        $action = $this->getRequest()->getParam('action');

        try {
            switch ($action) {
                case 'list_files':
                    $this->_handleListFiles();
                    break;

                case 'list_log_files':
                    $this->_handleListLogFiles();
                    break;

                case 'list_exception_files':
                    $this->_handleListExceptionFiles();
                    break;

                case 'read_file':
                    $this->_handleReadFile();
                    break;

                case 'delete_file':
                    $this->_handleDeleteFile();
                    break;

                case 'delete_all_log_files':
                    $this->_handleDeleteAllLogFiles();
                    break;

                case 'delete_all_exception_files':
                    $this->_handleDeleteAllExceptionFiles();
                    break;

                default:
                    $this->_returnJsonError('Invalid action');
                    break;
            }
        } catch (Exception $e) {
            $this->_helper->logError('AJAX action error', $e);
            // Sanitize error message for user display
            $userMessage = $this->_sanitizeErrorMessage($e->getMessage());
            $this->_returnJsonError('An error occurred: ' . $userMessage);
        }
    }

    /**
     * Handle file listing request
     */
    protected function _handleListFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle log files listing request
     */
    protected function _handleListLogFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverLogFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle exception files listing request
     */
    protected function _handleListExceptionFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverExceptionFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle file content reading request
     */
    protected function _handleReadFile()
    {
        $filePath = $this->getRequest()->getParam('file_path');

        if (empty($filePath)) {
            $this->_returnJsonError('File path is required');
            return;
        }

        // Additional input validation
        $filePath = trim($filePath);
        if (strlen($filePath) > 500) { // Reasonable path length limit
            $this->_returnJsonError('File path too long');
            return;
        }

        // Check for suspicious patterns
        if (preg_match('/[<>"|*?]/', $filePath)) {
            $this->_returnJsonError('Invalid characters in file path');
            return;
        }

        // Enhanced security validation
        $securityCheck = $this->_securityModel->validateFileAccess($filePath);
        if (!$securityCheck['allowed']) {
            $this->_returnJsonError('Access denied: ' . $securityCheck['reason']);
            return;
        }

        $result = $this->_fileModel->readLastLines($filePath);

        if (!$result['success']) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'lines' => $result['lines'],
            'total_lines' => $result['total_lines'],
            'file_size' => $result['file_size'],
            'file_size_formatted' => $result['file_size_formatted']
        ));
    }

    /**
     * Handle file deletion request
     */
    protected function _handleDeleteFile()
    {
        $filePath = $this->getRequest()->getParam('file_path');

        if (empty($filePath)) {
            $this->_returnJsonError('File path is required');
            return;
        }

        // Additional input validation
        $filePath = trim($filePath);
        if (strlen($filePath) > 500) { // Reasonable path length limit
            $this->_returnJsonError('File path too long');
            return;
        }

        // Check for suspicious patterns
        if (preg_match('/[<>"|*?]/', $filePath)) {
            $this->_returnJsonError('Invalid characters in file path');
            return;
        }

        // Enhanced security validation
        $securityCheck = $this->_securityModel->validateFileAccess($filePath);
        if (!$securityCheck['allowed']) {
            $this->_returnJsonError('Access denied: ' . $securityCheck['reason']);
            return;
        }

        // Additional check to ensure file exists
        if (!file_exists($filePath)) {
            $this->_returnJsonError('File not found');
            return;
        }

        // Check if file is readable/writable
        if (!is_writable($filePath)) {
            $this->_returnJsonError('File is not writable');
            return;
        }

        try {
            // Attempt to delete the file
            if (unlink($filePath)) {
                $this->_helper->log("File deleted successfully: " . $filePath);
                $this->_returnJsonSuccess(array(
                    'message' => 'File deleted successfully'
                ));
            } else {
                $this->_returnJsonError('Failed to delete file');
            }
        } catch (Exception $e) {
            $this->_helper->logError('Error deleting file: ' . $filePath, $e);
            $this->_returnJsonError('Error deleting file: ' . $e->getMessage());
        }
    }

    /**
     * Handle delete all log files request
     */
    protected function _handleDeleteAllLogFiles()
    {
        $confirmation = $this->getRequest()->getParam('confirmation');

        if ($confirmation !== 'DELETE') {
            $this->_returnJsonError('Invalid confirmation. Please type DELETE to confirm.');
            return;
        }

        try {
            // Get all log files
            $logFiles = $this->_fileModel->discoverLogFiles(1, 9999); // Get all files
            $files = $logFiles['files'];

            if (empty($files)) {
                $this->_returnJsonSuccess(array(
                    'message' => 'No log files found to delete',
                    'deleted_count' => 0,
                    'failed_count' => 0
                ));
                return;
            }

            $deletedCount = 0;
            $failedCount = 0;
            $failedFiles = array();

            foreach ($files as $file) {
                $filePath = $file['path'];

                // Enhanced security validation for each file
                $securityCheck = $this->_securityModel->validateFileAccess($filePath);
                if (!$securityCheck['allowed']) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Access denied)';
                    continue;
                }

                // Check if file exists and is writable
                if (!file_exists($filePath) || !is_writable($filePath)) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Not writable)';
                    continue;
                }

                // Attempt to delete the file
                if (unlink($filePath)) {
                    $deletedCount++;
                    $this->_helper->log("File deleted in bulk operation: " . $filePath);
                } else {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Delete failed)';
                }
            }

            $message = "Bulk deletion completed. Deleted: $deletedCount files";
            if ($failedCount > 0) {
                $message .= ", Failed: $failedCount files";
            }

            $this->_returnJsonSuccess(array(
                'message' => $message,
                'deleted_count' => $deletedCount,
                'failed_count' => $failedCount,
                'failed_files' => $failedFiles
            ));

        } catch (Exception $e) {
            $this->_helper->logError('Error in bulk delete log files operation', $e);
            $this->_returnJsonError('Error during bulk deletion: ' . $e->getMessage());
        }
    }

    /**
     * Handle delete all exception files request
     */
    protected function _handleDeleteAllExceptionFiles()
    {
        $confirmation = $this->getRequest()->getParam('confirmation');

        if ($confirmation !== 'DELETE') {
            $this->_returnJsonError('Invalid confirmation. Please type DELETE to confirm.');
            return;
        }

        try {
            // Get all exception files
            $exceptionFiles = $this->_fileModel->discoverExceptionFiles(1, 9999); // Get all files
            $files = $exceptionFiles['files'];

            if (empty($files)) {
                $this->_returnJsonSuccess(array(
                    'message' => 'No exception files found to delete',
                    'deleted_count' => 0,
                    'failed_count' => 0
                ));
                return;
            }

            $deletedCount = 0;
            $failedCount = 0;
            $failedFiles = array();

            foreach ($files as $file) {
                $filePath = $file['path'];

                // Enhanced security validation for each file
                $securityCheck = $this->_securityModel->validateFileAccess($filePath);
                if (!$securityCheck['allowed']) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Access denied)';
                    continue;
                }

                // Check if file exists and is writable
                if (!file_exists($filePath) || !is_writable($filePath)) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Not writable)';
                    continue;
                }

                // Attempt to delete the file
                if (unlink($filePath)) {
                    $deletedCount++;
                    $this->_helper->log("Exception file deleted in bulk operation: " . $filePath);
                } else {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Delete failed)';
                }
            }

            $message = "Bulk deletion completed. Deleted: $deletedCount files";
            if ($failedCount > 0) {
                $message .= ", Failed: $failedCount files";
            }

            $this->_returnJsonSuccess(array(
                'message' => $message,
                'deleted_count' => $deletedCount,
                'failed_count' => $failedCount,
                'failed_files' => $failedFiles
            ));

        } catch (Exception $e) {
            $this->_helper->logError('Error in bulk delete exception files operation', $e);
            $this->_returnJsonError('Error during bulk deletion: ' . $e->getMessage());
        }
    }

    /**
     * Check if file path is within allowed directories
     *
     * @param string $filePath
     * @return bool
     */
    protected function _isPathAllowed($filePath)
    {
        $realPath = realpath($filePath);
        if ($realPath === false) {
            return false;
        }

        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        // Check if file is within log or report directory
        return (strpos($realPath, $logDir) === 0) || (strpos($realPath, $reportDir) === 0);
    }

    /**
     * Return JSON success response
     *
     * @param array $data
     */
    protected function _returnJsonSuccess($data = array())
    {
        $response = array_merge(array('success' => true), $data);
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Return JSON error response
     *
     * @param string $message
     */
    protected function _returnJsonError($message)
    {
        $response = array(
            'success' => false,
            'error' => $message
        );
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Check admin permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        $session = Mage::getSingleton('admin/session');

        // Check if admin is logged in
        if (!$session->isLoggedIn()) {
            return false;
        }

        // Check specific ACL permission
        if (!$session->isAllowed('system/config/pfg_logreader')) {
            return false;
        }

        // Additional check for admin user validity
        $user = $session->getUser();
        if (!$user || !$user->getId()) {
            return false;
        }

        return true;
    }

    /**
     * Sanitize error messages for user display
     *
     * @param string $message
     * @return string
     */
    protected function _sanitizeErrorMessage($message)
    {
        // Remove sensitive file paths
        $message = preg_replace('/\/[a-zA-Z0-9_\-\/\.]+\//', '[PATH]/', $message);

        // Remove stack traces
        $message = preg_replace('/Stack trace:.*$/s', '', $message);

        // Remove line numbers and file references
        $message = preg_replace('/in \/[^\s]+ on line \d+/', '', $message);

        // Generic error messages for common issues
        $genericMessages = array(
            'Permission denied' => 'Access denied to requested resource',
            'No such file or directory' => 'Requested file not found',
            'failed to open stream' => 'Unable to access file',
            'Connection refused' => 'Service temporarily unavailable'
        );

        foreach ($genericMessages as $pattern => $replacement) {
            if (stripos($message, $pattern) !== false) {
                return $replacement;
            }
        }

        // Limit message length
        if (strlen($message) > 200) {
            $message = substr($message, 0, 200) . '...';
        }

        return trim($message);
    }
}
                                                                                                                                                                               app/etc/modules/PFG_LogReader.xml                                                                   0000666                 00000000562 15033320447 0012641 0                                                                                                    ustar 00                                                                                                                                                                                                                                                       <?xml version="1.0"?>
<!--
/**
 * PFG Log Reader Module Declaration
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */
-->
<config>
    <modules>
        <PFG_LogReader>
            <active>true</active>
            <codePool>local</codePool>
            <version>1.0.0</version>
        </PFG_LogReader>
    </modules>
</config>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              