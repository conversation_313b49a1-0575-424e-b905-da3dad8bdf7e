<?php
/**
 * PFG Log Reader Admin Controller
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 */

/**
 * Log Reader Admin Controller
 *
 * Handles AJAX requests for file listing and content reading.
 * Includes CSRF protection and comprehensive error handling.
 *
 * @category   PFG
 * @package    PFG_LogReader
 * <AUTHOR> Development Team
 * @version    1.0.0
 * @since      1.0.0
 */
class PFG_LogReader_Adminhtml_Pfg_LogreaderController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Helper instance
     *
     * @var PFG_LogReader_Helper_Data
     */
    protected $_helper;

    /**
     * File model instance
     *
     * @var PFG_LogReader_Model_File
     */
    protected $_fileModel;

    /**
     * Security model instance
     *
     * @var PFG_LogReader_Model_Security
     */
    protected $_securityModel;

    /**
     * Constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_helper = Mage::helper('pfg_logreader');
        $this->_fileModel = Mage::getModel('pfg_logreader/file');
        $this->_securityModel = Mage::getModel('pfg_logreader/security');
    }

    /**
     * AJAX endpoint for file operations
     */
    public function ajaxAction()
    {
        // Verify CSRF token
        if (!$this->_validateFormKey()) {
            $this->_returnJsonError('Invalid form key');
            return;
        }

        // Check rate limiting
        if (!$this->_securityModel->checkRateLimit()) {
            $this->_returnJsonError('Rate limit exceeded. Please try again later.');
            return;
        }

        // Check if module is enabled
        if (!$this->_helper->isEnabled()) {
            $this->_returnJsonError('Log Reader module is disabled');
            return;
        }

        // Check admin permissions
        if (!$this->_isAllowed()) {
            $this->_returnJsonError('Access denied');
            return;
        }

        $action = $this->getRequest()->getParam('action');

        try {
            switch ($action) {
                case 'list_files':
                    $this->_handleListFiles();
                    break;

                case 'list_log_files':
                    $this->_handleListLogFiles();
                    break;

                case 'list_exception_files':
                    $this->_handleListExceptionFiles();
                    break;

                case 'read_file':
                    $this->_handleReadFile();
                    break;

                case 'delete_file':
                    $this->_handleDeleteFile();
                    break;

                case 'delete_all_log_files':
                    $this->_handleDeleteAllLogFiles();
                    break;

                case 'delete_all_exception_files':
                    $this->_handleDeleteAllExceptionFiles();
                    break;

                default:
                    $this->_returnJsonError('Invalid action');
                    break;
            }
        } catch (Exception $e) {
            $this->_helper->logError('AJAX action error', $e);
            // Sanitize error message for user display
            $userMessage = $this->_sanitizeErrorMessage($e->getMessage());
            $this->_returnJsonError('An error occurred: ' . $userMessage);
        }
    }

    /**
     * Handle file listing request
     */
    protected function _handleListFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle log files listing request
     */
    protected function _handleListLogFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverLogFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle exception files listing request
     */
    protected function _handleListExceptionFiles()
    {
        $page = (int) $this->getRequest()->getParam('page', 1);
        $page = max(1, $page); // Ensure page is at least 1

        $result = $this->_fileModel->discoverExceptionFiles($page);

        if (isset($result['error'])) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'files' => $result['files'],
            'pagination' => $result['pagination']
        ));
    }

    /**
     * Handle file content reading request
     */
    protected function _handleReadFile()
    {
        $filePath = $this->getRequest()->getParam('file_path');

        if (empty($filePath)) {
            $this->_returnJsonError('File path is required');
            return;
        }

        // Additional input validation
        $filePath = trim($filePath);
        if (strlen($filePath) > 500) { // Reasonable path length limit
            $this->_returnJsonError('File path too long');
            return;
        }

        // Check for suspicious patterns
        if (preg_match('/[<>"|*?]/', $filePath)) {
            $this->_returnJsonError('Invalid characters in file path');
            return;
        }

        // Enhanced security validation
        $securityCheck = $this->_securityModel->validateFileAccess($filePath);
        if (!$securityCheck['allowed']) {
            $this->_returnJsonError('Access denied: ' . $securityCheck['reason']);
            return;
        }

        $result = $this->_fileModel->readLastLines($filePath);

        if (!$result['success']) {
            $this->_returnJsonError($result['error']);
            return;
        }

        $this->_returnJsonSuccess(array(
            'lines' => $result['lines'],
            'total_lines' => $result['total_lines'],
            'file_size' => $result['file_size'],
            'file_size_formatted' => $result['file_size_formatted']
        ));
    }

    /**
     * Handle file deletion request
     */
    protected function _handleDeleteFile()
    {
        $filePath = $this->getRequest()->getParam('file_path');

        if (empty($filePath)) {
            $this->_returnJsonError('File path is required');
            return;
        }

        // Additional input validation
        $filePath = trim($filePath);
        if (strlen($filePath) > 500) { // Reasonable path length limit
            $this->_returnJsonError('File path too long');
            return;
        }

        // Check for suspicious patterns
        if (preg_match('/[<>"|*?]/', $filePath)) {
            $this->_returnJsonError('Invalid characters in file path');
            return;
        }

        // Enhanced security validation
        $securityCheck = $this->_securityModel->validateFileAccess($filePath);
        if (!$securityCheck['allowed']) {
            $this->_returnJsonError('Access denied: ' . $securityCheck['reason']);
            return;
        }

        // Additional check to ensure file exists
        if (!file_exists($filePath)) {
            $this->_returnJsonError('File not found');
            return;
        }

        // Check if file is readable/writable
        if (!is_writable($filePath)) {
            $this->_returnJsonError('File is not writable');
            return;
        }

        try {
            // Attempt to delete the file
            if (unlink($filePath)) {
                $this->_helper->log("File deleted successfully: " . $filePath);
                $this->_returnJsonSuccess(array(
                    'message' => 'File deleted successfully'
                ));
            } else {
                $this->_returnJsonError('Failed to delete file');
            }
        } catch (Exception $e) {
            $this->_helper->logError('Error deleting file: ' . $filePath, $e);
            $this->_returnJsonError('Error deleting file: ' . $e->getMessage());
        }
    }

    /**
     * Handle delete all log files request
     */
    protected function _handleDeleteAllLogFiles()
    {
        $confirmation = $this->getRequest()->getParam('confirmation');

        if ($confirmation !== 'DELETE') {
            $this->_returnJsonError('Invalid confirmation. Please type DELETE to confirm.');
            return;
        }

        try {
            // Get all log files
            $logFiles = $this->_fileModel->discoverLogFiles(1, 9999); // Get all files
            $files = $logFiles['files'];

            if (empty($files)) {
                $this->_returnJsonSuccess(array(
                    'message' => 'No log files found to delete',
                    'deleted_count' => 0,
                    'failed_count' => 0
                ));
                return;
            }

            $deletedCount = 0;
            $failedCount = 0;
            $failedFiles = array();

            foreach ($files as $file) {
                $filePath = $file['path'];

                // Enhanced security validation for each file
                $securityCheck = $this->_securityModel->validateFileAccess($filePath);
                if (!$securityCheck['allowed']) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Access denied)';
                    continue;
                }

                // Check if file exists and is writable
                if (!file_exists($filePath) || !is_writable($filePath)) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Not writable)';
                    continue;
                }

                // Attempt to delete the file
                if (unlink($filePath)) {
                    $deletedCount++;
                    $this->_helper->log("File deleted in bulk operation: " . $filePath);
                } else {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Delete failed)';
                }
            }

            $message = "Bulk deletion completed. Deleted: $deletedCount files";
            if ($failedCount > 0) {
                $message .= ", Failed: $failedCount files";
            }

            $this->_returnJsonSuccess(array(
                'message' => $message,
                'deleted_count' => $deletedCount,
                'failed_count' => $failedCount,
                'failed_files' => $failedFiles
            ));

        } catch (Exception $e) {
            $this->_helper->logError('Error in bulk delete log files operation', $e);
            $this->_returnJsonError('Error during bulk deletion: ' . $e->getMessage());
        }
    }

    /**
     * Handle delete all exception files request
     */
    protected function _handleDeleteAllExceptionFiles()
    {
        $confirmation = $this->getRequest()->getParam('confirmation');

        if ($confirmation !== 'DELETE') {
            $this->_returnJsonError('Invalid confirmation. Please type DELETE to confirm.');
            return;
        }

        try {
            // Get all exception files
            $exceptionFiles = $this->_fileModel->discoverExceptionFiles(1, 9999); // Get all files
            $files = $exceptionFiles['files'];

            if (empty($files)) {
                $this->_returnJsonSuccess(array(
                    'message' => 'No exception files found to delete',
                    'deleted_count' => 0,
                    'failed_count' => 0
                ));
                return;
            }

            $deletedCount = 0;
            $failedCount = 0;
            $failedFiles = array();

            foreach ($files as $file) {
                $filePath = $file['path'];

                // Enhanced security validation for each file
                $securityCheck = $this->_securityModel->validateFileAccess($filePath);
                if (!$securityCheck['allowed']) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Access denied)';
                    continue;
                }

                // Check if file exists and is writable
                if (!file_exists($filePath) || !is_writable($filePath)) {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Not writable)';
                    continue;
                }

                // Attempt to delete the file
                if (unlink($filePath)) {
                    $deletedCount++;
                    $this->_helper->log("Exception file deleted in bulk operation: " . $filePath);
                } else {
                    $failedCount++;
                    $failedFiles[] = $file['name'] . ' (Delete failed)';
                }
            }

            $message = "Bulk deletion completed. Deleted: $deletedCount files";
            if ($failedCount > 0) {
                $message .= ", Failed: $failedCount files";
            }

            $this->_returnJsonSuccess(array(
                'message' => $message,
                'deleted_count' => $deletedCount,
                'failed_count' => $failedCount,
                'failed_files' => $failedFiles
            ));

        } catch (Exception $e) {
            $this->_helper->logError('Error in bulk delete exception files operation', $e);
            $this->_returnJsonError('Error during bulk deletion: ' . $e->getMessage());
        }
    }

    /**
     * Check if file path is within allowed directories
     *
     * @param string $filePath
     * @return bool
     */
    protected function _isPathAllowed($filePath)
    {
        $realPath = realpath($filePath);
        if ($realPath === false) {
            return false;
        }

        $logDir = realpath($this->_helper->getLogDirectory());
        $reportDir = realpath($this->_helper->getReportDirectory());

        // Check if file is within log or report directory
        return (strpos($realPath, $logDir) === 0) || (strpos($realPath, $reportDir) === 0);
    }

    /**
     * Return JSON success response
     *
     * @param array $data
     */
    protected function _returnJsonSuccess($data = array())
    {
        $response = array_merge(array('success' => true), $data);
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Return JSON error response
     *
     * @param string $message
     */
    protected function _returnJsonError($message)
    {
        $response = array(
            'success' => false,
            'error' => $message
        );
        $this->getResponse()
            ->setHeader('Content-Type', 'application/json')
            ->setBody(Mage::helper('core')->jsonEncode($response));
    }

    /**
     * Check admin permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        $session = Mage::getSingleton('admin/session');

        // Check if admin is logged in
        if (!$session->isLoggedIn()) {
            return false;
        }

        // Check specific ACL permission
        if (!$session->isAllowed('system/config/pfg_logreader')) {
            return false;
        }

        // Additional check for admin user validity
        $user = $session->getUser();
        if (!$user || !$user->getId()) {
            return false;
        }

        return true;
    }

    /**
     * Sanitize error messages for user display
     *
     * @param string $message
     * @return string
     */
    protected function _sanitizeErrorMessage($message)
    {
        // Remove sensitive file paths
        $message = preg_replace('/\/[a-zA-Z0-9_\-\/\.]+\//', '[PATH]/', $message);

        // Remove stack traces
        $message = preg_replace('/Stack trace:.*$/s', '', $message);

        // Remove line numbers and file references
        $message = preg_replace('/in \/[^\s]+ on line \d+/', '', $message);

        // Generic error messages for common issues
        $genericMessages = array(
            'Permission denied' => 'Access denied to requested resource',
            'No such file or directory' => 'Requested file not found',
            'failed to open stream' => 'Unable to access file',
            'Connection refused' => 'Service temporarily unavailable'
        );

        foreach ($genericMessages as $pattern => $replacement) {
            if (stripos($message, $pattern) !== false) {
                return $replacement;
            }
        }

        // Limit message length
        if (strlen($message) > 200) {
            $message = substr($message, 0, 200) . '...';
        }

        return trim($message);
    }
}
