{"timestamp": "2025-07-09 06:39:18", "exception": {"class": "Exception", "message": "Archive contains file that would be extracted outside allowed directory: pfg-pfg-analytics-164a06ec854b/app/", "code": 0, "file": "/magento/app/code/local/PFG/Core/Model/Installation.php", "line": 139, "trace": "#0 /magento/app/code/local/PFG/Core/controllers/Adminhtml/Pfg/CoreController.php(174): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 /magento/app/code/core/Mage/Core/Controller/Varien/Router/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 /magento/app/code/core/Mage/Core/Controller/Varien/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 /magento/app/code/core/Mage/Core/Model/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 /magento/app/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 /magento/index.php(86): Mage::run('default', 'store')\n#7 {main}"}, "context": {"module_name": "PFG_Analytics", "action": "install", "error_id": "PFG_20250709_063918_322f0f55"}, "system": {"php_version": "7.4.33", "magento_version": "*******", "memory_usage": 2097152, "memory_peak": 2097152, "execution_time": 1.1870319843292236}, "request": {"method": "POST", "uri": "/index.php/vipadmin/pfg_core/installModule/key/f4549cf84ef02db5191a52dec8e4b4ab/?isAjax=true", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "**********"}, "user": {"admin_user": "martinpfg", "session_id": "8c52acbfae1bac3214c94ec26e403243"}}