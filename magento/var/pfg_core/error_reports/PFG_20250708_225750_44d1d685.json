{"timestamp": "2025-07-08 22:57:50", "exception": {"class": "Exception", "message": "Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.5 GB ✓, Magento version: ******* ✓, Module PFG_Analytics is already installed and active, File conflict: /magento/app/etc/modules/PFG_Analytics.xml, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_LogReader, Module is already installed (version ), Repository is private ✓, <PERSON><PERSON><PERSON> is in PFG namespace ✓, Admin session required", "code": 0, "file": "/magento/app/code/local/PFG/Core/Model/Installation.php", "line": 73, "trace": "#0 /magento/test_analytics_install_fix.php(13): PFG_Core_Model_Installation->installModule('pfg-analytics', 'master')\n#1 {main}"}, "context": {"module_name": "PFG_Analytics", "action": "install", "error_id": "PFG_20250708_225750_44d1d685"}, "system": {"php_version": "7.4.33", "magento_version": "*******", "memory_usage": 8388608, "memory_peak": 8388608, "execution_time": 6.141654014587402}, "request": {"method": null, "uri": null, "user_agent": "", "ip_address": false}, "user": {"admin_user": "system", "session_id": ""}}