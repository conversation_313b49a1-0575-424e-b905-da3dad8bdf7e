{"timestamp": "2025-07-08 21:10:28", "exception": {"class": "Exception", "message": "Installation cannot proceed: PHP version: 7.4.33 ✓, PHP extension curl: ✓, PHP extension json: ✓, PHP extension zip: ✓, PHP extension phar: ✓, Directory writable: /magento/app/code/local ✓, Directory writable: /magento/app/etc/modules ✓, Directory writable: /magento/var ✓, Directory writable: /magento/var/pfg_core/backups ✓, Directory writable: /magento/var/pfg_core/temp ✓, Disk space available: 389.63 GB ✓, Magento version: ******* ✓, Potential rewrite conflict: Potential conflict with existing PFG module: PFG_Core, Invalid module name format: PFG_ analytics, Repository is private ✓, <PERSON>dule is in PFG namespace ✓, Admin session active ✓, Admin has required permissions ✓, User does not have administrator role - proceed with caution", "code": 0, "file": "/magento/app/code/local/PFG/Core/Model/Installation.php", "line": 72, "trace": "#0 /magento/app/code/local/PFG/Core/controllers/Adminhtml/Pfg/CoreController.php(138): PFG_Core_Model_Installation->installModule('PFG Analytics', 'master')\n#1 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 /magento/app/code/core/Mage/Core/Controller/Varien/Router/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 /magento/app/code/core/Mage/Core/Controller/Varien/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 /magento/app/code/core/Mage/Core/Model/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 /magento/app/Mage.php(694): Mage_Core_Model_App->run(Array)\n#6 /magento/index.php(86): Mage::run('default', 'store')\n#7 {main}"}, "context": {"module_name": "PFG_ analytics", "action": "install", "error_id": "PFG_20250708_211028_11889470"}, "system": {"php_version": "7.4.33", "magento_version": "*******", "memory_usage": 2097152, "memory_peak": 2097152, "execution_time": 14.164961814880371}, "request": {"method": "POST", "uri": "/index.php/vipadmin/pfg_core/installModule/key/23d558d8b0e54705432897af117f3623/?isAjax=true", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "**********"}, "user": {"admin_user": "martinpfg", "session_id": "e8ce4c238534461ce015525abfe8dae9"}}