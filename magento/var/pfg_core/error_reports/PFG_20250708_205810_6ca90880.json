{"timestamp": "2025-07-08 20:58:10", "exception": {"class": "Exception", "message": "Failed to download repository: HTTP error: 404", "code": 0, "file": "/magento/app/code/local/PFG/Core/Model/Installation.php", "line": 106, "trace": "#0 /magento/app/code/local/PFG/Core/controllers/Adminhtml/Pfg/CoreController.php(138): PFG_Core_Model_Installation->installModule('builder', 'latest')\n#1 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(418): PFG_Core_Adminhtml_Pfg_CoreController->installModuleAction()\n#2 /magento/app/code/core/Mage/Core/Controller/Varien/Router/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('installModule')\n#3 /magento/app/code/core/Mage/Core/Controller/Varien/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))\n#4 /magento/app/code/core/Mage/Core/Model/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()\n#5 /magento/app/Mage.php(694): Ma<PERSON>_Core_Model_App->run(Array)\n#6 /magento/index.php(86): Mage::run('default', 'store')\n#7 {main}"}, "context": {"module_name": "PFG_Builder", "action": "install", "error_id": "PFG_20250708_205810_6ca90880"}, "system": {"php_version": "7.4.33", "magento_version": "*******", "memory_usage": 2097152, "memory_peak": 2097152, "execution_time": 12.965086936950684}, "request": {"method": "POST", "uri": "/index.php/vipadmin/pfg_core/installModule/key/23d558d8b0e54705432897af117f3623/?isAjax=true", "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "**********"}, "user": {"admin_user": "martinpfg", "session_id": "e8ce4c238534461ce015525abfe8dae9"}}