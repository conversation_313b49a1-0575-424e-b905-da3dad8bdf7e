a:5:{i:0;s:678:"SELECT `e`.`entity_id` FROM `catalog_product_flat_1` AS `e`
 INNER JOIN `catalog_category_product_index` AS `cat_index` ON cat_index.product_id=e.entity_id AND cat_index.store_id=1 AND cat_index.visibility IN(2, 4) AND cat_index.category_id = '2'
 INNER JOIN `catalog_product_index_price` AS `price_index` ON price_index.entity_id = e.entity_id AND price_index.website_id = '1' AND price_index.customer_group_id = 0
 INNER JOIN `stenik_dynamiccategory_new_products` AS `new_products` ON e.entity_id = new_products.product_id AND new_products.store_id = 1 WHERE (e.status = 1) AND (new_products.is_new = 1) ORDER BY `cat_index_position` ASC, `cat_index`.`position` ASC LIMIT 20

";i:1;s:4660:"#0 /magento/lib/Varien/Db/Statement/Pdo/Mysql.php(110): Zend_Db_Statement_Pdo->_execute(Array)
#1 /magento/app/code/core/Zend/Db/Statement.php(291): Varien_Db_Statement_Pdo_Mysql->_execute(Array)
#2 /magento/lib/Zend/Db/Adapter/Abstract.php(480): Zend_Db_Statement->execute(Array)
#3 /magento/lib/Zend/Db/Adapter/Pdo/Abstract.php(238): Zend_Db_Adapter_Abstract->query('SELECT `e`.`ent...', Array)
#4 /magento/lib/Varien/Db/Adapter/Pdo/Mysql.php(504): Zend_Db_Adapter_Pdo_Abstract->query('SELECT `e`.`ent...', Array)
#5 /magento/lib/Zend/Db/Adapter/Abstract.php(737): Varien_Db_Adapter_Pdo_Mysql->query('SELECT `e`.`ent...', Array)
#6 /magento/lib/Varien/Data/Collection/Db.php(740): Zend_Db_Adapter_Abstract->fetchAll('SELECT `e`.`ent...', Array)
#7 /magento/app/code/core/Mage/Eav/Model/Entity/Collection/Abstract.php(1046): Varien_Data_Collection_Db->_fetchAll('SELECT `e`.`ent...')
#8 /magento/app/code/core/Mage/Eav/Model/Entity/Collection/Abstract.php(871): Mage_Eav_Model_Entity_Collection_Abstract->_loadEntities(false, false)
#9 /magento/app/code/core/Mage/Review/Model/Observer.php(78): Mage_Eav_Model_Entity_Collection_Abstract->load()
#10 /magento/app/code/core/Mage/Core/Model/App.php(1374): Mage_Review_Model_Observer->catalogBlockProductCollectionBeforeToHtml(Object(Varien_Event_Observer))
#11 /magento/app/code/core/Mage/Core/Model/App.php(1347): Mage_Core_Model_App->_callObserverMethod(Object(Mage_Review_Model_Observer), 'catalogBlockPro...', Object(Varien_Event_Observer))
#12 /magento/app/Mage.php(459): Mage_Core_Model_App->dispatchEvent('catalog_block_p...', Array)
#13 /magento/app/code/core/Mage/Catalog/Block/Product/List.php(160): Mage::dispatchEvent('catalog_block_p...', Array)
#14 /magento/app/code/core/Mage/Core/Block/Abstract.php(922): Mage_Catalog_Block_Product_List->_beforeToHtml()
#15 /magento/app/code/core/Mage/Core/Block/Abstract.php(641): Mage_Core_Block_Abstract->toHtml()
#16 /magento/app/code/core/Mage/Core/Block/Abstract.php(585): Mage_Core_Block_Abstract->_getChildHtml('product_list', true)
#17 /magento/app/code/core/Mage/Catalog/Block/Category/View.php(90): Mage_Core_Block_Abstract->getChildHtml('product_list')
#18 /magento/app/design/frontend/stenik/default/template/catalog/category/view.phtml(73): Mage_Catalog_Block_Category_View->getProductListHtml()
#19 /magento/app/code/core/Mage/Core/Block/Template.php(241): include('/magento/app/de...')
#20 /magento/app/code/core/Mage/Core/Block/Template.php(272): Mage_Core_Block_Template->fetchView('frontend/stenik...')
#21 /magento/app/code/core/Mage/Core/Block/Template.php(286): Mage_Core_Block_Template->renderView()
#22 /magento/app/code/core/Mage/Core/Block/Abstract.php(923): Mage_Core_Block_Template->_toHtml()
#23 /magento/app/code/core/Mage/Core/Block/Text/List.php(43): Mage_Core_Block_Abstract->toHtml()
#24 /magento/app/code/core/Mage/Core/Block/Abstract.php(923): Mage_Core_Block_Text_List->_toHtml()
#25 /magento/app/code/core/Mage/Core/Block/Abstract.php(641): Mage_Core_Block_Abstract->toHtml()
#26 /magento/app/code/core/Mage/Core/Block/Abstract.php(585): Mage_Core_Block_Abstract->_getChildHtml('content', true)
#27 /magento/app/design/frontend/stenik/default/template/page/1column.phtml(66): Mage_Core_Block_Abstract->getChildHtml('content')
#28 /magento/app/code/core/Mage/Core/Block/Template.php(241): include('/magento/app/de...')
#29 /magento/app/code/core/Mage/Core/Block/Template.php(272): Mage_Core_Block_Template->fetchView('frontend/stenik...')
#30 /magento/app/code/core/Mage/Core/Block/Template.php(286): Mage_Core_Block_Template->renderView()
#31 /magento/app/code/core/Mage/Core/Block/Abstract.php(923): Mage_Core_Block_Template->_toHtml()
#32 /magento/app/code/core/Mage/Core/Model/Layout.php(555): Mage_Core_Block_Abstract->toHtml()
#33 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(390): Mage_Core_Model_Layout->getOutput()
#34 /magento/app/code/core/Mage/Catalog/controllers/CategoryController.php(161): Mage_Core_Controller_Varien_Action->renderLayout()
#35 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(418): Mage_Catalog_CategoryController->viewAction()
#36 /magento/app/code/core/Mage/Core/Controller/Varien/Router/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('view')
#37 /magento/app/code/core/Mage/Core/Controller/Varien/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))
#38 /magento/app/code/core/Mage/Core/Model/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()
#39 /magento/app/Mage.php(694): Mage_Core_Model_App->run(Array)
#40 /magento/index.php(86): Mage::run('default', 'store')
#41 {main}";s:3:"url";s:5:"/novo";s:11:"script_name";s:10:"/index.php";s:4:"skin";s:7:"default";}