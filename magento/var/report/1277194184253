a:5:{i:0;s:17:"Invalid category.";i:1;s:2437:"#0 /magento/app/code/core/Mage/Catalog/Model/Layer.php(190): Mage::throwException('Invalid categor...')
#1 /magento/app/code/community/Stenik/DynamicCategory/Model/Observer.php(63): Mage_Catalog_Model_Layer->setCurrentCategory(Object(Mage_Catalog_Model_Category))
#2 /magento/app/code/core/Mage/Core/Model/App.php(1374): Stenik_DynamicCategory_Model_Observer->blockPrepareLayoutBefore(Object(Varien_Event_Observer))
#3 /magento/app/code/core/Mage/Core/Model/App.php(1353): Mage_Core_Model_App->_callObserverMethod(Object(Stenik_DynamicCategory_Model_Observer), 'blockPrepareLay...', Object(Varien_Event_Observer))
#4 /magento/app/Mage.php(459): Mage_Core_Model_App->dispatchEvent('core_block_abst...', Array)
#5 /magento/app/code/core/Mage/Core/Block/Abstract.php(296): Mage::dispatchEvent('core_block_abst...', Array)
#6 /magento/app/code/core/Mage/Core/Model/Layout.php(456): Mage_Core_Block_Abstract->setLayout(Object(Mage_Core_Model_Layout))
#7 /magento/app/code/core/Mage/Core/Model/Layout.php(472): Mage_Core_Model_Layout->createBlock('catalog/layer_v...', 'catalog.leftnav')
#8 /magento/app/code/core/Mage/Core/Model/Layout.php(239): Mage_Core_Model_Layout->addBlock('catalog/layer_v...', 'catalog.leftnav')
#9 /magento/app/code/core/Mage/Core/Model/Layout.php(205): Mage_Core_Model_Layout->_generateBlock(Object(Mage_Core_Model_Layout_Element), Object(Mage_Core_Model_Layout_Element))
#10 /magento/app/code/core/Mage/Core/Model/Layout.php(210): Mage_Core_Model_Layout->generateBlocks(Object(Mage_Core_Model_Layout_Element))
#11 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(344): Mage_Core_Model_Layout->generateBlocks()
#12 /magento/app/code/core/Mage/Catalog/controllers/CategoryController.php(148): Mage_Core_Controller_Varien_Action->generateLayoutBlocks()
#13 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(418): Mage_Catalog_CategoryController->viewAction()
#14 /magento/app/code/core/Mage/Core/Controller/Varien/Router/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('view')
#15 /magento/app/code/core/Mage/Core/Controller/Varien/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))
#16 /magento/app/code/core/Mage/Core/Model/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()
#17 /magento/app/Mage.php(694): Mage_Core_Model_App->run(Array)
#18 /magento/index.php(86): Mage::run('default', 'store')
#19 {main}";s:3:"url";s:5:"/novo";s:11:"script_name";s:10:"/index.php";s:4:"skin";s:7:"default";}