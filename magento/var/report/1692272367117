a:5:{i:0;s:58:"Datas must be string or set automatic_serialization = true";i:1;s:1127:"#0 /magento/lib/Zend/Cache/Core.php(365): Zend_Cache::throwException('Datas must be s...')
#1 /magento/lib/Varien/Cache/Core.php(145): Zend_Cache_Core->save(1, '7ac_pfg_logread...', Array, 60, 8)
#2 /magento/app/code/local/PFG/LogReader/Model/Security.php(249): Varien_Cache_Core->save(1, 'pfg_logreader_r...', Array, 60)
#3 /magento/app/code/local/PFG/LogReader/controllers/Adminhtml/Pfg/LogreaderController.php(68): PFG_LogReader_Model_Security->checkRateLimit()
#4 /magento/app/code/core/Mage/Core/Controller/Varien/Action.php(418): PFG_LogReader_Adminhtml_Pfg_LogreaderController->ajaxAction()
#5 /magento/app/code/core/Mage/Core/Controller/Varien/Router/Standard.php(254): Mage_Core_Controller_Varien_Action->dispatch('ajax')
#6 /magento/app/code/core/Mage/Core/Controller/Varien/Front.php(172): Mage_Core_Controller_Varien_Router_Standard->match(Object(Mage_Core_Controller_Request_Http))
#7 /magento/app/code/core/Mage/Core/Model/App.php(381): Mage_Core_Controller_Varien_Front->dispatch()
#8 /magento/app/Mage.php(694): Mage_Core_Model_App->run(Array)
#9 /magento/index.php(86): Mage::run('default', 'store')
#10 {main}";s:3:"url";s:88:"/index.php/vipadmin/pfg_logreader/ajax/key/9ec3b4d460ae477dd5021c7c416ef2b7/?isAjax=true";s:11:"script_name";s:10:"/index.php";s:4:"skin";s:5:"admin";}