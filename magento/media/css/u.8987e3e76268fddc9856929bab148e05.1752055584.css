

/* FILE: mirasvit_searchindex.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.searchindex-results {
  *zoom: 1;
  margin-bottom: 10px;
  border-bottom: 1px solid #a0b3c3;
}
.searchindex-results:before,
.searchindex-results:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchindex-results:after {
  clear: both;
}
.searchindex-results li {
  margin-bottom: -1px;
  float: left;
  *zoom: 1;
  margin: 0px 2px;
}
.searchindex-results li:before,
.searchindex-results li:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchindex-results li:after {
  clear: both;
}
.searchindex-results li a {
  font-size: 12px;
  font-weight: bold;
  color: #333;
  display: block;
  cursor: pointer;
  padding: 2px 5px 1px 5px;
  line-height: 20px;
  border: 1px solid transparent;
  border-bottom: none;
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
  text-decoration: none;
}
.searchindex-results li a:hover {
  background-color: #f1f1f1;
  background-image: -moz-linear-gradient(top, #e8e8e8, #ffffff);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e8e8e8), to(#ffffff));
  background-image: -webkit-linear-gradient(top, #e8e8e8, #ffffff);
  background-image: -o-linear-gradient(top, #e8e8e8, #ffffff);
  background-image: linear-gradient(to bottom, #e8e8e8, #ffffff);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8e8e8', endColorstr='#ffffffff', GradientType=0);
  border: 1px solid #a0b3c3;
  border-bottom: none;
}
.searchindex-results li a span {
  font-weight: normal;
  font-size: 0.9em;
}
.searchindex-results li.active a {
  background-color: #f1f1f1;
  background-image: -moz-linear-gradient(top, #e8e8e8, #ffffff);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e8e8e8), to(#ffffff));
  background-image: -webkit-linear-gradient(top, #e8e8e8, #ffffff);
  background-image: -o-linear-gradient(top, #e8e8e8, #ffffff);
  background-image: linear-gradient(to bottom, #e8e8e8, #ffffff);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8e8e8', endColorstr='#ffffffff', GradientType=0);
  cursor: default;
  border: 1px solid #a0b3c3;
  border-bottom: none;
}
.searchindex-result li {
  padding: 5px;
}
.searchindex-result li .title a {
  font-weight: bold;
  color: #203548;
  font-size: 13px;
}
.searchindex-result.searchindex-result-category {
  margin-bottom: 20px;
}
.searchindex-result.searchindex-result-category li {
  padding: 1px 0px;
}
.searchindex-result.searchindex-result-category li a {
  font-weight: bold;
  color: #203548;
}
.searchindex-highlight {
  background-color: #ff0;
}



/* FILE: product_onsale_label.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
.onsale-product-container {
    cursor: pointer;
    position: relative;
    z-index: 100;
}

.onsale-product-container-inside {
    cursor: pointer;
    position: absolute;
    z-index: 100;
}

div.onsale-product-label {
    position: absolute;
    top: 3px;
    left: 4px;
    margin-left: -2px;
    text-align: center;
}

div.onsale-onsale-product-label-text {
    FONT-FAMILY: inherit;
    FONT-SIZE: 16px;
    TEXT-ALIGN: center;
    VERTICAL-ALIGN: middle;
    COLOR: #FFFFFF;
}

div.new-onsale-product-label-text {
    FONT-FAMILY: inherit;
    FONT-SIZE: 16px;
    TEXT-ALIGN: center;
    VERTICAL-ALIGN: middle;
    COLOR: #FFFFFF;
}

div.custom-onsale-product-label-text {
    FONT-FAMILY: inherit;
    FONT-SIZE: 16px;
    TEXT-ALIGN: center;
    VERTICAL-ALIGN: middle;
    COLOR: #FFFFFF;
}


/* FILE: category_onsale_label.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
div.onsale-category-container-grid {
    position: relative;
}

div.onsale-category-container-list {
    position: relative;
    float: left;
}

.onsale-category-container {
    cursor: pointer;
    position: absolute;
    z-index: 100;
}

div.onsale-category-label {
    position: absolute;
    top: 3px;
    left: 4px;
    margin-left: -2px;
    text-align: center;
}

div.onsale-onsale-category-label-text {
    color: #FFFFFF;
    font-family: Arial;
    font-size: 11px;
    font-weight: bold;
}

div.new-onsale-category-label-text {
    color: #FFFFFF;
    font-family: Arial;
    font-size: 11px;
    font-weight: bold;
}

div.custom-onsale-category-label-text {
    color: #FFFFFF;
    font-family: Arial;
    font-size: 11px;
    font-weight: bold;
}






/* FILE: layer.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
#bubble-layer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1000000;
    background-color: #000;
    filter: alpha(opacity=50);
    opacity: .5;
}

#bubble-layer-loader {
    position: fixed;
    top: 50%;
    left: 50%;
    right: auto;
    bottom: auto;
    transform: translate(-50%, -50%);
    z-index: 1000001;
    text-align: center;
    width: auto;
}

.block-layered-nav dd li {
    padding: 2px 0;
}

div.layer-slider {
    min-width: 100px;
}
div.price-slider {
    position: relative;
    margin: 10px 0 5px;
    height: 22px;
    cursor: pointer;
}

div.price-slider .bg {
    position: absolute;
    top: 10px;
    background-color: #798794;
    height: 2px;
    width: 100%;
    cursor: default;
}

div.price-slider .handle {
    position: absolute;
    width: 9px;
    height: 22px;
    cursor: move;
    background: url(//localhost:8580/skin/frontend/default/default/images/magnifier_handle.**********.gif) left top no-repeat;
}

div.price-slider .span {
    position: absolute;
    top: 10px;
    margin-left: 9px;
    background-color: #e26703;
    height: 2px;
    cursor: default;
}

div.price-range {
    font-weight: bold;
    color: #e26703;
}

div.price-range input {
    width: 40px;
    margin: 0 4px;
    text-align: center;
}

div.price-limit {
    font-size: 11px;
}

div.price-limit .max {
    float: right;
}

.block-layered-nav dl#narrow-by-list dd ol > li > span,
.block-layered-nav dl#narrow-by-list dd ol > li > a {
    padding: 0;
    display: inline;
}

.block-layered-nav .category-filter-tree div.on {
    float: left;
    width: 0;
    height: 0;
    margin: 6px 4px 0 -14px;
    border-style: solid;
    border-width: 6px 5px 0 5px;
    border-color: #1e7ec8 transparent transparent transparent;
}

.block-layered-nav .category-filter-tree div.off {
    float: left;
    width: 0;
    height: 0;
    margin: 4px 4px 0 -12px;
    border-style: solid;
    border-width: 5px 0 5px 6px;
    border-color: transparent transparent transparent #1e7ec8;
}

.block-layered-nav .category-filter-tree li.active a {
    font-weight: bold;
}

.block-layered-nav .category-filter-tree .level1 {
    padding-left: 12px;
}

.block-layered-nav .category-filter-tree .level2 {
    padding-left: 24px;
}

.block-layered-nav .category-filter-tree .level3 {
    padding-left: 36px;
}

.block-layered-nav .category-filter-tree .level4 {
    padding-left: 48px;
}

.block-layered-nav .dropdown-filter {
    width: 100%;
}

.block-layered-nav .label-filter li {
    display: inline-block;
    margin: 0 4px 8px 0;
}

.block-layered-nav .label-filter a {
    font-weight: normal;
    display: block;
    padding: 4px 8px;
    background-color: #fff;
    color: #000;
    border-radius: 3px;
    text-decoration: none;
}

.block-layered-nav .label-filter a span.count {
    display: inline-block;
    min-width: 10px;
    white-space: nowrap;
    font-weight: bold;
    color: #777;
    background-color: #eee;
    padding: 3px 7px;
    border-radius: 10px;
    text-align: center;
    line-height: 1;
    vertical-align: middle;
    font-size: 12px;
    margin-left: 3px;
}

.block-layered-nav .label-filter li.active a,
.block-layered-nav .label-filter a:hover,
.block-layered-nav .label-filter a:focus {
    text-decoration: none;
    outline: 0;
    background-color: #496778;
    color: #fff;
    border-radius: 3px;
}

.block-layered-nav .label-filter li.active a span.count,
.block-layered-nav .label-filter a:hover span.count,
.block-layered-nav .label-filter a:focus span.count {
    background-color: #fff;
    color: #000;
}

.bubble-layer-top .block {
    margin-bottom: 0;
}

.bubble-layer-top .block-content {
    display: inline-table;
    margin-right: 20px;
    margin-bottom: 20px;
    max-width: 50%;
}

#narrow-by-list dd:last-child,
.bubble-layer-top .block-layered-nav dd {
    background: none;
}


/* FILE: amazon.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  line-height: 0;
}
.clearfix:after {
  clear: both;
}
.hide-text {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 30px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.searchautocomplete {
  position: relative;
  float: left;
  width: 560px;
  height: 50px;
  margin-top: 6px;
  background: #fff;
  display: block;
}
.searchautocomplete .sprite {
  background: url('//localhost:8580/skin/frontend/stenik/default/images/mirasvit/sprite.**********.png');
}
.searchautocomplete > label {
  float: left;
  margin-right: 10px;
  color: #333;
  font-weight: bold;
  margin-top: 8px;
}
.searchautocomplete .nav {
  background: url('//localhost:8580/skin/frontend/stenik/default/images/mirasvit/sprite.**********.png');
  float: left;
  position: relative;
  width: 100%;
}
.searchautocomplete .nav .nav-search-in {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 15;
}
.searchautocomplete .nav .nav-search-in .category {
  opacity: 0;
  filter: alpha(opacity=0);
  background: white;
  border: 1px solid #DDD;
  color: black;
  cursor: pointer;
  height: 29px;
  left: 5px;
  margin: 0;
  outline: 0;
  padding: 0;
  position: absolute;
  top: 5px;
  visibility: visible;
  width: auto;
}
.searchautocomplete .nav .nav-search-in .category-fake {
  -webkit-border-radius: 5px 0px 0px 5px;
  -moz-border-radius: 5px 0px 0px 5px;
  border-radius: 5px 0px 0px 5px;
  background-color: #fcfcfc;
  background-image: -moz-linear-gradient(top, #ffffff, #f7f7f7);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f7f7f7));
  background-image: -webkit-linear-gradient(top, #ffffff, #f7f7f7);
  background-image: -o-linear-gradient(top, #ffffff, #f7f7f7);
  background-image: linear-gradient(to bottom, #ffffff, #f7f7f7);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff7f7f7', GradientType=0);
  border-right: 1px solid #dddddd;
  color: #777;
  cursor: pointer;
  float: left;
  font-size: 11px;
  height: 27px;
  line-height: 27px;
  margin: 4px 0px 0px 2px;
  padding: 0px 25px 0px 10px;
  text-align: center;
  white-space: nowrap;
}
.searchautocomplete .nav .nav-search-in .nav-down-arrow {
  right: 10px;
  top: 16px;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 4.5px 0 4.5px;
  border-color: #000000 transparent transparent transparent;
  line-height: 0px;
}
.searchautocomplete .nav .nav-search-in:hover .category-fake {
  background-color: #eeeeee;
  background-image: -moz-linear-gradient(top, #f7f7f7, #e1e1e1);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f7f7f7), to(#e1e1e1));
  background-image: -webkit-linear-gradient(top, #f7f7f7, #e1e1e1);
  background-image: -o-linear-gradient(top, #f7f7f7, #e1e1e1);
  background-image: linear-gradient(to bottom, #f7f7f7, #e1e1e1);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff7f7f7', endColorstr='#ffe1e1e1', GradientType=0);
}
.searchautocomplete .nav .nav-search-in:hover .nav-down-arrow {
  background-position: -6px -34px;
}
.searchautocomplete .nav .nav-input {
  *zoom: 1;
  margin: 0;
  padding: 0;
  z-index: 12;
  position: relative;
}
.searchautocomplete .nav .nav-input:before,
.searchautocomplete .nav .nav-input:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchautocomplete .nav .nav-input:after {
  clear: both;
}
.searchautocomplete .nav .nav-input input {
  float: left;
  width: 100%;
  height: 50px;
  padding: 0px 58px 0px 20px;
  margin: 0px;
  color: #000;
  font-size: 18px;
  font-weight: normal;
  text-decoration: none;
  background: #fff;
  border: 1px solid #d9dbda;
  box-sizing: border-box;
  position: relative;
  z-index: 15;
  font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
}
.searchautocomplete .nav .nav-input input:hover{border-color: #777;}
.searchautocomplete .nav .nav-input input:focus{border-color: #777; background: #ebebeb;}
.searchautocomplete .nav .nav-input input.suggest {
  display: none;
  color: #999;
  z-index: 9;
}
.searchautocomplete button.button{
  width: 49px;
  height: 48px;
  position: absolute;
  right: 1px;
  top: 1px;
  z-index: 999;
  min-width: auto;
  background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/searchSubmitIcon.**********.png) no-repeat;
  background-position: center center;
  border-left: 1px solid #d9dbda;
}
.searchautocomplete button.button:hover {
  background: #ebebeb url(//localhost:8580/skin/frontend/stenik/default/images/searchSubmitIcon.**********.png) no-repeat;
  background-position: center center;
}
.searchautocomplete .nav-submit-button {
  background: url('//localhost:8580/skin/frontend/stenik/default/images/mirasvit/sprite.**********.png');
  background-position: 0px -34px;
  background-repeat: no-repeat;
  float: left;
  height: 34px;
  padding-left: 5px;
}
.searchautocomplete .nav-submit-button .button {
  width: 39px;
  height: 34px;
  padding: 0px 9px;
  margin: 0px;
  cursor: pointer;
  font-weight: bold;
  color: white;
  line-height: 12px;
  font-size: 13px;
  background-color: #3c454e;
  background-image: -moz-linear-gradient(top, #444c55, #313a44);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#444c55), to(#313a44));
  background-image: -webkit-linear-gradient(top, #444c55, #313a44);
  background-image: -o-linear-gradient(top, #444c55, #313a44);
  background-image: linear-gradient(to bottom, #444c55, #313a44);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff444c55', endColorstr='#ff313a44', GradientType=0);
  border: 1px solid #313a44;
  -webkit-border-radius: 0px 5px 5px 0px;
  -moz-border-radius: 0px 5px 5px 0px;
  border-radius: 0px 5px 5px 0px;
  z-index: 14;
}
.searchautocomplete .nav-submit-button .button:hover {
  background-color: #39414b;
  background-image: -moz-linear-gradient(top, #313a44, #444c55);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#313a44), to(#444c55));
  background-image: -webkit-linear-gradient(top, #313a44, #444c55);
  background-image: -o-linear-gradient(top, #313a44, #444c55);
  background-image: linear-gradient(to bottom, #313a44, #444c55);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff313a44', endColorstr='#ff444c55', GradientType=0);
}
.searchautocomplete .searchautocomplete-placeholder {
  background: #FFF;
  border: 1px solid #ededed;
  padding: 0;
  position: absolute;
  right: 0px;
  top: 49px;
  width: 100%;
  z-index: 1000;
}
.searchautocomplete .searchautocomplete-placeholder:before {
  border-color: transparent transparent #CCC transparent;
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 5px;
  height: 0;
  width: 0;
  top: -11px;
  left: 30px;
  display: none;
}
.searchautocomplete .searchautocomplete-placeholder ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.searchautocomplete .searchautocomplete-placeholder ul li {
  *zoom: 1;
  text-align: left;
  padding: 5px 5px;
  margin-bottom: 0px;
  border-bottom: 1px solid #e7e7e7;
}
.searchautocomplete .searchautocomplete-placeholder ul li:before,
.searchautocomplete .searchautocomplete-placeholder ul li:after {
  display: table;
  content: "";
  line-height: 0;
}
.searchautocomplete .searchautocomplete-placeholder ul li:after {
  clear: both;
}
.searchautocomplete .searchautocomplete-placeholder ul li a {
  text-decoration: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li a.name {
  color: #616161;
  display: block;
  margin: 40px 0px 5px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li a .pull-right {
  float: right;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active {
  background-color: #f9f9f9;
  cursor: pointer;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
}
.searchautocomplete .searchautocomplete-placeholder ul li.active a {
  text-decoration: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li .searchautocomlete-image {
  float: left;
  margin: 5px 10px 0px 0px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box {
  font-size: 16px;
  color: #cc1153;
  float: left;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .regular-price .price-label,
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price .price-label,
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .special-price .price-label {
  display: none;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .price-label {
  font-weight: normal;
  color: #999;
}
.searchautocomplete .searchautocomplete-placeholder ul li .price-box .old-price .price {
  font-size: 0.9em;
  color: #999;
}
.searchautocomplete .searchautocomplete-placeholder ul li .highlight strong {
  color: #616161;
  font-weight: bold;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings {
  margin: 0;
  line-height: 14px;
}
.searchautocomplete .searchautocomplete-placeholder ul li .ratings .rating-box {
  float: left;
  margin: 0 5px 0 0;
}
.searchautocomplete .searchautocomplete-placeholder .index-title {
  text-align: right;
  color: #0A263C;
  border-bottom: 1px solid #CCC;
  padding: 2px 5px;
}
.searchautocomplete .searchautocomplete-placeholder .index-title span {
  color: #666;
  font-size: 0.9em;
}
.searchautocomplete .searchautocomplete-placeholder .all {
  text-align: center;
  margin: 15px 0px;
}
.searchautocomplete .searchautocomplete-placeholder .all a{
  color: #cc1159;
}
.searchautocomplete .searchautocomplete-loader {
  position: absolute;
  width: 18px;
  height: 18px;
  top: 9px;
  right: 10px;
  z-index: 99;
  -webkit-transform: scale(0.6);
  -moz-transform: scale(0.6);
  -ms-transform: scale(0.6);
  -o-transform: scale(0.6);
  transform: scale(0.6);
  display: none;
}
.searchautocomplete .searchautocomplete-loader div {
  position: absolute;
  background-color: #FFFFFF;
  height: 3px;
  width: 3px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-animation-name: f_autocomplete_loader;
  animation-name: f_autocomplete_loader;
  -webkit-animation-duration: 0.64s;
  animation-duration: 0.64s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-direction: normal;
  animation-direction: normal;
}
.searchautocomplete .searchautocomplete-loader div#g01 {
  left: 0px;
  top: 7px;
  -webkit-animation-delay: 0.24s;
  animation-delay: 0.24s;
}
.searchautocomplete .searchautocomplete-loader div#g02 {
  left: 2px;
  top: 2px;
  -webkit-animation-delay: 0.32s;
  animation-delay: 0.32s;
}
.searchautocomplete .searchautocomplete-loader div#g03 {
  left: 7px;
  top: 0px;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}
.searchautocomplete .searchautocomplete-loader div#g04 {
  right: 2px;
  top: 2px;
  -webkit-animation-delay: 0.48s;
  animation-delay: 0.48s;
}
.searchautocomplete .searchautocomplete-loader div#g05 {
  right: 0px;
  top: 7px;
  -webkit-animation-delay: 0.56s;
  animation-delay: 0.56s;
}
.searchautocomplete .searchautocomplete-loader div#g06 {
  right: 2px;
  bottom: 2px;
  -webkit-animation-delay: 0.64s;
  animation-delay: 0.64s;
}
.searchautocomplete .searchautocomplete-loader div#g07 {
  left: 7px;
  bottom: 0px;
  -webkit-animation-delay: 0.72s;
  animation-delay: 0.72s;
}
.searchautocomplete .searchautocomplete-loader div#g08 {
  left: 2px;
  bottom: 2px;
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s;
}
@-moz-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-webkit-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-ms-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@-o-keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
@keyframes f_autocomplete_loader {
  0% {
    background-color: #000000;
  }
  100% {
    background-color: #ffffff;
  }
}
.searchautocomplete-widget {
  position: relative;
  top: auto;
  left: auto;
  bottom: auto;
  right: auto;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}



/* FILE: style.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
@charset "utf-8";
/* CSS Document
   Author: Veselin Trifonov as developer @ www.stenikgroup.com
*/

body {
	float: left;
	width: 100%;
	height: auto;
	direction: ltr;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	font-size: 18px;
	line-height: 22px;
	margin: 0px;
	padding: 0px;
	color: #777;
	font-weight: normal;
	background: #000;
	overflow-x: hidden;
}

@media only screen and (min-width: 0px) and (max-width: 1225px) { body {overflow-x:visible;} }


/* Stenik default style helpers */
ol { padding:0; margin:5px 0 10px 20px; }
ol li { padding:0; margin:0;  }
ul { margin: 0; padding: 0; }
ul li {	list-style: none; padding:0; margin:0; }
p {	padding:0; margin:0; }
iframe { border-width: 0px; }
a {	outline:none; }
a img { border:0; }
.clear { clear:both; }
.clearH { clear:both; height:10px; }
.clearH2 { clear:both; height:20px; }
.clearH3 { clear:both; height:30px; }
.clearH4 { clear:both; height:40px; }
.radiusL { border-radius: 0px 0px 0px 5px; }
.radiusR { border-radius: 0px 0px 5px 0px; }
.decNone {	text-decoration:none; }
.noBgr { background:none!important; }
.left {	float:left; }
.right { float:right; }
.noBorder { border: none!important; }
.noBorderB { border-bottom:none !important; }
.noBorderT { border-top:none !important; }
.noBorderL { border-left:none !important; }
.noBorderR { border-right:none !important; }
.noMarginR {margin-right:0 !important; }
.noMarginL {margin-left:0 !important; }
.noMarginB { margin-bottom:0 !important; }
.noMarginT { margin-top:0 !important; }
.noMargin { margin:0 !important; }
.noPaddingL { padding-left:0 !important; }
.noPaddingR { padding-left:0 !important; }
.noPaddingB { padding-bottom:0 !important; }
.noPaddingT { padding-top:0 !important; }
.noOverflow { overflow:hidden; }
.noOverflowX { overflow-x:hidden; }
/* End of Stenik default style helpers */





/* Some default resets */

.clearer:after, .header-container:after, .header-container .top-container:after, .header:after, .header .quick-access:after, #nav:after, .main:after, .footer:after, .footer-container .bottom-container:after, .col-main:after, .col2-set:after, .col3-set:after, .col3-layout .product-options-bottom .price-box:after, .col4-set:after, .search-autocomplete li:after, .block .block-content:after, .block .actions:after, .block li.item:after, .block-poll li:after, .block-layered-nav .currently li:after, .page-title:after, .products-grid:after, .products-list li.item:after, .box-account .box-head:after, .dashboard .box .box-title:after, .box-reviews li.item:after, .box-tags li.item:after, .pager:after, .sorter:after, .ratings:after, .add-to-box:after, .add-to-cart:after, .product-essential:after, .product-collateral:after, .product-view .product-img-box .more-views ul:after, .product-view .box-tags .form-add:after, .product-view .product-shop .short-description:after, .product-view .box-description:after, .product-options .options-list li:after, .product-options-bottom:after, .product-review:after, .cart:after, .cart-collaterals:after, .cart .crosssell li.item:after, .opc .step-title:after, .checkout-progress:after, .multiple-checkout .place-order:after, .group-select li:after, .form-list li:after, .form-list .field:after, .buttons-set:after, .page-print .print-head:after, .advanced-search-summary:after, .gift-messages-form .item:after, .send-friend .form-list li p:after {
    clear: both;
    content: ".";
    display: block;
    font-size: 0;
    height: 0;
    line-height: 0;
    overflow: hidden;
}



/* Fonts */

@font-face {
    font-family: 'ArialBG';
    src: url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbg.woff2') format('woff2'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbg.woff') format('woff'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbg.svg#arial_bulgarianregular') format('svg');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'ArialBG';
    src: url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgb.woff2') format('woff2'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgb.woff') format('woff'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgb.svg#arial_bulgarianbold') format('svg');
    font-weight: bold;
    font-style: normal;
}
@font-face {
    font-family: 'ArialBG';
    src: url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgi.woff2') format('woff2'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgi.woff') format('woff'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgi.svg#arial_bulgarianitalic') format('svg');
    font-weight: normal;
    font-style: italic;
}
@font-face {
    font-family: 'ArialBG';
    src: url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgz.woff2') format('woff2'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgz.woff') format('woff'),
         url('//localhost:8580/skin/frontend/stenik/default/fonts/arialbgz.svg#arial_bulgarianbold_italic') format('svg');
    font-weight: bold;
    font-style: italic;
}

@font-face {
  font-family: 'HKGrotesk';
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-regular-web.eot');
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-regular-web.woff2') format('woff2'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-regular-web.woff') format('woff'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-regular-web.ttf') format('truetype'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-regular-web.svg#hkgrotesk-regular-web') format('svg'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-regular-web.eot?#iefix') format('embedded-opentype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'HKGrotesk';
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-bold-web.eot');
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-bold-web.woff2') format('woff2'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-bold-web.woff') format('woff'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-bold-web.ttf') format('truetype'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-bold-web.svg#hkgrotesk-bold-web') format('svg'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-bold-web.eot?#iefix') format('embedded-opentype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'HKGrotesk';
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-medium-web.eot');
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-medium-web.woff2') format('woff2'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-medium-web.woff') format('woff'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-medium-web.ttf') format('truetype'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-medium-web.svg#hkgrotesk-medium-web') format('svg'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-medium-web.eot?#iefix') format('embedded-opentype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'HKGrotesk';
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-light-web.eot');
  src: url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-light-web.woff2') format('woff2'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-light-web.woff') format('woff'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-light-web.ttf') format('truetype'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-light-web.svg#hkgrotesk-light-web') format('svg'),
       url('//localhost:8580/skin/frontend/stenik/default/fonts/hkgrotesk-light-web.eot?#iefix') format('embedded-opentype');
  font-weight: 300;
  font-style: normal;
}



/* Default input, select and textarea styles
**********************************************/

input.input-text {
	float: left;
	width: 100%;
	height: 46px;
	padding: 0px 10px 0px 10px;
	margin: 0px 0px 18px 0px;
	border: 1px solid #dbdbdb;
	background: #fff;
	font-size: 16px;
	color: #000;
	text-decoration: none;
	font-weight: normal;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	outline: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
input.input-text:hover { border: 1px solid #bababa; }
input.input-text:focus { border: 1px solid #bababa; background: #f1f1f1; }

select {
	float: left;
	width: 100%;
	height: 46px;
	padding: 0px 44px 0px 12px;
	margin: 0px 0px 10px 0px;
	border: 1px solid #dbdbdb;
	background: #fff url(//localhost:8580/skin/frontend/stenik/default/images/select-arrow.**********.png) no-repeat right 17px;
	font-size: 16px;
	color: #000;
	text-decoration: none;
	font-weight: normal;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	position: relative;
	-moz-outline: none;
	outline: none;
	-webkit-appearance: none;
    -moz-appearance: none;
    -o-appearance: none;
    appearance: none;
    text-overflow: '';
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
select:hover { border: 1px solid #bababa; background: #fff url(//localhost:8580/skin/frontend/stenik/default/images/select-arrow.**********.png) no-repeat right 17px; }
select:focus { border: 1px solid #bababa; background: #f1f1f1 url(//localhost:8580/skin/frontend/stenik/default/images/select-arrow.**********.png) no-repeat right 17px; }

select::-ms-expand { display: none; }
select:-moz-focusring { color: transparent; text-shadow: 0 0 0 #000; }

textarea {
	float: left;
	width: 100%;
	height: 110px;
	padding: 10px;
	margin: 0px 0px 15px 0px;
	font-size: 16px;
	line-height: 18px;
	color: #2d2c2c;
	text-decoration: none;
	font-weight: bold;
	border: 1px solid #cbcbcb;
	background: #fff;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	resize: vertical;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
textarea:hover { border: 1px solid #bababa; }
textarea:focus { border: 1px solid #bababa; background: #f1f1f1; }


.button {
	float: left;
	width: auto;
	min-width: 140px;
	height: 42px;
	padding: 0px 25px 0px 25px;
	margin: 0px;
	background: #cc1153;
	border: none;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	font-size: 16px;
	color: #fff;
	font-weight: 500;
	text-align: center;
	text-decoration: none;
	outline: none;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.button:hover { background: #000; }
.button:active {
	-webkit-transition: all 0.1s linear; -moz-transition: all 0.1s linear; -o-transition: all 0.1s linear; transition: all 0.1s linear;
	background: #555;
}

/* End of default input, select and textare styles */




/* Main styles for web site start */

.wrapper {
	width: 1180px;
	height: auto;
	margin: 0 auto;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

header {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	z-index: 300;
	background: #fff;
	position: relative;
}
.opc-index-index header { z-index: 200; }

header .headerTop {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 27px 0px;
	background: #000;
}
header .headerContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
header .headerPhone {
	float: left;
	width: 180px;
	height: 50px;
	line-height: 50px;
	padding: 0px 10px 0px 44px;
	margin: 0px 64px 0px 0px;
	color: #ea135f;
	font-size: 20px;
	font-weight: normal;
	text-decoration: none;
	border-left: 1px solid #333;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/headerPhoneIcon.**********.png) no-repeat 16px 14px;
}
header .headerPhone p {
	padding: 0px;
	margin: 0px;
	color: #ea135f;
	font-size: 20px;
	line-height: 50px;
	font-weight: normal;
	text-decoration: none;
}
header .headerPhone p a {
	padding: 0px;
	margin: 0px;
	color: #ea135f;
	font-size: 20px;
	line-height: 50px;
	font-weight: normal;
	text-decoration: none;
}
header .headerTopDelivery {
	float: left;
	width: auto;
	min-width: 490px;
	height: 47px;
	line-height: 47px;
	padding: 0px 10px 3px 60px;
	margin: 0px 0px 0px 0px;
	color: #fff;
	font-size: 16px;
	font-weight: 300;
	text-decoration: none;
	background: #cc1153 url(//localhost:8580/skin/frontend/stenik/default/images/headerTopDelivery.**********.png) no-repeat 26px 14px;
}
header .headerTopDelivery p { padding: 0px; margin: 0px; color: #fff; font-size: 16px; font-weight: 300; text-decoration: none; }
header .headerTopDelivery p strong { font-size: 18px; font-weight: bold; text-transform: uppercase; }

header .headerTopDelivery p a { padding: 0px; margin: 0px; color: #fff; font-size: 16px; font-weight: 300; text-decoration: none; }
header .headerTopDelivery p a strong { font-size: 18px; font-weight: bold; text-transform: uppercase; }

header .headerRight {
	float: right;
	width: auto;
	height: 48px;
	padding: 0px;
	margin: 0px;
}
header .login {
	float: left;
	width: auto;
	height: 48px;
	line-height: 48px;
	padding: 0px 10px 2px 32px;
	margin: 0px 0px 0px 10px;
	border-right: 1px solid #333;
	background: #000 url(//localhost:8580/skin/frontend/stenik/default/images/headerLoginIcon.**********.png) no-repeat 0px 12px;
	color: #fff;
	font-size: 13px;
	font-weight: 300;
	text-align: left;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header .login:hover { color: #cc1153; }

header .wishlist {
	float: left;
	width: auto;
	height: 48px;
	line-height: 48px;
	padding: 0px 10px 2px 32px;
	margin: 0px 0px 0px 10px;
	border-right: 1px solid #333;
	background: #000 url(//localhost:8580/skin/frontend/stenik/default/images/headerWishlistIcon.**********.png) no-repeat 0px 12px;
	color: #fff;
	font-size: 13px;
	font-weight: 300;
	text-align: left;
	text-decoration: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header .wishlist:hover { color: #cc1153; }

header .wishlist .number {
	width: 18px;
	height: 18px;
	line-height: 18px;
	padding: 0px;
	margin: 0px;
	background: #cc1153;
	border-radius: 100%;
	color: #fff;
	font-size: 13px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	position: absolute;
	left: 16px;
	top: 7px;
	z-index: 10;
	opacity: 0.9;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

header .compare {
	float: left;
	width: auto;
	height: 48px;
	line-height: 48px;
	padding: 0px 10px 2px 32px;
	margin: 0px 0px 0px 10px;
	border-right: 1px solid #333;
	background: #000 url(//localhost:8580/skin/frontend/stenik/default/images/headerCompareIcon2.**********.png) no-repeat 0px 12px;
	color: #fff;
	font-size: 13px;
	font-weight: 300;
	text-align: left;
	text-decoration: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header .compare:hover { color: #cc1153; }

header .compare .number {
	width: 18px;
	height: 18px;
	line-height: 18px;
	padding: 0px;
	margin: 0px;
	background: #cc1153;
	border-radius: 100%;
	color: #fff;
	font-size: 13px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	position: absolute;
	left: 16px;
	top: 7px;
	z-index: 10;
	opacity: 0.9;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

header .logo {
	float: left;
	width: 250px;
	height: 70px;
	padding: 0px;
	margin: 0px 50px 0px 0px;
}
header .logo img {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
	z-index: 311;
}
header form.searchForm {
	float: left;
	width: 560px;
	height: 50px;
	padding: 0px;
	margin: 6px 0px 0px 0px;
	position: relative;
}
header form.searchForm input.searchInput {
	float: left;
	width: 100%;
	height: 50px;
	padding: 0px 58px 0px 20px;
	margin: 0px;
	color: #000;
	font-size: 18px;
	font-weight: normal;
	text-decoration: none;
	background: #fff;
	border: 1px solid #d9dbda;
	box-sizing: border-box;
	position: relative;
	z-index: 15;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header form.searchForm input.searchInput:hover { border-color: #777; }
header form.searchForm input.searchInput:focus { border-color: #777; background: #ebebeb; }

header form.searchForm .searchSubmit {
	float: left;
	width: 49px;
	height: 48px;
	padding: 0px 0px 0px 0px;
	margin: 0px;
	border: none;
	border-left: 1px solid #d9dbda;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/searchSubmitIcon.**********.png) no-repeat center center;
	position: absolute;
	right: 1px;
	top: 1px;
	z-index: 20;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header form.searchForm .searchSubmit:hover { background-color: #ebebeb; }

header .headerCart {
	float: right;
	width: auto;
	height: 28px;
	line-height: 28px;
	padding: 0px 0px 0px 40px;
	margin: 15px 0px 0px 0px;
	color: #cc1153;
	font-size: 16px;
	font-weight: 500;
	text-decoration: none;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/headerCartIcon.**********.png) no-repeat left 1px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header .headerCart:hover { color: #000; padding: 0px 0px 0px 36px; }
header .headerCart .number { font-size: 24px; }

header nav.mainNav {
	float: left;
	width: 100%;
	height: 38px;
	padding: 0px;
	margin: 16px 0px 0px 0px;
	border-bottom: 2px solid #cc1153;
}
header nav.mainNav ul {
	float: left;
	width: 100%;
	height: 38px;
	padding: 0px;
	margin: 0px;
	text-align: center;
	position: relative;
}
header nav.mainNav ul li {
	display: inline-block;
	float: none;
	width: auto;
	height: 40px;
	padding: 0px;
	margin: 0px auto;
}
header nav.mainNav ul li a {
	float: left;
	width: auto;
	height: 34px;
	line-height: 34px;
	padding: 0px 18px 4px 18px;
	margin: 0px 8px 0px 8px;
	color: #000;
	font-size: 17px;
	font-weight: bold;
	text-decoration: none;
	background: #fff;
	position: relative;
	z-index: 360;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header nav.mainNav ul li:hover a { background: #cc1155; color: #fff; }
header nav.mainNav ul li.sale a { color: #cc1155; }
header nav.mainNav ul li.sale:hover a { color: #fff; }

header nav.mainNav ul li.active > a { background: #cc1155 !important; color: #fff !important; }

header nav.mainNav .subMenu {
	float: left;
	width: 100%;
	height: auto;
	padding: 28px 0px 0px 0px;
	margin: 0px;
	background: #fff;
	box-shadow: 0px 5px 20px 0px #dbdbdb;
	position: absolute;
	left: 0px;
	top: 40px;
	z-index: 350;
	display: none;
}
header nav.mainNav .subMenu:after {
	content: '';
	width: 100%;
	height: 10px;
	padding: 0px;
	margin: 0px;
	background: #fff;
	position: absolute;
	left: 0px;
	bottom: -10px;
	z-index: 360;
}
header nav.mainNav .subMenu .subMenuCol {
	float: left;
	width: 174px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 20px;
}
header nav.mainNav .subMenu .subMenuCol.wideCol { width: 360px; }
header nav.mainNav .subMenu .subMenuCol.extraWideCol { width: 570px; }

header nav.mainNav .subMenu .subMenuCol .colTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 10px 8px 10px;
	margin: 0px 0px 8px 0px;
	color: #000;
	font-size: 16px;
	line-height: 18px;
	font-weight: bold;
	text-align: left;
	text-decoration: none;
	text-transform: uppercase;
	box-sizing: border-box;
	border-bottom: 1px solid #e5e5e5;
}
header nav.mainNav .subMenu .subMenuCol ul {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
header nav.mainNav .subMenu .subMenuCol.wideCol ul { width: 170px; margin-right: 10px; }
header nav.mainNav .subMenu .subMenuCol.extraWideCol ul { width: 180px; margin-right: 10px; }

header nav.mainNav .subMenu .subMenuCol ul li {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
header nav.mainNav .subMenu .subMenuCol ul li a {
	float: left;
	width: 100%;
	height: auto;
	padding: 5px 5px 5px 10px;
	margin: 2px 0px 2px 0px;
	color: #000;
	font-size: 15px;
	line-height: 18px;
	font-weight: 300;
	text-align: left;
	text-decoration: none;
	text-transform: none;
	background: #fff;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header nav.mainNav .subMenu .subMenuCol ul li a:hover { color: #cc1153; }
header nav.mainNav .subMenu .subMenuCol ul li a strong {
	font-weight: 700;
	color: #cc1153;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header nav.mainNav .subMenu .subMenuCol ul li a strong:hover { color: #000; }

header nav.mainNav .subMenu .subMenuCol.boldLinks ul li a { font-weight: 700; }

header nav.mainNav .subMenu .subMenuBanner {
	float: right;
	width: 350px;
	height: auto;
	padding: 0px;
	margin: 22px 20px 10px 0px;
}
header nav.mainNav .subMenu .subMenuBanner p { padding: 0px; margin: 0px; }
header nav.mainNav .subMenu .subMenuBanner a { height: auto; padding: 0px; margin: 0px; text-decoration: none; border: none; outline: none; }

header nav.mainNav .subMenu .subMenuBanner img {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
header nav.mainNav .subMenu .subMenuBanner a:hover img { opacity: 0.7; }























/* responsive header */

.responsiveHeader {
    float: left;
    width: 100%;
    height: 48px;
    padding: 0px;
    margin: 0px;
    position: relative;
    background: #fff;
    text-align: center;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2000;
    display: none;
}
.responsiveHeader .responsiveLogo {
    display: block;
    width: 155px;
    height: 46px;
    padding: 0px;
    margin: 1px auto 1px auto;
    position: relative;
    z-index: 5;
}
.responsiveHeader .responsiveCart {
    float: right;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveCart.**********.png) no-repeat 2px 3px;
    position: absolute;
    right: 2px;
    top: 9px;
    z-index: 10;
}
.responsiveHeader .responsiveCart .responsiveCartitems {
    float: left;
    width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 0px;
    margin: 0px;
    background: #e1155c;
    border-radius: 100%;
    font-size: 11px;
    color: #fff;
    text-decoration: none;
    text-align: center;
    font-weight: normal;
    display: none;
    position: absolute;
    right: 2px;
    top: 2px;
    z-index: 11;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveCart.hasItems .responsiveCartitems { display: block; }

.responsiveHeader .responsiveCall {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveCall2.**********.png) no-repeat center center;
    position: absolute;
    right: 40px;
    top: 6px;
    z-index: 10;
}
.responsiveHeader .responsiveMenu {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: 5px;
    top: 9px;
    z-index: 2500;
}
.responsiveHeader .responsiveMenu .openResponsiveMenu {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: relative;
}
.responsiveHeader .responsiveMenu .openResponsiveMenu:before {
    content: '';
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: 0;
    top: -8px;
    z-index: 10;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/openResponsiveMenu.**********.png) no-repeat center center;
    opacity: 1;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu.open .openResponsiveMenu:before { opacity: 0; left: -38px; }

.responsiveHeader .responsiveMenu .openResponsiveMenu:after {
    content: '';
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: -28px;
    top: -8px;
    z-index: 10;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/closeResponsiveMenu.**********.png) no-repeat center center;
    opacity: 0;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu.open .openResponsiveMenu:after { opacity: 1; left: 0px; }

.responsiveHeader .responsiveMenu .openResponsiveMenu span {
	width: 100%;
	color: #cc1153;
	font-size: 10px;
	line-height: 12px;
	font-weight: 700;
	text-align: center;
	text-transform: uppercase;
	position: absolute;
	left: 0;
	bottom: 2px;
	z-index: 11;
}

.responsiveHeader .responsiveMenu .responsiveMenuSub {
    float: left;
    width: 0px;
    height: auto;
    padding: 0px;
    margin: 0px;
    background: #e1155c;
    overflow: hidden;
    position: absolute;
    left: -100%;
    top: 39px;
    z-index: 2500;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu.open .responsiveMenuSub { width: 400px; left: -5px; opacity: 1; visibility: visible; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 10px 0px;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
    border-bottom: 2px solid #b5114a;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li:active {
    -webkit-transition: all 0.1s linear; -moz-transition: all 0.1s linear; -o-transition: all 0.1s linear; transition: all 0.1s linear;
    background: #343333;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li.color { background: #333; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li a {
    float: left;
    width: 360px;
    height: auto;
    padding: 12px 20px 8px 20px;
    margin: 0px;
    font-size: 20px;
    line-height: 22px;
    color: #fff;
    text-decoration: none;
    text-align: left;
    font-weight: bold;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li.parent > a { background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveMenuPlus.**********.png) no-repeat right 3px; }
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li.parent.openSub > a { background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveMenuMinus.**********.png) no-repeat right 3px; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li p { display: none; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul { display: none; background: #e1155c; padding: 7px 0px 7px 0px; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li { border-bottom: none; }
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li:active { background: none; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li a {
    width: 320px;
    padding: 7px 20px 7px 20px;
    font-size: 16px;
    line-height: 16px;
    font-weight: bold;
    text-transform: none;
}

.responsiveHeader .responsiveMenu .responsiveMenuSub .accountLinks {

	float: left;
	width: 100%;
	height: auto;
	padding: 3px 20px 3px 53px;
	margin: 10px 0px 0px 0px;
	box-sizing: border-box;
	font-size: 16px;
	line-height: 20px;
	color: #fff;
	text-decoration: none;
	text-align: left;
	font-weight: bold;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/headerLoginIcon.**********.png) no-repeat 18px 3px;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub .accountLinks a {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
	font-size: 16px;
	line-height: 20px;
	color: #fff;
	text-decoration: none;
	text-align: left;
	font-weight: bold;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub .accountLinks span.sep {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px 5px 0px 5px;
	font-size: 16px;
	line-height: 20px;
	color: #fff;
	text-decoration: none;
	text-align: left;
	font-weight: bold;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub .wishtlistItems {
	float: left;
	width: 100%;
	height: auto;
	padding: 3px 20px 3px 53px;
	margin: 10px 0px 0px 0px;
	box-sizing: border-box;
	font-size: 16px;
	line-height: 20px;
	color: #fff;
	text-decoration: none;
	text-align: left;
	font-weight: bold;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/wishtlistItemsRespon.**********.png) no-repeat 17px top;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub .wishtlistItems .responsiveWishlistitems {	display: none; }
.responsiveHeader .responsiveMenu .responsiveMenuSub .wishtlistItems.hasItems .responsiveWishlistitems { display: inline-block; }

.responsiveHeader .responsiveMenu .responsiveMenuSub .responsiveCompare {
	float: left;
	width: 100%;
	height: auto;
	padding: 3px 20px 3px 53px;
	margin: 10px 0px 10px 0px;
	box-sizing: border-box;
	font-size: 16px;
	line-height: 20px;
	color: #fff;
	text-decoration: none;
	text-align: left;
	font-weight: bold;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/headerCompareIcon2.**********.png) no-repeat 17px 2px;
}

.responsiveHeader .responsiveMenu .responsiveMenuSub .headerSearch {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 20px 0px 20px;
	margin: 20px 0px 10px 0px;
	box-sizing: border-box;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub #responsive_search_mini_form {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub #responsive_search_mini_form input.searchInput {
	float: left;
	width: 100%;
	height: 40px;
	padding: 0px 40px 0px 10px;
	margin: 0px;
	color: #000;
	font-size: 18px;
	font-weight: bold;
	text-decoration: none;
	background: #fff;
	border: 1px solid #d9dbda;
	box-sizing: border-box;
	position: relative;
	z-index: 15;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub #responsive_search_mini_form .searchSubmit {
	float: left;
	width: 38px;
	height: 38px;
	padding: 0px 0px 0px 0px;
	margin: 0px;
	border: none;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/searchSubmitIcon.**********.png) no-repeat center center;
	position: absolute;
	right: 0px;
	top: 1px;
	z-index: 20;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub #responsive_search_mini_form .searchSubmit:hover { background-color: #ebebeb; }

/* end responsive header */
























/* responsive header

.responsiveHeader {
    float: left;
    width: 100%;
    height: 81px;
    padding: 0px;
    margin: 0px;
    position: relative;
    background: #fff;
    border-bottom: 2px solid #cc1153;
    text-align: center;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2000;
    display: none;
}
.responsiveHeaderFixed .responsiveHeader { height: 40px; }

.responsiveHeader .responsiveSubLine {
	float: left;
	width: 100%;
	height: 1px;
	padding: 0px;
	margin: 0px;
	background: #f1f1f1;
	position: absolute;
	left: 0px;
	top: 40px;
	z-index: 10;
}
.responsiveHeaderFixed .responsiveHeader .responsiveSubLine { display: none; }

.responsiveHeader .responsiveLogo {
    float: left;
    width: 125px;
    height: 36px;
    padding: 0px;
    margin: 1px 0px 1px 5px;
    z-index: 5;
    position: relative;
    opacity: 1;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .responsiveLogo { opacity: 0; margin-top: -36px; }

.responsiveHeader .menuText {
	float: left;
	width: 60px;
	height: 34px;
	line-height: 34px;
	padding: 0px;
	margin: 0px;
	color: #666;
	font-size: 14px;
	font-weight: normal;
	text-decoration: none;
	text-transform: uppercase;
	position: absolute;
	right: 45px;
	top: 1px;
	z-index: 10;
	opacity: 1;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .menuText { opacity: 0; margin-top: -38px; }

.responsiveHeader .responsiveMenu {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    right: 5px;
    top: 1px;
    z-index: 2500;
}
.responsiveHeader .responsiveMenu .openResponsiveMenu {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: relative;
}
.responsiveHeader .responsiveMenu .openResponsiveMenu:before {
    content: '';
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 10;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/openResponsiveMenu.**********.png) no-repeat center center;
    opacity: 1;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu.open .openResponsiveMenu:before { opacity: 0; left: -38px; }

.responsiveHeader .responsiveMenu .openResponsiveMenu:after {
    content: '';
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: -28px;
    top: 0px;
    z-index: 10;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/closeResponsiveMenu.**********.png) no-repeat center center;
    opacity: 0;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu.open .openResponsiveMenu:after { opacity: 1; left: 0px; }

.responsiveHeader .responsiveMenu .responsiveMenuSub {
    float: left;
    width: 0px;
    height: auto;
    padding: 0px;
    margin: 0px;
    background: #cc1153;
    overflow: hidden;
    position: absolute;
    right: -100%;
    top: 39px;
    z-index: 2500;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu.open .responsiveMenuSub { width: 400px !important; right: -5px; opacity: 1; visibility: visible; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
    border-bottom: 1px solid #990b3d;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li:active {
    -webkit-transition: all 0.1s linear; -moz-transition: all 0.1s linear; -o-transition: all 0.1s linear; transition: all 0.1s linear;
    background: #990b3d;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li.color { background: #444; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li a {
    float: left;
    width: 380px;
    height: auto;
    padding: 12px 10px 12px 10px;
    margin: 0px;
    color: #fff;
    font-size: 18px;
    line-height: 20px;
    text-align: left;
    font-weight: normal;
    text-decoration: none;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li.parent > a { background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveMenuPlus.**********.png) no-repeat right 3px; }
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li.parent.openSub > a { background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveMenuMinus.**********.png) no-repeat right 3px; }


.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul { display: none; background: #19b5dc; padding: 7px 0px 7px 0px; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li { border-bottom: none; }
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li:active { background: none; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li a {
    width: 360px;
    padding: 7px 20px 7px 20px;
    font-size: 13px;
    line-height: 16px;
    font-weight: 600;
    text-transform: none;
}

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li.parent > a { background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveMenuPlus.**********.png) no-repeat right -2px; }
.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li.parent.openSub > a { background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveMenuMinus.**********.png) no-repeat right -2px; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li.active > a { color: #ed1c24; }

.responsiveHeader .responsiveMenu .jspPane { width: 100% !important; }
.responsiveHeader .responsiveMenu .jspDrag { background: #8e8e8e !important; }

.responsiveHeader .responsiveMenu .responsiveMenuSub ul li a .number { color: #e3000f; }


.responsiveHeader .responsiveCompare {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveCompareIcon.**********.png) no-repeat center center;
    position: absolute;
    left: 42px;
    top: 42px;
    z-index: 10;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .responsiveCompare { top: 1px; }

.responsiveHeader .responsiveCompare .number {
	display: none;
	float: left;
	width: 16px;
	height: 16px;
	line-height: 16px;
	padding: 0px;
	margin: 0px;
	background: #cc1153;
	border-radius: 100%;
	font-size: 11px;
	color: #fff;
	text-decoration: none;
	text-align: center;
	font-weight: normal;
	display: block;
	position: absolute;
	right: 1px;
	top: 2px;
	z-index: 11;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.responsiveHeader .responsiveCall {
    float: left;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveCall.**********.png) no-repeat center center;
    position: absolute;
    left: 1px;
    top: 42px;
    z-index: 10;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .responsiveCall { top: 1px; }

.responsiveHeader .responsiveLogin {
	float: left;
	width: 38px;
	height: 38px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 122px;
	top: 42px;
	z-index: 10;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveLoginIcon.**********.png) no-repeat center center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .responsiveLogin { top: 1px; }

.responsiveHeader .responsiveLogin:before {
	display: none;
	content: '';
	width: 14px;
	height: 14px;
	padding: 0px;
	margin: 0px;
	border-radius: 100%;
	background: #70bb0f;
	position: absolute;
	right: 3px;
	top: 3px;
	z-index: 10;
}
.responsiveHeader .responsiveLogin.loggedIn:before { display: block; }

.responsiveHeader .responsiveSearchBox {
    float: right;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: 162px;
    top: 42px;
    z-index: 10;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .responsiveSearchBox { top: 1px; }

.responsiveHeader .responsiveSearchBox .responsiveSearchOpenLink {
	float: left;
	width: 38px;
	height: 38px;
	padding: 0px;
	margin: 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/searchIconResponsive.**********.png) no-repeat left top;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveSearchBox.opened .responsiveSearchOpenLink { height: 40px; margin-top: -1px; background: url(//localhost:8580/skin/frontend/stenik/default/images/searchIconResponsive.**********.png) no-repeat left bottom; }

.responsiveHeader .wishtlistItems {
    float: right;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveWishtlistIcon.**********.png) no-repeat center center;
    position: absolute;
    left: 82px;
    top: 42px;
    z-index: 10;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .wishtlistItems { top: 1px; }

.responsiveHeader .wishtlistItems .responsiveWishlistitems {
	display: none;
	float: left;
	width: 16px;
	height: 16px;
	line-height: 16px;
	padding: 0px;
	margin: 0px;
	background: #cc1153;
	border-radius: 100%;
	font-size: 11px;
	color: #fff;
	text-decoration: none;
	text-align: center;
	font-weight: normal;
	display: none;
	position: absolute;
	right: 1px;
	top: 2px;
	z-index: 11;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .wishtlistItems.hasItems .responsiveWishlistitems { display: block; }

.responsiveHeader .responsiveCart {
    float: right;
    width: 38px;
    height: 38px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/responsiveCart.**********.png) no-repeat center center;
    position: absolute;
    left: 202px;
    top: 42px;
    z-index: 10;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeaderFixed .responsiveHeader .responsiveCart { top: 1px; }

.responsiveHeader .responsiveCart .responsiveCartitems {
    float: left;
    width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 0px;
    margin: 0px;
    background: #cc1153;
    border-radius: 100%;
    font-size: 11px;
    color: #fff;
    text-decoration: none;
    text-align: center;
    font-weight: normal;
    display: none;
    position: absolute;
    right: 1px;
    top: 2px;
    z-index: 11;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveHeader .responsiveCart.hasItems .responsiveCartitems { display: block; }

.responsiveSearchBox .headerSearch {
	float: left;
	width: 100%;
	height: auto;
	padding: 20px 20px 20px 20px;
	margin: 0px;
	position: fixed;
	left: 0px;
	top: 42px;
	z-index: 700;
	display: none;
	background: rgba(0, 0, 0, 0.8);
	box-sizing: border-box;
}
.responsiveSearchBox .headerSearch .searchForm {
	float: left;
	width: 100%;
	height: 50px;
	padding: 0px;
	margin: 10px 0px 20px 0px;
	position: relative;
}

.responsiveSearchBox .headerSearch #responsive_search_mini_form input.searchInput {
	float: left;
	width: 100%;
	height: 40px;
	padding: 0px 38px 0px 20px;
	margin: 0px;
	color: #000;
	font-size: 18px;
	font-weight: normal;
	text-decoration: none;
	background: #fff;
	border: 1px solid #d9dbda;
	box-sizing: border-box;
	position: relative;
	z-index: 15;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveSearchBox .headerSearch #responsive_search_mini_form input.searchInput:hover { border-color: #777; }
.responsiveSearchBox .headerSearch #responsive_search_mini_form input.searchInput:focus { border-color: #777; background: #ebebeb; }

.responsiveSearchBox .headerSearch #responsive_search_mini_form .searchSubmit {
	float: left;
	width: 38px;
	height: 38px;
	padding: 0px 0px 0px 0px;
	margin: 0px;
	border: none;
	border-left: 1px solid #d9dbda;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/searchSubmitIcon.**********.png) no-repeat center center;
	position: absolute;
	right: 21px;
	top: 21px;
	z-index: 20;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveSearchBox .headerSearch #responsive_search_mini_form .searchSubmit:hover { background-color: #ebebeb; }
*/
/* end responsive header */





















#main {
	float: left;
	width: 100%;
	height: auto;
	padding: 18px 0px 20px 0px;
	margin: 0px 0px 0px 0px;
	position: relative;
	z-index: 200;
	background: #fff;
}
#main .mainContent {
	float: left;
	width: 100%;
	height: auto;
	min-height: 150px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
}
#main.col2-left-layout .mainContent { float: right; width: 880px; padding: 0px 0px 0px 20px; background: #fff; }

#main.col2-left-layout .col2MainWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/leftColBg.**********.png) repeat-y left top;
}

#main h1 {
	float: left;
    width: 100%;
	height: auto;
	margin: 0px 0px 30px 0px;
	padding: 0px;
	color: #000;
	font-size: 45px;
	line-height: 52px;
	font-weight: 300;
	text-align: left;
	text-decoration: none;
}
.catalog-category-view #main h1 {
	font-size: 35px;
	line-height: 35px;
	text-align: center;
}
.checkout-cart-index #main h1 {
	font-size: 35px;
	line-height: 35px;
	text-align: left;
}
#main .homepageMainBannerSlider {
	float: left;
	width: 880px;
	height: 495px;
	padding: 0px;
	margin: 7px 0px 20px 0px;
	position: relative;
}
#main .homepageMainBannerSlider .item {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
	text-align: center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageMainBannerSlider .item .sliderIMGLink {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
	z-index: 10;
}
#main .homepageMainBannerSlider .item .sliderInfoBox {
	float: left;
	width: 400px;
	height: 400px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	position: absolute;
	top: 50px;
	left: 80px;
	z-index: 12;
}
#main .homepageMainBannerSlider .item .sliderInfoBox .sliderTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 25px 0px;
	font-size: 60px;
	line-height: 52px;
	color: #fff;
	text-align: left;
	font-weight: 300;
	text-decoration: none;
	opacity: 0;
	-webkit-transform: translateX(35px);
	-ms-transform: translateX(35px);
	-o-transform: translateX(35px);
	transform: translateX(35px);
	-webkit-transition: all 0.2s ease-out; -moz-transition: all 0.2s ease-out; -o-transition: all 0.2s ease-out; transition: all 0.2s ease-out;
}
#main .homepageMainBannerSlider .item .sliderInfoBox .sliderTitle:hover {
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageMainBannerSlider .active .item .sliderInfoBox .sliderTitle {
	opacity: 1;
	-webkit-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1);
	-webkit-transition-delay: 0.7s; transition-delay: 0.7s;
}
#main .homepageMainBannerSlider .item .sliderInfoBox .slidersubTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 31px 0px;
	font-size: 16px;
	line-height: 22px;
	color: #fff;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	opacity: 0;
	-webkit-transform: translateX(-35px);
	-ms-transform: translateX(-35px);
	-o-transform: translateX(-35px);
	transform: translateX(-35px);
	-webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out;
}
#main .homepageMainBannerSlider .active .item .sliderInfoBox .slidersubTitle {
	opacity: 1;
	-webkit-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1);
	-webkit-transition-delay: 0.9s; transition-delay: 0.9s;
}
#main .homepageMainBannerSlider .item .sliderInfoBox .buyBtn {
	float: left;
	width: auto;
	height: 35px;
	line-height: 35px;
	padding: 0px 14px 0px 14px;
	margin: 0px;
	border: 2px solid #fff;
	font-size: 15px;
	color: #fff;
	text-align: center;
	font-weight: 500;
	text-decoration: none;
	text-transform: uppercase;
	opacity: 0;
	-webkit-transform: translateY(-35px);
	-ms-transform: translateY(-35px);
	-o-transform: translateY(-35px);
	transform: translateY(-35px);
	-webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out;
}
#main .homepageMainBannerSlider .active .item .sliderInfoBox .buyBtn {
	opacity: 1;
	-webkit-transform: scale(1); -ms-transform: scale(1); -o-transform: scale(1); transform: scale(1);
	-webkit-transition-delay: 1.1s; transition-delay: 1.1s;
}
#main .homepageMainBannerSlider .active .item .sliderInfoBox .buyBtn:hover { background: #fff; color: #cc1155; -webkit-transition-delay: 0s; transition-delay: 0s; }

#main .homepageMainBannerSlider .owl-controls {
	float: left;
	width: 800px;
	height: 1px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	text-align: center;
	position: absolute;
	left: 40px;
	bottom: 36px;
	z-index: 110;
}
#main .homepageMainBannerSlider .owl-controls .owl-page {
	display: inline-block;
	width: 20px;
	height: 8px;
	padding: 0px;
	margin: 0px 4px 0px 4px;
	background: #fff;
	border: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageMainBannerSlider .owl-controls .owl-page:hover { background: #777; }
#main .homepageMainBannerSlider .owl-controls .owl-page.active { background: #cc1153; }
#main .homepageMainBannerSlider .owl-controls .owl-page.active:hover { background: #e4135d; }

#main .homepageMainBannerSlider .owl-controls .owl-prev {
	float: left;
	width: 60px;
	height: 60px;
	padding: 0px;
	margin: 0px;
	left: -40px;
	top: -240px;
	position: absolute;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/mainSliderNavLeftArrow.**********.png) no-repeat center center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageMainBannerSlider .owl-controls .owl-prev:hover { background-color: #000; }

#main .homepageMainBannerSlider .owl-controls .owl-next {
	float: left;
	width: 60px;
	height: 60px;
	padding: 0px;
	margin: 0px;
	right: -40px;
	top: -240px;
	position: absolute;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/mainSliderNavRightArrow.**********.png) no-repeat center center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageMainBannerSlider .owl-controls .owl-next:hover { background-color: #000; }











.mainTopStaticBanners {
    float: left;
    width: 100%;
    height: auto;
    padding: 0;
    margin: 12px 0px 14px 0px;
    display: none;
}

.mainTopStaticBanners a {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 0px 0px;
}

.mainTopStaticBanners a img {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 0px 0px;
}

.mainTopStaticBanners img {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 0px 0px;
}









#main .homepageBannerLeftToSlider {
	float: left;
	width: 280px;
	height: 495px;
	padding: 0px;
	margin: 7px 20px 20px 0px;
}
#main .homepageBannerLeftToSlider p { padding: 0px; margin: 0px; }


#main .homepageBannersContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 40px 0px;
}
#main .homepageBannersContent.paralax { margin: 0px; }
#main .homepageBannersContent.paralax p { display: none; }
#main .homepageBannersContent.wideContent { width: 2000px; left: 50%; position: relative; margin: 0px 0px 60px -1000px; padding: 75px 0px 60px 0px; background: #f6f6f6; }

#main .homepageBannersContent .textPage { width: 1200px; }
#main .homepageBannersContent p { padding: 0px; margin: 0px; }

#main .homepageInfoContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 40px 0px;
}
#main .homepageInfoContent .textPage h1 { font-size: 35px; line-height: 40px; }


#main .widgetBannerBox {
	float: left;
	width: 280px;
	height: 495px;
	padding: 115px 0px 0px 0px;
	margin: 0px 20px 0px 0px;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	cursor: pointer;
}
#main .widgetBannerBox img {
	float: left;
	width: 280px;
	height: 495px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	top: 0px;
	z-index: 5;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .widgetBannerBox:hover img { opacity: 0.9; }

#main .widgetBannerBox .titleWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 4px 0px;
	text-align: center;
	position: relative;
	z-index: 6;
}
#main .widgetBannerBox .titleWrapper span.title {
	display: inline-block;
	width: auto;
	height: auto;
	padding: 0px 40px 0px 40px;
	margin: 0px;
	color: #fff;
	font-size: 35px;
	line-height: 38px;
	font-weight: 300;
	text-decoration: none;
	position: relative;
}
#main .widgetBannerBox .titleWrapper span.title:before {
	content: '';
	width: 22px;
	height: 2px;
	padding: 0px;
	margin: 0px;
	background: #fff;
	position: absolute;
	left: 4px;
	top: 22px;
	z-index: 8;
}
#main .widgetBannerBox .titleWrapper span.title:after {
	content: '';
	width: 22px;
	height: 2px;
	padding: 0px;
	margin: 0px;
	background: #fff;
	position: absolute;
	right: 6px;
	top: 22px;
	z-index: 8;
}
#main .widgetBannerBox .subTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 20px 0px 20px;
	margin: 0px 0px 27px 0px;
	color: #fff;
	font-size: 20px;
	line-height: 22px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	z-index: 8;
}
#main .widgetBannerBox .line {
	float: left;
	width: 230px;
	height: 1px;
	padding: 0px;
	margin: 0px 0px 22px -115px;
	background: #c1929c;
	position: relative;
	left: 50%;
	z-index: 8;
}
#main .widgetBannerBox .widgetDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 20px 0px 20px;
	margin: 0px 0px 55px 0px;
	color: #fff;
	font-size: 18px;
	line-height: 22px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	z-index: 8;
}
#main .widgetBannerBox .buyBtn {
	float: left;
	width: auto;
	min-width: 90px;
	height: 35px;
	line-height: 35px;
	padding: 0px 10px 0px 10px;
	margin: 0px 0px 0px -55px;
	border: 2px solid #fff;
	color: #fff;
	font-size: 15px;
	font-weight: 500;
	text-transform: uppercase;
	text-align: center;
	left: 50%;
	position: relative;
	z-index: 8;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .widgetBannerBox .buyBtn:hover { background: #fff; color: #cc1155; }

#main .categoryWidgetBox {
	float: left;
	width: 280px;
	height: 280px;
	padding: 0px 0px 0px 0px;
	margin: 0px 20px 20px 0px;
	overflow: hidden;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	cursor: pointer;
}
#main .categoryWidgetBox img.banner {
	float: left;
	width: 280px;
	height: 280px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	top: 0px;
	z-index: 5;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .categoryWidgetBox:hover img.banner { opacity: 0.9; }

#main .categoryWidgetBox img.icon {
	float: left;
	width: 120px;
	height: 120px;
	padding: 0px;
	margin: 32px 0px 0px -60px;
	position: relative;
	left: 50%;
	top: auto;
	z-index: 8;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .categoryWidgetBox:hover img.icon { margin-top: 22px; opacity: 0.9; }

#main .categoryWidgetBox .line {
	float: left;
	width: 190px;
	height: 1px;
	padding: 0px;
	margin: 0px 0px 22px 46px;
	background: #5f5f5f;
	position: relative;
	clear: both;
	z-index: 9;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .categoryWidgetBox:hover .line { margin-bottom: 38px; background: #cc1153; }

#main .categoryWidgetBox span.title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 54px 0px 54px;
	margin: 0px;
	color: #fff;
	font-size: 25px;
	line-height: 26px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	z-index: 8;
}

.faq-box {
	margin-top: -1px;
	border-top: 1px solid #E9E9E9;
	border-bottom: 1px solid #E9E9E9;
}
.faq-box .faq-question {
	display: block;
	margin: 0;
	padding: 16px 50px 16px 20px;
	font-size: 18px;
	font-weight: 700;
	line-height: 22px;
	color: #000;
	cursor: pointer;
	position: relative;
}
.faq-box .faq-question:before {
	content: "";
	width: 14px;
	height: 2px;
	position: absolute;
	right: 20px;
	top:  50%;
	margin-top: -1px;
	background: #000;
}
.faq-box .faq-question:after {
	content: "";
	width: 2px;
	height: 14px;
	position: absolute;
	right: 26px;
	top:  50%;
	margin-top: -7px;
	background: #000;
}
.faq-box.active .faq-question:after {
	display: none;
}
.faq-box .faq-content {
	display: none;
	padding: 0 20px 30px;
	font-weight: 400;
	font-size: 18px;
	line-height: 22px;
	color: #707070;
}

#main .servicesWidgetBox {
	float: left;
	width: 220px;
	height: 255px;
	padding: 0px 0px 0px 0px;
	margin: 0px 40px 20px 40px;
	overflow: hidden;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	cursor: pointer;
}
#main .servicesWidgetBox .iconWrapper {
	float: left;
	width: 138px;
	height: 138px;
	padding: 0px;
	margin: 0px 40px 25px 40px;
	background: #fff;
	border: 1px solid #b9b9b9;
	border-radius: 100%;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .servicesWidgetBox:hover .iconWrapper { border-color: #e21a61; }

#main .servicesWidgetBox .iconWrapper img {
	float: left;
	width: 80px;
	height: 80px;
	padding: 0px;
	margin: -40px 0px 0px -40px;
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 8;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .servicesWidgetBox span.title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	color: #000;
	font-size: 20px;
	line-height: 22px;
	font-weight: normal;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .servicesWidgetBox:hover span.title { margin: -8px 0px 15px 0px; color: #e21a61; }

#main .servicesWidgetBox span.subTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	color: #707070;
	font-size:16px;
	line-height: 22px;
	font-weight: normal;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
}


#main .featureProductsListing {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 90px 0px;
}
#main .featureProductsListing.upSell {margin: 0px 0px 60px 0px; }

#main .featureProductsListing .title {
	float: left;
    width: 990px;
	height: auto;
	margin: 0px 0px 55px 10px;
	padding: 0px;
	font-size: 50px;
	line-height: 55px;
	color: #000;
	text-decoration: none;
	font-weight: 300;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .featureProductsListing .title:hover { color: #666; }

#main .featureProductsListing.upSell .title { width: 100%; font-size: 35px; line-height: 38px; margin: 0px 0px 40px 0px; }

#main .featureProductsListing .viewAllBtn {
	float: right;
	width: auto;
	min-width: 110px;
	height: 37px;
	line-height: 37px;
	padding: 0px 20px 0px 20px;
	margin: 10px 0px 0px 0px;
	color: #000;
	font-size: 15px;
	font-weight: 500;
	text-align: center;
	text-decoration: none;
	text-transform: uppercase;
	background: #fff;
	border: 1px solid #000;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .featureProductsListing .viewAllBtn:hover { background: #000; color: #fff; }

#main .productListingSlider {
	float: left;
	width: 1192px;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
}
#main .productListingSlider .sliderItem {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}










#main .productListingSlider .owl-controls {
	float: left;
	width: 100%;
	height: 1px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	text-align: center;
	position: relative;
	left: 0px;
	bottom: 5px;
	z-index: 110;
}

#main .productListingSlider .owl-controls .owl-pagination { display: none; }

#main .productListingSlider .owl-controls .owl-page {
	display: inline-block;
	width: 20px;
	height: 8px;
	padding: 0px;
	margin: 0px 4px 0px 4px;
	background: #000;
	border: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .productListingSlider .owl-controls .owl-page:hover { background: #777; }
#main .productListingSlider .owl-controls .owl-page.active { background: #cc1153; }
#main .productListingSlider .owl-controls .owl-page.active:hover { background: #e4135d; }











#main .productListingSlider .owl-controls .owl-prev {
	float: left;
	width: 40px;
	height: 40px;
	padding: 0px;
	margin: 0px;
	left: -31px;
	top: -240px;
	position: absolute;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/productSliderLeftArrow.**********.png) no-repeat center center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .productListingSlider .owl-controls .owl-prev:hover { background-color: #e8e8e8; }

#main .productListingSlider .owl-controls .owl-next {
	float: left;
	width: 40px;
	height: 40px;
	padding: 0px;
	margin: 0px;
	right: -31px;
	top: -240px;
	position: absolute;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/productSliderRightArrow.**********.png) no-repeat center center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .productListingSlider .owl-controls .owl-next:hover { background-color: #e8e8e8; }



#main .parallaxWidgetBox {
	width: 2000px;
	height: 500px;
	margin: 0px 0px 0px -1000px;
	position: relative;
	left: 50%;
	overflow: hidden;
}

#main .parallaxWidgetBox .overlay { display: none; }


#main .parallaxWidgetBox .parallax { position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: -1; }
#main .parallaxWidgetBox .parallax img { position: absolute; left: 50%; bottom: 0; min-width: 100%; min-height: 100%; }

#main .parallaxWidgetBox .bannerIMGLink {
	float: left;
	width: 100%;
	height: 500px;
	padding: 0px;
	margin: 0px;
}
#main .parallaxWidgetBox .parallaxInfoContent {
	float: left;
	width: 420px;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: absolute;
	right: 10px;
	top: 65px;
	z-index: 10;
}
#main .parallaxWidgetBox .paralaxBannerTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 30px 0px;
	color: #fff;
	font-size: 55px;
	line-height: 60px;
	font-weight: 300;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .parallaxWidgetBox .paralaxBannerTitle:hover { opacity: 0.7; }

#main .parallaxWidgetBox .paralaxDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 45px 0px;
	color: #fff;
	font-size: 18px;
	line-height: 22px;
	font-weight: 300;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

#main .parallaxWidgetBox .viewMoreBtn {
	float: left;
	width: auto;
	min-width: 125px;
	height: 35px;
	line-height: 35px;
	padding: 0px 20px 0px 20px;
	margin: 0px 0px 0px 0px;
	border: 2px solid #fff;
	color: #fff;
	font-size: 15px;
	font-weight: 500;
	text-decoration: none;
	text-transform: uppercase;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .parallaxWidgetBox .viewMoreBtn:hover { border-color: #cc1152; background: #000; }


#main .homepageBrandsContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 40px 0px;
}
#main .homepageBrandsContent .homepageBrandsTitle {
	float: left;
    width: 990px;
	height: auto;
	margin: 0px 0px 55px 10px;
	padding: 0px;
	font-size: 55px;
	line-height: 60px;
	color: #000;
	text-decoration: none;
	font-weight: 300;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageBrandsContent .homepageBrandsTitle:hover { color: #666; }

#main .homepageBrandsContent .allBrandsBtn {
	float: right;
	width: auto;
	min-width: 110px;
	height: 37px;
	line-height: 37px;
	padding: 0px 20px 0px 20px;
	margin: 10px 0px 0px 0px;
	color: #000;
	font-size: 15px;
	font-weight: 500;
	text-align: center;
	text-decoration: none;
	text-transform: uppercase;
	background: #fff;
	border: 1px solid #000;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .homepageBrandsContent .allBrandsBtn:hover { background: #000; color: #fff; }

#main .homepageBrandsContent .homepageBrandsListing {
	float: left;
	width: 1182px;
	height: auto;
	padding: 0px;
	margin: 0px;
}

.brandsListingWrapper {
	float: left;
	width: 1182px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 40px 0px;
}

.brandsListingWrapper .brandsDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 20px 0px;
}

#main .brandBox {
	float: left;
	width: 196px;
	height: 80px;
	padding: 0px;
	margin: 0px 1px 1px 0px;
	background: #f6f6f6;
	overflow: hidden;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .brandBox:hover { background: #fff; }

#main .brandBox img {
	float: left;
	width: 196px;
	height: auto;
	padding: 0px;
	margin: 0px;
}


#main nav.breadcrumbs {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	position: relative;
}
#main nav.breadcrumbs ul {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
}
#main nav.breadcrumbs ul li {
	float: left;
	width: auto;
	height: 16px;
	line-height: 16px;
	padding: 0px;
	margin: 0px;
	font-size: 14px;
	color: #000;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main nav.breadcrumbs ul li a {
	float: left;
	width: auto;
	height: 16px;
	line-height: 16px;
	padding: 0px 15px 0px 0px;
	margin: 0px 5px 0px 0px;
	font-size: 14px;
	color: #707070;
	font-weight: normal;
	text-decoration: none;
	position: relative;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/breadcrumbsArrow.**********.png) no-repeat right 4px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main nav.breadcrumbs ul li a:hover { color: #cc1153; }




.filtersContentWrapper.sticky {
	top: 0;
	position: fixed;
	z-index: 1000;
	width: 1180px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.filterContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filtersContentWrapper.sticky .filterContent { margin-bottom: 0px; }

.filterContent .responsiveOpenFilters {
	display: none;
	float: left;
	width: 300px;
	height: auto;
	padding: 10px 15px 10px 0px;
	margin: 0px 150px 0px 150px;
	background: #f8f8f8;
	box-sizing: border-box;
	border: 1px solid #d9dbda;
	font-size: 14px;
	color: #000;
	font-weight: bold;
	text-align: center;
	text-decoration: none;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/filterIcon.**********.png) no-repeat 200px 17px;
}
.filterContent.filtersAreOpened .responsiveOpenFilters { background-color: #eae9e9; }

.filterContent .filtersWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 5px 0px 5px;
	margin: 0px 0px 0px 0px;
	background: #f8f8f8;
	box-sizing: border-box;
	border: 1px solid #d9dbda;
}
.filterContent .filtersWrapper .block-layered-nav {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.filterContent .dropDownfilter {
	float: left;
	width: auto;
	height: 48px;
	padding: 0px;
	margin: 0px 4px 0px 4px;
	background: #fff;
	position: relative;
	outline: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .dropDownfilter.sort { float: right; margin-left: 0px; }

.filterContent .dropDownfilter .openFilters {
	float: left;
	width: auto;
	height: 48px;
	line-height: 48px;
	padding: 0px 24px 0px 10px;
	margin: 0px;
	color: #000;
	font-size: 14px;
	font-weight: bold;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	background: #f8f8f8;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .dropDownfilter:hover .openFilters { background: #d9d9d9; }
.filterContent .dropDownfilter.open .openFilters { background: #000; color: #fff; }

.filterContent .dropDownfilter.sort .openFilters { width: 40px; padding: 0px; background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/openSorterIcon2.**********.png) no-repeat 9px 16px; }
.filterContent .dropDownfilter.open.sort .openFilters { background: #000 url(//localhost:8580/skin/frontend/stenik/default/images/openSorterIcon2.**********.png) no-repeat -26px 16px; }

.filterContent .dropDownfilter .openFilters:before {
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 4px 4px 0px 4px;
	border-color: #000 transparent transparent transparent;
	position: absolute;
	right: 10px;
	top: 24px;
	z-index: 10;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .dropDownfilter.sort .openFilters:before { display: none; }
.filterContent .dropDownfilter.open .openFilters:before { border-color: transparent transparent #fff transparent; border-width: 0px 4px 4px 4px; }

.filterContent .dropDownfilter ul.subOptions {
	float: left;
	width: 230px;
	height: auto;
	max-height: 352px;
	overflow-y: auto;
	padding: 10px 0px 10px 0px;
	margin: 0px;
	background: #000;
    outline: none;
	position: absolute;
	left: 0px;
	top: 48px;
	z-index: 300;
    display: none;
}
.filterContent .dropDownfilter.sort ul.subOptions { right: 0px; left: auto; }

.filterContent .dropDownfilter ul.subOptions li {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	outline: none;
	border: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .dropDownfilter ul.subOptions li a {
	float: left;
	width: 100%;
	height: auto;
	margin: 0px;
	padding: 6px 10px 8px 30px;
	box-sizing: border-box;
	border: none;
	font-size: 14px;
	line-height: 16px;
	color: #fff;
	font-weight: 300;
	text-decoration: none;
	outline: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .dropDownfilter ul.subOptions li a:hover { background: #333; }

.filterContent .dropDownfilter ul.subOptions li a:before {
	content: '';
	width: 10px;
	height: 10px;
	padding: 0px;
	margin: 0px;
	background: #000;
	border: 1px solid #fff;
	position: absolute;
	left: 10px;
	top: 9px;
	z-index: 5;
}
.filterContent .dropDownfilter ul.subOptions li a.selected:before { background:url(//localhost:8580/skin/frontend/stenik/default/images/dropDownfilterSelectedBG.**********.jpg) no-repeat center center; }

.filterContent .dropDownfilter ul.subOptions li input.checkbox-filter { display: none; }

.filterContent .dropDownfilter .layer-slider.subOptions {
	float: left;
	width: 230px;
	height: auto;
	max-height: 352px;
	overflow-y: auto;
	padding: 10px 10px 10px 10px;
	margin: 0px;
	background: #000;
	box-sizing: border-box;
    outline: none;
	position: absolute;
	left: 0px;
	top: 48px;
	z-index: 300;
	display: none;
}
.filterContent .dropDownfilter .layer-slider .price-limit { display: none; }
.filterContent .dropDownfilter .layer-slider .bg { pointer-events: none; }
.filterContent .dropDownfilter .layer-slider .span { background: #cc1153; pointer-events: none; }
.filterContent .dropDownfilter .layer-slider .handle { background: #fff; }
.filterContent .dropDownfilter .layer-slider .left { float: left; }
.filterContent .dropDownfilter .layer-slider .right { float: right; }

.filterContent .dropDownfilter .layer-slider .price {
	color: #a5a5a5;
	font-size: 16px;
	line-height: 18px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
}
.filterContent .dropDownfilter .layer-slider .priceSliderInputs {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 10px 0px 0px 0px;
}
.filterContent .dropDownfilter .layer-slider .priceSliderInputs input.input-text {
	width: 100px;
	height: 28px;
	margin: 0px;
	font-size: 14px;
	background: #ececec;
}
.filterContent .dropDownfilter .layer-slider .filterButton {
	float: left;
	width: 100%;
	height: 28px;
	line-height: 28px;
	padding: 0px 15px 0px 15px;
	margin: 10px 0px 0px 0px;
	box-sizing: border-box;
	color: #fff;
	font-size: 12px;
	font-weight: 500;
	text-align: center;
	text-decoration: none;
	text-transform: uppercase;
	background: #373737;
	border: 1px solid #676767;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .dropDownfilter .layer-slider .filterButton:hover { background: #676767; }



.filterContent .choosenFilterBox {
	float: left;
	width: 100%;
	height: auto;
	padding: 20px 0px 20px 0px;
	margin: 0px;
	background: #fff;
	border-bottom: 1px solid #d9dbda;
}
.filterContent .choosenFilterBox .removeAllFilters {
	float: left;
	width: auto;
	height: 25px;
	line-height: 25px;
	padding: 0px 10px 2px 10px;
	margin: 0px 10px 0px 0px;
	border: 1px solid #000;
	color: #000;
	font-size: 12px;
	font-weight: 500;
	text-decoration: none;
	text-transform: uppercase;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .choosenFilterBox .removeAllFilters:hover { background: #000; color: #fff; }

.filterContent .choosenFilterBox .choosenFilter {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.filterContent .choosenFilterBox .choosenFilter .filterLabel {
	float: left;
	width: auto;
	height: 27px;
	line-height: 27px;
	padding: 0px 0px 2px 0px;
	margin: 0px 5px 0px 0px;
	color: #000;
	font-size: 14px;
	font-weight: normal;
	text-decoration: none;
}
.filterContent .choosenFilterBox .choosenFilter .filter {
	float: left;
	width: auto;
	height: 27px;
	line-height: 27px;
	padding: 0px 30px 2px 10px;
	margin: 0px 5px 0px 0px;
	background: #f8f8f8;
	color: #666;
	font-size: 14px;
	font-weight: normal;
	text-decoration: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.filterContent .choosenFilterBox .choosenFilter .filter:hover { background: #000; color: #fff; }

.filterContent .choosenFilterBox .choosenFilter .filter:before {
	content: '';
	width: 12px;
	height: 12px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	right: 9px;
	top: 10px;
	z-index: 10;
	background:url(//localhost:8580/skin/frontend/stenik/default/images/filterRemoveIcon.**********.png) no-repeat left top;
}





















.toolbarBox {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 20px 0px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .toolbarRightPosition {
	float: right;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.toolbarBox .dropDownfilter {
	float: left;
	width: auto;
	height: 35px;
	padding: 0px;
	margin: 0px 4px 0px 4px;
	background: #fff;
	position: relative;
	outline: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .dropDownfilter.sort { float: right; margin-left: 0px; }

.toolbarBox .dropDownfilter .openFilters {
	float: left;
	width: auto;
	height: 35px;
	line-height: 35px;
	padding: 0px 24px 0px 10px;
	margin: 0px;
	color: #000;
	font-size: 14px;
	font-weight: normal;
	text-decoration: none;
	box-sizing: border-box;
	position: relative;
	background: #f8f8f8;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .dropDownfilter:hover .openFilters { background: #d9d9d9; }
.toolbarBox .dropDownfilter.open .openFilters { background: #000; color: #fff; }

.toolbarBox .dropDownfilter.sort .openFilters { width: 40px; padding: 0px; background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/openSorterIcon2.**********.png) no-repeat 9px 16px; }
.toolbarBox .dropDownfilter.open.sort .openFilters { background: #000 url(//localhost:8580/skin/frontend/stenik/default/images/openSorterIcon2.**********.png) no-repeat -26px 16px; }


.toolbarBox .dropDownfilter .openFilters:before {
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 4px 4px 0px 4px;
	border-color: #000 transparent transparent transparent;
	position: absolute;
	right: 8px;
	top: 17px;
	z-index: 10;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .dropDownfilter.sort .openFilters:before { display: none; }
.toolbarBox .dropDownfilter.open .openFilters:before { border-color: transparent transparent #fff transparent; border-width: 0px 4px 4px 4px; }

.toolbarBox .dropDownfilter ul.subOptions {
	float: left;
	width: 230px;
	height: auto;
	max-height: 352px;
	overflow-y: auto;
	padding: 10px 0px 10px 0px;
	margin: 0px;
	background: #000;
    outline: none;
	position: absolute;
	left: 0px;
	top: 35px;
	z-index: 300;
    display: none;
}
.toolbarBox .dropDownfilter.showBy ul.subOptions { width: 122px; }
.toolbarBox .dropDownfilter.sortBy ul.subOptions { width: 180px; }

.toolbarBox .dropDownfilter ul.subOptions li {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	outline: none;
	border: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .dropDownfilter ul.subOptions li a {
	float: left;
	width: 100%;
	height: auto;
	margin: 0px;
	padding: 6px 10px 8px 30px;
	box-sizing: border-box;
	border: none;
	font-size: 14px;
	line-height: 16px;
	color: #fff;
	font-weight: 300;
	text-decoration: none;
	outline: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .dropDownfilter ul.subOptions li a:hover { background: #333; }

.toolbarBox .dropDownfilter ul.subOptions li a:before {
	content: '';
	width: 10px;
	height: 10px;
	padding: 0px;
	margin: 0px;
	background: #000;
	border: 1px solid #fff;
	position: absolute;
	left: 10px;
	top: 9px;
	z-index: 5;
}
.toolbarBox .dropDownfilter ul.subOptions li a.selected:before { background:url(//localhost:8580/skin/frontend/stenik/default/images/dropDownfilterSelectedBG.**********.jpg) no-repeat center center; }

.toolbarBox .dropDownfilter ul.subOptions li input.checkbox-filter { display: none; }

.toolbarBox .arrowUp {
	float: left;
	width: 35px;
	height: 35px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/toolbarArrowUp2.**********.png) no-repeat center top;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .arrowUp:hover { background-color: #000; background-position: center bottom; }

.toolbarBox .arrowDown {
	float: left;
	width: 35px;
	height: 35px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/toolbarArrowDown2.**********.png) no-repeat center top;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.toolbarBox .arrowDown:hover { background-color: #000; background-position: center bottom; }








.pagingBox {
	float: left;
	width: 100%;
	height: 49px;
	padding: 0px;
	margin: 0px 0px 40px 0px;
}
.pagingBox .paging {
	float: left;
	width: 100%;
	height: 45px;
	padding: 0px;
	margin: 0px;
	text-align: center;
}
.pagingBox .paging .pagingItems {
	float: left;
	width: 678px;
	height: 49px;
	background: #f8f8f8;
	text-align: center;
	border-bottom: 1px solid #e4e4e4;
}
.pagingBox .paging a,
.pagingBox .paging span {
	display: inline-block;
	width: 60px;
	height: 49px;
	line-height: 49px;
	padding: 0px;
	margin: 0px;
	background: #f8f8f8;
	color: #666;
	font-size: 30px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.pagingBox .paging .pagingItems a:hover,
.pagingBox .paging .pagingItems span:hover { background-color: #555; color: #fff; }
.pagingBox .paging .pagingItems a.selected,
.pagingBox .paging .pagingItems span.selected { background-color: #cc1156; color: #fff; }



.pagingBox .paging a.prev {
	float: left;
	width: 248px;
	height: 50px;
	line-height: 49px;
	padding: 0px 0px 0px 24px;
	margin: 0px 3px 0px 0px;
	border-bottom: 1px solid #e4e4e4;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/pagingPrev.**********.png) no-repeat 24px 18px;
	color: #666;
	font-size: 17px;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.pagingBox .paging a.prev:hover { background-color: #000; color: #fff; }
.pagingBox .paging a.prev.disable { opacity: 0.4; background-color: #f8f8f8 !important; color: #666 !important; }

.pagingBox .paging a.next {
	float: right;
	width: 248px;
	height: 50px;
	line-height: 49px;
	padding: 0px 24px 0px 0px;
	margin: 0px;
	border-bottom: 1px solid #e4e4e4;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/pagingNext.**********.png) no-repeat 204px 18px;
	color: #666;
	font-size: 17px;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.pagingBox .paging a.next:hover { background-color: #000; color: #fff; }
.pagingBox .paging a.next.disable { opacity: 0.4; background-color: #f8f8f8 !important; color: #666 !important; }



.categoryDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 30px 0px;
	background: #fff;
}

.brandDescriptionWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 40px 0px;
}

.brandDescriptionWrapper .brandDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	background: #fff;
	border: none;
}

.brandDescriptionWrapper .brandImage {
	float: right;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px 0px 20px 20px;
	background: #fff;
	border: none;
}

.brandDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 20px 20px 50px 20px;
	margin: 0px 0px 0px 0px;
	background: #fff;
	box-sizing: border-box;
	border: 1px solid #dfdede;
}
.brandDescription .brandLogoIMg {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px 35px 0px 0px;
}
.brandDescription .brandInfoContent {
	float: left;
	width: 525px;
	height: auto;
	padding: 0px;
	margin: 0px;
}


#main .productListing {
	float: left;
	width: 1192px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 50px 0px;
	position: relative;
}
#main.col2-left-layout .productListing {  }


.productBox	{
	float: left;
	width: 280px;
	height: 388px;
	padding: 0px;
	margin: 5px 9px 20px 9px;
	position: relative;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox:hover { box-shadow: 0px 0px 6px 0px #ddd; }

.productBox .productIMGLink {
	float: left;
	width: 280px;
	height: 280px;
	padding: 0px;
	margin: 0px 0px 15px 0px;
	position: relative;
	text-decoration: none;
	z-index: 5;
}
.onsale-category-container-grid { float: left; width: 100%; height: 100%; }
.productBox .productIMGLink img {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox:hover .productIMGLink img { opacity: 0.8; }

.productBox .label {
	float: left;
	width: auto;
	height: 20px;
	line-height: 20px;
	padding: 0px 6px 1px 6px;
	margin: 0px;
	color: #fff;
	font-size: 14px;
	font-weight: 300;
	text-decoration: none;
	text-transform: uppercase;
	background: #cc1153;
	position: absolute;
	right: 0px;
	top: 0px;
	z-index: 22;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox .label.new { right: auto; left: 0px; background: #000; }

.productBox .label:before {
	content: '';
	width: 0;
	height: 0;
	position: absolute;
	right: 0px;
	bottom: -7px;
	z-index: 23;
	border-style: solid;
	border-width: 0px 7px 7px 0px;
	border-color: transparent #cc1152 transparent transparent;
}
.productBox .label.new:before { left: 0px; right: auto; border-width: 7px 7px 0px 0px; border-color: #000 transparent transparent transparent; }

.productBox .productTitle {
	float: left;
	width: 100%;
	height: 64px;
	overflow: hidden;
	padding: 0px 8px 0px 8px;
	margin: 0px 0px 0px 0px;
	color: #707070;
	font-size: 18px;
	line-height: 21px;
	font-weight: 300;
	text-align: left;
	text-decoration: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox:hover .productTitle { color: #000; }

.productBox .price-box {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 8px 0px 8px;
	margin: 0px;
	position: relative;
	z-index: 20;
	box-sizing: border-box;
}
.productBox .price-box .price-label { display: none; }

.productBox .price-box .regular-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.productBox .price-box .regular-price .price {
	padding: 0px;
	margin: 0px;
	color: #000;
	font-size: 18px;
	line-height: 20px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox:hover .price-box .regular-price .price { color: #000; }

.productBox .price-box .old-price {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px 8px 0px 0px;
	position: relative;
	text-decoration: line-through;
}
/*.productBox .price-box .old-price:before {
	content: '';
	width: 55px;
	height: 18px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	top: 3px;
	z-index: 5;
	background:url(//localhost:8580/skin/frontend/stenik/default/images/oldPriceLine.**********.png) no-repeat left top;
}*/
.productBox .price-box .old-price .price {
	padding: 0px;
	margin: 0px;
	color: #878787;
	font-size: 18px;
	line-height: 20px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox:hover .price-box .old-price .price { color: #878787; }

.productBox .price-box .special-price {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.productBox .price-box .special-price .price {
	padding: 0px;
	margin: 0px;
	color: #cc1153;
	font-size: 18px;
	line-height: 20px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productBox:hover .price-box .special-price .price { color: #cc1153; }




.productViewContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 8px 0px 0px 0px;
}
.productViewContent .productViewLeft {
	float: left;
	width: 580px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 50px 0px;
}
.productViewContent .productViewGallery {
	float: left;
	width: 580px;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
}

.productViewContent .productViewGallery .royalSlider.rsUni {
    width: 580px;
    height: 510px;
}
.productViewContent .onsale-product-container { float: left; width: 100%; height: 100%; }
.productViewContent .productViewGallery .royalSlider.rsUni .rsThumbsVer { width: 80px; }
.productViewContent .productViewGallery .royalSlider.rsUni.oneImage  .rsThumbsVer { display: none; }
.productViewContent .productViewGallery .royalSlider.rsUni .rsThumb { width: 78px; height: 78px;  border: 1px solid #fff; }
.productViewContent .productViewGallery .royalSlider.rsUni .rsThumb.rsNavSelected { border-color: #e3e3e3; }
.productViewContent .productViewGallery .royalSlider.rsUni .rsArrow { display: none !important; width: 30px; height: 30px; background-color: #ccc; background-repeat: no-repeat; background-position: center center; }
.productViewContent .productViewGallery .royalSlider.rsUni.showArrows .rsArrow { display: block !important; }

.productViewContent .productViewGallery .royalSlider.rsUni.rsHor .rsArrowLeft {
    left: 5px;
    top: 350px;
    background-image:url(//localhost:8580/skin/frontend/stenik/default/images/rsArrowRight.**********.png);
    opacity: 0.6;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productViewGallery .royalSlider.rsUni.rsHor .rsArrowLeft:hover { opacity: 1; }

.productViewContent .productViewGallery .royalSlider.rsUni.rsHor .rsArrowRight {
    left: 45px;
    top: 350px;
    background-image:url(//localhost:8580/skin/frontend/stenik/default/images/rsArrowLeft.**********.png);
    opacity: 0.6;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productViewGallery .royalSlider.rsUni.rsHor .rsArrowRight:hover { opacity: 1; }

.productViewContent .productViewGallery .royalSlider.rsUni .rsThumbsVer { top: 5px; height: auto; max-height: 338px; }
.productViewContent .productViewGallery .royalSlider.rsUni.oneImage .rsOverflow { padding: 0px 0px 0px 0px; }
.productViewContent .productViewGallery .rsOverflow { padding: 0px 0px 0px 80px; }
.productViewContent .productViewGallery .rsOverflow img.rsMainSlideImage { margin-top: 0px !important; margin-right: 0px !important; margin-left: 10px !important; }
.productViewContent .productViewGallery .rsOverflow .rsContainer { cursor: pointer; }

.productViewContent .productViewGallery .label {
	float: left;
	width: auto;
	height: 20px;
	line-height: 20px;
	padding: 0px 6px 1px 6px;
	margin: 0px;
	color: #fff;
	font-size: 14px;
	font-weight: 300;
	text-decoration: none;
	text-transform: uppercase;
	background: #cc1153;
	position: absolute;
	right: 0px;
	top: 0px;
	z-index: 22;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productViewGallery .label.new { right: auto; left: 0px; background: #000; }

.productViewContent .productViewGallery .label:before {
	content: '';
	width: 0;
	height: 0;
	position: absolute;
	right: 0px;
	bottom: -7px;
	z-index: 23;
	border-style: solid;
	border-width: 0px 7px 7px 0px;
	border-color: transparent #cc1152 transparent transparent;
}
.productViewContent .productViewGallery .label.new:before { left: 0px; right: auto; border-width: 7px 7px 0px 0px; border-color: #000 transparent transparent transparent; }

.productViewContent .productViewGallery .videoThumbBox {
	float: left;
	width: 80px;
	height: 80px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	bottom: 30px;
}
.productViewContent .productViewGallery .videoThumbBox:before {
	content: '';
	width: 100%;
	height: 100%;
	padding: 0px;
	margin: 0px;
	background:url(//localhost:8580/skin/frontend/stenik/default/images/videoThumbBoxFade.**********.png);
	position: absolute;
	left: 0px;
	top: 0px;
	z-index: 10;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productViewGallery .videoThumbBox:hover:before { opacity: 0.5; }

.productViewContent .productViewGallery .videoThumbBox:after {
	content: '';
	width: 50px;
	height: 50px;
	padding: 0px;
	margin: -25px 0px 0px -25px;
	background:url(//localhost:8580/skin/frontend/stenik/default/images/videoThumbBoxPlayIcon.**********.png) no-repeat left top;
	position: absolute;
	left: 50%;
	top: 50%;
	z-index: 15;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productViewGallery .videoThumbBox:after { opacity: 0.5; }

.productViewContent .productViewGallery .videoThumbBox img {
	float: left;
	width: 80px;
	height: 80px;
	padding: 0px;
	margin: 0px;
	position: relative;
	z-index: 5;
}

.productViewContent .relatedProductsContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.productViewContent .relatedProductsContent .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 18px 0px;
	color: #000;
	font-size: 16px;
	line-height: 18px;
	font-weight: bold;
	text-decoration: none;
}
.productViewContent .relatedProductsContent .relatedItem {
	float: left;
	width: 78px;
	height: 78px;
	padding: 0px;
	margin: 0px 10px 0px 0px;
	border: 1px solid #e3e3e3;
}

.productViewContent .productViewRight {
	float: right;
	width: 580px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 50px 0px;
}
.productViewContent h1 { margin: -5px 0px 15px 0px !important; font-size: 27px !important; line-height: 29px !important; text-align: left !important; }

.productViewContent .sku {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
	color: #707070;
	font-size: 14px;
	line-height: 16px;
	font-weight: 300;
	text-decoration: none;
}
.productViewContent .sku strong { font-weight: normal; color: #000; }

.productViewContent .productInfo {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 0px 0px;
}
.productViewContent .productInfo .productInfoLeftCol {
	float: left;
	width: 300px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 25px 0px;
}
.productViewContent .productInfo .productInfoRightCol {
	float: right;
	width: 260px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 25px 0px;
}
.productViewContent .productInfo .addToCartBox {
	float: left;
	width: 300px;
	height: auto;
	padding: 20px 20px 15px 20px;
	margin: 0px;
	text-align: center;
	background: #f8f8f8;
	box-sizing: border-box;
	border: 1px solid #d9dbda;
}
.productViewContent .productInfo .addToCartBox .price-box {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
	text-align: left;
}
.productViewContent .productInfo .addToCartBox .price-box .price-label { display: none; }

.productViewContent .productInfo .addToCartBox .price-box .old-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	position: relative;
}
.productViewContent .productInfo .addToCartBox .price-box .old-price:before {
	content: '';
	width: 130px;
	height: 24px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	bottom: 3px;
	z-index: 5;
	background:url(//localhost:8580/skin/frontend/stenik/default/images/oldPriceLine2.**********.png) no-repeat left top;
}
.productViewContent .productInfo .addToCartBox .price-box .old-price .price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	color: #878787;
	font-size: 30px;
	line-height: 32px;
	font-weight: 300;
	text-decoration: none;
}
.productViewContent .productInfo .addToCartBox .price-box .special-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 0px 0px;
}
.productViewContent .productInfo .addToCartBox .price-box .special-price .price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	color: #cc1153;
	font-size: 40px;
	line-height: 42px;
	font-weight: normal;
	text-decoration: none;
}
.productViewContent .productInfo .addToCartBox .price-box .regular-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 0px ;
}
.productViewContent .productInfo .addToCartBox .price-box .regular-price .price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	color: #000;
	font-size: 40px;
	line-height: 42px;
	font-weight: normal;
	text-decoration: none;
}
.productViewContent .productInfo .addToCartBox .addToCart {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.productViewContent .productInfo .addToCartBox .addToCart .amount { display: none; }

.productViewContent .productInfo .addToCartBox .addToCart .addToCartBtn {
	float: left;
	width: 100%;
	height: auto;
	padding: 20px 0px 20px 0px;
	margin: 0px 0px 10px 0px;
	background: #cc1153;
	color: #fff;
	font-size: 22px;
	line-height: 24px;
	text-align: center;
	font-weight: bold;
	text-decoration: none;
	letter-spacing: 3px;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .addToCartBox .addToCart .addToCartBtn:hover { background: #e91862; }

.productViewContent .productInfo .addToCartBox .byOnLesingContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
}
.productViewContent .productInfo .addToCartBox .byOnLesingContent .byOnLeasingBtn {
	float: left;
	width: 100%;
	height: auto;
	padding: 12px 0px 14px 106px;
	margin: 0px;
	border: 1px solid #e3e3e3;
	color: #f18800;
	font-size: 18px;
	line-height: 20px;
	font-weight: bold;
	text-align: left;
	text-decoration: none;
	box-sizing: border-box;
	background: #fff url(//localhost:8580/skin/frontend/stenik/default/images/byOnLeasingBtn.**********.png) no-repeat 12px 9px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .addToCartBox .byOnLesingContent .byOnLeasingBtn:hover { border-color: #cc1153; }

.productViewContent .productInfo .addToCartBox .byOnLesingContent .byOnLesing { display: none; }

.productViewContent .productInfo .addToCartBox .buyOnLeasingButton {
	float: left;
	width: 100%;
	padding: 11px 10px 12px 10px;
	margin: 0 0 10px 0;
	font-size: 18px;
	line-height: 23px;
	border: 2px solid #cb1854;
	color: #cb1854;
	text-decoration: none;
	font-weight: bold;
	box-sizing: border-box;
}
.productViewContent .productInfo .addToCartBox #newpay-open-popup {
	float: left;
	width: 100%;
	margin: 0 0 10px 0;
	cursor: pointer;
}
.productViewContent .productInfo .addToCartBox #newpay-open-popup img {
	float: left;
	max-width: 100%;
}
.productViewTabsContnt .productsTabInfo .tabs-nav-leasing {
	float: left;
	width: 25%;
	padding-right: 30px;
	box-sizing: border-box;
}
.productViewTabsContnt .productsTabInfo .leasing-tabs ul {
	float: left;
	width: 100%!important;
	height: auto;
}

.productViewTabsContnt .productsTabInfo .leasing-tabs li {
	float: left;
	width: 100%;
	height: auto;
	box-sizing: border-box;
}

.leasing-tabs li a.leasing-tabs-nav {
	float: left;
    width: 100%;
    height: auto;
    padding: 13px 10px 13px 10px;
    margin: 0px;
    background: #fff;
    border-bottom: 1px solid #d5cec8;
    position: relative;
    font-size: 15px;
    line-height: 17px;
    color: #424242;
    box-sizing: border-box;
    font-weight: normal;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
}

table.stylized th.promo { background: #e51e26; color: #fff; position: relative; }
table.stylized th.promo .months{ color: #fff; }
table.stylized th.promo span.promo-text { width: 100%; height: 14px; line-height: 14px; padding: 0px; margin: 0px; text-align: center; color: #fff; font-size: 11px; text-decoration: none; font-weight: normal; background: #e51e26; position: absolute; left: 0px; top: 2px; z-index: 10; }
table.stylized{
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 20px 0px;
	border-spacing: 0px;
	border-collapse: separate;
	border: none;
	border-top: 1px solid #dcdcdc;
	border-left: 1px solid #dcdcdc;
}

table.stylized th {
	border-right: 1px solid #dcdcdc;
    border-bottom: 1px solid #3097d1;
    padding: 10px 12px 10px 12px;
    vertical-align: middle;
    background: #fff;
    color: #333;
    font-size: 13px;
    line-height: 15px;
    text-align: center;
    font-weight: bold;
}
table.stylized td {
    border-right: 1px solid #dcdcdc;
    border-bottom: 1px solid #dcdcdc;
    padding: 10px 12px 10px 12px;
    vertical-align: top;
    background: #f4f4f4;
    color: #333;
    font-size: 13px;
    line-height: 15px;
    text-align: left;
    font-weight: normal;
}
table.stylized tr:nth-child(2n) td {
    background: #e9e9e9;
}

.leasing-calculator h3{
	font-size: 25px;
	line-height: 30px;
	font-weight: 300;
	color: #222;
	margin: 0 0 15px 0;
}
.leasing-calculator .principal {
	padding: 0px;
	margin: 0px 0px 15px 0px;
    font-size: 18px;
    font-weight: normal;
    text-decoration: none;
    color: #222;
}
.leasing-calculator .downpayment-content { float: left; width: 100%; height: auto; padding: 0px; margin: 0 0 20px 0; position: relative; }
.leasing-calculator .downpayment-content .button.recalc{ background: #222; color: #fff; height: 40px; line-height: 38px; min-width: 120px; }
.leasing-calculator .downpayment-content .button.recalc:hover{opacity: 0.8; }
.leasing-calculator .downpayment-content label { margin: 0px 0px 8px 0px; }
.leasing-calculator .downpayment-content label.downpayment-label {float: left; width: 100%; font-size: 18px; color: #222; }
.leasing-calculator .downpayment-content input.input-text.downpayment { float: left; width: 240px; height: 40px; margin: 0px 10px 0px 0px; }
.leasing-calculator .horizontal-scroll-wrapper { width: 100%; margin-bottom: 10px; overflow-x: auto; }
.leasing-calculator .horizontal-scroll-wrapper table.stylized.variants { margin-bottom: 10px;  }
.leasing-calculator .recalc-loader { width: 35px; height: 35px; padding: 0px; margin: 0px; background: url(//localhost:8580/skin/frontend/stenik/default/images/preloader-28x28.**********.gif) no-repeat center center; position: absolute; left: 425px; top: 34px; z-index: 20; }
.leasing-message { float: left; width: auto; margin: 14px 0px 0px 14px; }
.leasing-calculator .textInfo {float: left;width: 100%;height: auto;margin: 0 0 20px 0;}
.leasing-calculator .promo-offer { float: left; width: 100%; height: auto; margin: 0 0 20px 0; }
.leasing-calculator .checkout-agreements { float: left; width: 100%; margin: 0 0 10px 0; }
.leasing-calculator .checkout-agreements label{ width: 85%;display: inline-block; }
.leasing-calculator .checkout-agreements label a{ color: #cc1153; text-decoration: none; border-bottom: 1px solid #cc1153; -webkit-transition: all 0.2s linear;-moz-transition: all 0.2s linear;-o-transition: all 0.2s linear;transition: all 0.2s linear;}
.leasing-calculator .checkout-agreements label a:hover{ color: #222; border-bottom: 1px solid #222; }
.payment-block  .leasing-calculator { float: left; width: 100%; margin: 10px 0px 10px 0px; }
.payment-block  .leasing-calculator .downpayment-content { padding: 0px; }
.opc-wrapper-opc .payment-block .leasing-calculator h3 { float: left;width: 100%;height: auto; border: none; padding: 0px;margin: 0px 0px 15px 0px;font-size: 25px;line-height: 30px;color: #222; }
.payment-block .leasing-calculator .promo-offer p {margin: 0; }
.opc-wrapper-opc .payment-block .leasing-calculator p{ font-style: normal; font-size: 16px; }
.payment-block .leasing-calculator .checkout-agreements input { float: left; margin: 6px 8px 0px 0px; }
.payment-block .leasing-calculator .checkout-agreements label { float: left; width: 85%; }
.payment-block .leasing-calculator .horizontal-scroll-wrapper { width: 100%; box-sizing: border-box; overflow-x: auto; }
.payment-block .leasing-calculator .checkout-agreements { float: left; width: 100%; margin: 0px 0px 10px 0px; }
.payment-block .leasing-calculator .checkout-agreements label { margin: 0; padding: 0; }
.payment-block .leasing-calculator .checkout-agreements label p{ margin: 0; }
.payment-block .leasing-calculator .textInfo p{ margin: 0; }
.payment-block .leasing-calculator .principal{ margin: 0 0 20px 0; float: left; width: 100%; font-style: normal; }
.payment-block .leasing-calculator .horizontal-scroll-wrapper table.stylized td { text-align: center; }
.payment-block .lease-calculator .form-list .field .input-text { margin: 3px 0px 3px 0px; }

.payment-block .leasing-calculator .form-list .field {
	float: left;
	width: 340px;
	margin-bottom: 7px;
	margin: 0px 15px 7px 0px;
}
.payment-block .leasing-calculator h4 {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
	font-size: 25px;line-height: 30px
	;color: #222;
	font-weight: 400;
}
.payment-block .leasing-calculator {
	float: left;
	width: 1100px;
	height: auto;
	padding: 10px 10px 5px 10px;
	margin: 10px 0px 10px -15px;
	background: #fff;
	border: 3px solid #e9e9e9;
}
.payment-block .leaseCalculator .variants td label { text-align: center; }
.opc-wrapper-opc .payment-block #checkout-payment-method-load dd .leasing-calculator ul.form-list { padding-left: 0; }

.leaseCalculator .variants ul.messages li.error-msg { width: 100% !important; box-sizing: border-box; }
.leaseCalculator .variants ul.messages { width: 100% !important; box-sizing: border-box; }

.leasing-tabs { margin-bottom: 20px; }
.leasing-tabs ul { float: left; width: 100%; height: auto; }
.leasing-tabs li { float: left; width: 100%; height: auto; }
.leasing-tabs li a.leasing-tabs-nav { float: left; width: 100%; height: auto; padding: 10px; margin: -1px 0 0 0; background: #fff; border: 1px solid #e0dfdf; position: relative; font-size: 15px; line-height: 17px; color: #424242; font-weight: normal; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.leasing-tabs li a.leasing-tabs-nav:hover { background: #e9e8e8; }
.leasing-tabs li a.leasing-tabs-nav.selected { background: #e9e8e8; }
.leasing-tabs li a.leasing-tabs-nav:after { content: '»'; width: 17px; height: 17px; line-height: 17px; color: #c2b7ad; font-size: 14px; font-weight: normal; position: absolute; right: 10px; top: 18px; z-index: 10;  -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.leasing-tabs li a.leasing-tabs-nav.selected:after { color: #333; }
.leasing-tabs li a.leasing-tabs-nav img { float: left; height: 35px; }

.productViewTabsContnt .productsTabInfo .tabs-content{
	float: left;
	width: 75%;
	box-sizing: border-box;
}

.productViewTabsContnt .productsTabInfo .tabs-content .leaseCalculator {
	width: 100%;
	box-sizing: border-box;
	margin: 0 0 10px 0;
}
.productViewTabsContnt .productsTabInfo .tabs-content .leasing-add-to-cart { line-height: 40px; }
.productViewTabsContnt .productsTabInfo .tabs-content .byOnLesingContent{
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
}
.productViewTabsContnt .productsTabInfo .tabs-content .byOnLesingContent .byOnLeasingBtn{
	float: left;
	width: 100%;
	height: auto;
	padding: 12px 0px 14px 106px;
	margin: 0px;
	border: 1px solid #e3e3e3;
	color: #f18800;
	font-size: 18px;
	line-height: 20px;
	font-weight: bold;
	text-align: left;
	text-decoration: none;
	box-sizing: border-box;
	background: #fff url(//localhost:8580/skin/frontend/stenik/default/images/byOnLeasingBtn.**********.png) no-repeat 12px 9px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .addToCartBox .availabilityInStores {
	float: none;
	display: inline-block;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0;
	color: #cc1153;
	font-size: 16px;
	line-height: 18px;
	font-weight: bold;
	text-align: center;
	text-decoration: underline;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .addToCartBox .availabilityInStores:hover { color: #000; }

.productViewContent .productInfo .addToCartBox .giftBoxWrapper {
	float: left;
	width: 100%;
	position: relative;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper .gift-box-button {
	float: left;
	width: 100%;
	margin: 0 0 10px 0;
	padding: 0;
	background: #fff;
	border: 1px solid #e3e3e3;
	color: #000;
	font-size: 18px;
	line-height: 48px;
	font-weight: bold;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	transition: all 0.2s linear;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper .gift-box-button:hover {
	border-color: #cc1153;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper .gift-box-button .gift-icon {
	display: inline-block;
	width: 23px;
	height: 23px;
	margin: 0 3px -4px 0;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/giftBoxButtonIcon.**********.png) no-repeat center center;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper .giftBoxPopup {
	display: none;
	float: left;
	width: 253px;
	min-height: 100px;
	padding: 10px 10px 2px 10px;
	background: #d9dbda;
	box-sizing: border-box;
	position: absolute;
	right: -274px;
	top: 0;
	z-index: 10;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper .giftBoxPopup:before {
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 14.5px 16px 14.5px 0;
	border-color: transparent #d9dbda transparent transparent;
	position: absolute;
	left: -16px;
	top: 10px;
	z-index: 11;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper .giftBoxPopup p {
	margin: 0 0 8px 0;
	color: #000;
	font-size: 16px;
	line-height: 19px;
	text-align: left;
}
.productViewContent .productInfo .addToCartBox .giftBoxWrapper:hover .giftBoxPopup {
	display: block;
}
.productViewContent .productInfo .infoRatingBox {
	float: left;
	width: 100%;
	height: 24px;
	padding: 0px;
	margin: 5px 0px 5px 0px;
	text-decoration: none;
}
.productViewContent .productInfo .infoRatingBox .ratingBox {
	float: left;
	width: 140px;
	height: 24px;
	padding: 0px;
	margin: 0px;
	position: relative;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) repeat-x left top;
}
.productViewContent .productInfo .infoRatingBox .ratingBox .rating {
	float: left;
	height: 24px;
	padding: 0px;
	margin: 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) repeat-x left bottom;
}
.productViewContent .productInfo .infoRatingBox .ratingInfo {
	float: right;
	width: 115px;
	height: 22px;
	line-height: 22px;
	padding: 0px;
	margin: 0px 0px 2px 0px;
	color: #878787;
	font-size: 16px;
	text-align: center;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .infoRatingBox .ratingInfo strong { color: #000; font-weight: bold; }

.productViewContent .productInfo .infoRatingBox .rateHere {
	float: right;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 3px 0px 0px 0px;
	color: #555;
	font-size: 15px;
	line-height: 17px;
	text-align: right;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .infoRatingBox .rateHere:hover { color: #cc1153; }

.productViewContent .productInfo .addToCompareist {
	float: left;
	width: 100%;
	height: 30px;
	line-height: 30px;
	padding: 0px 0px 0px 54px;
	margin: 3px 0px 3px 0px;
	box-sizing: border-box;
	color: #000;
	font-size: 16px;
	font-weight: bold;
	text-align: left;
	text-decoration: none;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/addToCompareistIcon.**********.png) no-repeat 10px 4px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .addToCompareist:hover { color: #cc1153; background-position: 14px 4px; }

.productViewContent .productInfo .addToWishlist {
	float: left;
	width: 100%;
	height: 30px;
	line-height: 30px;
	padding: 0px 0px 0px 54px;
	margin: 3px 0px 3px 0px;
	box-sizing: border-box;
	color: #000;
	font-size: 16px;
	font-weight: bold;
	text-align: left;
	text-decoration: none;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/addToWishlistIcon.**********.png) no-repeat 10px 4px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .addToWishlist:hover { color: #cc1153; background-position: 14px 4px; }

.productViewContent .productInfo .productViewSocial {
	float: left;
	width: 100%;
	height: 33px;
	padding: 0px;
	margin: 10px 0px 0px 0px;
}
.productViewContent .productInfo .productViewSocial .socialTitle {
	float: left;
	width: 110px;
	height: 33px;
	line-height: 33px;
	padding: 0px;
	margin: 0px 0px 0px 0px;
	color: #000;
	font-size: 18px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
}
.productViewContent .productInfo .productViewSocial .addThisBox {
	float: left;
	width: 150px;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.productViewContent .productInfo .productViewSocial .addThisBox .social {
	float: left;
	width: 33px;
	height: 33px;
	padding: 0px;
	margin: 0px 5px 0px 5px;
	background: #000;
	border-radius: 100%;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .productViewSocial .addThisBox .social:hover { background: #cc1153; }

.productViewContent .productInfo .productViewSocial .addThisBox .social:before {
	content: '';
	width: 33px;
	height: 33px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	top: 0px;
	z-index: 5;
}
.productViewContent .productInfo .productViewSocial .addThisBox .social.facebook:before { background: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxFacebook2.**********.png) no-repeat center center; }
.productViewContent .productInfo .productViewSocial .addThisBox .social.gPlus:before { background: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxgPlus2.**********.png) no-repeat center center; }
.productViewContent .productInfo .productViewSocial .addThisBox .social.twitter:before { background: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxTwitter2.**********.png) no-repeat center center; }

.productViewContent .productInfo .line {
	float: left;
	width: 100%;
	height: 1px;
	padding: 0px;
	margin: 10px 0px 10px 0px;
	background: #d6d6d6;
}

.productViewContent .productInfo .productInfoAccordion {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.productViewContent .productInfo .productInfoAccordion .accordionItem {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	border-bottom: 1px solid #d6d6d6;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionItemPlus.**********.png) no-repeat right 22px;
}
.productViewContent .productInfo .productInfoAccordion .accordionItem.icon1 { background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionIcon1.**********.png) no-repeat 12px 16px; }
.productViewContent .productInfo .productInfoAccordion .accordionItem.icon2 { background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionIcon2.**********.png) no-repeat 12px 16px; }
.productViewContent .productInfo .productInfoAccordion .accordionItem.icon3 { background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionIcon3.**********.png) no-repeat 12px 16px; }
.productViewContent .productInfo .productInfoAccordion .accordionItem.icon4 { background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionIcon4.**********.png) no-repeat 12px 16px; }

.productViewContent .productInfo .productInfoAccordion .accordionItem .link {
	float: left;
	width: 100%;
	height: 40px;
	line-height: 40px;
	padding: 0px 50px 0px 64px;
	margin: 10px 0px 10px 0px;
	color: #000;
	font-size: 16px;
	font-weight: bold;
	text-align: left;
	text-decoration: none;
	box-sizing: border-box;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionItemPlus.**********.png) no-repeat right 12px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productViewContent .productInfo .productInfoAccordion .accordionItem .link.close { padding: 0px 50px 0px 55px; color: #cc1153; background: url(//localhost:8580/skin/frontend/stenik/default/images/accordionItemMinus.**********.png) no-repeat right 12px; }

.productViewContent .productInfo .productInfoAccordion .accordionSub {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 55px;
	margin: 0px;
	box-sizing: border-box;
	display: none;
}
.productViewContent .productInfo .productInfoAccordion .accordionSub .textPage p { font-size: 16px; line-height: 20px; }

.productViewContent .productInfo .productInfoAccordion .accordionSub form.stenikForm {
	float: left;
	width: 80%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 20px 0px;
}
.productViewContent .productInfo .productInfoAccordion .accordionSub form.stenikForm h6 {
	margin: 0px 0px 10px 0px;
}
.productViewContent .productInfo .productInfoAccordion .accordionSub form.stenikForm .validation-advice {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: -10px 0px 10px 0px;
	color: #ed1c24;
	font-size: 14px;
	line-height: 16px;
	font-weight: 300;
	text-transform: none;
}


#main .productViewTabsContnt {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 60px 0px;
}
#main .productViewTabsContnt .productsTabInfo {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
#main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav {
	float: left;
	width: 100%;
	height: 50px;
	padding: 0px;
	margin: 0px 0px 40px 0px;
	border-bottom: 3px solid #000;
}
#main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav li {
	float: left;
	width: auto;
	height: 50px;
	padding: 0px;
	margin: 0px;
}
#main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav li a {
	float: left;
	width: auto;
	height: 50px;
	line-height: 50px;
	padding: 0px 20px 0px 20px;
	margin: 0px 4px 0px 0px;
	background: #fff;
	color: #000;
	font-size: 19px;
	text-align: center;
	font-weight: normal;
	text-decoration: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav li.ui-tabs-active a { background: #000; color: #fff; }

#main .productViewTabsContnt .productsTabInfo .ui-tabs-panel {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}

#main .productViewTabsContnt .productsTabInfo .ui-tabs-panel .descriptionContent {
	float: left;
	width: auto;
	max-width: 570px;
	height: auto;
	padding: 0px;
	margin: 0px;
}

#main .productViewTabsContnt .commentsFormWrapper {
	float: left;
	width: 487px;
	height: auto;
	padding: 0px;
	margin: 0px;
}
#main .commentsFormWrapper h4 { margin-top: 0px; }

#main .commentsFormWrapper .rating {
	float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 15px 0px;
}
#main .commentsFormWrapper .rating .rateTitle {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px;
    color: #333;
    font-size: 16px;
    line-height: 22px;
    font-weight: 300;
    text-decoration: none;
}
#main .commentsFormWrapper .rating .star-rating-control {
    float: left;
    width: auto;
    height: 15px;
    padding: 0px;
    margin: 0px 5px 0px 5px;
}
#main .commentsFormWrapper .star {
    display: inline-block;
    width: 24px;
    height: 24px;
    padding: 0px;
    margin: 0px;
    border-bottom: none;
    cursor: pointer;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) no-repeat left top;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .commentsFormWrapper .star.star-rating-hover { background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) no-repeat left bottom; }
#main .commentsFormWrapper .star.star-rating-on { background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) no-repeat left bottom; }

#main .commentsFormWrapper .star a { display: none; }

#main .commentsFormWrapper .validation-advice {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: -10px 0px 10px 0px;
    font-size: 13px;
    line-height: 15px;
    color: #d81717;
    text-decoration: none;
    font-weight: normal;
}
#main .commentsFormWrapper .textarea-box .validation-advice { margin-top: 0px; }

#main .productViewTabsContnt .commentsFormWrapper .captcha {
	float: left;
	padding: 0px;
	margin: 0px;
	position: relative;
}
#main .productViewTabsContnt .commentsFormWrapper button.button.addCommentBtn { float: right; height: 76px !important; font-size: 17px; }

#main .commentsFormWrapper .captcha .validation-advice { margin-top: 0px; }

#main .productViewTabsContnt .commentsByUsers {
    float: right;
    width: 585px;
    height: auto;
    padding: 0px;
    margin: 0px;
}
#main .productViewTabsContnt .commentsByUsers h4 { margin-top: 0px; }

#main .productViewTabsContnt .commentsByUsers .commentsItems {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 20px 0px;
    margin: 0px 0px 20px 0px;
    border-bottom: 1px solid #d6d6d6;
}
#main .productViewTabsContnt .commentsByUsers .nameAndDate {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 8px 0px;
    box-sizing: border-box;
    color: #000;
    font-size: 16px;
    line-height: 20px;
    font-weight: 300;
    text-decoration: none;
}
#main .productViewTabsContnt .commentsByUsers .nameAndDate span.author { font-weight: 500; }

#main .productViewTabsContnt .commentsByUsers .comment {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 5px 0px;
    color: 555;
    font-size: 15px;
    line-height: 18px;
    font-weight: 300;
    text-decoration: none;
}
#main .productViewTabsContnt .commentsByUsers .ratingBox {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 10px 0px;
}
#main .productViewTabsContnt .commentsByUsers .ratingBox .title {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px;
    font-weight: 300;
    color: #000;
}
#main .productViewTabsContnt .commentsByUsers .ratingBox .rating-available {
    float: left;
    width: 140px;
    height: 24px;
    padding: 0px;
    margin: 0px 5px 0px 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) repeat-x left top;
    position: relative;
}
#main .productViewTabsContnt .commentsByUsers .ratingBox .rating-available .rating {
    float: left;
    width: auto;
    height: 24px;
    padding: 0px;
    margin: 0px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/ratingStars.**********.png) repeat-x left bottom;
}
#main .productViewTabsContnt .noReviewsWrapper {
	float: right;
	width: 585px;
	height: auto;
	padding: 0px;
	margin: 0px;
}
#main .lastSeenItemsContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 30px 30px 20px 30px;
	margin: 0px 0px 30px 0px;
	background: #f8f8f8;
	border-bottom: 1px solid #ececec;
	box-sizing: border-box;
}
#main .lastSeenItemsContent .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 45px 0px;
	color: #000;
	font-size: 35px;
	line-height: 37px;
	text-align: left;
	font-weight: 300;
	text-decoration: none;
}
#main .lastSeenItemsContent .lastSeenItems {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
#main .lastSeenItemsContent .lastSeenItems .lastSeenItem {
	float: left;
	width: 130px;
	height: 130px;
	padding: 0px;
	margin: 0px 15px 20px 15px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .lastSeenItemsContent .lastSeenItems .lastSeenItem:hover { opacity: 0.7; }

#main .lastSeenItemsContent .lastSeenItems .lastSeenItem img {
	float: left;
	width: 130px;
	height: 130px;
	padding: 0px;
	margin: 0px;
}

.videoPopUpContent {
	float: left;
	width: 800px;
	height: auto;
	padding: 0px;
	margin: 0px;
}

.leasingPopUpContent {
	float: left;
	width: 1120px;
	height: auto;
	min-height: 200px;
	padding: 0px;
	margin: 0px;
}
.leasingPopUpContent .textPage h3 { margin-top: 0px; }

.product-options {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
}
.product-options dl {
    float: left;
    width: 100%;
    height: auto;
    padding: 0;
    margin: 0;
}
.product-options dl dt {
    float: left;
    width: 100%;
    height: auto;
    padding: 0;
    margin: 0;
}
.product-options dl dt label {
    float: left;
    margin: 0 0 8px 0;
}
.product-options dl dd {
    float: left;
    width: 100%;
    height: auto;
    padding: 0;
    margin: 0;
}







.loginRegBoxWrapper {
    float: left;
    width: 100%;
    height: auto;
    padding: 25px 0px 25px 0px;
    margin: 0px;
    border-top: 1px solid #e8e8e8;
    position: relative;
    box-sizing: border-box;
}

.loginRegBox {
    float: left;
    width: 50%;
    height: auto;
    min-height: 385px;
    padding: 0px 10px 0px 0px;
    margin: 0px 0px 80px 0px;
    position: relative;
    box-sizing: border-box;
}

.loginRegBox.login { float: right; width: 50%; padding: 0px 0px 0px 10px; }
.loginRegBox.register { float: left; width: 50%; padding: 0px 10px 0px 0px; }
.loginRegBox.contactsInfo { float: left; width: 50%; padding: 0px 10px 0px 0px; }
.loginRegBox.contacts { float: right; width: 50%; padding: 0px 0px 0px 10px; margin-bottom: 40px; }
.loginRegBox.register .customer-name .field label{ display: none; }

.loginRegBox.login input.input-text { width: 400px; }
.loginRegBox.register input.input-text { width: 400px; }

.loginRegBox.forgotPassword {
    min-height: 250px;
}

.loginRegBox .recaptcha {
	float: left;
	width: 100%;
}

.loginRegBox.forgotPassword form {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
}

.loginRegBox.forgotPassword .buttons-set {
    position: relative;
}

.loginRegBox.forgotPassword .forgotpassword {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 12px 0px 0px 12px;
    font-size: 14px;
    line-height: 25px;
    color: #212220;
    border-bottom: 1px solid #212220;
    text-decoration: none;
    font-weight: normal;
    position: relative;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.loginRegBox.forgotPassword .forgotpassword:hover { color: #6a6a6a; }

.loginRegBox h2 {
    float: left;
    width: 100%;
    max-width: 540px;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 30px 0px;
    font-size: 32px;
    line-height: 34px;
    color: #000000;
    font-weight: 300;
    text-decoration: none;
    background: url("//localhost:8580/skin/frontend/stenik/default/images/loginRegBoxTitleBg.**********.jpg") 0px 23px repeat-x;
}

.loginRegBox h2 span {
    float: left;
    width: auto;
    height: auto;
    padding: 0px 15px 0px 0px;
    margin: 0px 0px 0px 0px;
    background: #fff;
}

.loginRegBox.contactsInfo h2 { background: none; }
.loginRegBox.contacts h2 { background: none; }

.loginRegBox .textPage {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 15px 0px;
}

.loginRegBox .contactsForm {
    float: left;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
}

.loginRegBox .buttons-set {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
    position: absolute;
    bottom: -15px;
}

.loginRegBox .goToRegistrationBtn {
	float: left;
	width: 170px;
	height: 42px;
	padding: 0px 12px 0px 12px;
	margin: 0px;
	background: #cc1153;
	border: none;
	font-size: 16px;
	line-height: 42px;
	color: #fff;
	font-weight: 400;
	text-align: center;
	text-decoration: none;
	outline: none;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.loginRegBox .goToRegistrationBtn:hover { background: #030303; }

.loginRegBox .forgotpassword {
    clear: both;
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 15px 0px;
    border-bottom: 1px solid #cc1153;
    font-size: 16px;
    line-height: 20px;
    color: #cc1153;
    text-decoration: none;
    font-weight: normal;
    position: relative;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.loginRegBox .forgotpassword:hover { color: #030303; }

.loginRegBox .forgotpassword.backToLogin { clear: none; float: right; }

.loginRegBox .checkboxContent {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 10px 0px 30px 0px;
}

.loginRegBox .checkboxContent input.checkbox {
    float: left;
    clear: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 4px 13px 5px 2px;
}

.loginRegBox .checkboxContent label {
	float: left;
    width: auto;
    max-width: 85%;
    height: auto;
    margin-bottom: 7px;
    font-size: 18px;
    line-height: 20px;
    color: #707070;
    font-weight: 300;
    text-transform: none;
}

.loginRegBox .checkboxContent label a {
    text-decoration: none;
    color: #cc1153;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.loginRegBox .checkboxContent label a:hover { color: #030303; }

.loginRegBox .validation-advice {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: -10px 0px 10px 0px;
	color: #ed1c24;
	font-size: 14px;
	line-height: 16px;
	font-weight: 300;
	text-transform: none;
}
.loginRegBox .checkboxContent .validation-advice { margin: 0px 0px 10px 0px; }
.loginRegBox .captchaBox .validation-advice { margin: 0px 0px 10px 0px; }

.genderBox {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 15px 0px 0px 0px;
}

.genderBox .genderTitle {
	float: left;
	width: auto;
	height: auto;
	padding: 0px 12px 0px 0px;
	margin: 0px 0px 0px 0px;
	display: inline-block;
}

.genderBox ul.invoice-fields-wrapper {
	margin: 0;
	padding: 0 2px;
}

.genderBox ul.invoice-fields-wrapper li.show-invoice-fileds {
	float: left;
	width: 100%;
	padding: 0;
	margin: 0 0 5px 0;
}
.genderBox ul.invoice-fields-wrapper li.fields {
	float: left;
	width: 100%;
	padding: 0;
	margin: 5px 0 0 0;
}
.genderBox ul.invoice-fields-wrapper li.fields label {
	float: left;
	width: 90%;
	height: auto;
	padding: 0;
	margin: 0 0 5px 0;
}
.genderBox ul.invoice-fields-wrapper li.fields input.checkbox {
	float: left;
	margin: 4px 4px 0 0;
}

#main .facebookLoginBtn {
	float: left;
	width: 190px;
	height: 42px;
	line-height: 42px;
	padding: 0px 0px 2px 52px;
	margin: 0px 20px 25px 0px;
	box-sizing: border-box;
	color: #fff;
	font-size: 14px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	background: #3b5791 url(//localhost:8580/skin/frontend/stenik/default/images/facebookLoginIcon.**********.png) no-repeat 20px 13px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .facebookLoginBtn:hover { opacity: 0.9; padding-left: 57px; background-position: 26px 13px; }

#main .loginRegBox.registration .facebookLoginBtn { margin-left: 65px; }
#main .checkoutSocialLoginWrapper .facebookLoginBtn { float: none; display: inline-block; height: 39px; line-height: 39px; margin: 0px 0px 0px 10px; background-position: 10px 11px; }

#main .gPlusLoginBtn {
	float: left;
	width: 190px;
	height: 42px;
	line-height: 42px;
	padding: 0px 0px 2px 65px;
	margin: 0px 20px 25px 0px;
	box-sizing: border-box;
	color: #fff;
	font-size: 14px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	background: #e04b33 url(//localhost:8580/skin/frontend/stenik/default/images/GPlusLoginIcon.**********.png) no-repeat 20px 11px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .gPlusLoginBtn:hover { opacity: 0.9; padding-left: 70px; background-position: 26px 11px; }

#main .checkoutSocialLoginWrapper .gPlusLoginBtn { float: none; display: inline-block; height: 39px; line-height: 39px; margin: 0px 0px 0px 10px; background-position: 10px 10px; }



.registrationBanner {
    float: left;
    width: 50%;
    height: auto;
    min-height: 306px;
    padding: 0px 0px 0px 10px;
    margin: 0px;
    position: relative;
    box-sizing: border-box;
}

.registrationBanner a {
    float: left;
    width: 100%;
    height: auto;
    min-height: 490px;
    padding: 0px 0px 0px 0px;
    margin: 0px;
    position: relative;
    box-sizing: border-box;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.registrationBanner a:hover {
    opacity: 0.8;
}

.registrationBanner a img {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 80px 0px;
    position: relative;
    box-sizing: border-box;
}

.termsPopUpContent.textPage { width: 880px; height: 600px; overflow-y: scroll; padding: 15px; background: #fff; }






.loginRegBox.contactsInfo .textPage .viewShops {
	float: left;
	width: auto;
	height: 38px;
	padding: 0px 10px 0px 10px;
	margin: 0px 12px 0px 0px;
	border:1px solid #636363;
	color: #636363;
	font-size: 15px;
	line-height: 38px;
	text-decoration: none;
	text-transform: uppercase;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.loginRegBox.contactsInfo .textPage .viewShops:hover { color: #fff; background: #636363; }


.loginRegBox.contacts .captchaBox {
	float: left;
	width: auto;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	position: relative;
}
.loginRegBox.contacts button.button {
	float: right;
	width: 258px;
	height: 76px;
	padding: 0px 12px 0px 12px;
	margin: 0px;
	background: #cc1153;
	border: none;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	font-size: 16px;
	color: #fff;
	font-weight: 400;
	text-align: center;
	text-decoration: none;
	outline: none;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.loginRegBox.contacts button.button:hover { background: #030303; }


.contactGoogleMapWrapper  {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
}

.contactGoogleMapWrapper #mapContainer  {
    float: left;
    width: 540px;
    height: 500px;
    padding: 0px;
    margin: 0px;
}





.newsListingWrapper {
	float: left;
	width: 1200px;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
}
.col2-left-layout .newsListingWrapper { width: 888px; }

.newsListingWrapper .newsDescription {
	float: left;
	width: 100%;
	max-width: 1180px;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 20px 0px;
}

.newsListingWrapper .newsBox {
	float: left;
	width: 280px;
	height: 415px;
	padding: 0px 0px 0px 0px;
	margin: 0px 20px 20px 0px;
}
.col2-left-layout .newsListingWrapper .newsBox { margin-right: 16px; }

.newsListingWrapper .newsBox .newsImgWrapper {
	float: left;
	width: 100%;
	height: 210px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	background: #dfdfdf;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.newsListingWrapper .newsBox .newsImg {
	float: left;
	width: 100%;
	height: 210px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.newsListingWrapper .newsBox:hover .newsImg {
	opacity: 0.8;
}

.newsListingWrapper .newsBox .newsImg img {
	float: left;
	width: 100%;
	height: 210px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
}

.newsListingWrapper .newsBox .newsInfo {
	float: left;
	width: 256px;
	height: auto;
	padding: 25px 12px 12px 12px;
	margin: 0px 0px 0px 0px;
	position: relative;
}

.newsListingWrapper .newsBox .newsInfo .newsTitle {
	float: left;
	width: 100%;
	height: auto;
	max-height: 74px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 12px 0px;
	overflow: hidden;
	display: block;
	font-size: 20px;
	line-height: 24px;
	color: #000;
	font-weight: 400;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.newsListingWrapper .newsBox:hover .newsInfo .newsTitle {
	color: #cc1153;
}

.newsListingWrapper .newsBox .newsInfo .newsTitle:hover {
	color: #3d3d3d;
}

.newsListingWrapper .newsBox .newsInfo .newsShortDescription {
	float: left;
	width: 100%;
	height: auto;
	max-height: 90px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 12px 0px;
	overflow: hidden;
	font-size: 16px;
	line-height: 22px;
	color: #707070;
	font-weight: 300;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.newsListingWrapper .newsBox .newsInfo .dateLabel {
	float: left;
	width: auto;
	height: 30px;
	line-height: 30px;
	padding: 0px 10px 1px 10px;
	margin: 0px;
	color: #fff;
	font-size: 15px;
	font-weight: 400;
	text-decoration: none;
	background: #000;
	position: absolute;
	left: 0px;
	top: -15px;
	z-index: 22;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.newsListingWrapper .newsBox .newsInfo .dateLabel:before {
	content: '';
	width: 0;
	height: 0;
	position: absolute;
	left: 0px;
	bottom: -7px;
	z-index: 23;
	border-style: solid;
	border-width: 7px 7px 0px 0px;
	border-color: #000 transparent transparent;
}




.innerNewsWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 50px 0px;
}

.innerNewsWrapper .textPage {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 30px 0px;
}

.innerNewsWrapper .newsImage {
	float: right;
	margin: 18px 0px 18px 18px;
}

.innerNewsWrapper .newsBackBtn {
	float: left;
	width: auto;
	height: 38px;
	padding: 0px 10px 0px 10px;
	margin: 0px 12px 0px 0px;
	border:1px solid #636363;
	font-size: 15px;
	line-height: 38px;
	color: #636363;
	text-transform: uppercase;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.innerNewsWrapper .newsBackBtn:hover {
	color: #fff;
	background: #636363;
}

.innerNewsWrapper .newsShare {
    float: right;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 0px 0px;
}

.innerNewsWrapper .newsShare .title {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 8px 0px 0px;
    font-size: 18px;
    line-height: 34px;
    color: #453933;
    font-weight: 400;
}

.innerNewsWrapper .newsShare .social {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 30px 0px;
    box-sizing: border-box;
}

.innerNewsWrapper .newsShare .social li {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 0px 0px;
    box-sizing: border-box;
}

.innerNewsWrapper .newsShare .social li a {
    float: left;
    width: 34px;
    height: 34px;
    padding: 0px;
    margin: 0px 5px 0px 0px;
    border-radius: 50%;
    background-color: #000;
    background-repeat: no-repeat;
    background-position: center;
    box-sizing: border-box;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.innerNewsWrapper .newsShare .social li a:hover { background-color: #cc1153; }

.innerNewsWrapper .newsShare .social li a.facebook { background-image: url("//localhost:8580/skin/frontend/stenik/default/images/iconFacebookShare.**********.png"); }
.innerNewsWrapper .newsShare .social li a.googlePlus { background-image: url("//localhost:8580/skin/frontend/stenik/default/images/iconGooglePlusShare.**********.png"); }
.innerNewsWrapper .newsShare .social li a.twitter { background-image: url("//localhost:8580/skin/frontend/stenik/default/images/iconTwitterShare.**********.png"); }








.shopsDescription {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 30px 0px;
}

.shopsListingWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 5px 0px 30px 0px;
}

.shopsListingWrapper .shopsListing {
	float: left;
	width: 280px;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 20px 0px 0px;
}

.shopsListingWrapper .shopsListing .shopsBox {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 20px 0px;
	margin: 0px 0px 20px 0px;
	border-bottom: 1px solid #d9dbda;
}

.shopsListingWrapper .shopsListing .shopsBox .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
	color: #000;
    font-size: 25px;
    font-weight: 300;
    line-height: 27px;
    text-decoration: none;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shopsListingWrapper .shopsListing .shopsBox .title:hover { color: #cc1153; }

.shopsListingWrapper .shopsListing .shopsBox .textPage p { margin: 0px 0px 10px 0px; }

.shopsListingWrapper .shopsListing .shopsBox .viewOnMap { float: left; }
.shopsListingWrapper .shopsListing .shopsBox .viewMore { float: left; margin: 0px 0px 0px 10px; }

.shopsListingWrapper .shopsListingGoogleMap {
	float: left;
	width: 880px;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
}

.shopsListingWrapper .shopsListingGoogleMap #gmapContent {
	float: left;
	width: 880px;
	height: 700px;
	padding: 0px;
	margin: 0px;
}
.shopsListingWrapper .shopsListingGoogleMap #gmapIframes iframe{
	display: none;
}
.shopsListingWrapper .shopsListingGoogleMap #gmapIframes p:first-child iframe{
	display: block;
}


.responsiveBackToTop {
    float: left;
	width: 100%;
	height: 46px;
	padding: 0px 0px 0px 0px;
	margin: 20px 0px 0px 0px;
	background: #cc1153;
	border: none;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	font-size: 16px;
	line-height: 46px;
	color: #fff;
	font-weight: 400;
	text-align: center;
	text-decoration: none;
	outline: none;
	cursor: pointer;
	display: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.responsiveBackToTop:hover { background: #030303; }













.shopsInnerWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 30px 0px;
}

.shopsInnerWrapper .shopInfo {
	float: left;
	width: 50%;
	height: auto;
	padding: 0px 10px 0px 0px;
	margin: 0px 0px 30px 0px;
	box-sizing: border-box;
}

.shopsInnerWrapper .shopImage {
	float: left;
	width: 50%;
	height: auto;
	padding: 0px 0px 0px 10px;
	margin: 0px 0px 30px 0px;
	box-sizing: border-box;
}

.shopsInnerWrapper .shopImage img {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	box-sizing: border-box;
}

.shopsInnerWrapper .shopImageResponsive {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 10px;
	margin: 0px 0px 30px 0px;
	display: none;
	box-sizing: border-box;
}

.shopsInnerWrapper .shopImageResponsive img {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	box-sizing: border-box;
}

.shopsInnerWrapper .shopsInnerGoogleMap {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 30px 0px;
	box-sizing: border-box;
	position: relative;
}
.shopsInnerWrapper .shopsInnerGoogleMap #gmapContent {
	float: left;
	width: 100%;
	height: 500px;
	padding: 0px;
	margin: 0px;
}

.shopsInnerWrapper .gmapIframeView {
	float: left;
	width: 100%;
	height: auto;
	margin: 0px 0px 20px 0px;
}


.shopsInnerWrapper .shopsBackBtn {
	float: left;
	width: auto;
	height: 38px;
	padding: 0px 10px 0px 10px;
	margin: 0px 12px 0px 0px;
	border:1px solid #636363;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	font-size: 15px;
	line-height: 38px;
	color: #636363;
	text-transform: uppercase;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.shopsInnerWrapper .shopsBackBtn:hover {
	color: #fff;
	background: #636363;
}

.shopsInnerWrapper .shopsShare {
    float: right;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 0px 0px;
}

.shopsInnerWrapper .shopsShare .title {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 8px 0px 0px;
    font-size: 18px;
    line-height: 34px;
    color: #453933;
    font-weight: 400;
}

.shopsInnerWrapper .shopsShare .social {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 30px 0px;
    box-sizing: border-box;
}

.shopsInnerWrapper .shopsShare .social li {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 0px 0px;
    box-sizing: border-box;
}

.shopsInnerWrapper .shopsShare .social li a {
    float: left;
    width: 34px;
    height: 34px;
    padding: 0px;
    margin: 0px 5px 0px 0px;
    border-radius: 50%;
    background-color: #000;
    background-repeat: no-repeat;
    background-position: center;
    box-sizing: border-box;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.shopsInnerWrapper .shopsShare .social li a:hover { background-color: #cc1153; }

.shopsInnerWrapper .shopsShare .social li a.facebook { background-image: url("//localhost:8580/skin/frontend/stenik/default/images/iconFacebookShare.**********.png"); }
.shopsInnerWrapper .shopsShare .social li a.googlePlus { background-image: url("//localhost:8580/skin/frontend/stenik/default/images/iconGooglePlusShare.**********.png"); }
.shopsInnerWrapper .shopsShare .social li a.twitter { background-image: url("//localhost:8580/skin/frontend/stenik/default/images/iconTwitterShare.**********.png"); }














.compareProductsWrapper {
	float: left;
	width: auto;
	max-width: 1180px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 30px 0px;
	overflow-x: auto;
}


table.compareProductsTable {
	float: left;
	width: 100%;
	padding: 0px;
	margin: 0px;
	border-collapse: separate;
	box-sizing: border-box;
}

table.compareProductsTable td,
table.compareProductsTable th {
	width: 25%;
	height: auto;
	padding: 18px 25px 18px 25px;
	margin: 0px 0px 0px 0px;
	border: 1px solid #fff;
	position: relative;
	vertical-align: top;
	text-align: left;
	font-weight: 300;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

table.compareProductsTable td {
	background: #f6f6f6;
	font-size: 16px;
	line-height: 18px;
	color: #707070;
}

table.compareProductsTable td:hover {
	background: #ececec;
}

table.compareProductsTable th {
	background: #ececec;
	font-size: 20px;
	line-height: 22px;
	color: #000000;
}

table.compareProductsTable .compareImg {
	float: left;
	width: 220px;
	height: 220px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 12px 0px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

table.compareProductsTable td .compareImg:hover {
	opacity: 0.8;
}

table.compareProductsTable td .compareImg img {
	float: left;
	width: 220px;
	height: 220px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
}

table.compareProductsTable td .compareTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 12px 0px;
	font-size: 18px;
	line-height: 20px;
	color: #707070;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

table.compareProductsTable td:hover .compareTitle {	color: #000; }
table.compareProductsTable td .compareTitle:hover { color: #cc1153; }

table.compareProductsTable td .price-box {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
	z-index: 20;
	box-sizing: border-box;
}
table.compareProductsTable td .price-box .price-label { display: none; }

table.compareProductsTable td .price-box .regular-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
table.compareProductsTable td .price-box .regular-price .price {
	padding: 0px;
	margin: 0px;
	color: #707070;
	font-size: 18px;
	line-height: 20px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
table.compareProductsTable td:hover .price-box .regular-price .price { color: #707070; }

table.compareProductsTable td .price-box .old-price {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px 8px 0px 0px;
	position: relative;
}
table.compareProductsTable td .price-box .old-price:before {
	content: '';
	width: 55px;
	height: 18px;
	padding: 0px;
	margin: 0px;
	position: absolute;
	left: 0px;
	top: 3px;
	z-index: 5;
	background:url(//localhost:8580/skin/frontend/stenik/default/images/oldPriceLine.**********.png) no-repeat left top;
}
table.compareProductsTable td .price-box .old-price .price {
	padding: 0px;
	margin: 0px;
	color: #878787;
	font-size: 18px;
	line-height: 20px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
table.compareProductsTable td:hover .price-box .old-price .price { color: #878787; }

table.compareProductsTable td .price-box .special-price {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
}
table.compareProductsTable td .price-box .special-price .price {
	padding: 0px;
	margin: 0px;
	color: #cc1153;
	font-size: 18px;
	line-height: 20px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
table.compareProductsTable td:hover .price-box .special-price .price { color: #cc1153; }




table.compareProductsTable td .deleteItem {
	width: 17px;
	height: 17px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	background: url("//localhost:8580/skin/frontend/stenik/default/images/compareCloseIcon.**********.png") 0px 0px no-repeat;
	position: absolute;
	top: 5px;
	right: 5px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

table.compareProductsTable td .deleteItem:hover {
	background: url("//localhost:8580/skin/frontend/stenik/default/images/compareCloseIcon.**********.png") 0px -17px no-repeat;
}

table.compareProductsTable td .buy {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	background: #cc1153;
	font-size: 22px;
	line-height: 60px;
	color: #ffffff;
	font-weight: 400;
	text-decoration: none;
	text-transform: uppercase;
	text-align: center;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

table.compareProductsTable td .buy:hover {
	background: #030303;
}






















.shoppingCartContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 10px 0px 0px 0px;
}
.shoppingCartContent .shoppingCartItems {
	float: left;
	width: 880px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 20px 0px;
}
.shoppingCartContent .shoppingCartItems .row {
	float: left;
	width: 100%;
	height: auto;
	padding: 18px 0px 18px 0px;
	margin: 0px 0px 0px 0px;
	background: #fff;
	border-bottom: 1px solid #e4e4e4;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .row.headerRow { height: 56px; margin: 0px; padding: 0px; background: #f8f8f8; border-bottom: 1px solid #e4e4e4; }

.shoppingCartContent .shoppingCartItems .cell {
	float: left;
	min-width: 30px;
	padding: 0px;
	margin: 0px;
	color: #555;
	font-size: 14px;
	line-height: 18px;
	font-weight: normal;
	text-align: center;
	position: relative;
}
.shoppingCartContent .shoppingCartItems .headerRow .cell {
	height: 56px;
	line-height: 56px;
	padding: 0px;
	color: #000000;
	font-size: 20px;
	font-weight: 400;
}
.shoppingCartContent .shoppingCartItems .headerRow .cell.col1 {  text-align: left; padding-left: 20px; width: 280px; }

.shoppingCartContent .shoppingCartItems .col1 { width: 300px; text-align: left; }
.shoppingCartContent .shoppingCartItems .col2 { width: 120px; }
.shoppingCartContent .shoppingCartItems .col3 { width: 210px; }
.shoppingCartContent .shoppingCartItems .col4 { width: 120px; }
.shoppingCartContent .shoppingCartItems .col5 { width: 130px; }

.shoppingCartContent .shoppingCartItems .productIMGLink {
	float: left;
	width: 100px;
	height: 100px;
	padding: 0px;
	margin: 0px 12px 0px 0px;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .productIMGLink:hover {
	opacity: 0.8;
}
.shoppingCartContent .shoppingCartItems .productIMGLink img {
	display: block;
	max-width: 100px;
	height: auto;
	padding: 0px;
	margin: auto;
	position: relative;
	z-index: 5;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .cartInfoContent {
	float: left;
	width: 170px;
	height: auto;
	padding: 0px;
	margin: 12px 0px 0px 0px;
}
.shoppingCartContent .shoppingCartItems .itemTitle {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	font-size: 16px;
	line-height: 20px;
	color: #000000;
	font-weight: 300;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .itemTitle:hover { color: #cc1153; }

.shoppingCartContent .shoppingCartItems .itemOptionsBox {
	float: left;
	width: 100%;
	height: auto;
	margin: 0px;
	padding: 0px;
}

.shoppingCartContent .shoppingCartItems .price-box {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.shoppingCartContent .shoppingCartItems .price-box .price-label { display: none; }

.shoppingCartContent .shoppingCartItems .price-box .regular-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 22px 0px 0px 0px;
}
.shoppingCartContent .shoppingCartItems .price-box .regular-price .price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	font-size: 16px;
	line-height: 18px;
	color: #000000;
	font-weight: 700;
	text-align: center;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .price-box .old-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 12px 0px 0px 0px;
	position: relative;
}
.shoppingCartContent .shoppingCartItems .price-box .old-price .price {
	padding: 0px;
	margin: 0px;
	font-size: 16px;
	line-height: 16px;
	color: #878787;
	font-weight: normal;
	text-decoration: line-through;
	text-align: center;
	position: relative;
	z-index: 5;
}
.shoppingCartContent .shoppingCartItems .price-box .special-price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.shoppingCartContent .shoppingCartItems .price-box .special-price .price {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	color: #cc1153;
	font-size: 16px;
	line-height: 18px;
	text-align: center;
	font-weight: 700;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .amountBox {
	float: left;
	width: 120px;
	height: 40px;
	padding: 0px;
	margin: 12px 0px 0px 45px;
	position: relative;
}
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner {
	float: left;
	width: 120px;
	height: 40px;
	padding: 0px;
	margin: 0px;
	position: relative;
}
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner input.amount {
	float: left;
	width: 40px;
	height: 38px;
	line-height: 38px;
	padding: 0px;
	margin: 0px 0px 0px 40px;
	border: none;
	border-top: 1px solid #d9dbda;
	border-bottom: 1px solid #d9dbda;
	color: #000;
	font-size: 16px;
	background: none;
	text-align: center;
	font-weight: normal;
	text-decoration: none;
	background: #fff;
	font-family: "HKGrotesk","ArialBG",Arial,sans-serif;
}
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-up {
	float: left;
	width: 38px;
	height: 38px;
	padding: 0px;
	margin: 0px;
	border: 1px solid #d9dbda;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/spinnerPlus.**********.png) no-repeat center center;
	cursor: pointer;
	position: absolute;
	right: 0px;
	top: 0px;
	z-index: 10;
	-webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out;
}
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-up:hover { background-color: #cbcbcb; }
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-up .ui-button-text { display: none; }

.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-down {
	float: left;
	width: 38px;
	height: 38px;
	padding: 0px;
	margin: 0px;
	border: 1px solid #d9dbda;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/spinnerMinus.**********.png) no-repeat center center;
	cursor: pointer;
	position: absolute;
	left: 0px;
	top: 0px;
	z-index: 10;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-down:hover { background-color: #cbcbcb; }
.shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-button .ui-button-text { display: none; }

.shoppingCartContent .shoppingCartItems .amountBox .loader {
	width: 35px;
	height: 35px;
	padding: 0px;
	margin: 0px 0px 0px -17px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/preloader-28x28.**********.gif) no-repeat center center;
	position: absolute;
	left: 50%;
	top: 48px;
	z-index: 20;
}
.shoppingCartContent .shoppingCartItems .itemDelete {
	float: left;
	width: 22px;
	height: 30px;
	padding: 0px;
	margin: 17px 52px 0px 52px;
	background: #fff url(//localhost:8580/skin/frontend/stenik/default/images/itemDeleteIcon.**********.png) no-repeat 0px 0px;
	-webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out;
}
.shoppingCartContent .shoppingCartItems .row .itemDelete:hover { background: #fff url(//localhost:8580/skin/frontend/stenik/default/images/itemDeleteIcon.**********.png) no-repeat 0px -30px; }




.shoppingCartContent .underCartContent {
	float: right;
	width: 280px;
	height: auto;
	padding: 0px;
	margin: -62px 0px 0px 0px;
}
.shoppingCartContent .cartPromoBox {
	float: left;
	width: 280px;
	height: auto;
	padding: 16px 16px 16px 16px;
	margin: 0px 0px 0px 0px;
	position: relative;
	background: #f8f8f8;
	border: 1px solid #d9dbda;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .cartPromoBox .title {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px 0px 16px 0px;
	color: #000000;
	font-size: 20px;
	line-height: 22px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
}
.shoppingCartContent .cartPromoBox input.promoInput {
	float: left;
	width: 167px;
	height: 48px;
	padding: 0px 10px 0px 10px;
	margin: 0px 0px 0px 0px;
	background: #fff;
	border: 1px solid #d9dbda;
	outline: none;
	font-size: 18px;
	color: #000;
	font-weight: normal;
	text-decoration: none;
	font-family: "HKGrotesk","ArialBG",Arial,sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .cartPromoBox input.promoInput:hover { background: #fff; }
.shoppingCartContent .cartPromoBox input.promoInput:focus { background: #d8d8d8; }

.shoppingCartContent .cartPromoBox input.promoSubmit {
	float: right;
	width: 57px;
	height: 50px;
	line-height: 50px;
	padding: 0px 0px 0px 0px;
	margin: 0px;
	background: #030303 url("//localhost:8580/skin/frontend/stenik/default/images/promoSubmitIcon.**********.png") center center no-repeat;
	border: none;
	font-size: 20px;
	color: #fff;
	text-decoration: none;
	text-align: center;
	font-weight: normal;
	font-family: "HKGrotesk","ArialBG",Arial,sans-serif;
	outline: none;
	cursor: pointer;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.shoppingCartContent .cartPromoBox input.promoSubmit:hover { background: #cc1153 url("//localhost:8580/skin/frontend/stenik/default/images/promoSubmitIcon.**********.png") center center no-repeat; }

.shoppingCartContent .cartPromoBox .validation-advice {
    float: left;
    width: 223px;
    height: auto;
    padding: 10px 10px 10px 10px;
    margin: 0px;
    color: #ed1c24;
    font-size: 15px;
    line-height: 18px;
    text-align: left;
    font-weight: normal;
    text-decoration: none;
    background: #fff;
    border: 1px solid #ed1c24;
    position: absolute;
    left: 16px;
    top: 105px;
    z-index: 20;
}


.shoppingCartRight {
	float: right;
	width: 280px;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.shoppingCartRight .underCartTotalBox {
	float: left;
	width: 280px;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 20px 0px;
}
.shoppingCartRight .underCartTotalBox table.underCartTable {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	border-spacing: 0px;
	border-collapse: separate;
    border: 1px solid #d9dbda;
    background: #f8f8f8;
}
.shoppingCartRight .underCartTotalBox table.underCartTable th {
	width: 170px;
	padding: 8px 0px 8px 20px;
	margin: 0px 0px 0px 0px;
	color: #000;
	font-size: 16px;
	line-height: 18px;
	text-align: left;
	font-weight: normal;
	text-decoration: none;
}
.shoppingCartRight .underCartTotalBox table.underCartTable tfoot th { font-size: 22px; line-height: 24px; padding: 8px 0px 18px 20px; color: #000; border: none; }

.shoppingCartRight .underCartTotalBox table.underCartTable td {
	width: 130px;
	padding: 8px 20px 8px 0px;
	margin: 0px;
	font-size: 20px;
	line-height: 26px;
	color: #000;
	font-weight: normal;
	text-align: right;
	text-decoration: none;
}
.shoppingCartRight .underCartTotalBox table.underCartTable tfoot td { color: #000; font-size: 22px; padding: 8px 20px 18px 0px; line-height: 24px; border: none; }

.shoppingCartRight .underCartTotalBox table.underCartTable td .price {
	float: left;
	width: 100%;
	padding: 0px;
	margin: 0px;
	font-size: 16px;
	line-height: 18px;
	color: #000;
	text-align: right;
	font-weight: normal;
	text-decoration: none;
}
.shoppingCartRight .underCartTotalBox table.underCartTable tfoot td .price { font-size: 22px; line-height: 24px; color: #000; }

.underCartContent button.btn-checkout {
	float: right;
	width: 100%;
	height: 60px;
	line-height: 56px;
	padding: 0px 0px 4px 0px;
	margin: 0px 0px 0px 0px;
	box-sizing: border-box;
	background: #cc1153;
	border: none;
	border-radius: 0px;
	cursor: pointer;
	color: #fff;
	font-size: 18px;
	text-align: center;
	font-weight: normal;
	text-decoration: none;
	text-transform: none;
	font-family: "HKGrotesk", "ArialBG", Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.underCartContent button.btn-checkout:hover { background: #030303; }


.shoppingCartContent .backBtn {
	float: left;
	width: 218px;
	height: 38px;
	line-height: 38px;
	padding: 0px 0px 0px 0px;
	margin: 20px 0px 20px 0px;
	border: 1px solid #636363;
	color: #636363;
	font-size: 15px;
	font-weight: 400;
	text-align: center;
	text-decoration: none;
	-webkit-transition: all 0.3s ease-out; -moz-transition: all 0.3s ease-out; -o-transition: all 0.3s ease-out; transition: all 0.3s ease-out;
}
.shoppingCartContent .backBtn:hover { background-color: #cc1153; color: #fff; border: 1px solid #b50f4a; }

.shoppingCartContent .deliveryText {
	float: right;
	width: auto;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 28px 0px 20px 0px;
	position: relative;
	color: #cc1153;
	font-size: 15px;
	font-weight: 700;
	text-align: center;
	text-decoration: none;
}

.deliveryText:before {
	content: "";
	width: 22px;
	height: 17px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	position: absolute;
	top: 2px;
	left: -32px;
	background: url("//localhost:8580/skin/frontend/stenik/default/images/deliveryTextBefore.**********.png") 0px 0px no-repeat;
}












.textPage {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.cms-page-view .textPage { background: #fff; }

.textPage hr { height: 1px; padding: 0px; margin: 10px 0px 10px 0px; background: #dfdede; border: none; }

.textPage p {
	padding: 0px;
	margin: 0px 0px 20px 0px;
	font-size: 18px;
	line-height: 22px;
	color: #707070;
	font-weight: 300;
	text-decoration: none;
	word-wrap: break-word;
}
.productsTabInfo .ui-tabs-panel .textPage p { font-size: 16px; line-height: 20px; }

.textPage p strong {
	padding: 0px;
	margin: 0px 0px 20px 0px;
	font-size: 18px;
	line-height: 22px;
	color: #000;
	font-weight: 500;
	text-decoration: none;
}

.textPage p a {
	color: #cc1153;
	font-weight: normal;
	text-decoration: none;
	font-size: 18px;
	line-height: 22px;
	text-decoration: underline;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.textPage p a:hover { color: #030303; }

.textPage h2 {
	padding: 0px;
	margin: 30px 0px 30px 0px;
	font-size: 40px;
	line-height: 43px;
	color: #000;
	font-weight: 300;
	text-decoration: none;
}
.textPage h3 {
	padding: 0px;
	margin: 30px 0px 30px 0px;
	font-size: 35px;
	line-height: 38px;
	color: #000;
	font-weight: 300;
	text-decoration: none;
}
.textPage h4 {
	padding: 0px;
	margin: 30px 0px 30px 0px;
	font-size: 30px;
	line-height: 34px;
	color: #000;
	font-weight: 300;
	text-decoration: none;
}
.textPage h5 {
	padding: 0px;
	margin: 30px 0px 30px 0px;
	font-size: 25px;
	line-height: 28px;
	color: #000;
	font-weight: 300;
	text-decoration: none;
}
.textPage h6 {
	padding: 0px;
	margin: 30px 0px 30px 0px;
	font-size: 21px;
	line-height: 25px;
	color: #000;
	font-weight: 300;
	text-decoration: none;
}
.textPage.storeAvailabilityContent h6 { margin-top: 0px; }

.textPage blockquote {
	display: block;
	clear: both;
	padding: 8px 0px 8px 25px;
	margin: 0px 0px 18px 45px;
	border-left: 2px solid #000;
	font-size: 18px;
	line-height: 24px;
	text-align: left;
	font-weight: normal;
	font-style: italic;
	color: #888;
	position: relative;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/blockquoteBG.**********.png) no-repeat left 20px;
}
.textPage blockquote p { color: #888; font-size: 18px; line-height: 24px; }

.textPage ul { margin: 0px 0px 10px 0px; padding: 0px; }

.textPage ul li {
    display: block;
    margin-left: 0px;
    margin-bottom: 2px;
    padding: 4px 0px 4px 25px;
    color: #707070;
    font-size: 18px;
    line-height: 22px;
    font-weight: normal;
    text-decoration: none;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/textPageBullets.**********.png) no-repeat 0px 13px;
}
.textPage ul li a {
	color: #cc1153;
	font-weight: normal;
	text-decoration: underline;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.textPage ul li a:hover { color: #030303; }

.textPage ol li {
    margin-left: 0px;
    margin-bottom: 2px;
    padding: 5px 0px 8px 20px;
    color: #707070;
    font-size: 18px;
    line-height: 22px;
    font-weight: normal;
    text-decoration: none;
}
.textPage ol li a {
	color: #cc1153;
	font-weight: normal;
	text-decoration: underline;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.textPage ol li a:hover { color: #030303; }


.textPage table.styles {
	width: 100%;
	height: auto;
	padding: 0px 0px 0px 0px;
	margin: 20px 0px 20px 0px;
	border-spacing: 2px;
    border-collapse: separate;
	border: none;
}
.textPage table.styles.attributes { float: right; width: 580px; margin: 0px 0px 0px 20px; }

.textPage table.styles tr:nth-child(2n) td { background: #ececec; }

.textPage table.styles td {
	padding: 12px 10px 12px 20px;
	vertical-align: top;
	background: #f8f8f8;
	color: #000;
	font-size: 16px;
	line-height: 18px;
	text-align: left;
	font-weight: normal;
}
.textPage table.styles.attributes td { width: 50%; }
.textPage table.styles td a { display: inline-table; font-size: 13px; color: #ee2749; text-decoration: underline; }


.cms-no-route .textPage {
    float: none;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0 auto 100px;
    text-align: center;
}

.cms-no-route .textPage h2 {
    text-align: center;
}

.cms-no-route .textPage p { text-align: center; }

.cms-no-route .textPage p img { margin: 30px 0px 30px 0px; }

.cms-no-route .textPage .linkBtn {
    display: inline-block;
    width: auto;
    height: 42px;
    padding: 0px 12px 0px 12px;
    margin: 20px auto;
    font-size: 16px;
    line-height: 42px;
    color: #fff;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    position: relative;
    background-color: #cc1153;
    border: none;
    position: relative;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.cms-no-route .textPage .linkBtn:hover {
    background-color: #030303;
}


















aside.leftCol {
	float: left;
	width: 280px;
	height: auto;
	min-height: 200px;
	padding: 0px 0px 0px 0px;
	margin: 0px 0px 0px 0px;
	background: #f8f8f8;
}
aside.leftCol .leftMenu {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px 0px 0px 0px;
    margin: 0px 0px 0px 0px;
    box-sizing: border-box;
}

aside.leftCol .leftMenu ul {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

aside.leftCol .leftMenu ul li {
    width: 100%;
    height: auto;
    padding: 0;
    margin: 0;
    display: block;
    list-style: none;
    box-sizing: border-box;
}

aside.leftCol .leftMenu ul li a {
    width: 100%;
    height: auto;
    padding: 25px 30px 25px 30px;
    margin: 0px 0px 0px 0px;
    border-bottom: 2px solid #fff;
    border-right: 2px solid #eeeeee;
    display: block;
    list-style: none;
    font-size: 20px;
    line-height: 24px;
    color: #000;
    text-decoration: none;
    position: relative;
    box-sizing: border-box;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
aside.leftCol .leftMenu ul li a:hover { color: #cc1153; }
aside.leftCol .leftMenu ul li.selected a { color: #cc1153; }
aside.leftCol .leftMenu ul li.current a { color: #cc1153; }
aside.leftCol .leftMenu ul li.active a { color: #cc1153; }

aside.leftCol .block-compare { display: none; }



















/* Profile styles
********************/

#main .mainContent .my-account { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 20px 0px; }

section#main.col2-left-layout .mainContent .my-account h1 { padding-left: 0px; padding-right: 0px; }
.sales-order-view section#main.col2-left-layout .mainContent .my-account h1 { padding-left: 0px; padding-right: 0px; margin-bottom: 10px; }

#main .mainContent .my-account .col2-set .col-1 {
    float: left;
    width: 48.5%;
}
#main .mainContent .my-account .col2-set .col-2 {
    float: right;
    width: 48.5%;
}
#main .mainContent .my-account h2 {
    color: #322f31;
    font-size: 20px;
    line-height: 24px;
    margin: 10px 0 6px;
    padding: 0px;
    font-weight: bold;
    text-transform: uppercase;
    text-decoration: none;
    font-family: "Open Sans Condensed", sans-serif;
}
#main .mainContent .my-account h2.table-caption { float:none; display:block; margin: 25px 0px 10px 0px; }
.customer-address-form #main .mainContent .my-account h2 { margin: 10px 0px 12px 0px; }

#main .mainContent .my-account h2.table-caption a {
    color:#82970C;
    text-decoration:underline;
    font-size:16px;
    line-height:18px;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .mainContent .my-account h2.table-caption a:hover { color: #FF7E00; }

.sales-order-view #main .mainContent .my-account .page-title { }
.sales-order-shipment .mainContent .my-account .page-title { }
.sales-order-invoice .mainContent .my-account .page-title { }

#main .mainContent .my-account p {
    color: #000;
    font-size: 13px;
    font-weight: 300;
    line-height: 18px;
    margin: 0 0 7px;
    padding: 0px;
    text-decoration: none;
}
#main .mainContent .my-account p a { color: #111; text-decoration: underline; }
#main .mainContent .my-account p a:hover { text-decoration: none; }

#main .mainContent .my-account .page-title {
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 10px 0px;
}

#main .mainContent .my-account .page-title h1 {
    padding: 0px;
    margin: 0px 0px 18px 0px;
    font-size: 26px!important;
    line-height: 28px!important;
    color: #030303!important;
    font-weight: 700!important;
    text-transform: uppercase;
}

#main .mainContent .my-account .limiter select {
    width: 65px;
    margin: 5px 3px 15px 3px;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .mainContent .my-account .limiter select:focus { border: 1px solid #111111; }

#main .mainContent .my-account .data-table {
    float: left;
    width: 100%;
    padding: 0px;
    margin: 5px 0px 20px 0px;
    border-spacing: 0px;
    border-collapse: separate;
    border: none;
}
#main .mainContent .my-account .data-table th {
    height: auto;
    padding: 10px 0px 10px 0px;
    background: #f5f5f5;
    border: none;
    border-bottom: 1px solid #e5e5e5;
    color: #030303;
    font-size: 14px;
    line-height: 17px;
    font-weight: bold;
    text-align: center;
    text-decoration: none;
    font-family: "Open Sans Condensed", sans-serif;
}
#main .mainContent .my-account .data-table th.first { text-align: left; }


#main .mainContent .my-account .data-table td {
    height: auto;
    margin: 0px;
    padding: 10px 0px;
    color: #0e0e0e;
    font-weight: normal;
    font-size: 14px;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
}
#main .mainContent .my-account .data-table td.a-right { text-align: right; }

#main .mainContent .my-account .data-table td a {
    color: #cc1153;
    font-weight: normal;
    text-transform: none;
    text-decoration: underline;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .mainContent .my-account .data-table td a:hover { color: #030303; }

#main .mainContent .my-account .data-table td a.link-reorder { color: #cc1153; }
#main .mainContent .my-account .data-table td a.link-reorder:hover { color: #030303; }

#main .mainContent .my-account .data-table td a.link-edit { font-size: 13px; margin-left: 10px; }

#main .mainContent .my-account .data-table td a.product-title {
    display: block;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 6px 0px 6px 0px;
    color: #90AC19;
    font-weight: normal;
    text-decoration: none;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    text-shadow: 0px 1px 1px #d9d9d9;
}
#main .mainContent .my-account .data-table td a.product-image {
    float: left;
    width: 88px;
    height: 88px;
    margin: 0 15px 0 0;
    padding: 1px;
    box-shadow: 0 1px 1px #D1D1D1;
}
#main .mainContent .my-account .data-table td a.product-image img { float: left; width: 88px; height: 88px; padding: 0px; margin: 0px; }

#main .mainContent .my-account .data-table td h3.product-name { font-size: 14px; margin: 0px; padding: 0px; }
.sales-order-view #main .mainContent .my-account .data-table td h3.product-name { width: 300px; display: inline-block; }

.wishlist-index-index #main .mainContent .my-account .data-table td h3.product-name { margin: 0px 0px 10px 0px; text-align: left; }
.wishlist-index-index #main .mainContent .my-account .data-table td h3.product-name a {
    margin: 0px;
    padding: 0px;
    font-size: 14px;
    line-height: 16px;
    font-weight: bold;
    text-decoration: none;
    color: #030303;
    font-family: "Open Sans Condensed", sans-serif;
}
.wishlist-index-index #main .mainContent .my-account .data-table td .description { margin-bottom: 10px; }
.wishlist-index-index #main .mainContent .my-account .data-table td .description .inner { font-size: 13px; line-height: 15px; text-align: left; }

#main .mainContent .my-account .data-table td dl dt { padding: 0px; margin: 0px; display: inline-block; }
#main .mainContent .my-account .data-table td dl dd { padding: 0px; margin: 0px; display: inline-block; }

#main .mainContent .my-account .data-table td textarea { width: 240px; height: 75px; }

.wishlist-index-index #main .mainContent .my-account .data-table td textarea { width: 370px; height: 75px; }

#main .mainContent .my-account .data-table td .cart-cell {
    float: left;
    width: 206px;
    height: auto;
    padding: 0px 30px 0px 30px;
    margin: 0px 0px 0px 10px;
}
#main .mainContent .my-account .data-table td .cart-cell .add-to-cart-alt {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 5px 0px;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box {
    float:left;
    width:100%;
    height:24px;
    line-height:24px;
    padding:0px;
    margin:0px 0px 5px 0px;
    position:relative;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .price-label { display:none; }
#main .mainContent .my-account .data-table td .cart-cell .price-box .regular-price {
    float:left;
    width:100%;
    height:24px;
    padding:0px;
    margin:0px;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .regular-price span.price {
    width:100%;
    padding:0px;
    margin:0px;
    color:#030303;
    font-size:16px;
    line-height:20px;
    text-decoration:none;
    font-weight:normal;
    text-align:center;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .special-price {
    display: inline-block;
    width:auto;
    height:24px;
    padding:0px;
    margin:0px;
    text-align: center;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .special-price span.price {
    width:100%;
    padding:0px;
    margin:0px;
    color:#b40000;
    font-size:16px;
    line-height:20px;
    text-decoration:none;
    font-weight:normal;
    text-align:center;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .old-price {
    display: inline-block;
    width:auto;
    height:24px;
    padding:0px;
    margin:0px;
    text-align: center;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .old-price span.price {
    width:100%;
    padding:0px;
    margin:0px;
    color:#888;
    font-size: 16px;
    line-height:20px;
    text-decoration: line-through;
    font-weight:normal;
    text-align:center;
}
#main .mainContent .my-account .data-table td .cart-cell .price-box .minimal-price-link { display: none; }

#main .mainContent .my-account .data-table td .cart-cell input.qty {
    float: left;
    width: 70px;
    height: 30px;
    padding: 2px 0px 0px 0px;
    margin: 0px 0px 6px 68px;
    border: 1px solid #dbdbdb;
    border-radius: 3px;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/amountBoxBG.**********.jpg) repeat-x left top;
    font-size: 18px;
    line-height: 30px;
    color: #4c572d;
    text-decoration: none;
    font-weight: normal;
    text-align: center;
    font-family: "Open Sans Condensed", sans-serif;
    outline: none;
}
#main .mainContent .my-account .data-table td .cart-cell p { text-align: center; }

#main .mainContent .my-account .data-table td p {
    display: block;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 0px 4px 0px;
    color: #444;
    font-weight: normal;
    text-decoration: none;
    font-size: 15px;
    line-height: 18px;
    text-align: left;
}
#main .mainContent .my-account .data-table td p strong { font-weight: normal; }
#main .mainContent .my-account .data-table td .btn-remove { margin: 30px 5px 0px 0px; float: left; }

#main .mainContent .my-account .order-comments { padding-top: 15px; clear: both; display: block; }
#main .mainContent .my-account .order-comments dl.order-about dd { margin-left: 0px; }

.wishlist-index-index .my-wishlist fieldset { padding: 0px; margin: 0px; border: none; }
.wishlist-index-index .my-wishlist h1 {  margin:0px 0px 5px 0px; }
.wishlist-index-configure #messages_product_view { margin-left: 336px; }


#main .mainContent .my-account .paging {
    float: right;
    width: auto;
    height: 35px;
    padding: 0px;
    margin: 0px 0px 0px 0px;
}
#main .mainContent .my-account .paging a {
    float: left;
    width: 20px;
    height: 21px;
    line-height: 21px;
    padding: 2px 3px 2px 2px;
    margin: 5px 0px 5px 0px;
    font-size: 14px;
    color: #989898;
    font-weight: normal;
    text-align: center;
    text-decoration: none;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/pagingLine.**********.jpg) no-repeat right center;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .mainContent .my-account .paging a:hover { background-color: #ddd; }
#main .mainContent .my-account .paging a.selected { color: #ea7b5d; font-weight: bold; }

#main .mainContent .my-account .paging a.prev { background: url(//localhost:8580/skin/frontend/stenik/default/images/pagingPrev.**********.png) no-repeat center center; margin-right: 4px; }
#main .mainContent .my-account .paging a.prev:hover { opacity: 0.7; }

#main .mainContent .my-account .paging a.next { background: url(//localhost:8580/skin/frontend/stenik/default/images/pagingNext.**********.png) no-repeat center center; margin-left: 4px; }
#main .mainContent .my-account .paging a.next:hover { opacity: 0.7; }



.wishlist-shared-index #main .mainContent .my-account { width: 965px; margin: 20px 0px 0px 0px; }
.wishlist-shared-index #main .mainContent .my-account .data-table { width: 965px; }
.wishlist-shared-index #main .mainContent .my-account p.back-link { display: none; }
.wishlist-shared-index #main .mainContent .my-account .data-table h2 { font-size: 14px; line-height: 16px; }
.wishlist-shared-index #main .mainContent .my-account .data-table h2 a { font-size: 14px; line-height: 16px; }
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .price-label { display: none; }
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .regular-price {
    display: inline-block;
    width:auto;
    height:24px;
    padding:0px;
    margin:0px;
}
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .regular-price span.price {
    width:100%;
    padding:0px;
    margin:0px;
    color:#444;
    font-size:14px;
    line-height:24px;
    text-decoration:none;
    font-weight:normal;
    text-align:center;
}
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .special-price {
    display: inline-block;
    width:auto;
    height:24px;
    padding:0px;
    margin:0px;
    text-align: center;
}
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .special-price span.price {
    width:100%;
    padding:0px;
    margin:0px;
    color:#444;
    font-size:14px;
    line-height:24px;
    text-decoration:none;
    font-weight:normal;
    text-align:center;
}
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .old-price {
    display: inline-block;
    width:auto;
    height:24px;
    padding:0px;
    margin:0px;
    text-align: center;
}
.wishlist-shared-index #main .mainContent .my-account .data-table .price-box .old-price span.price {
    width:100%;
    padding:0px;
    margin:0px;
    color:#444;
    font-size:13px;
    line-height:24px;
    text-decoration: line-through;
    font-weight:normal;
    text-align:center;
}
.wishlist-shared-index #main .mainContent .my-account .data-table .link-wishlist {
    float: right;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 10px 86px 0px 0px;
    clear: both;
}
.wishlist-shared-index #main .mainContent .my-account .data-table button.button { float: right; margin: 0px 40px 0px 0px; }

#main .mainContent .my-account .box-head h2 {
    margin: 17px 0 10px;
    padding: 0px;
    color: #322f31;
    font-size: 24px;
    font-weight: bold;
    line-height: 27px;
    text-decoration: none;
    font-family: "Open Sans Condensed", sans-serif;
}
.customer-account-index #main .mainContent .my-account .box-head h2 { float:left; }

#main .mainContent .my-account .box-head a {
    float: right;
    padding: 0px;
    margin: 32px 0px 0px 0px;
    color: #cc1153;
    font-size: 12px;
    line-height: 13px;
    font-weight: bold;
    text-decoration: none;
    font-family: "Open Sans Condensed", sans-serif;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .mainContent .my-account .box-head a:hover { color: #030303; }

#main .mainContent .my-account .box-title {
    float: left;
    width: 100%;
    padding-bottom: 4px;
    margin-bottom: 10px;
    margin-top:15px;
    border-bottom: 1px solid #e5e5e5;
}
#main .mainContent .my-account .box-title h3,
#main .mainContent .my-account .box-title h2 {
    float: left;
    color: #0e0e0e;
    font-size: 16px;
    font-weight: bold;
    line-height: 20px;
    margin: 8px 0 6px;
    padding: 0px;
    text-decoration: none;
    font-family: "Open Sans Condensed", sans-serif;
}

#main .mainContent .my-account .box-title a {
    float: right;
    padding: 0px;
    margin: 11px 0px 0px 0px;
    color: #cc1153;
    font-size: 12px;
    line-height: 13px;
    font-weight: bold;
    text-decoration: none;
    font-family: "Open Sans Condensed", sans-serif;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
#main .mainContent .my-account .box-title a:hover { color: #030303; }


.customer-address-index #main .mainContent .my-account button.button { clear: both; float: left; margin: 0px 0px 10px 0px; }

.mainContent .my-account .my-wishlist table#wishlist-table button.button { margin: 0px 0px 0px 0px; width: 225px; }
.mainContent .my-account .my-wishlist table#wishlist-table button.button:hover { }

#main .mainContent .my-account .box-content h4 {
    margin-top:5px;
    margin-bottom:5px;
    font-size: 14px;
    line-height: 16px;
    font-weight: bold;
    font-family: "Open Sans Condensed", sans-serif;
}
#main .mainContent .my-account .box-content address a {
    color:#555;
    text-decoration:underline;
}
#main .mainContent .my-account .box-content address a:hover {
    text-decoration:none;
}
#main .mainContent .my-account .box-content p a {
    color:#555!important;
    text-decoration:underline;
}
#main .mainContent .my-account .box-content p a:hover {
    text-decoration:none;
}
#main .mainContent .my-account .welcome-msg {
    float:left;
    width:100%;
    margin-bottom:5px;
}
#main .mainContent .my-account #street_2 {
    display:none;
}
.customer-account-edit .my-account h2 { display:none; }

.customer-account-edit .my-account .customer-name { width: 350px; }

.customer-account-edit .back-link {
    display:none;
}
.customer-account-edit li.control {
    line-height:18px!important;
    float:left;
    width:450px;
}
.customer-account-edit li.control label {
    float:left;
    width:auto!important;
}
.customer-account-edit ul.form-list {
    padding:0px;
    margin:0px;
    background: none;
}
.customer-account-edit ul.form-list li {
    padding:0px;
    margin:0px;
    background: none;
}
.customer-account-edit .form-list label {
    float: left;
    width: 90%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 4px 0px;
    font-size: 14px;
    line-height: 16px;
    color: #322f31;
    text-decoration: none;
    font-weight: normal;
}
.customer-account-edit .form-list label span.star { color: #ee2749; }


.customer-account-edit .form-list label.radioLabel {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: 0px 15px 0px 0px;
    font-size: 13px;
    line-height: 15px;
    color: #888;
    font-weight: normal;
    text-align: left;
    text-decoration: none;
}

.customer-account-edit .form-list input.radio {
    float: left;
    width: auto;
    height: auto;
    margin: 2px 6px 0px 0px;
    padding: 0px;
}

.customer-account-edit .form-list .genderTitle {
    display: inline-block;
    float: left;
    height: auto;
    margin: 0;
    padding: 0 12px 0 0;
    width: auto;
    font-size: 14px;
    line-height: 14px;
}

.mainContent .my-account .form-list input.input-text.no-display { display: none; }

.mainContent .my-account .form-list .validation-advice {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: -5px 0px 10px 0px;
    color: #fb3b3b;
    font-size: 13px;
    line-height: 15px;
    text-decoration: none;
    font-weight: normal;
    clear: both;
}

.mainContent .my-account .inchoo-socialconnect-account .col-1 { width: 100% !important; }

.mainContent .my-account a.facebooklogin {
    float: left;
    width: 200px;
    height: 35px;
    line-height: 35px;
    margin: 10px 0 0;
    padding: 0;
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    text-align: center;
    text-decoration: none;
    background: url("//localhost:8580/skin/frontend/stenik/default/images/facebookloginIcon.**********.png") no-repeat scroll 10px 5px #3e62a0;
    border-radius: 5px;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.mainContent .my-account a.facebooklogin:hover { opacity: 0.7; }

.customer-account-edit p.required { display:none; }
.customer-account-edit .form-list li.control { margin-top:10px; }
.customer-account-edit .form-list li.control #change_password {
    float:left;
    width: auto;
    padding: 0px;
    margin: 0px 6px 12px 0px;
}
.customer-account-edit .form-list li.control label {
    clear: none;
    float: left;
    height: 14px;
    line-height: 14px;
    text-align: left!important;
    margin-bottom: 10px;
}
.customer-account-edit .main .col-main ul.form-list li label { width:195px!important; }

.customer-account-edit .my-account .buttons-set p.required { display:none; }
.customer-account-edit .my-account .buttons-set p.back-link { display:none; }

.customer-account-edit .fieldset {
    float:left;
    width:100%;
    padding:0px;
    margin:0px;
}
.customer-account-edit .fieldset h2.legend { margin-top:10px; }
.customer-account-edit #main .mainContent .buttons-set { margin-top: 0px; }


.mainContent .my-account button.button { }


.customer-address-form #main .my-account input#invoice_include_checkbox {
    float:left;
    width: auto;
    padding:0px;
    margin:0px 8px 0px 0px !important;
}
.customer-address-index .my-account .buttons-set p.back-link { display:none; }


.customer-address-form #main .my-account .regInfo ul li.fields label.addressLabel { clear: none; width: auto; float: left; }
.customer-address-form #main .my-account .regInfo ul li.fields #invoice_dds { width: auto; float: left; padding: 0px; margin: -2px 0px 0px 8px; clear: none; }
.customer-address-form ul.form-list li.control label.addressLabel2 {
    float: left;
    width: 290px;
    height: auto;
    clear: none;
    margin: 4px 0px 5px 5px;
    font-size: 14px;
    line-height: 16px;
}

.customer-address-form #main .mainContent .my-account .buttons-set p.required { display:none; }
.customer-address-form #main .mainContent .my-account .buttons-set p.back-link { float:right; padding:0px; margin:16px 145px 0px 0px; }

.customer-address-form .my-account .form-list .validation-advice {
    float: left;
    width: auto;
    height: auto;
    padding: 0px;
    margin: -10px 0px 18px 0px;
    color: #fb3b3b;
    font-size: 13px;
    line-height: 15px;
    text-decoration: none;
    font-weight: normal;
    clear: both;
}

.customer-address-form .fieldset {
    float: left;
    width: 100%;
    margin: 0px 40px 0px 0px;
}
.customer-address-form .fieldset input.input-text { }
.customer-address-form .fieldset select { }
.customer-address-form .fieldset.personal-info li.fields { margin: 0 -12px; }
.customer-address-form .fieldset.personal-info li.fields .field{ float: left; width: 50%; padding: 0 12px; box-sizing: border-box; }
.customer-address-form .fieldset .invoiceFields {
    float:left;
    width:100%;
    padding:0px;
    margin:15px 0px 0px 0px;
}
.customer-address-form .form-list label {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px 0px 4px 0px;
    font-size: 14px;
    line-height: 16px;
    color: #322f31;
    text-decoration: none;
    font-weight: normal;
}
.customer-address-form .form-list label span.star { color: #ee2749; }

.customer-address-form .form-list li.control label { width: 90%; clear: none; display: inline-block; margin: 6px 0px 4px 0px; }

.customer-address-form .form-list select.no-display { display: none; }


.customer-address-form ul.form-list { padding:0px; margin:0px; }
.customer-address-form ul.form-list li { padding:0px; margin:0px; background: none; }


#main .mainContent .my-account .buttons-set {
    float:left;
    width:100%;
    padding:0px;
    margin:20px 0px 0px 0px;
}
.customer-address-form #primary_billing {
    float: left;
    width: auto;
    margin: 7px 5px 5px 0px;
}
.customer-address-form #primary_shipping {
    float:left;
    width: auto;
    margin: 7px 5px 5px 0px;
}

.customer-address-form #invoice {
    float:left;
    width: auto;
    margin: 7px 5px 5px 0px;
}

.customer-address-form .my-account ul.form-list li.control label.checkboxText {
    font-weight:normal;
    float:left;
    width:275px;
    line-height:15px;
    padding:0px;
    margin: 6px 0px 15px 10px;
    clear: none;
}
.customer-address-form .my-account ul.form-list li.control label.checkboxText {
    font-weight:normal;
    float:left;
    width:275px;
    line-height:15px;
    padding:0px;
    margin: 6px 0px 0px 10px;
    clear: none;
}
.customer-address-index .my-account h3 {
    background:none;
    padding:0px 0px 0px 0px;
    margin:10px 0px 5px 0px;
    color:#030303;
    font-size:14px;
    text-decoration:none;
    font-weight:normal;
    line-height:16px;
}

.customer-address-index .my-account address,
.customer-account-index .my-account address {
	font-size: 14px;
	line-height: 16px;
	margin:  0px 0px 6px 0px;
}

.customer-address-index .back-link {
    display:none;
}
.sales-order-history .back-link {
    display:none;
}
.newsletter-manage-index ul.form-list { padding:0px; margin:0px 0px 10px 0px; }
.newsletter-manage-index ul.form-list li { padding:0px; margin:0px 0px 0px 0px; background: none; }


.newsletter-manage-index .my-account .buttons-set { margin-top:0px; }

.order-info dd {
    padding: 0px;
    margin: 10px 0px 10px 0px;
}
.order-info dd ul {
    margin-left: 0px;
    padding-left: 0px;
}

.order-info dd ul li {
    background: url(//localhost:8580/skin/frontend/stenik/default/images/textPageBullets.**********.jpg) no-repeat 8px 8px;
    color: #030303;
    display: block;
    font-size: 14px;
    font-weight: normal;
    line-height: 16px;
    margin-left: 0px;
    padding-bottom: 2px;
    padding-left: 22px;
    padding-top: 2px;
    text-decoration: none;
}
.order-info dd ul li a {
    display: inline-table;
    line-height: 14px;
    color: #ee2749;
    text-decoration: none;
    border-bottom: 1px solid #ee2749;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.order-info dd ul li a:hover { color: #607b8b; }


.sales-order-view .back-link {
    margin-right:0px!important;
    margin-top: 10px;
    border: none;
}
.sales-order-view .back-link a { color: #cc1153 !important; text-decoration: none !important; font-weight: bold; }

.sales-order-view #main .mainContent .my-account h2 {
    font-weight: normal !important;
    font-size: 14px !important;
    line-height: 16px !important;
    text-transform: uppercase;
}

.link-reorder {
    color: #cc1153;
    text-decoration: underline;
    font-weight: bold;
    transition: all 0.2s linear 0s;
}
.link-reorder:hover { color: #e12020; }

.link-print {
    color: #cc1153;
    text-decoration: underline;
    font-weight: bold;
    transition: all 0.2s linear 0s;
}
.link-print:hover { color: #e12020; }

.sales-order-view .subtotal td {
    padding-right:10px;
    font-size:12px;
}
.sales-order-view .shipping td {
    padding-right:10px;
    font-size:12px;
}
.sales-order-view .grand_total td {
    padding-right:10px;
    font-size:12px;
}
.sales-order-view td.last.a-right {
    text-align:center!important;
    padding-right:0px!important;
}
.newsletter-manage-index #subscription {
    float: left;
    margin-right: 7px;
    margin-top: 3px!important;
}
.newsletter-manage-index #form-validate li.control label {
    float:left;
    width:200px;
    line-height:18px;
    text-align:left;
}
.newsletter-manage-index #form-validate h2 {
    display:none;
}
.newsletter-manage-index .back-link {
    display:none;
}

.main .col-main .my-account ul#order-info-tabs {
    margin-bottom: 10px;
    margin-left: 0;
    margin-top: 10px;
    padding: 0;
}
.main .col-main .my-account ul#order-info-tabs li {
    background: url(//localhost:8580/skin/frontend/stenik/default/images/textPageBullets.**********.png) no-repeat 3px 7px;
    color: #626262;
    display: block;
    font-size: 13px;
    font-weight: normal;
    line-height: 16px;
    margin-left: 20px;
    padding-bottom: 2px;
    padding-left: 18px;
    padding-top: 2px;
    text-decoration: none;
}
.main .col-main .my-account ul#order-info-tabs li a {
    color: #7A4C93;
    text-decoration: underline;
}
.main .col-main .my-account ul#order-info-tabs li a:hover {
    text-decoration: none;
}
.sales-order-history .pager {
    float:left;
    width:100%;
    text-align:left;
    margin:10px 0px 10px 0px;
    padding:0px;
}
.main .col-main .my-account a.link-print {
    color: #7A4C93;
    text-decoration: underline;
}
.main .col-main .my-account a.link-print:hover {
    text-decoration:none;
}
.main .col-main .my-account .link-reorder {
    color: #7A4C93;
    text-decoration: underline;
}
.main .col-main .my-account .link-reorder:hover {
    text-decoration:none;
}
.sales-order-history .pager .amount { float:right!important; }
.main .col-main .my-account .field.name-firstname, .main .col-main .my-account .field.name-lastname { margin-top:5px; }
.main .col-main .my-account li.item p a { color:#776C68!important; }

.main .col-main .my-account .limiter select {
    margin:0px 5px 0px 0px;
    width: 60px;
}
.main .col-main .my-account .limiter label {
    float:left;
    margin-right:5px;
    margin-top:2px;
}
.sales-order-history .pages {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px;
}
.sales-order-history .pages strong {
    float: left;
    width: auto;
    height: 20px;
    line-height: 20px;
    padding: 0px;
    margin: 0px;
}
.sales-order-history .pages ol {
    float: left;
    width: auto;
    height: 20px;
    line-height: 20px;
    padding: 0px;
    margin: 0px;
    list-style: none;
}
.sales-order-history .pages ol li {
    float: left;
    width: auto;
    height: 20px;
    line-height: 20px;
    padding: 0px;
    margin: 0px 0px 0px 4px;
}
.sales-order-history .pages ol li a {
    float: left;
    width: auto;
    height: 20px;
    line-height: 20px;
    padding: 0px;
    margin: 0px;
    font-size: 13px;
    line-height: 17px;
    color: #D62027;
    font-weight: normal;
    text-decoration: underline;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.sales-order-history .pages ol li a:hover { color: #030303; }


.main .col-main .my-account #my-orders-table {
    margin-top:5px!important;
    margin-bottom:15px!important;
}
.main .col-main .my-account #my-rewardpoints-table {
    margin-top:0px!important;
    margin-bottom:15px!important;
}
.main .col-main .my-account #my-orders-table th {
    line-height:15px;
    padding-left:4px;
    padding-right:4px;
}
.main .col-main .my-account #my-orders-table td {
    padding-left:4px;
    padding-right:4px;
}

.main .col-main .my-account #my-orders-table td dl {
    padding:0px;
    margin:0px;
}
.main .col-main .my-account #my-orders-table td dd {
    padding:0px;
    margin:0px;
}
.my-account #my-orders-table tr.shipping { float: none; }

.main .col-main .my-account .order-items {
    margin-top:15px;
}
.main .col-main .my-account .col-left .cartHeader {
    display:none!important;
}
.main .col-main .my-account .col-left p a {
    color: #0095DA;
}
.main .col-main .my-account .my-account #form-validate {
    background:none;
    border:none;
    width:708px;
    padding-left:0px;
    padding-right:0px;
}
.newsletter-manage-index #form-validate {
    padding:0px!important;
}
.main .col-main .my-account .data-table {
    border: none;
    border-spacing:1px;
    empty-cells: show;
    font-size: 100%;
    margin-bottom: 0;
    margin-top: 15px;
    width: 100%;
}
.main .col-main .my-account .data-table colgroup {
    width: 918px !important;
}
.main .col-main .my-account .data-table th {
    height: 33px ;
    line-height: 33px;
    vertical-align: middle;
    background:#EDEDED;
    color:#626262;
    font-size:13px;
    text-align:center;
    border-radius:2px;
    overflow:hidden;
    font-weight:normal;
    border:1px solid #EDEDED;
}
.main .col-main .my-account .data-table td {
    border:1px solid #c5c5c5;
    padding-top:4px;
    padding-bottom:4px;
    text-align:center;
    color:#666;
    font-size:12px;
    border-radius:2px;
    overflow:hidden;
}
.sales-order-shipment .main .col-main .my-account .data-table td a {
    color: #A53C65;
    text-decoration: underline;
}
.sales-order-shipment .main .col-main .my-account .data-table td a:hover {
    text-decoration:none;
}
.main .col-main .my-account .data-table td h3 {
    color: #5D5961;
    font-size: 14px;
    font-weight: bold;
    line-height: 17px;
    margin: 10px 0;
    padding: 0;
    text-decoration: none;
}
.main .col-main .my-account .data-table td span.cart-price {
    margin-left: 0 ;
    margin-top: 0 ;
    text-align: center ;
}
.main .col-main .my-account .data-table td span.cart-price .price {
    margin-left: 0 ;
    margin-top: 0 ;
    text-align: center ;
}
.main .col-main .my-account .data-table td span.price {
    margin-left: 0 ;
    margin-top: 0 ;
    text-align: center ;

}
.main .col-main .my-account .data-table td span.nobr {
    margin-left: 0 ;
    margin-top: 0 ;
    text-align: center ;
}
.main .col-main .my-account .data-table th span.nobr {
    margin-left: 0 ;
    margin-top: 0 ;
    text-align: center ;
}
.main .col-main .my-account .data-table td span.nobr a { color:#000; }
.main .col-main .my-account .data-table td span.nobr a:hover { text-decoration: none; }
.main .col-main .my-account .data-table td span.nobr a.link-reorder { color:#7A4C93; }
.main .col-main .my-account .data-table .odd { background: #fff; }
.main .col-main .my-account .data-table .even { background: #f7f7f7; }
.customer-address-index .main .col-main .my-account h3 { font-size:15px; }

.wishlist-index-index .back-link { display: none; }

.mainContent .my-account .buttons-set button.button.btn-share {
    float: left;
    width: auto;
    height: 43px;
    line-height: 43px;
    margin: 0px 30px 0px 0px;
    padding: 0px 0px 0px 26px;
    text-align: left;
    letter-spacing: 0px;
    text-transform: none;
    font-size: 15px;
    color: #cc1153;
    border: none;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/btnShareIcon.**********.png) no-repeat 0px 13px;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.mainContent .my-account .buttons-set button.button.btn-share:hover { color: #030303; }

.mainContent .my-account .buttons-set button.button.btn-update {
    float: right;
    width: auto;
    height: 43px;
    line-height: 43px;
    margin: 0px 0px 0px 0px;
    padding: 0px 0px 0px 32px;
    text-align: left;
    letter-spacing: 0px;
    text-transform: none;
    font-size: 13px;
    color: #cc1153;
    border: none;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/refreshCode.**********.png) no-repeat 0px 4px;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.mainContent .my-account .buttons-set button.button.btn-update:hover { color: #030303; }

.wishlist-index-index .mainContent .my-account .buttons-set button.btn-add { width: 350px; }

.wishlist-index-share #main .mainContent .my-account textarea {
    width: 380px;
}
.wishlist-index-share #main .mainContent .my-account textarea:focus { outline: medium none;background: #f2f2f2; }
.wishlist-index-share #main .mainContent .my-account textarea:hover { border:1px solid #a0a0a0; }

.wishlist-index-share #main .mainContent .my-account p.back-link { float: right; width: auto; margin: 7px 354px 0px 0px; }
.wishlist-index-share #main .mainContent .my-account p.required { display: none; }

.customer-account input.input-text {

}

.customer-account input.input-text { border: 1px solid #d2d2d2; }
.customer-account input.input-text:hover { border: 1px solid #ababab; }
.customer-account input.input-text:focus { border: 1px solid #ababab; background: #f7f7f7; }

.customer-account textarea { border: 1px solid #d2d2d2; }
.customer-account textarea:hover { border: 1px solid #ababab; }
.customer-account textarea:focus { border: 1px solid #ababab; background: #f7f7f7; }

.customer-account select { border: 1px solid #d2d2d2; }
.customer-account select:hover { border: 1px solid #ababab; }
.customer-account select:focus { border: 1px solid #ababab; background: #f1f1f1 url(//localhost:8580/skin/frontend/stenik/default/images/select-arrow.**********.png) no-repeat right 17px; }

#main .mainContent .my-account .dashboard {
    margin-bottom: 40px;
}

/*** end of Profile styles ***/















.sitemapToProductsBtn ul {
    clear: both;
}

.sitemapToProductsBtn ul li {
    padding: 0px 0px 0px 0px;
}

.sitemapToProductsBtn ul li a {
	float: left;
	width: auto;
	height: 56px;
	padding: 0px 12px 0px 12px;
	margin: 0px;
	background: #cc1153;
	border: none;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	font-size: 16px;
	line-height: 56px;
	color: #fff;
	font-weight: 400;
	text-align: center;
	text-decoration: none;
	outline: none;
	cursor: pointer;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.sitemapToProductsBtn ul li a:hover { background: #030303; color: #fff; }

.mainContent .sitemap {
    clear: both;
    float: left;
}

/* site map styles */

.catalog-seo-sitemap-product .pagingBox,
.catalog-seo-sitemap-category .pagingBox {
	float: left;
	width: 100%;
	height: 49px;
	padding: 0px;
	margin: 0px 0px 40px 0px;
}
.catalog-seo-sitemap-product .paging,
.catalog-seo-sitemap-category .paging {
	float: left;
	clear: both;
	width: 100%;
	height: 45px;
	padding: 0px;
	margin: 30px 0px 30px 0px;
	text-align: center;
}
.catalog-seo-sitemap-product .paging .pagingItems,
.catalog-seo-sitemap-category .paging .pagingItems {
	float: left;
	width: 678px;
	height: 49px;
	background: #f8f8f8;
	text-align: center;
	border-bottom: 1px solid #e4e4e4;
}
.catalog-seo-sitemap-product .paging a,
.catalog-seo-sitemap-product .paging span,
.catalog-seo-sitemap-category .paging a,
.catalog-seo-sitemap-category .paging span {
	display: inline-block;
	width: 60px;
	height: 49px;
	line-height: 49px;
	padding: 0px;
	margin: 0px;
	background: #f8f8f8;
	color: #666;
	font-size: 30px;
	font-weight: 300;
	text-align: center;
	text-decoration: none;
	position: relative;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.catalog-seo-sitemap-product .paging .pagingItems a:hover,
.catalog-seo-sitemap-product .paging .pagingItems span:hover,
.catalog-seo-sitemap-category .paging .pagingItems a:hover,
.catalog-seo-sitemap-category .paging .pagingItems span:hover { background-color: #555; color: #fff; }


.catalog-seo-sitemap-product .paging .pagingItems a.selected,
.catalog-seo-sitemap-product .paging .pagingItems span.selected,
.catalog-seo-sitemap-category .paging .pagingItems a.selected,
.catalog-seo-sitemap-category .paging .pagingItems span.selected { background-color: #cc1156; color: #fff; }



.catalog-seo-sitemap-product .paging a.prev,
.catalog-seo-sitemap-category .paging a.prev {
	float: left;
	width: 248px;
	height: 50px;
	line-height: 49px;
	padding: 0px 0px 0px 24px;
	margin: 0px 3px 0px 0px;
	border-bottom: 1px solid #e4e4e4;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/pagingPrev.**********.png) no-repeat 24px 18px;
	color: #666;
	font-size: 17px;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.catalog-seo-sitemap-product .paging a.prev:hover,
.catalog-seo-sitemap-category .paging a.prev:hover { background-color: #000; color: #fff; }
.catalog-seo-sitemap-product .paging a.prev.disable,
.catalog-seo-sitemap-category .paging a.prev.disable { opacity: 0.4; background-color: #f8f8f8 !important; color: #666 !important; }

.catalog-seo-sitemap-product .paging a.next,
.catalog-seo-sitemap-category .paging a.next {
	float: right;
	width: 248px;
	height: 50px;
	line-height: 49px;
	padding: 0px 24px 0px 0px;
	margin: 0px;
	border-bottom: 1px solid #e4e4e4;
	background: #f8f8f8 url(//localhost:8580/skin/frontend/stenik/default/images/pagingNext.**********.png) no-repeat 204px 18px;
	color: #666;
	font-size: 17px;
	text-align: center;
	text-decoration: none;
	box-sizing: border-box;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.catalog-seo-sitemap-product .paging a.next:hover,
.catalog-seo-sitemap-category .paging a.next:hover { background-color: #000; color: #fff; }
.catalog-seo-sitemap-product .paging a.next.disable,
.catalog-seo-sitemap-category .paging a.next.disable { opacity: 0.4; background-color: #f8f8f8 !important; color: #666 !important; }

/* end site map styles */


.advanced-gifts-wrapper {
	float: left;
	width: 880px;
	margin: 20px 0 20px 0;
	padding: 0 0 20px 0;
	border-bottom: 1px solid #e4e4e4;
}
.advanced-gifts-wrapper .title {
	float: left;
	width: 100%;
	padding: 17px 20px 16px 20px;
	margin: 0 0 20px 0;
	background: #f8f8f8;
	border-bottom: 1px solid #e4e4e4;
	box-sizing: border-box;
	color: #000;
	font-size: 20px;
	line-height: 24px;
	font-weight: 400;
}
.advanced-gifts-wrapper .title strong {
	color: #CB1152;
	font-weight: 400;
}
#main .advanced-gifts-wrapper .productListingSlider {
	width: 100%;
	padding: 0 40px;
	box-sizing: border-box;
}
#main .advanced-gifts-wrapper .productListingSlider .owl-controls {
	bottom: auto;
	top: 50%;
	position: absolute;
}
#main .advanced-gifts-wrapper .productListingSlider .owl-controls .owl-prev {
	top: 0;
	left: 0;
	margin-top: -20px;
}
#main .advanced-gifts-wrapper .productListingSlider .owl-controls .owl-next {
	top: 0;
	right: 0;
	margin-top: -20px;
}
.advanced-gifts-wrapper .productBox {
	width: 183px;
	height: 189px;
	margin: 0;
	text-align: center;
}
.advanced-gifts-wrapper .productBox:hover {
	box-shadow: none;
}
.advanced-gifts-wrapper .productBox img {
	float: none;
	display: inline-block;
	margin: 0 auto 10px auto;
}
.advanced-gifts-wrapper .productBox .productTitle {
	height: 22px;
	padding: 0;
	margin: 0 0 10px 0;
	color: #000;
	font-size: 16px;
	line-height: 20px;
	font-weight: 400;
}
.advanced-gifts-wrapper .productBox .gridAddToCartBtn {
	float: left;
    width: 100%;
    height: auto;
    padding: 11px 0 12px 0;
    margin: 0;
    background: #cc1153;
    color: #fff;
    font-size: 16px;
    line-height: 19px;
    font-weight: 400;
    text-align: center;
    text-decoration: none;
}














footer {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	position: relative;
	z-index: 190;
}
footer .footerPaymentContent {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	background: #fff;
}
footer .footerPaymentWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 40px 0px 30px 0px;
	margin: 0px;
	border-top: 1px solid #e4e4e4;
}
footer .footerPaymentContent .footerPaymentInfo {
	float: left;
	width: 550px;
	height: auto;
	padding: 0px;
	margin: 0px;
}
footer .footerPaymentContent .footerPaymentInfo .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 18px 0px;
	color: #000;
	font-size: 30px;
	line-height: 32px;
	font-weight: 300;
	text-decoration: none;
}
footer .footerPaymentContent .footerPaymentInfo p {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	color: #000;
	font-size: 16px;
	line-height: 20px;
	font-weight: 300;
	text-decoration: none;
}
footer .footerPaymentContent .footerPaymentBox {
	float: right;
	width: 585px;
	height: auto;
	padding: 0px;
	margin: 10px 0px 0px 0px;
	text-align: center;
}
footer .footerPaymentContent .footerPaymentBox p { padding: 0px; margin: 0px; }
footer .footerPaymentContent .footerPaymentBox p img { display:inline-block; width: auto; height: auto; padding: 0px; margin: 0px 7px 7px 7px; }


footer .footerNewsLetterContent {
	float: left;
	width: 100%;
	height: 117px;
	padding: 43px 0px 11px 0px;
	margin: 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/footerNewsLetterContentBG.**********.jpg) no-repeat center top;
}
footer .newsLetterFotoer {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
footer .newsLetterFotoer .newsLetterInfo {
	float: left;
	width: 550px;
	height: auto;
	padding: 0px;
	margin: 0px;
}
footer .newsLetterFotoer .newsLetterInfo .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 18px 0px;
	color: #fff;
	font-size: 30px;
	line-height: 32px;
	font-weight: 300;
	text-decoration: none;
}
footer .newsLetterFotoer .newsLetterInfo p {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	color: #fff;
	font-size: 16px;
	line-height: 20px;
	font-weight: 300;
	text-decoration: none;
}
footer .newsLetterFotoer .newsletterForm {
	float: right;
	width: 580px;
	padding: 0px;
	margin: 15px 0px 0px 0px;
	position: relative;
}
footer .newsLetterFotoer .newsletterForm input.newsletterInput {
	float: left;
	width: 414px;
	height: 47px;
	padding: 0px 14px 0px 14px;
	margin: 0px;
	box-sizing: border-box;
	background: #fff;
	border: 1px solid #cc1156;
	border-right: none;
	color: #acacac;
	font-size: 15px;
	font-weight: 500;
	text-decoration: none;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .newsLetterFotoer .newsletterForm:hover input.newsletterInput { border-color: #fff; }
footer .newsLetterFotoer .newsletterForm input.newsletterInput:focus { border-color: #fff; background: #f1f1f1; color: #333; }

footer .newsLetterFotoer .newsletterForm button.newsLetterBtn {
	float: left;
	width: 165px;
	height: 49px;
	padding: 0px;
	margin: -1px 0px 0px 0px;
	background: #000;
	color: #fff;
	font-size: 15px;
	font-weight: 500;
	text-decoration: none;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	border: none;
	cursor: pointer;
	outline: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .newsLetterFotoer .newsletterForm button.newsLetterBtn:hover { background: #333; }

footer .newsLetterFotoer .newsletterForm .validation-advice {
    float: left;
    width: 393px;
    height: auto;
    padding: 10px 10px 10px 10px;
    margin: 0px;
    font-size: 15px;
    line-height: 18px;
    color: #ed1c24;
    font-weight: normal;
    text-align: left;
    text-decoration: none;
    background: #fff;
    border: 1px solid #ed1c24;
    position: absolute;
    left: 0px;
    top: 46px;
    z-index: 20;
}
footer .newsLetterFotoer .newsletterForm #resultOfAddSubscribers {
    float: left;
    width: 414px;
    height: auto;
    padding: 0px;
    margin: 0px;
    position: absolute;
    left: 0px;
    top: 47px;
    z-index: 20;
}
footer .newsLetterFotoer .newsletterForm #resultOfAddSubscribers ul {
    float: left;
    width: 100%;
    height: auto;
    padding: 0px;
    margin: 0px !important;
}
footer .newsLetterFotoer .newsletterForm #resultOfAddSubscribers ul li.error-msg {
    float: left;
    width: 388px;
    height: auto;
    padding: 10px;
    background: #fff;
    font-size: 15px;
    line-height: 18px;
    color: #ed1c24;
    font-weight: normal;
    text-align: left;
    text-decoration: none;
    border: 1px solid #ed1c24;
}
footer .newsLetterFotoer .newsletterForm #resultOfAddSubscribers ul li.success-msg {
    float: left;
    width: 393px;
    height: auto;
    padding: 10px;
    background: #fff;
    font-size: 14px;
    line-height: 18px;
    color: #48bb1a;
    text-align: left;
    font-weight: normal;
    text-decoration: none;
    border: 1px solid #48bb1a;
}
footer .newsLetterFotoer .newsletterForm #resultOfAddSubscribers ul li.success-msg span { color: #48bb1a !important; }

footer .newsLetterFotoer .newsletterForm .newsletterLoader {
    float: left;
    width: 35px;
    height: 35px;
    padding: 0px;
    margin: 0px;
    position: absolute;
    right: 165px;
    top: 10px;
    z-index: 20;
    display: none;
    background: url(//localhost:8580/skin/frontend/stenik/default/images/preloader-28x28.**********.gif) no-repeat left top;
}




footer .footerContent {
	float: left;
	width: 100%;
	height: auto;
	min-height: 365px;
	padding: 35px 0px 0px 0px;
	margin: 0px;
	background: #000 url(//localhost:8580/skin/frontend/stenik/default/images/footerContentBG.**********.jpg) no-repeat center bottom;
}
footer .footerContent .footerCol {
	float: left;
	width: 280px;
	height: auto;
	padding: 0px;
	margin: 0px 10px 0px 0px;
}
footer .footerContent .footerCol.contacts { width: 255px; }
footer .footerContent .footerCol.social { float: right; }

footer .footerContent .footerCol .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 25px 0px;
	color: #fff;
	font-size: 30px;
	line-height: 32px;
	font-weight: 300;
	text-decoration: none;
}
footer .footerContent .footerCol ul {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
footer .footerContent .footerCol ul li {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
	color: #fff;
	font-size: 16px;
	line-height: 18px;
	font-weight: 300;
	text-decoration: none;
}
footer .footerContent .footerCol ul li a {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 6px 0px 6px 0px;
	color: #fff;
	font-size: 16px;
	line-height: 18px;
	font-weight: 300;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .footerContent .footerCol ul li a:hover { color: #ff005e; }

footer .footerContent .footerCol p {
	float: left;
	width: 100%;
	padding: 0px;
	margin: 0px 0px 10px 0px;
	color: #fff;
	font-size: 16px;
	line-height: 20px;
	font-weight: 300;
	text-decoration: none;
}
footer .footerContent .footerCol p strong.big { color: #ff005e; font-weight: bold; font-size: 30px; line-height: 30px; }

footer .footerContent .footerCol p a {
	color: #cc1153;
	text-decoration: underline;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .footerContent .footerCol p a:hover { color: #fff; }

footer .footerContent .footerCol p strong { font-weight: bold; }

footer .footerContent .footerCol .socialBox {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px;
}
footer .footerContent .footerCol .socialBox .social {
	float: left;
	width: auto;
	height: 34px;
	line-height: 34px;
	padding: 0px 0px 2px 46px;
	margin: 0px 0px 5px 0px;
	color: #fff;
	font-size: 16px;
	font-weight: 300;
	text-decoration: none;
	position: relative;
	clear: both;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .footerContent .footerCol .socialBox .social:hover { color: #ff005e; padding-left: 42px; }

footer .footerContent .footerCol .socialBox .social:before {
	content: '';
	width: 32px;
	height: 32px;
	background-color: #fff;
	background-repeat: no-repeat;
	background-position: center center;
	border-radius: 100%;
	position: absolute;
	left: 0px;
	top: 2px;
	z-index: 10;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .footerContent .footerCol .socialBox .social:hover:before { opacity: 0.7; }

footer .footerContent .footerCol .socialBox .social.facebook:before { background-image: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxFacebook.**********.png); }
footer .footerContent .footerCol .socialBox .social.gPlus:before { background-image: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxgPlus.**********.png); }
footer .footerContent .footerCol .socialBox .social.twitter:before { background-image: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxTwitter.**********.png); }
footer .footerContent .footerCol .socialBox .social.pinterest:before { background-image: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxPinterest.**********.png); }
footer .footerContent .footerCol .socialBox .social.youTube:before { background-image: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxYouTube.**********.png); }
footer .footerContent .footerCol .socialBox .social.insta:before { background-image: url(//localhost:8580/skin/frontend/stenik/default/images/socialBoxInstagram.**********.png); }


footer .footerBottom {
	float: left;
	width: 100%;
	height: auto;
	padding: 27px 0px 27px 0px;
	margin: 20px 0px 0px 0px;
	border-top: 1px solid #272727;
}
footer .footerBottom .copy {
	float: left;
	width: auto;
	height: 22px;
	line-height: 22px;
	padding: 0px;
	margin: 0px;
	color: #fff;
	font-size: 14px;
	font-weight: 300;
	text-decoration: none;
}
footer .footerBottom .copy a {
	display: inline-block;
	width: auto;
	height: auto;
	padding: 0px 0px 0px 5px;
	margin: 0px 10px 0px 0px;
	color: #cc1153;
	font-size: 14px;
	line-height: 22px;
	font-weight: 500;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .footerBottom .copy a:hover { color: #fff; }

footer .footerBottom .stenik {
    float: right;
    width: auto;
    height: 22px;
    line-height: 22px;
    padding: 0;
    margin: 0px 0px 0px 0px;
    color: #fff;
    font-size: 14px;
}
footer .footerBottom .stenik a {
    color: #fff;
    display: inline-block;
    text-decoration: none;
}
footer .footerBottom .stenik a:hover {
    text-decoration: none;
}
footer .footerBottom .stenik a strong {
    color: #cc1153;
    display: inline-block;
    text-decoration: none;
    font-weight: normal;
    -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
footer .footerBottom .stenik a:hover strong {
    color: #fff;
    text-decoration: none;
}
footer .footerBottom .stenik img {
    display: inline-block;
    line-height: 22px;
    margin: 0px 3px -6px 0px;
    padding: 0px;
}







/* TBI Leasing styles in checkout */

.leaseCalculator {
	float: left;
	width: 1100px;
	height: auto;
	padding: 10px 10px 5px 10px;
	margin: 10px 0px 10px -15px;
	background: #fff;
	border: 3px solid #e9e9e9;
}
.productLeasingCalc .leaseCalculator { width: 1000px; margin: 10px 0px 10px 0px; }
.productLeasingCalc h1 { color: #000; }
#colorbox .productLeasingCalc h1 { margin-top: 0px; }

.leaseCalculator .title {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 15px 0px;
	font-size: 25px;
	line-height:30px;
	color: #222;
}
.leaseCalculator .textInfo {
	float: left;
	width: 100%;
	height: auto;
	margin: 0 0 10px 0;
}
.leaseCalculator input.downpayment { width: 202px; height: 32px; }

.leaseCalculator .recalcBtn {
	float: left;
	width: 215px;
	height: 32px;
	line-height: 32px;
	padding: 0px;
	margin: 0px 0px 0px 10px;
	color: #000;
	font-size: 15px;
	font-weight: bold;
	text-align: center;
	text-decoration: none;
	background: #dbdbdb;
	border: none;
	cursor: pointer;
	outline: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.leaseCalculator .recalcBtn:hover { background-color: #535353; color: #fff; }
.leaseCalculator .recalcBtn:active {
	-webkit-transition: all 0.1s linear; -moz-transition: all 0.1s linear; -o-transition: all 0.1s linear; transition: all 0.1s linear;
	-moz-transform: scale(0.95) skewX(-10deg);; -webkit-transform: scale(0.95) skewX(-10deg);; -o-transform: scale(0.95) skewX(-10deg);; transform: scale(0.95) skewX(-10deg);
}
.leaseCalculator .notice {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 4px 0px 10px 0px;
	font-size: 11px;
	line-height: 14px;
	color: #767676;
	text-decoration: none;
	font-style: italic;
	font-weight: normal;
}

.leaseCalculator .ui-tabs {
	float: left;
	width: 1100px;
	height: auto;
	padding: 0px;
	margin: 10px 0px 0px 0px;
}
.leaseCalculator .ui-tabs ul.ui-tabs-nav {
	float: left;
	width: 1100px;
	height: auto;
	padding: 0px;
	margin: 0px;
	border: 1px solid #e9e9e9;
}
#co-payment-form .leaseCalculator .ui-tabs ul.ui-tabs-nav { border: none !important; }

.leaseCalculator .ui-tabs ul.ui-tabs-nav li {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
	background: none !important;
	border: none !important;
	border-radius: 0px !important;
}
.leaseCalculator .ui-tabs ul.ui-tabs-nav li a {
	float: left;
	width: 181px;
	height: auto;
	min-height: 30px;
	padding: 7px 5px 7px 5px;
	margin: 0px;
	font-size: 13px;
	line-height: 15px;
	color: #333;
	text-decoration: none;
	text-align: center;
	font-weight: normal;
	border-right: 1px solid #e9e9e9;
	border-radius: 5px 5px 0px 0px !important;
}

.leaseCalculator .ui-tabs ul.ui-tabs-nav li.ui-tabs-active a { color: #df0002; background: #f1f1f1; }

#co-payment-form .leaseCalculator .ui-tabs ul.ui-tabs-nav li a { width: 161.8px; }

.leaseCalculator .ui-tabs .ui-tabs-panel {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 10px 0px 0px 0px;
}

.leaseCalculator .horizontalScrollWrapper {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px !important;
	margin: 0px;
	overflow: hidden;
}
.leaseCalculator .variants {
	float: left;
	width: 100%;
	padding: 0px;
	margin: 0px 0px 20px 0px;
	border-spacing: 0px;
	border-collapse: separate;
	border: none;
}
.leaseCalculator .variants th {
	width: 120px;
	border-top:1px solid #c1c1c1;
	border-bottom:1px solid #c1c1c1;
	margin: 0px;
	padding: 0px 3px 0px 3px;
}
.leaseCalculator .variants td {
	width: 120px;
	padding: 2px 3px 2px 3px;
	color: #222;
	font-size: 12px;
	line-height: 12px;
	text-align: center;
}
.leaseCalculator .variants td.first {
	width: 150px;
	text-align: right;
}
.leaseCalculator .variants td .details .detail {
	font-size: 13px;
	margin-bottom: 2px;
	display: block;
}
.leaseCalculator .variants td .details .detail.installment {
	font-weight: bold;
	margin: 5px 0px 6px 0px;
}
.leaseCalculator .variants tr .bankLogo {
	float: right;
	vertical-align: middle;
}
.leaseCalculator .variants tfoot td {
	padding-top: 0px;
}
.leaseCalculator .variants .months {
	color: #df0002;
	font-size: 13px;
	line-height: 30px;
	text-align: center;
}
.leaseCalculator .variants .promo .months {
	color: #BC3333;
}
.leaseCalculator .variants .percent {
	display: block;
}
.leaseCalculator .form-list .field {
	float: left;
	width: 340px;
	margin-bottom: 7px;
	margin: 0px 15px 7px 0px;
}
.leaseCalculator .form-list .field .input-text { margin: 3px 0px 3px 0px; }

.leaseCalculator .form-list .field input#payment_tbi_variant_pin.input-text { border: 2px solid #8b8b8b; }

.leaseCalculator .placeOrderHelpMessage {
	margin: 10px 0px 10px 0px;
	font-size: 15px;
	color: #555;
	font-weight: bold;
	display: none;
}
.leaseCalculator .recalcLoader {
	float: left;
	width: 35px;
	height: 35px;
	margin: 0px 0px 0px 20px;
	padding: 0px;
	background: url(//localhost:8580/skin/frontend/stenik/default/images/preloader-28x28.**********.gif) no-repeat center center;
}
.leaseCalculator .checkbox-box {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 5px 0px 10px 0px;
}
.leaseCalculator .checkbox-box input.checkbox {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 5px 8px 0px 0px;
}
.leaseCalculator .checkbox-box label {
	float: left;
	width: auto;
	height: auto;
	padding: 0px !important;
	margin: 0px;
}
.leaseCalculator .checkbox-box label p {
	float: left;
	width: auto;
	height: auto;
	padding: 0px;
	margin: 0px;
}
.leaseCalculator .checkbox-box label p a {
	color: #cc1153;
	text-decoration: underline;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.leaseCalculator .checkbox-box label p a:hover { color: #555; }

#checkoutSteps .checkbox-box .validation-advice { margin-top: 5px; margin-bottom: 0px; }


.leaseCalculator .variantsWrapper {
    float: left;
    width: 100%;
    max-width: 1100px;
    padding: 0px;
    margin: 0px;
    overflow: auto;
    background: #fff;
    box-sizing: border-box;
}

.leaseCalculator.tbiLeasing label.downpayment-label {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 5px 0px;
}
.leaseCalculator.tbiLeasing .principalInfo {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
}
.leaseCalculator.tbiLeasing .principalInfo .principalAmount .price { font-size: 15px; font-weight: bold; }

.leaseCalculator.tbiLeasing .promoOffer {
	float: left;
	width: 100%;
	height: auto;
	padding: 0px;
	margin: 0px 0px 10px 0px;
}
#checkoutSteps .leaseCalculator.tbiLeasing .promoOffer p {
	font-size: 15px;
	line-height: 16px;
}
.leaseCalculator.tbiLeasing .recalcBtn { background-color: #f18800; color: #fff; }
.leaseCalculator.tbiLeasing .recalcBtn:hover { background-color: #ff9e21; }

.leaseCalculator.tbiLeasing .variants .months { color: #f18800; }
.leaseCalculator.tbiLeasing .variants .promo .months { color: #ff9e21; }

.productLeasingCalc .addToCartBtn {
	float: left;
	width: auto;
	height: auto;
	padding: 20px 20px 20px 20px;
	margin: 0px 0px 10px 0px;
	background: #cc1153;
	color: #fff;
	font-size: 18px;
	line-height: 20px;
	text-align: center;
	font-weight: bold;
	text-decoration: none;
	text-transform: uppercase;
	font-family: 'HKGrotesk', 'ArialBG', Arial, sans-serif;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.productLeasingCalc .addToCartBtn:hover { background: #e91862; }


/* End of TBI Leasing styles in checkout */












/* Error and seuccsess msg, validate errors
********************************************/

ul.messages { width: auto!important; padding:0px!important;	margin: 0px 0px 20px 0px!important; list-style:none!important; border:none!important; overflow: hidden!important; clear:both!important; }
ul.messages li { margin: 0px; padding:0px 0px 0px 0px; }

ul.messages li.error-msg {
	min-height: 22px;
	line-height: 18px;
	padding: 10px 10px 6px 48px;
	color: #333!important;
	border: 1px solid #bf3d27 !important;
	font-size: 13px;
	text-decoration: none;
	font-weight: normal;
	background: #f8d7d1 url(//localhost:8580/skin/frontend/stenik/default/images/errorMsgIcon.**********.png) no-repeat 6px -1px;
	overflow: hidden!important;
}
ul.messages li.error-msg ul { padding:0px; margin:0px; }
ul.messages li.error-msg ul li { padding:0px; margin:0px;  background:none; color:#333!important; }

ul.messages li.error-msg ul li a { color:#cc1153; text-decoration:underline; }
ul.messages li.error-msg ul li a:hover { text-decoration:none; }

ul.messages li.success-msg {
	min-height: 22px;
	line-height: 18px;
	padding: 10px 10px 6px 48px;
	border: 1px solid #e1f4d1 !important;
	font-size: 13px;
	color: #333!important;
	text-decoration: none;
	font-weight: normal;
	background: #f3ffe8 url(//localhost:8580/skin/frontend/stenik/default/images/successMsgIcon.**********.png) no-repeat 6px -1px;
	overflow: hidden!important;
}

ul.messages li.success-msg ul { padding:0px; margin:0px; }
ul.messages li.success-msg ul li { padding:0px; margin:0px; background: none; color:#333!important; }

ul.messages li.success-msg ul li a { color:#cc1153; text-decoration:underline; }
ul.messages li.success-msg ul li a:hover { text-decoration:none; }

ul.messages li.notice-msg {
	min-height: 22px;
	line-height: 18px;
	padding: 10px 10px 6px 48px;
	color: #333!important;
	border: 1px solid #d77e47 !important;
	font-size: 13px;
	text-decoration: none;
	font-weight: normal;
	background: #fbe6da url(//localhost:8580/skin/frontend/stenik/default/images/noticeMsgIcon.**********.png) no-repeat 6px -1px;
	overflow: hidden!important;
}
ul.messages li.notice-msg ul { padding:0px; margin:0px; }
ul.messages li.notice-msg ul li { padding:0px; margin:0px; background: none; color:#333!important; }

/* end of error and succsess msg */






/*** Autocomplate  styles ***/
.search-autocomplete {
	width: 560px !important;
    left: 0px;
    top: 49px !important;
	border-radius: 0px !important;
    z-index: 999;
	overflow: hidden !important;
}
.responsiveHeader .search-autocomplete { top: 40px !important; width: 100% !important; }

.search-autocomplete ul {
    background-color: #ececec;
    border-top: 1px solid #d0d0d0;
    border-left: 1px solid #d0d0d0;
    border-right: 1px solid #d0d0d0;
    border-bottom: 1px solid #d0d0d0;
    margin-left: 0;
    padding-left: 2px;
    border-radius: 0px !important;
	overflow: hidden !important;
}
.responsiveHeader .search-autocomplete ul { width: 100%; margin-bottom: 0px !important; box-sizing: border-box; }

.search-autocomplete li {
    background-color: #fff;
    border-bottom: 1px solid #fff;
    cursor: pointer;
    margin-left: 0;
    padding: 4px 8px 3px 10px;
    text-align: left;
	color: #333;
	font-size: 12px;
}
.responsiveHeader .search-autocomplete li { width: 100%; padding: 4px 8px 3px 10px !important; border-bottom: 1px solid #fff !important; box-sizing: border-box; }

.search-autocomplete li .amount { float: right; font-weight: bold; color: #da2c33; }
.search-autocomplete li.odd { background: #fff; }
.search-autocomplete li.selected { background: #F8F8F8; }

.search-autocomplete ul.ajaxsearch li.suggest {
	background: #F3F3F3!important;
    border-bottom: 1px solid #EBEBEB!important;
	color:#0075BC;
	font-size: 12px;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.search-autocomplete ul.ajaxsearch li.suggest.selected { background: #F8F8F8 !important; color:#F58220 !important; }

.search-autocomplete ul.ajaxsearch li.suggest:hover { color:#F58220; }
.search-autocomplete ul.ajaxsearch li.suggest span { color:#EB7F23; }

.search-autocomplete ul.ajaxsearch .preview img { margin-right:8px; }
.search-autocomplete ul.ajaxsearch .preview a { line-height: 16px; }
.search-autocomplete ul.ajaxsearch .preview a:hover { text-decoration:none; }

.search-autocomplete ul.ajaxsearch li .productRow {
	float: left;
	width: 100%;
	height: auto;
	padding: 6px 0px 6px 0px;
	margin: 0px;
	text-decoration: none;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}

.search-autocomplete ul.ajaxsearch li .productRow .productImg {
	float: left;
	width: 50px;
	height: 50px;
	padding: 0px;
	margin: 3px 10px 6px 0px;
}
.search-autocomplete ul.ajaxsearch li .productRow .productName {
	float: left;
	width: 161px;
	height: auto;
	padding: 0px;
	margin: 0px 0px 4px 0px;
	font-size: 12px;
	line-height: 15px;
	text-decoration: none;
	font-weight: 500;
	color: #0075BC;
	-webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;
}
.search-autocomplete ul.ajaxsearch li .productRow .productName:hover { color: #F58220; }
.search-autocomplete ul.ajaxsearch li.selected .productRow .productName { color: #F58220; }header .search-autocomplete ul.ajaxsearch li .productRow .productDescription {
	float: left;
	width: 161px;
	height: auto;
	padding: 0px;
	margin: 0px;
	font-size: 11px;
	line-height: 13px;
	text-decoration: none;
	font-weight: 500;
	color: #555;
}
/*** end of autocomplate styles ***/


/*** Personalized label styles ***/

.laser-button { float: left; width: 100%; padding-left: 35px; margin-bottom: 10px; text-align: center; font-size: 18px; font-weight: bold; border: 1px solid #e3e3e3; color: #000; text-decoration: none; line-height: 48px; background: #FFF url(//localhost:8580/skin/frontend/stenik/default/images/accordionIcon4.**********.png) no-repeat left 30px center; box-sizing: border-box; transition: all 0.2s linear; }
.laser-button:hover { border-color: #cc1153; }
.personalized-label-section { float: left; width: 80%; padding-bottom: 20px; }
.chars-left { float: left; width: 100%; margin: -15px 0 10px; font-size: 14px; font-style: italic; }
.font1 { font-family: 'Arial'; }
.personalized-label-section .validation-advice { float: left; width: 100%; height: auto; padding: 0px; margin: -10px 0px 10px 0px; color: #ed1c24; font-size: 14px; line-height: 16px; font-weight: 300; text-transform: none; }
#stenik_personalizedlabel_font { float: left; width: 100% !important; margin-bottom: 20px; }
#stenik_personalizedlabel_font .dd-select { float: left; width: 100% !important; box-sizing: border-box; background: none !important; }
#stenik_personalizedlabel_font .dd-selected { padding: 7px; font-weight: normal; font-size: 16px; line-height: 32px; }
#stenik_personalizedlabel_font .dd-options { float: left; width: 100% !important; }
#stenik_personalizedlabel_font .dd-option-image { max-width: 335px; height: 30px; }
#stenik_personalizedlabel_font .dd-selected-image { max-width: 335px; height: 30px; }
#stenik_personalizedlabel_font .dd-option-selected { background: #FFF; }
#stenik_personalizedlabel_font .dd-option { font-size: 16px; border-bottom: none; padding: 5px 10px; }
.personalized-label-section #stenik_personalizedlabel_font .validation-advice { position: absolute; top: 123%; left: 0; }



/*GDPR styles*/
.opc-wrapper-opc .agreement-content { float: left; width: 100%; padding-left: 24px; box-sizing: border-box; margin-bottom: 10px; }
.gdpr-info { float: left; width: 100%; padding-left: 27px; box-sizing: border-box; margin-bottom: 10px; }
.opc-wrapper-opc .agreement-content p { font-size: 14px; }
.gdpr-info p { font-size: 14px; }
.checkbox-box { float: left; width: 100%; }
.checkbox-box input { float: left; margin-right: 10px; }
.checkbox-box label { float: left; max-width: 85%; }
.checkbox-box label a { text-decoration: none; color: #cc1153; }
.newsletterForm .checkbox-box { margin-top: 10px; }
.newsletterForm .checkbox-box label { color: #FFF; font-size: 14px; line-height: 18px; }
.commentsFormWrapper .checkbox-box { margin-bottom: 10px; }
.contactsForm .checkbox-box { margin-bottom: 10px; }
.terms-popup { float: left; width: 560px; height: 600px; padding: 10px; background: #fff; overflow-y: scroll; }
.checkbox-content input { float: left; margin-right: 10px; }
.checkbox-content label { float: left; max-width: 85%; }
.checkbox-content label a { text-decoration: none; color: #cc1153; }
.my-account .button { line-height: 42px; color: #FFF; text-decoration: none; }
.button.right { float: right; }
.stenik-gdprcompliance-customer-dashboard a,
.back-link,
.link-remove { color: #cc1153; }


/* Stenik Invoice Fileds styles
*********************************/

.invoice-fields-wrapper input.checkbox { float: left; margin: 4px 10px 0px 0px; }
.invoice-fields-wrapper .checkbox-content label { float: left; width: auto; }
.my-account .invoice-fields-wrapper input.checkbox{ margin: 0 6px 0 0; }

/* Stenik Checkout Styles
****************************/
.validation-advice { color: #da1515; }
.stenik-checkout .field .validation-advice { margin: -10px 0 5px 0; float: left; width: 100%; box-sizing: border-box; }
em { color: #cb1854 }
p.notice-text{ margin: 0 0 30px 0; }
.stenik-checkout-top-login { float: left; width: 100%; height: auto; padding: 0px; margin: 10px 0px 10px 0px; }
.stenik-checkout-top-login .button.open-popup { min-width: 70px; padding: 0 10px; line-height: 40px; margin:  0 18px 0 0; }
.stenik-checkout-top-login p { float: left; width: auto; height: 42px; padding: 0px; margin: 0px 18px 0px 0px; line-height: 42px; }
.stenik-checkout-top-login .social-login { float: left; width: auto; height: 42px; padding: 0px; margin: 0px 0px 0px 0px; }
.checkout-popup-login { width: 500px; height: 380px; padding: 20px; background: #fff;  color: #000;}
.checkout-popup-login label { width: 100%; display: block; margin: 0 0 5px 0; }
.checkout-popup-login .forgotpassword { float: right; margin: 7px 0px 0px 20px; line-height: 20px; color: #cc1153; border-bottom: 1px solid #cc1153; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear; }
.checkout-popup-login .forgotpassword:hover { color: #030303; }
#main .stenik-checkout-top-login .facebookLoginBtn { width: 200px; }
#main .stenik-checkout-top-login .gPlusLoginBtn { width: 200px; }

.stenik-checkout { position: relative; float: left; width: 100%; box-sizing: border-box; color: #000; font-size: 14px; }
.stenik-checkout p.no-selected-address-text{ padding: 0 0 0 8px; }
.stenik-checkout p.control { margin: 0 0 10px 0; }
.stenik-checkout input.input-text{ height: 40px; }
.stenik-checkout .customer-name input.input-text::-webkit-input-placeholder { color: transparent; }
.stenik-checkout .customer-name input.input-text::-moz-placeholder { color: transparent; }
.stenik-checkout .customer-name input.input-text:-ms-input-placeholder { color: transparent; }
.stenik-checkout .customer-name input.input-text:-moz-placeholder { color: transparent; }
.stenik-checkout .col-xs-8 { position: relative; float: left; width: 810px; box-sizing: border-box;}
.stenik-checkout .discount-form{display: none;float: left;width: 100%;height: auto;padding: 20px; margin: 0px 0px 0px 0px;position: relative;background: #f8f8f8;border: 1px solid #ebebeb;border-bottom: none;box-sizing: border-box;position: relative;}
.stenik-checkout .discount-form input.input-text{ padding-right: 45px; margin: 0; }
.stenik-checkout .discount-form .discount-button{ float: right; width: 38px; height: 38px; padding: 0px 0px 0px 0px; margin: -39px 1px 0 0; background: #030303 url("//localhost:8580/skin/frontend/stenik/default/images/promoSubmitIcon.**********.png") center center no-repeat; border: none; font-size: 20px; color: #fff; text-decoration: none; text-align: center; font-weight: normal; outline: none; cursor: pointer; -webkit-transition: all 0.2s linear; -moz-transition: all 0.2s linear; -o-transition: all 0.2s linear; transition: all 0.2s linear;}
.stenik-checkout .discount-form .discount-button:hover{ background: #cc1153 url("//localhost:8580/skin/frontend/stenik/default/images/promoSubmitIcon.**********.png") center center no-repeat; }

.stenik-checkout .step-title .step-number{display: block; width: 50px; font-size: 24px; line-height: 50px; text-align: center; color: #fff; background: #000; position: absolute; top: 0; left: 0; }
.stenik-checkout .step-title { float: left; width: 100%; box-sizing: border-box; position: relative; margin: 0px 0px 0px 0px; padding: 13px 10px 13px 60px; background: #f8f8f8; border-top: 1px solid #ebebeb;  border-right: 1px solid #ebebeb; color: #000; font-size: 18px; line-height: 23px; font-weight: 700; }
.stenik-checkout .stenik-onepage-section { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 15px 52px; margin: 0px 0px 20px 0px; border: 1px solid #ebebeb; }
.stenik-checkout #stenik-onepage-section-coupon { border: none; padding: 0; margin: 0;}
.stenik-checkout #stenik-onepage-section-review { padding: 0; margin: 0; }
.stenik-checkout .checkout-review-col-discount { float: left; width: 100%; }
.stenik-checkout .checkout-review-col-discount .title{ display: inline-block; font-size: 18px; background: url('//localhost:8580/skin/frontend/stenik/default/images/downArrow.**********.png') no-repeat top 22px left 186px; box-sizing: border-box; text-decoration: underline; padding: 14px 20px; width: 100%; cursor: pointer; }
.stenik-checkout .fields { margin: 0 -12px; }
.stenik-checkout .invoice-fields-wrapper .fields { margin: 0; }
.stenik-checkout .fields.econt-door-address-fields{ margin: 0; padding: 0; }
.stenik-checkout .fields.econt-office-address-fields{ margin: 0; padding: 0 0px; }
.stenik-checkout .fields .field { float: left; width: 50%; box-sizing: border-box; margin: 0px; padding: 0px 12px 0px 12px; }
.stenik-checkout .fields .field label{ display: inline-block; width: 100%; margin: 0 0 5px 0; }
.stenik-checkout .fields.wide { float: left; width: 100%; box-sizing: border-box; margin: 0px 0 15px 0; padding: 0px; }
.stenik-checkout .fields.wide label {  }
.stenik-checkout .fields.wide.create-account-checkbox{ margin: 0 0 10px 0; }
.stenik-checkout .create-account-checkbox .input-checkbox { float: left; margin: 4px 10px 0px 0px; }
.stenik-checkout .create-account-checkbox .label-checkbox { float: left; margin: 0px 0px 0px 0px; }

.stenik-checkout #billing-new-address-form .fields.control input.checkbox { float: left; margin: 4px 10px 0px 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dt { display: none; float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-weight: 700; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd ul { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd ul li { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px 0px 8px 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd input.radio { float: left; width: auto; height: auto; padding: 0px; margin: 4px 10px 0px 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd input[type="radio"] { float: left; width: auto; height: auto; padding: 0px; margin: 4px 10px 0px 0px; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd label { float: left; width: 90%; height: auto; padding: 0px; margin: 0px 0px 0px 0px; font-weight: 400; }
.stenik-checkout #checkout-shipping-method-load dl.sp-methods dd label .price { font-weight: 700; }
.stenik-checkout #onepage-checkout-shipping-method-additional-load .gift-messages { display: none; }
.stenik-checkout #onepage-checkout-shipping-method-additional-load .gift-messages-form { display: none; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-weight: 400; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt input.paymentMethod { float: left; width: auto; height: auto; padding: 0px; margin: 6px 10px 0px 0px; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt#dt_method_stenik_leasingunicredit label { padding: 3px 90px 3px 0px; position: relative; background: url("//localhost:8580/skin/frontend/stenik/default/images/unicredit-logo.**********.png") no-repeat right top; background-size: 71px 25px; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt#dt_method_stenik_leasingtbi label { padding: 3px 65px 3px 0px; position: relative; background: url("//localhost:8580/skin/frontend/stenik/default/images/tbilogo.**********.png") no-repeat right top; background-size: 55px 25px; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dt#dt_method_newpay label { padding: 0 115px 0 0; position: relative; background: url("//localhost:8580/skin/frontend/stenik/default/images/newpay-logo.**********.png") no-repeat right top; background-size: 100px 19px; }
.stenik-checkout #stenik-onepage-section-payment_method dl.sp-methods dd { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px 20px 0px 20px; margin: 0px 0px 20px 0px; }

.stenik-checkout .comment-box { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px 0px 20px 0px; }
.stenik-checkout .comment-box h5 { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px; margin: 0px 0px 10px 0px; font-size: 16px; line-height: 19px; }
.stenik-checkout .comment-box .textarea-box textarea { height: 80px; margin: 0px; }
.stenik-checkout ol.checkout-agreements { float: left; width: 100%; box-sizing: border-box; padding: 0px; margin: 0px 0px 10px 0px; list-style-type: none; position: relative; }
.stenik-checkout ol.checkout-agreements li { float: left; width: 100%; box-sizing: border-box; height: auto; margin: 0px 0px 8px 0px; }
.stenik-checkout ol.checkout-agreements input.checkbox { float: left; margin: 4px 10px 0px 0px; }
.stenik-checkout ol.checkout-agreements label { float: left; width: 90%; margin: 0px; }
.stenik-checkout ol.checkout-agreements label a{ color: #cc1153; -webkit-transition: all 0.2s linear;-moz-transition: all 0.2s linear;-o-transition: all 0.2s linear;transition: all 0.2s linear; }
.stenik-checkout ol.checkout-agreements label a:hover{ color: #000;  }
.stenik-checkout ol.checkout-agreements .agreement-content { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px 0px 0px 24px; margin: 0px; }
.stenik-checkout ol.checkout-agreements .agreement-content p { margin: 0px 0px 6px 0px; font-size: 12px; line-height: 16px; }
.stenik-checkout .checkbox-content { float: left; width: 100%; box-sizing: border-box; height: auto; margin: 0px 0px 10px 0px; padding: 0px 0px 0px 24px; }

.stenik-checkout .coupon-review-col { float: left; width: 350px; box-sizing: border-box; height: auto; margin: 0px 0px 10px 0px; position: absolute !important; top: 0px; right: -390px; }
.stenik-checkout .coupon-review-wrapper { float: left; width: 100%; height: auto; }
.stenik-checkout .coupon-review-wrapper.sticky-sidebar { position: fixed; width: 350px; top: 5px; right: 50%; z-index: 200; transform: translateX(174%); -webkit-transform: translateX(174%); }
.stenik-checkout .coupon-review-wrapper.sticky-sidebar.bottom-stop { position: absolute; top: auto; right: 0; bottom: 0; transform: translateX(0%); -webkit-transform: translateX(0%); }
.stenik-checkout .order-review { float: left; width: 100%; height: auto; }
.stenik-checkout .checkout-review-box { float: left; width: 100%; box-sizing: border-box; background: #fff; }
.stenik-checkout .checkout-review-box .review-title { box-sizing: border-box; display: block; cursor: pointer; position: relative; background: #f8f8f8; margin: 0; padding: 10px 40px 38px 20px; font-size: 18px; font-weight: 700; border-bottom: 1px solid #ebebeb; }
.stenik-checkout .checkout-review-box .review-title-wrapper{ float: left; width: 100%; box-sizing: border-box; }
.stenik-checkout .checkout-review-box .review-title-wrapper .edit-link { float: left; display: inline-block; margin: -35px 0 0 24px; color: #000; padding-left: 20px; font-weight: 400; position: relative; z-index: 50; -webkit-transition: all 0.2s linear;-moz-transition: all 0.2s linear;-o-transition: all 0.2s linear;transition: all 0.2s linear; }
.stenik-checkout .checkout-review-box .review-title-wrapper .edit-link:before{ content: ''; display: block; width: 13px; height: 13px; position: absolute; left: 0; top: 6px; background: url('//localhost:8580/skin/frontend/stenik/default/images/editIcon.**********.png') no-repeat center center; }
.stenik-checkout .checkout-review-box .review-title-wrapper .edit-link:hover { color: #cc1153; }
.stenik-checkout .checkout-review-box .review-title:before { content: ''; position: absolute; right: 24px; top: 25px; width: 3px; height: 21px; background: #000; }
.stenik-checkout .checkout-review-box .review-title:after { content: ''; position: absolute; right: 15px; top: 34px; width: 21px; height: 3px; background: #000;}
.stenik-checkout .checkout-review-box .review-title.opened:before { display: none; }
.stenik-checkout .checkout-review-box .review-title .items-qty { display: block; margin: 8px 0px 0px 0px; font-size: 14px; text-transform: uppercase; line-height: 15px; }
.stenik-checkout .checkout-review-box .review-items-box { display: none; float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item { float: left; width: 100%; height: auto; min-height: 145px; padding: 20px 20px 20px 20px; border-bottom: 1px solid #ebebeb; margin: 0px 0px 0px 0px; background: #fff; box-sizing: border-box; position: relative; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .cart-img-wrapper { float: left; width: 102px; height: 102px; padding: 0px; margin: 0px; position: absolute; left: 20px; top: 20px; z-index: 10; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .cart-img-wrapper img { float: left; width: 100px; height: 100px; padding: 0px; margin: 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .review-item-info { float: left; width: 100%; box-sizing: border-box; height: auto; padding: 0px 0px 0px 126px; margin: 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .item-title { float: left; width: 100%; height: 38px; overflow: hidden; margin: 0px 0px 5px 0px; color: #000; font-size: 16px; line-height: 18px;}
.stenik-checkout .checkout-review-box .review-items-box .review-item dl.item-options { float: left; width: 100%; box-sizing: border-box; height: auto; margin: 0px 0px 5px 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item dl.item-options dt { float: left; width: auto; height: auto; margin: 0px 4px 0px 0px; padding: 0px; color: #8f8f8f; font-size: 13px; line-height: 15px; font-weight: 400; clear: left; }
.stenik-checkout .checkout-review-box .review-items-box .review-item dl.item-options dd { float: left; width: auto; height: auto; margin: 0px; padding: 0px; color: #8f8f8f; font-size: 13px; line-height: 15px; font-weight: 400; clear: right; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .item-qty { float: left; width: auto; height: auto; margin: 3px 3px 0px 0px; font-size: 16px; line-height: 20px; color: #666; }

.stenik-checkout .checkout-review-box .review-items-box .review-item .price-box { float: right; width: 100px; text-align: right; margin: 4px 0 0 0; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .regular-price { display: inline-block; color: #000; font-size: 16px; line-height: 18px; font-weight: 700; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .old-price { display: inline-block; width: auto; height: auto; margin: 0px 0px 0px 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .old-price .cart-price { margin: 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .old-price .price { color: #666; font-size: 16px; line-height: 18px; font-weight: 400; text-decoration: line-through; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .special-price { display: inline-block; width: auto; height: auto; margin: 0px; }
.stenik-checkout .checkout-review-box .review-items-box .review-item .special-price .price { color: #cc1153; font-size: 16px; line-height: 18px; font-weight: 700; }
.stenik-checkout .checkout-review-box .total-table { width: 100%; border-collapse: collapse; margin: 0px; }
.stenik-checkout .checkout-review-box .total-table th { text-align: left; padding: 0px 10px 10px 20px; font-size: 18px; background: #fff; }
.stenik-checkout .checkout-review-box .total-table td { text-align: right; padding: 0px 20px 10px 10px; font-size: 18px; background: #fff;}
.stenik-checkout .checkout-review-box .total-table .totalCartPrice th { padding: 24px 10px 24px 20px; font-weight: 700; background: #f8f8f8; border-top: 1px solid #ebebeb; font-size: 21px; }
.stenik-checkout .checkout-review-box .total-table .totalCartPrice td { padding: 24px 20px 24px 10px; font-weight: 700; background: #f8f8f8; border-top: 1px solid #ebebeb; font-size: 21px; }
.stenik-checkout .checkout-review-box .total-table tfoot tr:nth-child(1) td { padding-top: 8px; }
.stenik-checkout .checkout-review-box .total-table tfoot tr:nth-child(1) th { padding-top: 8px; }
.stenik-onepage-section-overlay .loaderIcon { width: 28px; height: 28px; margin: -14px 0px 0px -14px; position: absolute; left: 50%; top: 50%; z-index: 9999; background: url(//localhost:8580/skin/frontend/stenik/default/images/preloader-28x28.**********.gif) no-repeat center center; }
.stenik-checkout .coupon-review-col .stenik-onepage-section-overlay .loaderIcon { top: 65px; margin-top: 0px; }

.stenik-onepage-section-overlay.stenik-checkout-overlay { display: none; width: 100%; height: 100%; background: rgba(255,255,255,0.5); position: absolute; top: 0px; left: 0px; z-index: 9999; }
.stenik-onepage-section-overlay.stenik-checkout-overlay .loaderIcon { position: fixed; }

.stenik-checkout .buttons-set { float: left; width: 100%; height: auto; padding: 0px; margin: 0px 0px 40px 0px; }
.stenik-checkout .buttons-set .button.checkout-color { min-width: 280px; height: 60px; }

.must-login-checkout { position: relative; height: 500px; }
.login-first-msg { float: left; width: 100%; height: auto; padding: 20px 15px 20px 15px; margin: 10px 0px 20px 0px; background: #f8f8f8; color: #000; font-size: 16px; line-height: 18px; font-weight: normal; }
.social-login-checkout { float: left; width: 90%; }
.social-login-checkout .social-login { float: left; width: 100%; }
.social-login-checkout .social-login .facebook-login { padding-left: 12px; padding-right: 12px; }
.social-login-checkout .social-login .gplus-login { padding-left: 14px; padding-right: 14px; }
.stenik-checkout.must-login-checkout .coupon-review-col { top: 20px; }


/* Stenik Checkout Econt And Stenik Checkout Rapido Styles
*************************************************************/

.delivery-to-wrapper { float: left; width: 100%; box-sizing: border-box; position: relative; height: auto; padding: 15px 0; margin: 10px 0 0 0; }
.stenik-checkout .delivery-to-wrapper:before{ content: ''; display: block; height: 1px; width: 810px; position: absolute; top: 0; left: -53px; background: #ebebeb; }
.delivery-to-wrapper .form-list { float: left; width: 100%; }
.delivery-to-wrapper .step-sub-title { padding: 0px 0px 15px 0px; color: #000; font-size: 16px; line-height: 20px; font-weight: 700; }
.delivery-to-wrapper .step-sub-title em { font-style: normal; margin-left: 3px; }
.delivery-to-wrapper .standart-address-fileds-wrapper { margin: 0px -12px 0px -12px; }
.shipping-methods-group { float: left; width: 100%; height: auto; margin: 0px 0px 10px 0px; }
.shipping-methods-group .shippingMethod { margin: 0 0 10px 0; }
.shipping-methods-group .shippingMethod input.radio.prechoose { float: left; margin: 4px 10px 0px 0px; }
.delivery-to-wrapper #extensa_econt-form { padding: 0; margin: 0px; }
.delivery-to-wrapper #extensa_econt-form input.input-text { width: 100%; height: 40px; padding: 0px 10px 0px 10px; box-sizing: border-box; }
.delivery-to-wrapper #extensa_econt-form p.comb-text { width: 100%; padding: 0 0 12px 12px; box-sizing: border-box; }
.delivery-to-wrapper #extensa_econt-form .extensa_econt_services { padding: 0px; float: left; width: 100%; }
.delivery-to-wrapper #extensa_econt-form .extensa_econt_services p { margin-bottom: 5px; }
.delivery-to-wrapper #extensa_econt-form .officeLocator button.button { margin-top: 27px; background: #000; height: 40px; min-width: 200px; line-height: 40px;}
.delivery-to-wrapper #extensa_econt-form .officeLocator button.button:hover { background: #cc1153; }
.delivery-to-wrapper #extensa_rapido-form { padding: 10px 0px 10px 0px; margin: 0px -20px 0px -20px; }
.delivery-to-wrapper p.error-msg { padding: 0px; float: left; width: 100%; box-sizing: border-box; margin: 0 0 10px 0; color: #e7352b; }
.stenik-checkout .autocomplete { width: 100%; margin: 0px; padding: 0px; background: #fff; border: 1px solid #cbcbcb; border-radius: 0px; position: absolute; left: 0px; top: 38px; z-index: 10000; }
.stenik-checkout .autocomplete ul { margin: 0px; padding: 0px; }
.stenik-checkout .autocomplete ul li.selected { background: #ccc; }
.stenik-checkout .autocomplete ul li { padding: 6px 10px; cursor: pointer; text-align: left; color: #000; font-size: 14px; }
.stenik-checkout .please-wait { display: none; position: absolute; right: 5px; top: 5px; z-index: 20; }
.stenik-checkout .please-wait .v-middle { width: 28px; height: 28px; }
.stenik-checkout .select2-container { width: 100% !important; height: 40px; margin: 0px 0px 8px 0px; outline: none; font-size: 14px; }
.stenik-checkout .select2-container .select2-selection { height: 40px; border: 1px solid #dbdbdb; border-radius: 0px; outline: none;  }
.stenik-checkout .select2-container .select2-selection .select2-selection__rendered { height: 38px; line-height: 38px; padding: 0px 30px 0px 10px; outline: none; font-size: 13px; color: #000; text-decoration: none; font-weight: normal; }
.stenik-checkout .select2-container.select2-container--open .select2-selection .select2-selection__rendered{ background: #f7f7f7; }
.stenik-checkout .select2-container .select2-selection .select2-selection__arrow { height: 38px; width: 29px; border-radius: 0px; background: url(//localhost:8580/skin/frontend/stenik/default/images/select-arrow.**********.png) no-repeat center center; }
.stenik-checkout .select2-container .select2-selection .select2-selection__arrow b { display: none; }
.stenik-checkout .select2-container--default .select2-results__option--highlighted { color: #333 !important; background: #ccc !important; }
.stenik-checkout .select2-container--default .select2-search--dropdown .select2-search__field { border: 1px solid #dbdbdb; height: 30px; outline: none; }
.stenik-checkout .select2-container--default .select2-search--dropdown .select2-search__field:focus { outline: none; }
.stenik-checkout .select2-dropdown{ border-radius: 0; border: 1px solid #cbcbcb; color: #000; }
.stenik-checkout .select2-results__option { font-size: 14px; }
.checkout-onepage-index .select2-results { font-size: 14px; color: #000; }
.checkout-onepage-index .select2-dropdown { border-radius: 0; }
.checkout-onepage-index .select2-container.select2-container--open .select2-dropdown { background: #f7f7f7; }

.my-account .delivery-to-wrapper #extensa_econt-form { margin: 0px; }
.my-account .delivery-to-wrapper #extensa_rapido-form { margin: 0px; }
.my-account .delivery-to-wrapper #extensa_econt-form p.comb-text { padding: 0px 0px 20px 0px; margin: 0;}
.my-account .delivery-to-wrapper #extensa_econt-form p.error-msg { color: #fb3b3b; }
.my-account .delivery-to-wrapper #extensa_econt-form .extensa_econt_services { padding: 0px 0px 20px 0px; margin: 0;}
.my-account p.error-msg { padding: 0px 0px 10px 0px; color: #e7352b; }
.my-account ul.form-list li.fields.econt-door-address-fields { width: 100%; margin: 0px; }
.my-account ul.form-list li.fields.econt-door-address-fields ul li.fields { margin: 0px -12px; }
.my-account ul.form-list li.fields.econt-door-address-fields ul li.fields .field { display: inline-block; box-sizing: border-box; float: left; width: 50%; padding: 0 12px; margin: 0px 0 0px 0px; vertical-align: top; }
.my-account ul.form-list li.fields.econt-office-address-fields { width: 100%; margin: 0px; }
.my-account ul.form-list li.fields.econt-office-address-fields ul li.fields { margin: 0px -12px; }
.my-account ul.form-list li.fields.econt-office-address-fields ul li.fields .field { display: inline-block; box-sizing: border-box; float: left; width: 50%; padding: 0 12px; margin: 0px 0 0px 0px; vertical-align: top; }
.my-account ul.form-list li.fields.econt-door-address-fields .validation-advice{ margin: 0 0 5px 0; }
.my-account ul.form-list li#extensa_rapido-to_door { width: 100%; margin: 0px; }
.my-account ul.form-list li#extensa_rapido-to_door ul li.fields { width: 100%; margin: 0px; }
.my-account ul.form-list li#extensa_rapido-to_door ul li.fields.wide { width: 98%; margin: 0px; }
.my-account ul.form-list li#extensa_rapido-to_door ul li.fields .field { display: inline-block; width: 100%; margin: 0px 0 0px 0px; vertical-align: top; }
.my-account .rapido-address-fields-content li.control .field { display: inline-block; width: 100%; margin: 0px 0 0px 0px; vertical-align: top; }
.my-account .select2-container { width: 100% !important; height: 46px; margin: 0px 0px 8px 0px; outline: none; font-size: 14px; }
.my-account .select2-container .select2-selection { height: 46px; border: 1px solid #cbcbcb; color: #000; border-radius: 0px; outline: none; }
.my-account .select2-container .select2-selection .select2-selection__rendered { height: 44px; line-height: 44px; padding: 0px 30px 0px 10px; outline: none; font-size: 14px; text-decoration: none; font-weight: normal; }
.my-account .select2-container.select2-container--open .select2-selection .select2-selection__rendered { background: #f7f7f7; }
.my-account .select2-container .select2-selection .select2-selection__arrow { height: 44px; width: 29px; border-radius: 0px; background: url(//localhost:8580/skin/frontend/stenik/default/images/select-arrow.**********.png) no-repeat center center; }
.my-account .select2-container .select2-selection .select2-selection__arrow b { display: none; }
.my-account .prechooseShippingMethods .shippingMethod{  float: left; width: 100%; margin: 0 0 15px 0; }
.my-account .prechooseShippingMethods label{ width: 90%; margin: 0; line-height: 22px; }
.my-account .prechooseShippingMethods input.radio{ float: left; margin: 4px 6px 0 0; }
.customer-address-form .select2-results { font-size: 14px; color: #000; }
.customer-address-form .select2-dropdown { border-radius: 0; }
.customer-address-form .select2-container.select2-container--open .select2-dropdown { background: #f7f7f7; }
.my-account .fields.addressDetails { margin: 0 -12px; }
.my-account .fields.addressDetails .field{ float: left; width: 50%; padding: 0 12px; box-sizing: border-box; }
.my-account .delivery-to-wrapper { padding: 24px; background: #f8f8f8; }


/* FILE: colorbox.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
/*
    Colorbox Core Style:
    The following CSS is consistent between example themes and should not be altered.
*/
#colorbox, #cboxOverlay, #cboxWrapper{position:absolute; top:0; left:0; z-index:9999; overflow:hidden; -webkit-transform: translate3d(0,0,0);}
#cboxWrapper {max-width:none;}
#cboxOverlay{position:fixed; width:100%; height:100%;}
#cboxMiddleLeft, #cboxBottomLeft{clear:left;}
#cboxContent{position:relative;}
#cboxLoadedContent{overflow:auto; -webkit-overflow-scrolling: touch;}
#cboxTitle{margin:0; display: none !important;}
#cboxLoadingOverlay, #cboxLoadingGraphic{position:absolute; top:0; left:0; width:100%; height:100%;}
#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow{cursor:pointer;}
.cboxPhoto{float:left; margin:auto; border:0; display:block; max-width:none; -ms-interpolation-mode:bicubic;}
.cboxIframe{width:100%; height:100%; display:block; border:0; padding:0; margin:0;}
#colorbox, #cboxContent, #cboxLoadedContent{box-sizing:content-box; -moz-box-sizing:content-box; -webkit-box-sizing:content-box;}

/*
    User Style:
    Change the following styles to modify the appearance of Colorbox.  They are
    ordered & tabbed in a way that represents the nesting of the generated HTML.
*/
#cboxOverlay{background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/overlay.**********.png) repeat 0 0; opacity: 0.9; filter: alpha(opacity = 90);}
#colorbox{outline:0;}
    #cboxTopLeft{width:21px; height:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -101px 0;}
    #cboxTopRight{width:21px; height:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -130px 0;}
    #cboxBottomLeft{width:21px; height:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -101px -29px;}
    #cboxBottomRight{width:21px; height:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -130px -29px;}
    #cboxMiddleLeft{width:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) left top repeat-y;}
    #cboxMiddleRight{width:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) right top repeat-y;}
    #cboxTopCenter{height:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/border.**********.png) 0 0 repeat-x;}
    #cboxBottomCenter{height:21px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/border.**********.png) 0 -29px repeat-x;}
    #cboxContent{background:#fff; overflow:hidden;}
        .cboxIframe{background:#fff;}
        #cboxError{padding:50px; border:1px solid #ccc;}
        #cboxLoadedContent{margin-bottom:0px; margin-top: 25px;}
        #cboxTitle{position:absolute; bottom:4px; left:0; text-align:center; width:100%; color:#949494;}
        #cboxCurrent{position:absolute; bottom:4px; left:58px; color:#949494;}
        #cboxLoadingOverlay{background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/loading_background.**********.png) no-repeat center center;}
        #cboxLoadingGraphic{background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/loading.**********.gif) no-repeat center center;}

        /* these elements are buttons, and may need to have additional styles reset to avoid unwanted base styles */
        #cboxPrevious, #cboxNext, #cboxSlideshow, #cboxClose {border:0; padding:0; margin:0; overflow:visible; width:auto; background:none; }

        /* avoid outlines on :active (mouseclick), but preserve outlines on :focus (tabbed navigating) */
        #cboxPrevious:active, #cboxNext:active, #cboxSlideshow:active, #cboxClose:active {outline:0;}

        #cboxSlideshow{position:absolute; bottom:4px; right:30px; color:#0092ef;}
        #cboxPrevious{position:absolute; bottom:0; left:0; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -75px 0; width:25px; height:25px; text-indent:-9999px;}
        #cboxPrevious:hover{background-position:-75px -25px;}
        #cboxNext{position:absolute; bottom:0; left:27px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -50px 0; width:25px; height:25px; text-indent:-9999px;}
        #cboxNext:hover{background-position:-50px -25px;}
        #cboxClose{position:absolute; top:0; right:0px; background:url(//localhost:8580/skin/frontend/stenik/default/css/colorbox/images/controls.**********.png) no-repeat -25px 0; width:25px; height:25px; text-indent:-9999px;}
        #cboxClose:hover{background-position:-25px -25px;}

/*
  The following fixes a problem where IE7 and IE8 replace a PNG's alpha transparency with a black fill
  when an alpha filter (opacity change) is set on the element or ancestor element.  This style is not applied to or needed in IE9.
  See: http://jacklmoore.com/notes/ie-transparency-problems/
*/
.cboxIE #cboxTopLeft,
.cboxIE #cboxTopCenter,
.cboxIE #cboxTopRight,
.cboxIE #cboxBottomLeft,
.cboxIE #cboxBottomCenter,
.cboxIE #cboxBottomRight,
.cboxIE #cboxMiddleLeft,
.cboxIE #cboxMiddleRight {
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#00FFFFFF,endColorstr=#00FFFFFF);
}


/* FILE: jquery.jscrollpane.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
/*
 * CSS Styles that are needed by jScrollPane for it to operate correctly.
 *
 * Include this stylesheet in your site or copy and paste the styles below into your stylesheet - jScrollPane
 * may not operate correctly without them.
 */


.horizontal-only
{
	height: auto;
	max-height: 200px;
}

.jspContainer
{
	overflow: hidden;
	position: relative;
}

.jspPane {
	position: absolute;
}
.storesListing.jspScrollable .jspPane { width: 300px !important; margin-left: 20px !important; }

.jspVerticalBar
{
	position: absolute;
	top: 0px;
	right: 0px;
	width: 10px;
}

.jspHorizontalBar
{
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 16px;
	background: red;
}

.jspVerticalBar *,
.jspHorizontalBar *
{
	margin: 0;
	padding: 0;
}

.jspCap
{
	display: none;
}

.jspHorizontalBar .jspCap
{
	float: left;
}

.jspTrack {
	position: relative;
	margin-top: 0px;
	margin-bottom: 0px;
	height: 100%;
	width: 20px;
	border-radius: 0px;
	background: #e9e9e9;
	overflow: hidden;
}
.jspDrag {
	position: relative;
	width: 10px;
	top: 0px;
	left: 0;
	cursor: pointer;
	border-radius: 0px;
	background: #cc1153;
	box-shadow: none;
	overflow: hidden;
	border: none;
	-webkit-transition: background 0.2s linear; -moz-transition: background 0.2s linear; -o-transition: background 0.2s linear; transition: background 0.2s linear;
}
.jspDrag:hover { background: #b3144c; }

.responsiveMenuSub .jspDrag { background: #b3144c; }
.responsiveMenuSub .jspDrag:hover { background: #830b35; }


.jspHorizontalBar .jspTrack,
.jspHorizontalBar .jspDrag
{
	float: left;
	height: 100%;
}

.jspArrow
{
	background: #fff;
	text-indent: -20000px;
	display: block;
	cursor: pointer;
}
.jspArrow.jspDisabled
{
	cursor: default;
	background: #80808d;
}

.jspVerticalBar .jspArrow
{
	height: 16px;
	line-height:8px;
}

.jspVerticalBar .jspArrowUp {
	background: url("//localhost:8580/skin/frontend/stenik/default/images/scroll-arow-up.**********.jpg") no-repeat left 3px;
	height:12px;
}

.jspVerticalBar .jspArrowDown {
	background: url("//localhost:8580/skin/frontend/stenik/default/images/scroll-arow-down.**********.jpg") no-repeat left 3px;
	height:12px;
}

.jspHorizontalBar .jspArrow
{
	width: 16px;
	float: left;
	height: 100%;
}

.jspVerticalBar .jspArrow:focus
{
	outline: none;
}

.jspCorner
{
	background: #eeeef4;
	float: left;
	height: 100%;
}

/* Yuk! CSS Hack for IE6 3 pixel bug :( */
* html .jspCorner
{
	margin: 0 -3px 0 0;
}


/* FILE: owl.carousel.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
/* 
 * 	Core Owl Carousel CSS File
 *	v1.3.3
 */

/* clearfix */
.owl-carousel .owl-wrapper:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
/* display none until init */
.owl-carousel{
	display: none;
	position: relative;
	width: 100%;
	-ms-touch-action: pan-y;
}
.owl-carousel .owl-wrapper{
	display: none;
	position: relative;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-wrapper-outer{
	overflow: hidden;
	position: relative;
	width: 100%;
}
.owl-carousel .owl-wrapper-outer.autoHeight{
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}
	
.owl-carousel .owl-item{
	float: left;
}
.owl-controls .owl-page,
.owl-controls .owl-buttons div{
	cursor: pointer;
}
.owl-controls {
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* mouse grab icon */
.grabbing { 
    cursor:url(//localhost:8580/skin/frontend/stenik/default/css/grabbing.**********.png) 8 8, move;
}

/* fix */
.owl-carousel  .owl-wrapper,
.owl-carousel  .owl-item{
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility:    hidden;
	-ms-backface-visibility:     hidden;
  -webkit-transform: translate3d(0,0,0);
  -moz-transform: translate3d(0,0,0);
  -ms-transform: translate3d(0,0,0);
}




/* FILE: owl.transitions.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
/* 
 *  Owl Carousel CSS3 Transitions 
 *  v1.3.2
 */

.owl-origin {
	-webkit-perspective: 1200px;
	-webkit-perspective-origin-x : 50%;
	-webkit-perspective-origin-y : 50%;
	-moz-perspective : 1200px;
	-moz-perspective-origin-x : 50%;
	-moz-perspective-origin-y : 50%;
	perspective : 1200px;
}
/* fade */
.owl-fade-out {
  z-index: 10;
  -webkit-animation: fadeOut .7s both ease;
  -moz-animation: fadeOut .7s both ease;
  animation: fadeOut .7s both ease;
}
.owl-fade-in {
  -webkit-animation: fadeIn .7s both ease;
  -moz-animation: fadeIn .7s both ease;
  animation: fadeIn .7s both ease;
}
/* backSlide */
.owl-backSlide-out {
  -webkit-animation: backSlideOut 1s both ease;
  -moz-animation: backSlideOut 1s both ease;
  animation: backSlideOut 1s both ease;
}
.owl-backSlide-in {
  -webkit-animation: backSlideIn 1s both ease;
  -moz-animation: backSlideIn 1s both ease;
  animation: backSlideIn 1s both ease;
}
/* goDown */
.owl-goDown-out {
  -webkit-animation: scaleToFade .7s ease both;
  -moz-animation: scaleToFade .7s ease both;
  animation: scaleToFade .7s ease both;
}
.owl-goDown-in {
  -webkit-animation: goDown .6s ease both;
  -moz-animation: goDown .6s ease both;
  animation: goDown .6s ease both;
}
/* scaleUp */
.owl-fadeUp-in {
  -webkit-animation: scaleUpFrom .5s ease both;
  -moz-animation: scaleUpFrom .5s ease both;
  animation: scaleUpFrom .5s ease both;
}

.owl-fadeUp-out {
  -webkit-animation: scaleUpTo .5s ease both;
  -moz-animation: scaleUpTo .5s ease both;
  animation: scaleUpTo .5s ease both;
}
/* Keyframes */
/*empty*/
@-webkit-keyframes empty {
  0% {opacity: 1}
}
@-moz-keyframes empty {
  0% {opacity: 1}
}
@keyframes empty {
  0% {opacity: 1}
}
@-webkit-keyframes fadeIn {
  0% { opacity:0; }
  100% { opacity:1; }
}
@-moz-keyframes fadeIn {
  0% { opacity:0; }
  100% { opacity:1; }
}
@keyframes fadeIn {
  0% { opacity:0; }
  100% { opacity:1; }
}
@-webkit-keyframes fadeOut {
  0% { opacity:1; }
  100% { opacity:0; }
}
@-moz-keyframes fadeOut {
  0% { opacity:1; }
  100% { opacity:0; }
}
@keyframes fadeOut {
  0% { opacity:1; }
  100% { opacity:0; }
}
@-webkit-keyframes backSlideOut {
  25% { opacity: .5; -webkit-transform: translateZ(-500px); }
  75% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(-200%); }
  100% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(-200%); }
}
@-moz-keyframes backSlideOut {
  25% { opacity: .5; -moz-transform: translateZ(-500px); }
  75% { opacity: .5; -moz-transform: translateZ(-500px) translateX(-200%); }
  100% { opacity: .5; -moz-transform: translateZ(-500px) translateX(-200%); }
}
@keyframes backSlideOut {
  25% { opacity: .5; transform: translateZ(-500px); }
  75% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
  100% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
}
@-webkit-keyframes backSlideIn {
  0%, 25% { opacity: .5; -webkit-transform: translateZ(-500px) translateX(200%); }
  75% { opacity: .5; -webkit-transform: translateZ(-500px); }
  100% { opacity: 1; -webkit-transform: translateZ(0) translateX(0); }
}
@-moz-keyframes backSlideIn {
  0%, 25% { opacity: .5; -moz-transform: translateZ(-500px) translateX(200%); }
  75% { opacity: .5; -moz-transform: translateZ(-500px); }
  100% { opacity: 1; -moz-transform: translateZ(0) translateX(0); }
}
@keyframes backSlideIn {
  0%, 25% { opacity: .5; transform: translateZ(-500px) translateX(200%); }
  75% { opacity: .5; transform: translateZ(-500px); }
  100% { opacity: 1; transform: translateZ(0) translateX(0); }
}
@-webkit-keyframes scaleToFade {
  to { opacity: 0; -webkit-transform: scale(.8); }
}
@-moz-keyframes scaleToFade {
  to { opacity: 0; -moz-transform: scale(.8); }
}
@keyframes scaleToFade {
  to { opacity: 0; transform: scale(.8); }
}
@-webkit-keyframes goDown {
  from { -webkit-transform: translateY(-100%); }
}
@-moz-keyframes goDown {
  from { -moz-transform: translateY(-100%); }
}
@keyframes goDown {
  from { transform: translateY(-100%); }
}

@-webkit-keyframes scaleUpFrom {
  from { opacity: 0; -webkit-transform: scale(1.5); }
}
@-moz-keyframes scaleUpFrom {
  from { opacity: 0; -moz-transform: scale(1.5); }
}
@keyframes scaleUpFrom {
  from { opacity: 0; transform: scale(1.5); }
}

@-webkit-keyframes scaleUpTo {
  to { opacity: 0; -webkit-transform: scale(1.5); }
}
@-moz-keyframes scaleUpTo {
  to { opacity: 0; -moz-transform: scale(1.5); }
}
@keyframes scaleUpTo {
  to { opacity: 0; transform: scale(1.5); }
}


/* FILE: responsive.css */
/* HANDLES: default,cms_page,STORE_default,THEME_frontend_stenik_default,cms_index_index,page_one_column,customer_logged_out */
@charset "utf-8";
/* CSS Document
   Author: Veselin Trifonov as developer @ www.stenikgroup.com
*/



/* Start Media Queries for tablets
****************************************/
@media only screen and (min-width: 1025px) and (max-width: 1260px) {
    .stenik-checkout .col-xs-8{ width: 620px; }
    .stenik-checkout .delivery-to-wrapper::before{ width: 620px; left: -21px; }
    .stenik-checkout .coupon-review-wrapper.sticky-sidebar { transform: translateX(174%); -webkit-transform: translateX(142%); }
    .stenik-checkout .stenik-onepage-section { padding: 15px 20px; }
    .checkout-onepage-index .wrapper { width: 1020px; }
    .checkout-onepage-index header nav.mainNav ul li a { padding: 0px 15px 4px 15px; margin: 0px 10px 0px 10px; }
    .checkout-onepage-index header .headerPhone { margin-right: 10px; width: 160px; }
    .checkout-onepage-index header .headerTopDelivery { min-width: 390px;}
    .checkout-onepage-index footer .footerPaymentContent .footerPaymentInfo { width: 490px;}
    .checkout-onepage-index footer .footerPaymentContent .footerPaymentBox { width: 490px;}
    .checkout-onepage-index footer .footerNewsLetterContent { background-size: cover; }
    .checkout-onepage-index footer .newsLetterFotoer .newsLetterInfo { width: 490px; }
    .checkout-onepage-index footer .newsLetterFotoer .newsletterForm { width: 490px; }
    .checkout-onepage-index footer .newsLetterFotoer .newsletterForm input.newsletterInput { width: 320px; }
    .checkout-onepage-index footer .footerNewsLetterContent { padding: 30px 0px 11px 0px; height: 140px; }
    .checkout-onepage-index footer .footerContent .footerCol { width: 235px; }
    .stenik-checkout #extensa_econt-form .fields .field { width: 45%; }
}

@media only screen and (min-width: 600px) and (max-width: 1024px) {

    .wrapper { width: 600px; }

    body { overflow-x: hidden; }

    header { display: none; }
    .responsiveHeader { display: block; }
    .responsiveHeader .responsiveMenu.open .responsiveMenuSub { width: 400px !important; }

    body > .widget.widget-static-block {margin-top:50px;}
    #main { padding: 50px 0 0; }
    body > .widget.widget-static-block ~ #main {padding-top: 10px;}
    .wideContent { padding: 40px 0px 0px 0px; }
    .cms-index-index .wideContent { padding: 40px 0px 0px 0px; margin: 0px 0px 0px 0px; }
    .mainContent h1 { margin: 10px 0px 20px 0px; }
    #main.col2-left-layout .mainContent { width: 600px; margin: 0px 0px 0px 0px; }
    #main .pageTitleWrapper { padding: 0px; }
    .catalogsearch-result-index #main h1 {font-size: 32px; line-height: 38px;}

    .headerSearch { margin: 14px auto 14px auto; }

    nav.breadcrumbs { display: none; }

    #main .homepageBannerLeftToSlider { width: 144px; height: 252px; margin: 7px 8px 8px 0px; }
    #main .widgetBannerBox { width: 144px; height: 252px; padding: 58px 0px 0px 0px; }
    #main .widgetBannerBox img { width: 144px; height: 252px; }
    #main .widgetBannerBox .titleWrapper span.title { font-size: 18px; line-height: 20px; padding: 0px 20px 0px 20px; }
    #main .widgetBannerBox .titleWrapper span.title:before { width: 12px; height: 1px; left: 4px; top: 12px; }
    #main .widgetBannerBox .titleWrapper span.title:after { width: 12px; height: 1px; right: 4px; top: 12px; }
    #main .widgetBannerBox .subTitle { margin: 0px 0px 12px 0px; font-size: 12px; line-height: 14px; }
    #main .widgetBannerBox .line { width: 118px; margin: 0px 0px 12px -59px; }
    #main .widgetBannerBox .widgetDescription { margin: 0px 0px 26px 0px; font-size: 12px; line-height: 14px; }
    #main .widgetBannerBox .buyBtn { font-size: 10px; line-height: 22px; height: 22px; min-width: 60px; margin: 0px 0px 0px -40px; padding: 0px 6px 0px 6px; }

    #main .homepageMainBannerSlider { width: 448px; height: 252px; margin: 7px 0px 8px 0px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox { width: 240px; height: 200px; top: 34px; }
    #main .homepageMainBannerSlider .item .sliderIMGLink img { float: left; width: 100%; height: auto; }
    #main .homepageMainBannerSlider .owl-controls .owl-prev { width: 40px; height: 40px; top: -112px; }
    #main .homepageMainBannerSlider .owl-controls .owl-next { width: 40px; height: 40px; top: -112px; }
    #main .homepageMainBannerSlider .owl-controls { width: 368px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox .sliderTitle { font-size: 24px; line-height: 26px; margin: 0px 0px 10px 0px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox .slidersubTitle { font-size: 11px; line-height: 13px; margin: 0px 0px 18px 0px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox .buyBtn { font-size: 10px; line-height: 22px; height: 22px; min-width: 60px; }

    #main .homepageBannersContent { margin: 0px 0px 15px 0px; }
    #main .homepageBannersContent .textPage { width: 608px; }
    #main .categoryWidgetBox { width: 144px; height: 144px; margin: 0px 8px 8px 0px; }
    #main .categoryWidgetBox img.banner { width: 100%; height: auto; }
    #main .categoryWidgetBox img.icon { width: 60px; height: 60px; margin: 18px 0px 0px -30px; }
    #main .categoryWidgetBox:hover img.icon { margin-top: 12px; }
    #main .categoryWidgetBox .line { width: 100px; margin: 0 0 14px 22px }
    #main .categoryWidgetBox:hover .line { margin-bottom: 24px; }
    #main .categoryWidgetBox span.title { font-size: 14px; line-height: 16px; padding: 0px 24px 0px 24px; }

    #main .homepageInfoContent .textPage h1 { font-size: 18px; line-height: 20px; }

    .toolbarBox .toolbarRightPosition { margin-right: 133px; }
    #main .featureProductsListing { margin: 0px 0px 45px 0px; }
    #main .productListingSlider { width: 600px; }
    #main .featureProductsListing .title { width: 60%; margin: 0px 0px 20px 0px; font-size: 28px; line-height: 30px; }
    #main .featureProductsListing .viewAllBtn { height: 30px; min-width: 80px; font-size: 12px; line-height: 30px; margin: 0px 0px 0px 0px; }
    .productBox { width: 140px; height: 255px; margin: 5px 5px 5px 5px; }
    .productBox .productIMGLink { width: 140px; height: 140px; }
    .productBox .productIMGLink .onsale-category-container { transform: scale(0.8); }
    .productBox .productTitle { font-size: 12px; line-height: 15px; height: 48px; }
    .productBox .price-box .regular-price .price { font-size: 12px; line-height: 14px; }
    .productBox .price-box .old-price .price { font-size: 12px; line-height: 14px; }
    .productBox .price-box .special-price .price { font-size: 12px; line-height: 14px; }
    #main .productListingSlider .owl-controls .owl-pagination { display: block; }
    #main .productListingSlider .owl-controls .owl-buttons { display: none; }
    #main .productListing { width: 600px; }

    #main .parallaxWidgetBox { width: 1100px; height: 275px; margin: 0px 0px 0px -550px; }
    #main .parallaxWidgetBox .overlay { display: none; }
    #main .parallaxWidgetBox .bannerIMGLink { height: 275px; }
    #main .parallaxWidgetBox .parallax img { width: 100%; height: auto; min-width: 100%; min-height: auto; }
    #main .parallaxWidgetBox .parallaxInfoContent { width: 305px; top: 48px; }
    #main .parallaxWidgetBox .paralaxBannerTitle { font-size: 26px; line-height: 28px; margin: 0px 0px 15px 0px; }
    #main .parallaxWidgetBox .paralaxDescription { font-size: 14px; line-height: 16px; margin: 0px 0px 20px 0px; }
    #main .parallaxWidgetBox .viewMoreBtn { font-size: 12px; line-height: 25px; height: 25px; min-width: 80px; text-align: center; }

    #main .homepageBannersContent.wideContent { width: 1100px; margin: 0px 0px 30px -550px; padding: 30px 0px 30px 0px; }
    #main .servicesWidgetBox { width: 110px; height: 194px; margin: 0px 20px 20px 20px; }
    #main .servicesWidgetBox .iconWrapper { width: 70px; height: 70px; margin: 0px 20px 12px 20px; }
    #main .servicesWidgetBox .iconWrapper img { width: 40px; height: 40px; margin: -20px 0px 0px -20px }
    #main .servicesWidgetBox span.title { font-size: 12px; line-height: 14px; }
    #main .servicesWidgetBox:hover span.title { margin: -4px 0px 8px 0px; }
    #main .servicesWidgetBox span.subTitle { font-size: 12px; line-height: 14px; }

    #main .homepageBrandsContent .homepageBrandsListing { width: 600px; }
    #main .homepageBrandsContent .homepageBrandsTitle { width: 60%; margin: 0px 0px 20px 0px; font-size: 28px; line-height: 30px; }
    #main .homepageBrandsContent .allBrandsBtn { height: 30px; min-width: 80px; font-size: 12px; line-height: 30px; margin: 0px 0px 0px 0px; }
    .brandsListingWrapper { width: 600px; }
    .brandDescriptionWrapper .brandImage { float: left; margin: 0px 0px 20px 0px; }

    .filtersContentWrapper.sticky { position: relative; width: 600px; }
    .filterContent { position: relative; }
    .filterContent .responsiveOpenFilters { display: block; }
    .filterContent .filtersWrapper { display: none; width: 300px; padding: 0px; border-top: none; position: absolute; left: 150px; top: 43px; z-index: 200; }
    .filterContent .filtersWrapper .block-layered-nav { width: 298px; }
    .filterContent .dropDownfilter { width: 50%; margin: 0px 0px 0px 0px; }
    .filterContent .dropDownfilter .openFilters { width: 100%; font-size: 13px; }
    .filterContent .dropDownfilter.sort { width: 40px; }
    .filterContent .dropDownfilter ul.subOptions { width: 299px; }
    .filterContent .dropDownfilter .layer-slider.subOptions { padding-top: 5px; }
    .filterContent .dropDownfilter .price-slider { display: none; }

    .pagingBox .paging a.prev { width: 60px; font-size: 0px; text-indent: -9999px; margin: 0px 0px 0px 0px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingPrev.**********.png") 22px 18px no-repeat }
    .pagingBox .paging a.next { width: 60px; font-size: 0px; text-indent: -9999px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingNext.**********.png") 22px 18px no-repeat }
    .pagingBox .paging .pagingItems { width: 480px; }
    .pagingBox .paging a, .pagingBox .paging span { width: 48px; font-size: 22px; }

    .catalog-seo-sitemap-product .paging a.prev,
    .catalog-seo-sitemap-category .paging a.prev { width: 60px; font-size: 0px; text-indent: -9999px; margin: 0px 0px 0px 0px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingPrev.**********.png") 22px 18px no-repeat }
    .catalog-seo-sitemap-product .paging a.next,
    .catalog-seo-sitemap-category .paging a.next { width: 60px; font-size: 0px; text-indent: -9999px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingNext.**********.png") 22px 18px no-repeat }
    .catalog-seo-sitemap-product .paging .pagingItems,
    .catalog-seo-sitemap-category .paging .pagingItems { width: 480px; }
    .catalog-seo-sitemap-product .paging a,
    .catalog-seo-sitemap-product .paging span,
    .catalog-seo-sitemap-category .paging a,
    .catalog-seo-sitemap-category .paging span { width: 48px; font-size: 22px; }

    .productViewContent .productViewLeft { width: 600px; }
    .productViewContent .productViewGallery { width: 600px; }
    .productViewContent .productViewGallery .royalSlider.rsUni { width: 600px; height: 528px; }
    .productViewContent .productViewGallery .royalSlider.rsUni.oneImage { width: 600px; height: 528px; }
    .productViewContent .productViewGallery .royalSlider.rsUni.oneImage .rsOverflow { padding: 0px 0px 0px 40px; }
    .productViewContent .productViewRight { width: 600px; }
    #main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav li a { margin: 0px 0px 0px 0px; padding: 0 7px; font-size: 14px; }
    #main .lastSeenItemsContent { padding: 10px 20px 10px 20px; }
    #main .lastSeenItemsContent .lastSeenItems .lastSeenItem { width: 102px; height: 102px; margin: 0px 5px 20px 5px; }
    #main .lastSeenItemsContent .lastSeenItems .lastSeenItem img { width: 102px; height: 102px; }
    #main .productViewTabsContnt .commentsFormWrapper { width: 600px; }
    #main .productViewTabsContnt .noReviewsWrapper { width: 600px; }
    #main .productViewTabsContnt .commentsByUsers { width: 600px; }
    .leaseCalculator .horizontalScrollWrapper { overflow-x: scroll; }
    .leaseCalculator .horizontalScrollWrapper::-webkit-scrollbar { -webkit-appearance: none; width: 7px; }
    .leaseCalculator .horizontalScrollWrapper::-webkit-scrollbar-thumb { border-radius: 4px; background-color: #ccc; }
    .lease-calculator .horizontal-scroll-wrapper { overflow-x: scroll; }
    .lease-calculator .horizontal-scroll-wrapper::-webkit-scrollbar { -webkit-appearance: none; width: 7px; }
    .lease-calculator .horizontal-scroll-wrapper::-webkit-scrollbar-thumb { border-radius: 4px; background-color: #ccc; }

    .textPage table.styles.attributes { width: 600px; margin: 0px 0px 20px 0px; }

    .productLeasingCalc .leaseCalculator { width: 520px; margin: 10px 0px 10px 0px; }
    .videoPopUpContent { width: 500px; height: 350px; }
    .videoPopUpContent iframe { width: 100%; height: 330px; }

    .advanced-gifts-wrapper { width: 100%; }

    .shoppingCartContent .shoppingCartItems { width: 600px; margin: 0px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .col1 { width: 252px; }
    .shoppingCartContent .shoppingCartItems .col2 { width: 100px; padding: 0px; }
    .shoppingCartContent .shoppingCartItems .col3 { width: 100px; padding: 0px; }
    .shoppingCartContent .shoppingCartItems .col4 { width: 100px; padding: 0px; }
    .shoppingCartContent .shoppingCartItems .col5 { width: 48px; }
    .shoppingCartContent .shoppingCartItems .row { width: 600px; padding: 10px 0px 10px 0px; margin: 0px; }
    .shoppingCartContent .shoppingCartItems .row.headerRow { padding: 5px 0px 5px 0px; width: 600px; margin: 0px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell { padding: 5px 0px 10px 0px; font-size: 16px; height: 46px; line-height: 46px; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell.col1 { width: 234px; padding-left: 18px; }
    .shoppingCartContent .shoppingCartItems .productIMGLink { width: 60px; height: 60px; margin-right: 10px; }
    .shoppingCartContent .shoppingCartItems .productIMGLink img { width: 60px; height: 60px; }
    .shoppingCartContent .shoppingCartItems .cartInfoContent { float: left; width: 146px; }
    .shoppingCartContent .shoppingCartItems .itemSKU { width: auto; font-size: 10px; }
    .shoppingCartContent .shoppingCartItems .itemEdit { font-size: 10px; }
    .shoppingCartContent .shoppingCartItems .itemTitle { font-size: 13px; line-height: 15px; }
    .shoppingCartContent .shoppingCartItems .itemInfoBox { width: 165px; }
    .shoppingCartContent .shoppingCartItems .itemBrand { width: 100%; }
    .shoppingCartContent .shoppingCartItems .itemName { width: 100%; font-size: 13px; line-height: 15px; }
    .shoppingCartContent .shoppingCartItems .itemOption { width: 100%; font-size: 13px; }
    .shoppingCartContent .shoppingCartItems .qtyContent { margin: 12px 0px 0px 3px; padding: 0px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .price-box { margin: 12px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .price-box .old-price { margin: 0px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .price-box .old-price .price { font-size: 12px; }
    .shoppingCartContent .shoppingCartItems .price-box .special-price .price { font-size: 12px; }
    .shoppingCartContent .shoppingCartItems .price-box .regular-price { margin: 13px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .price-box .regular-price .price { font-size: 12px; }
    .shoppingCartContent .shoppingCartItems .itemDelete { margin: 16px 0px 0px 14px; }
    .shoppingCartContent .underCartContent { width: 600px; margin: 0px 0px 30px 0px; }
    .shoppingCartContent .promoBoxContinueShoppingWrapper { width: 50%; padding-right: 6px; min-height: 100px; box-sizing: border-box; }
    .shoppingCartContent .cartPromoBox input.promoInput { width: 175px; }
    .shoppingCartContent .continueShoppingBox { position: relative; margin: 30px 0px 0px 0px; }
    .shoppingCartRight { width: 48%; box-sizing: border-box; }
    .shoppingCartRight .underCartTotalBox { width: 100%; }
    .shoppingCartContent .cartPromoBox { width: 48%; }
    .shoppingCartContent .cartPromoBox .validation-advice { width: 232px; }
    .underCartContent .topCartBtn { display: none; }
    .shoppingCartContent .shoppingCartItems .amountBox { width: 99px; margin: 18px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner { width: 99px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner input.amount { width: 33px; height: 31px; margin: 0px 0px 0px 33px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-up { width: 31px; height: 31px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-down { width: 31px; height: 31px; }
    .shoppingCartContent .shoppingCartItems .amountBox .loader { margin: 0px 0px 0px -18px; top: 33px; }

    .newsListingWrapper { width: 600px; margin: 0px 0px 30px 0px; }
    .newsListingWrapper .titleSection { margin: 0px 0px 50px 0px; }
    .newsListingWrapper .newsBox { margin: 0px 10px 22px 10px; }
    .col2-left-layout .newsListingWrapper { width: 600px; }
    .col2-left-layout .newsListingWrapper .newsBox { margin-right: 10px; }

    .innerNewsWrapper .newsImage { float: left; margin: 18px 0px 18px 0px; }

    .loginRegBox { padding: 20px 20px 20px 0px; min-height: 461px; }
    .loginRegBox .buttons-set {  }
    .loginRegBox .goToRegistrationBtn { width: 256px; margin: 4px 0px 0px 0px; }
    .loginRegBox.login { padding: 20px 0px 20px 20px; }
    .loginRegBox.login button.button { width: 280px; }
    #main .loginRegBox.login .facebookLoginBtn { width: 280px; padding: 0px 0px 2px 102px; margin: 0px 0px 15px 0px; background: #3b5791 url("//localhost:8580/skin/frontend/stenik/default/images/facebookLoginIcon.**********.png") 70px 13px no-repeat; }
    #main .loginRegBox.login .facebookLoginBtn:hover { padding: 0px 0px 2px 108px; background: #3b5791 url("//localhost:8580/skin/frontend/stenik/default/images/facebookLoginIcon.**********.png") 76px 13px no-repeat; }
    #main .loginRegBox.login .gPlusLoginBtn { width: 280px; padding: 0px 0px 2px 102px; margin: 0px 0px 15px 0px; background: #e04b33 url("//localhost:8580/skin/frontend/stenik/default/images/GPlusLoginIcon.**********.png") 70px 11px no-repeat; }
    #main .loginRegBox.login .gPlusLoginBtn:hover { padding: 0px 0px 2px 108px; background: #e04b33 url("//localhost:8580/skin/frontend/stenik/default/images/GPlusLoginIcon.**********.png") 76px 11px no-repeat; }
    .loginRegBox h2 { font-size: 26px; }
    .loginRegBox .forgotpassword { margin: 0px 0px 30px 0px; }
    .loginRegBox.login input.input-text { width: 100%; }
    .loginRegBox.register { padding: 20px 20px 20px 0px; }
    .loginRegBox.register input.input-text { width: 100%; }
    .loginRegBox .validation-advice { width: 248px; }
    .loginRegBoxWrapperLeft.registerWrapper { width: 280px; }
    .loginRegBoxWrapperLeft.registerWrapper .facebookLogin { margin: 0px 0px 22px 0px; width: 264px; }
    .loginRegBoxWrapperLeft.registerWrapper .facebookLogin::before { left: 34px; }
    .loginRegBox.register button.button { width: 280px; }
    .loginRegBox .checkboxContent input.checkbox { margin: 7px 6px 5px 2px; }
    .loginRegBox .checkboxContent label.checkboxLabel { width: 238px; font-size: 12px; }
    .registrationBanner { float: right; width: 50%; padding: 20px 0px 20px 20px; height: auto; }
    .registrationBanner img { width: 100%; height: auto; }
    .loginRegBox.forgotPassword {  }
    .loginRegBox.forgotPassword input.input-text { width: 280px; }
    .termsPopUpContent.textPage { width: 500px; height: 400px; }
    .loginRegBoxWrapperFullWidth h2 { padding: 0px 90px 16px 0px; }
    .customer-address-form .fieldset {  }
    .loginRegBoxWrapperFullWidth { margin: 0px 0px 20px 0px; }
    .loginRegBox.contactsInfo { width: 50%; margin: 0px 0px 20px 0px; padding: 15px 15px 15px 0px; }
    .loginRegBox.contactsInfo .textPage h6 { font-size: 15px; }
    .loginRegBox.contactsInfo .textPage table { width: 100%!important; height: auto!important; }
    .loginRegBox.contactsInfo .textPage .viewShops { font-size: 14px; }
    .loginRegBox.contacts { width: 50%; height: auto; margin: 0px 0px 20px 0px; padding: 15px 0px 15px 15px; }
    .loginRegBox.contacts button.button { float: left; width: 100%; }
    .contactGoogleMapWrapper { width: 100%; padding: 0px 0px 0px 0px; }
    .contactGoogleMapWrapper #mapContainer { width: 100%; height: 285px; }
    .contactGoogleMapWrapper #mapContainer iframe{ height: 285px; }
    .contactsBanner { width: 270px; }
    .contactsBanner img { width: 270px; }
    .loginRegBox.contacts .validation-advice { width: 267px; }
    .loginRegBox.contacts .recaptcha { width: 302px; margin-left: -16px;  -ms-transform: scale(0.9);  -webkit-transform: scale(0.9); transform: scale(0.9);  }
    .loginRegBox.contacts .recaptcha .validation-advice { width: 290px; margin: 6px 0px 6px 0px; }

    .textPage img { width: 100%; height: auto; }

    #main.col2-left-layout .col2MainWrapper { background: no-repeat; }
    aside.leftCol { width: 300px; min-height: 180px; padding: 0px 150px 0px 150px!important; margin: 0px 0px 20px 0px; background: no-repeat; }
    .cms-page-view aside.leftCol { padding: 0px 150px 0px 150px; }
    .contacts-index-index aside.leftCol { padding: 0px 150px 0px 150px; }
    .customer-account-index aside.leftCol { padding: 0px 150px 0px 150px!important; }
    aside.leftCol .leftColWrapper { width: 300px; }
    aside.leftCol .leftMenu { width: 300px; }
    aside.leftCol .leftMenu ul li a { background: #f8f8f8; border-right: none; }

    #main .mainContent .my-account .data-table th { font-size: 12px; }
    #main .mainContent .my-account .data-table td { font-size: 11px; }
    .customer-account-edit li.control { width: auto; }
    .customer-account-edit .my-account .customer-name { width: auto; }
    .wishlist-index-index #main .mainContent .my-account .data-table td h3.product-name { width: 190px; line-height: 15px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td textarea { width: 168px; height: 60px; font-weight: normal; font-size: 12px; line-height: 14px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .cart-cell { padding: 0px 15px 0px 15px; }
    .wishlist-index-index #main .mainContent .my-account .my-wishlist table#wishlist-table button.button { margin-left: 24px; width: 165px; height: 40px; line-height: 39px; font-size: 11px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .btn-remove { margin: 30px 0px 0px -5px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set button.button.btn-share { font-size: 14px; margin: 0px 10px 0px 0px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set button.btn-add { width: 280px; padding-left: 5px; padding-right: 5px; margin-top: 5px; height: 40px; line-height: 39px; font-size: 11px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set button.button.btn-update { min-width: 100px; }

    .cms-no-route .textPage { width: 600px; }
    .cms-index-noroute .textPage { margin: 0px 0px 0px 0px; }

    .shopsListingWrapper .shopsListingGoogleMap { width: 300px; }
    .shopsListingWrapper .shopsListingGoogleMap #gmapContent { width: 300px; }

    .shopsInnerWrapper .shopImage { display: none; }
    .shopsInnerWrapper .shopImageResponsive { display: block; }
    .shopsInnerWrapper .shopInfo { width: 100%; padding: 0px 0px 0px 0px; }

    .opc-wrapper-opc { width: 100%; }
    .opc-wrapper-opc .opc-col-left .wide { width: 100%; }
    .opc-wrapper-opc .opc-col-left .control { width: 100%; }
    .opc-wrapper-opc .opc-col-left .fields .field { width: 100%; margin: 0px 0px 0px 0px; }
    .opc-wrapper-opc .validation-advice { float: left; width: 100%; }
    .opc-wrapper-opc .commentAndAgreements { width: 456px; }
    .opc-wrapper-opc .comment-block { width: 100%; }
    .opc-review-actions .opcReviewContent .grandTotalBox { width: 460px; margin: 28px 62px 10px 0px; }
    #checkout-review-submit.opc-review-actions .opcReviewContent form#checkout-agreements label.checkboxLabel { width: 90%; }
    .opc-review-actions .opcReviewContent button.btn-checkout.opc-btn-checkout { width: 460px; margin: 0px 62px 0px 0px; }
    .opc-wrapper-opc #checkout-review-table.opc-data-table tbody td img { width: 30px; margin: 0px 10px 20px 0px; }
    .opc-wrapper-opc .opc-data-table thead th { font-size: 14px; }
    .opc-wrapper-opc .opc-data-table tbody td { font-size: 12px; }
    .opc-wrapper-opc .opc-data-table tbody td h3 { font-size: 12px; }
    .opc-wrapper-opc .opc-data-table tbody td .price { font-size: 12px; }
    .opc-wrapper-opc .opc-menu .checkoutSocialLoginWrapper { margin: 12px 0px 0px 0px; }
    .opc-wrapper-opc .opc-col-left li#register-customer-password { width: 458px; }
    .opc-wrapper-opc .opc-col-left li#register-customer-password .field { width: 100%; }
    .opc-wrapper-opc .opc-col-left li#register-customer-password .field .input-box input.input-text { width: 416px; }
    .opc-wrapper-opc .opc-col-right .leaseCalculator { width: 575px; margin-left: -40px; }
    .opc-wrapper-opc .opc-col-right .leaseCalculator .form-list .field { width: 259px; }
    .payment-block .leasing-calculator { width: 575px; margin-left: -40px;}
    .payment-block .leasing-calculator .form-list .field { width: 259px; }
    .payment-block .leasing-calculator .recalc-loader { left: 410px; }

    .stenik-checkout-top-login { width: 100%; }
    .stenik-checkout-top-login .social-login { height: auto; }
    .stenik-checkout-top-login p { display: none; margin: 0 5px 0 0 }
    .stenik-checkout-top-login .button.open-popup { margin: 0 5px 10px 0; }
    #main .stenik-checkout-top-login .facebookLoginBtn { margin: 0 5px 10px 0;  }
    #main .stenik-checkout-top-login .gPlusLoginBtn { margin: 0 5px 10px 0;  }
    .stenik-checkout .col-xs-8 { width: 100%; }
    .stenik-checkout.must-login-checkout .col-xs-4.new-users { width: 100%; min-height: 20px; }
    .stenik-checkout.must-login-checkout .login-first-msg { margin-bottom: 0px; }
    .stenik-checkout.must-login-checkout .col-xs-8.registered-users:after { display: none; }
    .stenik-checkout.must-login-checkout .social-login-checkout { width: 100%; }
    .stenik-checkout .coupon-review-col { position: relative !important; width: 100%; right: auto; top: auto; }
    .stenik-checkout.must-login-checkout .coupon-review-col { margin-bottom: 40px; }
    .stenik-checkout .checkout-review-box .review-items-box .review-item .cart-price { float: left; }
    .stenik-checkout .officeLocator button.button { display: none; }
    .stenik-checkout .stenik-onepage-section { padding: 15px 20px; }
    .stenik-checkout .delivery-to-wrapper::before{ width: 600px; left: -21px; }
    .checkout-popup-login { width: 280px; padding: 10px; }
    .checkout-popup-login .button { min-width: 80px; padding: 10px; }
    .stenik-checkout #extensa_econt-form .fields .field { width: 45%; }

    .compareProductsWrapper { max-width: 600px; }
    table.compareProductsTable td, table.compareProductsTable th { min-width: 198px; padding: 8px 8px 8px 8px; }
    table.compareProductsTable .compareImg { width: 180px; height: 180px; }
    table.compareProductsTable td .compareImg img { width: 180px; height: 180px; }
    table.compareProductsTable th { font-size: 17px; line-height: 20px; }
    table.compareProductsTable td .deleteItem { top: 10px; right: 10px; }

    footer .footerPaymentContent .footerPaymentInfo { width: 600px; }
    footer .footerPaymentContent .footerPaymentBox { width: 600px; text-align: center; }
    footer .newsLetterFotoer .newsLetterInfo { width: 50%; padding: 0px 12px 0px 0px; box-sizing: border-box; }
    footer .newsLetterFotoer .newsletterForm { width: 50%; padding: 0px 0px 0px 12px; margin: 44px 0px 0px 0px; box-sizing: border-box; }
    footer .newsLetterFotoer .newsLetterInfo .title { font-size: 24px; line-height: 26px; }
    footer .newsLetterFotoer .newsLetterInfo p { font-size: 14px; line-height: 16px; }
    footer .newsLetterFotoer .newsletterForm input.newsletterInput { width: 174px; }
    footer .newsLetterFotoer .newsletterForm button.newsLetterBtn { width: 114px; font-size: 13px; }
    footer .newsLetterFotoer .newsletterForm .validation-advice { left: 12px; width: 266px; }
    footer .footerContent .footerCol { width: 50%; min-height: 260px; margin: 0px 0px 0px 0px; padding: 0px 6px 0px 6px; box-sizing: border-box; }
    footer .footerBottom .copy { width: 100%; text-align: center; }
    footer .footerBottom .stenik { width: 100%; text-align: center; }

    #back-top { right: 2%; margin: 0px 0px 0px 0px; }

    .searchautocomplete { width: 240px; }
    .searchautocomplete:hover { width: 240px; }
    .searchautocomplete .nav { width: 214px; border-bottom: solid 1px #cacaca; }
    .searchautocomplete:hover .nav { width: 214px; border-bottom: solid 1px #cacaca; }
    .searchautocomplete:after { width: 0px; }

    .responsiveClearFix { display: block; }

    .reviewsWrapper .commentsFormWrapper .validation-advice { width: 276px; }
    .reviewsWrapper .commentsFormWrapper .captcha .validation-advice { width: 290px; }
    .reviewsWrapper .commentsFormWrapper button.button.addCommentBtn { margin: 16px 0px 0px 0px; }

    ul.messages { margin: 10px 0px 10px 0px!important; }
    .productViewTabsContnt .productsTabInfo .tabs-nav-leasing{ width: 100%; padding: 0; }
    .productViewTabsContnt .productsTabInfo .leasing-tabs ul { margin: 0 0 30px 0; }
    .productViewTabsContnt .productsTabInfo .leasing-tabs li { width: 50%; }
    .productViewTabsContnt .productsTabInfo .tabs-content { width: 100%; }
    .productViewTabsContnt .productsTabInfo .leasing-tabs ul.messages li { width: 100%; }

}

















/* Start Media Queries for smartphones
****************************************/
@media only screen and (min-width: 0px) and (max-width: 599px) {
    body { background: #fff !important; }

    body { overflow-x: hidden; }

    .wrapper { width: 300px; }

    header { display: none; }
    .responsiveHeader { display: block; }
    .responsiveHeader .responsiveMenu.open .responsiveMenuSub { width: 280px !important; }
    .responsiveHeader .responsiveMenu .responsiveMenuSub ul li a { width: 240px; }
    .responsiveHeader .responsiveMenu .responsiveMenuSub ul li ul li a { width: 240px; }

    .topBanner { display: none; }

    body > .widget.widget-static-block {margin-top:50px;}
    #main { padding: 50px 0px 0px 0px; }
    body > .widget.widget-static-block ~ #main {padding-top: 10px;}
    .wideContent { padding: 40px 0px 0px 0px; }
    .cms-index-index .wideContent { padding: 40px 0px 0px 0px; margin: 0px 0px 0px 0px; }
    .mainContent h1 { font-size: 30px; line-height: 32px; }
    #main.col2-left-layout .mainContent { width: 300px; margin: 0px 0px 0px 0px; }
    #main .pageTitleWrapper { padding: 0px 0px 0px 0px; margin: 10px 0px 10px 0px; }
    #main .pageTitleWrapper h1 { font-size: 32px; line-height: 34px; }
    .catalogsearch-result-index #main h1 {font-size: 32px; line-height: 38px;}

    .headerSearch { margin: 14px auto 14px auto; }

    nav.breadcrumbs { display: none; }

    .mainTopStaticBanners { display: block; }

    #main .homepageBannerLeftToSlider { width: 300px; height: 525px; margin: 7px 0px 8px 0px; }
    #main .widgetBannerBox { width: 300px; height: 525px; padding: 115px 0px 0px 0px; }
    #main .widgetBannerBox img { width: 300px; height: 525px; }

    #main .homepageMainBannerSlider { width: 448px; height: 252px; margin: 7px 0px 8px 0px; display: none!important; }
    #main .homepageMainBannerSlider .item .sliderInfoBox { width: 240px; height: 200px; top: 34px; }
    #main .homepageMainBannerSlider .item .sliderIMGLink img { float: left; width: 100%; height: auto; }
    #main .homepageMainBannerSlider .owl-controls .owl-prev { width: 40px; height: 40px; top: -112px; }
    #main .homepageMainBannerSlider .owl-controls .owl-next { width: 40px; height: 40px; top: -112px; }
    #main .homepageMainBannerSlider .owl-controls { width: 368px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox .sliderTitle { font-size: 24px; line-height: 26px; margin: 0px 0px 10px 0px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox .slidersubTitle { font-size: 11px; line-height: 13px; margin: 0px 0px 18px 0px; }
    #main .homepageMainBannerSlider .item .sliderInfoBox .buyBtn { font-size: 10px; line-height: 22px; height: 22px; min-width: 60px; }

    #main .homepageBannersContent { margin: 0px 0px 15px 0px; }
    #main .homepageBannersContent .textPage { width: 304px; }
    #main .categoryWidgetBox { width: 144px; height: 144px; margin: 0px 4px 8px 4px; }
    #main .categoryWidgetBox img.banner { width: 100%; height: auto; }
    #main .categoryWidgetBox img.icon { width: 60px; height: 60px; margin: 18px 0px 0px -30px; }
    #main .categoryWidgetBox:hover img.icon { margin-top: 12px; }
    #main .categoryWidgetBox .line { width: 100px; margin: 0 0 14px 22px }
    #main .categoryWidgetBox:hover .line { margin-bottom: 24px; }
    #main .categoryWidgetBox span.title { font-size: 14px; line-height: 16px; padding: 0px 24px 0px 24px; }

    #main .homepageInfoContent { margin: 0px 0px 20px 0px; }
    #main .homepageInfoContent .textPage h1 { font-size: 18px; line-height: 20px; }

    #main .featureProductsListing { margin: 0px 0px 45px 0px; }
    #main .productListingSlider { width: 300px; }
    #main .featureProductsListing .title { width: 100%; margin: 0px 0px 20px 0px; font-size: 26px; line-height: 28px; }
    #main .featureProductsListing .viewAllBtn { height: 30px; min-width: 80px; font-size: 12px; line-height: 30px; margin: 0px 94px 12px 0px; }
    .productBox { width: 140px; height: 255px; margin: 5px 5px 5px 5px; }
    .productBox .productIMGLink { width: 140px; height: 140px; }
    .productBox .productIMGLink .onsale-category-container { transform: scale(0.7); }
    .productBox .productTitle { font-size: 12px; line-height: 15px; height: 48px; }
    .productBox .price-box .regular-price .price { font-size: 12px; line-height: 14px; }
    .productBox .price-box .old-price .price { font-size: 12px; line-height: 14px; }
    .productBox .price-box .special-price .price { font-size: 12px; line-height: 14px; }
    #main .productListingSlider .owl-controls .owl-pagination { display: block; }
    #main .productListingSlider .owl-controls .owl-buttons { display: none; }
    #main .productListing { width: 300px; margin: 0px 0px 20px 0px; }

    #main .parallaxWidgetBox { width: 320px; height: 275px; margin: 0px 0px 0px -10px; left: 0; }
    #main .parallaxWidgetBox .overlay { float: left; width: 900px; height: 275px; margin: 0px 0px 0px -300px; display: block; background: rgba( 0, 0, 0, 0.5 ); }
    #main .parallaxWidgetBox .bannerIMGLink { height: 275px; }
    #main .parallaxWidgetBox .parallax img { width: auto; height: auto; min-width: 100%; min-height: auto; }
    #main .parallaxWidgetBox .parallaxInfoContent { width: 300px; top: 32px; right: 0px; }
    #main .parallaxWidgetBox .paralaxBannerTitle { font-size: 26px; line-height: 28px; margin: 0px 0px 15px 0px; }
    #main .parallaxWidgetBox .paralaxDescription { font-size: 14px; line-height: 16px; margin: 0px 0px 20px 0px; }
    #main .parallaxWidgetBox .viewMoreBtn { font-size: 12px; line-height: 25px; height: 25px; min-width: 80px; text-align: center; }

    #main .homepageBannersContent.wideContent { width: 100%; margin: 0px 0px 30px 0px; padding: 30px 0px 30px 0px; left: 0; }
    #main .servicesWidgetBox { width: 110px; height: 194px; margin: 0px 20px 20px 20px; }
    #main .servicesWidgetBox .iconWrapper { width: 70px; height: 70px; margin: 0px 20px 12px 20px; }
    #main .servicesWidgetBox .iconWrapper img { width: 40px; height: 40px; margin: -20px 0px 0px -20px }
    #main .servicesWidgetBox span.title { font-size: 12px; line-height: 14px; }
    #main .servicesWidgetBox:hover span.title { margin: -4px 0px 8px 0px; }
    #main .servicesWidgetBox span.subTitle { font-size: 12px; line-height: 14px; }

    #main .homepageBrandsContent .homepageBrandsListing { width: 300px; }
    #main .homepageBrandsContent .homepageBrandsTitle { width: 30%; margin: 0px 0px 20px 0px; font-size: 28px; line-height: 30px; }
    #main .homepageBrandsContent .allBrandsBtn { height: 30px; min-width: 80px; font-size: 12px; line-height: 30px; margin: 0px 0px 0px 0px; }
    #main .brandBox { width: 149px; height: 61px; }
    #main .brandBox img { width: 149px; }
    .brandsListingWrapper { width: 300px; }
    .brandDescriptionWrapper .brandImage { float: left; margin: 0px 0px 20px 0px; }
    .brandDescriptionWrapper .brandImage { float: left; width: 300px; height: auto; }

    .filtersContentWrapper.sticky { position: relative; width: 300px; }

    .filterContent { position: relative; }
    .filterContent .responsiveOpenFilters { display: block; margin: 0px; }
    .filterContent .filtersWrapper { display: none; width: 300px; padding: 0px; border-top: none; position: absolute; left: 0px; top: 43px; z-index: 200; }
    .filterContent .filtersWrapper .block-layered-nav { width: 298px; }
    .filterContent .dropDownfilter { width: 100%; margin: 0px 0px 0px 0px; }
    .filterContent .dropDownfilter .openFilters { width: 100%; }
    .filterContent .dropDownfilter.sort { width: 40px; }
    .filterContent .dropDownfilter ul.subOptions { width: 298px; }
    .filterContent .dropDownfilter .layer-slider.subOptions { width: 298px; padding-top: 0px; }
    .filterContent .dropDownfilter .price-slider { display: none; }
    .toolbarBox .toolbarRightPosition { margin-right: 12px; }
    .toolbarBox .dropDownfilter { margin: 0px 2px; }
    .toolbarBox .dropDownfilter .openFilters { padding: 0px 22px 0px 5px; font-size: 12px; }
    .toolbarBox .dropDownfilter .openFilters:before { right: 6px; }
    .toolbarBox .dropDownfilter.showBy ul.subOptions { width: 103px; }
    .toolbarBox .dropDownfilter.sortBy ul.subOptions { width: 151px; }
    .pagingBox { height: 28px; }
    .pagingBox .paging { height: 28px; }
    .pagingBox .paging a.prev { width: 26px; height: 29px; font-size: 0px; text-indent: -9999px; margin: 0px 0px 0px 0px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingPrevSm.**********.png") 6px 10px no-repeat }
    .pagingBox .paging a.next { width: 26px; height: 29px; font-size: 0px; text-indent: -9999px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingNextSm.**********.png") 6px 10px no-repeat }
    .pagingBox .paging .pagingItems { width: 248px; height: 28px; line-height: 24px; }
    .pagingBox .paging a, .pagingBox .paging span { width: 28px; height: 28px; line-height: 30px; font-size: 12px; margin: 0px -3px 0px -3px; }

    .catalog-seo-sitemap-product .paging,
    .catalog-seo-sitemap-category .paging { height: 28px; }
    .catalog-seo-sitemap-product .paging a.prev,
    .catalog-seo-sitemap-category .paging a.prev { width: 26px; height: 29px; font-size: 0px; text-indent: -9999px; margin: 0px 0px 0px 0px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingPrevSm.**********.png") 6px 10px no-repeat }
    .catalog-seo-sitemap-product .paging a.next,
    .catalog-seo-sitemap-category .paging a.next { width: 26px; height: 29px; font-size: 0px; text-indent: -9999px; background: #f8f8f8 url("//localhost:8580/skin/frontend/stenik/default/images/pagingNextSm.**********.png") 6px 10px no-repeat }
    .catalog-seo-sitemap-product .paging .pagingItems,
    .catalog-seo-sitemap-category .paging .pagingItems { width: 248px; height: 28px; line-height: 24px; }
    .catalog-seo-sitemap-product .paging a,
    .catalog-seo-sitemap-product .paging span,
    .catalog-seo-sitemap-category .paging a,
    .catalog-seo-sitemap-category .paging span { width: 28px; height: 28px; line-height: 30px; font-size: 12px; margin: 0px -3px 0px -3px; }

    .sitemapToProductsBtn ul li a { width: 100%; }

    .productViewContent .productViewLeft { width: 300px; margin-bottom: 20px; }
    .productViewContent .productViewGallery { width: 300px; }
    .productViewContent .productViewGallery .royalSlider.rsUni { width: 300px; height: 264px; }
    .productViewContent .productViewGallery .royalSlider.rsUni.oneImage { width: 300px; height: 264px; }
    .productViewContent .productViewGallery .royalSlider.rsUni.oneImage .rsOverflow { padding: 0px 0px 0px 20px; }
    .productViewContent .productViewGallery .rsOverflow { padding: 0px 0px 0px 40px; }
    .productViewContent .productViewGallery .royalSlider.rsUni .rsThumbsVer { width: 40px; max-height: 178px; }
    .productViewContent .productViewGallery .royalSlider.rsUni .rsThumb { width: 38px; height: 38px; }
    .productViewContent .productViewGallery .videoThumbBox { bottom: 0px; z-index: 200; }
    .productViewContent .productViewRight { width: 300px; }
    .productViewContent .productInfo .productInfoRightCol { width: 300px; }
    .productViewContent .productInfo .productInfoAccordion .accordionItem .link { line-height: 16px; padding: 12px 50px 0px 64px; }
    .productViewContent .productInfo .productInfoAccordion .accordionItem .link.close { padding: 12px 50px 0px 55px; }
    .productViewContent .relatedProductsContent .title { margin-bottom: 10px; }
    .productViewContent .relatedProductsContent .relatedItem { margin: 0px 5px 10px 5px; }
    #main .productViewTabsContnt .productsTabInfo .ui-tabs-panel .descriptionContent { max-width: 300px; }
    #main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav { height: auto; }
    #main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav li { width: 50%; height: 40px; }
    #main .productViewTabsContnt .productsTabInfo ul.ui-tabs-nav li a { width: 100%; height: 40px; line-height: 40px; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px; font-size: 13px; background: #f1f1f1; }
    #main .lastSeenItemsContent { padding: 10px 20px 10px 20px; }
    #main .lastSeenItemsContent .title { font-size: 22px; line-height: 24px; }
    #main .lastSeenItemsContent .lastSeenItems .lastSeenItem { width: 76px; height: 76px; margin: 0px 5px 20px 5px; }
    #main .lastSeenItemsContent .lastSeenItems .lastSeenItem img { width: 76px; height: 76px; }
    #main .productViewTabsContnt .commentsFormWrapper { width: 300px; margin-bottom: 20px; }
    #main .productViewTabsContnt .commentsFormWrapper button.button.addCommentBtn { height: 50px!important; }
    #main .productViewTabsContnt .noReviewsWrapper { width: 300px; }
    #main .productViewTabsContnt .commentsByUsers { width: 300px; }
    .leaseCalculator .horizontalScrollWrapper { overflow-x: scroll; }
    .leaseCalculator .horizontalScrollWrapper::-webkit-scrollbar { -webkit-appearance: none; width: 7px; }
    .leaseCalculator .horizontalScrollWrapper::-webkit-scrollbar-thumb { border-radius: 4px; background-color: #ccc; }
    .textPage table.styles.attributes { width: 300px; margin: 0px 0px 20px 0px; }

    .productViewContent .productInfo .addToCartBox .giftBoxWrapper .giftBoxPopup {
        right: auto;
        left: 3px;
        top: 70px;
    }
    .productViewContent .productInfo .addToCartBox .giftBoxWrapper .giftBoxPopup:before {
        margin-left: -14px;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 14.5px 16px 14.5px;
        border-color: transparent transparent #d9dbda transparent;
        left: 50%;
        top: -16px;
    }

    .productLeasingCalc h1 { font-size: 22px; }
    .productLeasingCalc .leaseCalculator { width: 240px; margin: 10px 0px 10px 0px; padding: 5px; }
    .productLeasingCalc .leaseCalculator .textInfo p { font-size: 13px; line-height: 16px; }
    .videoPopUpContent { width: 280px; height: 280px; }
    .videoPopUpContent iframe { width: 100%; height: 260px; }

    .advanced-gifts-wrapper { width: 100%; margin-top: 10px; }
    .advanced-gifts-wrapper .title { padding-left: 10px; padding-right: 10px; font-size: 18px; line-height: 21px; }
    #main .advanced-gifts-wrapper .productListingSlider { padding: 0; }
    .advanced-gifts-wrapper .productBox { width: 160px; padding-left: 10px; padding-right: 10px; box-sizing: border-box; }
    .advanced-gifts-wrapper .productBox .gridAddToCartBtn { padding-top: 13px; padding-bottom: 14px; font-size: 12px; line-height: 18px; }
    #main .advanced-gifts-wrapper .productListingSlider .owl-controls { margin-top: 12px; margin-bottom: 14px; position: relative; bottom: 0; top: auto; }

    .shoppingCartContent .shoppingCartItems .cell { min-width: 0px; }
    .shoppingCartContent .shoppingCartItems { width: 300px; margin: 0px 0px 10px 0px; }
    .shoppingCartContent .shoppingCartItems .col1 { width: 300px; }
    .shoppingCartContent .shoppingCartItems .col2 { width: 85px; padding: 0px; }
    .shoppingCartContent .shoppingCartItems .col3 { width: 96px; padding: 0px; }
    .shoppingCartContent .shoppingCartItems .col4 { width: 85px; padding: 0px; }
    .shoppingCartContent .shoppingCartItems .col5 { width: 24px; }
    .shoppingCartContent .shoppingCartItems .row { width: 300px; padding: 10px 0px 10px 0px; margin: 0px; }
    .shoppingCartContent .shoppingCartItems .row.headerRow { padding: 0px 0px 0px 0px; width: 300px; height: 36px; margin: 0px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .headerRow .cell { height: 36px; line-height: 36px; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell.col1 { width: 300px; padding-left: 12px; text-align: left; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell.col2 { display: none; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell.col3 { display: none; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell.col4 { display: none; }
    .shoppingCartContent .shoppingCartItems .row.headerRow .cell.col5 { display: none; }
    .shoppingCartContent .shoppingCartItems .productIMGLink { width: 60px; height: 60px; margin-right: 10px; }
    .shoppingCartContent .shoppingCartItems .productIMGLink img { width: 60px; height: 60px; }
    .shoppingCartContent .shoppingCartItems .cartInfoContent { float: left; width: 228px; }
    .shoppingCartContent .shoppingCartItems .itemTitle { font-size: 13px; line-height: 15px; }
    .shoppingCartContent .shoppingCartItems .itemInfoBox { width: 239px; }
    .shoppingCartContent .shoppingCartItems .itemBrand { width: 100%; }
    .shoppingCartContent .shoppingCartItems .itemName { width: 100%; font-size: 12px; }
    .shoppingCartContent .shoppingCartItems .itemSKU { width: auto; font-size: 10px; }
    .shoppingCartContent .shoppingCartItems .itemEdit { font-size: 10px; }
    .shoppingCartContent .shoppingCartItems .itemOption { width: 100%; font-size: 13px; }
    .shoppingCartContent .shoppingCartItems .price-box { margin: 8px 0px 0px 0px }
    .shoppingCartContent .shoppingCartItems .price-box .regular-price { margin-top: 17px; }
    .shoppingCartContent .shoppingCartItems .price-box .regular-price .price { margin-top: 0px; font-size: 10px; }
    .shoppingCartContent .shoppingCartItems .price-box .old-price { margin: 0px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .price { font-size: 10px!important; margin-top: 0px; }
    .shoppingCartContent .shoppingCartItems .itemDelete { margin: 20px 0px 0px 4px; }
    .shoppingCartContent .shoppingCartItems .qtyContent { margin: 12px 0px 0px 0px; }
    .shoppingCartContent .underCartContent { width: 300px; margin: 10px 0px 60px 0px; }
    .shoppingCartContent .promoBoxContinueShoppingWrapper { width: 100%; padding-right: 0px; min-height: 100px; box-sizing: border-box; }
    .shoppingCartContent .cartPromoBox input.promoInput { width: 187px; }
    .shoppingCartContent .continueShoppingBox { position: relative; margin: 30px 0px 0px 0px; }
    .shoppingCartContent .continueShoppingBox .backBtn { width: 100%; text-align: center; height: 36px; line-height: 36px; box-sizing: border-box; }
    .shoppingCartRight { width: 100%; padding-left: 0px; box-sizing: border-box; }
    .shoppingCartRight .underCartTotalBox table.underCartTable td { font-size: 15px; line-height: 20px; }
    .shoppingCartRight .underCartTotalBox table.underCartTable td .price { font-size: 15px; line-height: 20px; }
    .shoppingCartRight .underCartTotalBox table.underCartTable tfoot td .price { font-size: 18px; line-height: 20px; }
    .shoppingCartContent .cartPromoBox .validation-advice { width: 244px; }
    .underCartContent .topCartBtn { display: none; }
    .shoppingCartContent .shoppingCartItems .amountBox { width: 90px; margin: 18px 0px 0px 0px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner { width: 90px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner input.amount { width: 30px; height: 28px; margin: 0px 0px 0px 30px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-up { width: 28px; height: 28px; }
    .shoppingCartContent .shoppingCartItems .amountBox .ui-spinner .ui-spinner-down { width: 28px; height: 28px; }
    .shoppingCartContent .backBtn { width: 100%; margin: 20px 0px 0px 0px; }
    .shoppingCartContent .deliveryText { float: left; margin: 10px 0px -20px 32px; }
    .shoppingCartContent .cartPromoBox { width: 300px; }
    .shoppingCartRight .underCartTotalBox { width: 300px; }
    .shoppingCartContent .shoppingCartItems .amountBox .loader { margin: 0px 0px 0px -19px; top: 29px; }

    .newsListingWrapper { width: 300px; margin: 0px 0px 30px 0px; }
    .newsListingWrapper .titleSection { margin: 0px 0px 50px 0px; }
    .newsListingWrapper .newsBox { width: 300px; height: 430px; margin: 0px 0px 22px 0px; }
    .newsListingWrapper .newsBox .newsImgWrapper { height: 225px; }
    .newsListingWrapper .newsBox .newsImg { height: 225px; }
    .newsListingWrapper .newsBox .newsImg img { height: 225px; }
    .col2-left-layout .newsListingWrapper { width: 300px; }
    .col2-left-layout .newsListingWrapper .newsBox { margin-right: 0px; }

    .innerNewsWrapper .newsImage { float: left; margin: 18px 0px 18px 0px; }
    .innerNewsWrapper .newsImage img { float: left; width: 300px; height: 224px; }
    .innerNewsWrapper .newsBackBtn { width: 100%; padding: 0px 0px 0px 0px; margin: 0px 0px 20px 0px; text-align: center; box-sizing: border-box; }
    .innerNewsWrapper .newsShare { float: left; }

    .loginRegBox { width: 100%; padding: 15px 0px 15px 0px; margin: 0px 0px 20px 0px; min-height: 310px; }
    .loginRegBox .buttons-set { width: 270px; }
    .loginRegBox .goToRegistrationBtn { width: 276px; margin: 4px 0px 0px 0px; }
    .loginRegBox.login { width: 100%; padding: 15px 0px 15px 0px; }
    .loginRegBox.login button.button { width: 270px; }
    #main .loginRegBox.login .facebookLoginBtn { width: 300px; padding: 0px 0px 2px 112px; margin: 0px 0px 15px 0px; background: #3b5791 url("//localhost:8580/skin/frontend/stenik/default/images/facebookLoginIcon.**********.png") 80px 13px no-repeat; }
    #main .loginRegBox.login .facebookLoginBtn:hover { padding: 0px 0px 2px 118px; background: #3b5791 url("//localhost:8580/skin/frontend/stenik/default/images/facebookLoginIcon.**********.png") 86px 13px no-repeat; }
    #main .loginRegBox.login .gPlusLoginBtn { width: 300px; padding: 0px 0px 2px 112px; margin: 0px 0px 15px 0px; background: #e04b33 url("//localhost:8580/skin/frontend/stenik/default/images/GPlusLoginIcon.**********.png") 80px 11px no-repeat; }
    #main .loginRegBox.login .gPlusLoginBtn:hover { padding: 0px 0px 2px 118px; background: #e04b33 url("//localhost:8580/skin/frontend/stenik/default/images/GPlusLoginIcon.**********.png") 86px 11px no-repeat; }
    .loginRegBox h2 { font-size: 26px; margin: 0px 0px 15px 0px; }
    .loginRegBox .forgotpassword { margin: 0px 0px 30px 0px; }
    .loginRegBox.login input.input-text { width: 100%; }
    .loginRegBox.register { width: 100%; padding: 15px 0px 15px 0px; }
    .loginRegBox.register input.input-text { width: 100%; }
    .loginRegBox .validation-advice { width: 258px; }
    .loginRegBoxWrapperLeft.registerWrapper { width: 300px; margin: 0px 0px 0px 0px; }
    .loginRegBoxWrapperLeft.registerWrapper .facebookLogin { margin: 0px 0px 22px 0px; width: 284px; }
    .loginRegBoxWrapperLeft.registerWrapper .facebookLogin:before { left: 46px; }
    .loginRegBox.register button.button { width: 300px; }
    .loginRegBox .checkboxContent input.checkbox { margin: 7px 6px 5px 2px; }
    .loginRegBox .checkboxContent label.checkboxLabel { width: 248px; font-size: 12px; }
    .registrationBanner { float: right; width: 300px; height: auto; padding: 15px 0px 15px 0px; }
    .registrationBanner img { width: 300px; height: auto; }
    .loginRegBox.forgotPassword {  }
    .loginRegBox.forgotPassword input.input-text { width: 270px; }
    .loginRegBoxWrapperFullWidth h2 { padding: 0px 0px 16px 0px; }
    .loginRegBox.forgotPassword button.button { width: 270px; }
    .termsPopUpContent.textPage { height: 360px; width: 210px; }
    .loginRegBoxWrapperFullWidth { margin: 0px 0px 20px 0px; }
    .loginRegBox.contactsInfo { width: 300px; height: auto; min-height: auto; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px; background: none; }
    .loginRegBox.contactsInfo .textPage table { width: 100%!important; height: auto!important; }
    .loginRegBox.contactsInfo .textPage table tr td { float: left; width: 100%!important; height: auto!important; }
    .loginRegBox.contactsInfo .textPage .viewShops { width: 100%; padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px; text-align: center; font-size: 14px; }
    .googleMapAndBannerWrapper { width: 300px; margin: 0px 0px 20px 0px; }
    .contactGoogleMapWrapper { width: 100%; padding: 0px 0px 0px 0px; }
    .contactGoogleMapWrapper #mapContainer { width: 100%; height: 285px; }
    .contactGoogleMapWrapper #mapContainer iframe { height: 285px; }
    .contactsBanner { width: 300px; }
    .contactsBanner img { width: 300px; }
    .loginRegBox.contacts { width: 300px; height: auto; min-height: auto; padding: 0px 0px 0px 0px; margin: 0px 0px 20px 0px; }
    .loginRegBox.contacts .validation-advice { width: 288px; }
    .loginRegBox.contacts .recaptcha .validation-advice { width: 288px; margin: 6px 0px 6px 0px; }
    .loginRegBox.contacts button.button { float: left; width: 100%; height: 44px; }
    .loginRegBoxWrapperRightSmall { width: 300px; margin: 0px 0px 20px 0px; }

    .textPage img { width: 100%; height: auto; }
    .textPage ul li { width: auto; }
    .textPage iframe { width: 100%!important; height: auto!important; }


    #main.col2-left-layout .col2MainWrapper { background: no-repeat; }
    aside.leftCol { width: 300px; min-height: 180px; padding: 0px 0px 0px 0px!important; margin: 0px 0px 20px 0px; }
    .cms-page-view aside.leftCol { padding: 0px 0px 0px 0px; }
    .contacts-index-index aside.leftCol { padding: 0px 0px 0px 0px; }
    .customer-account-index aside.leftCol { padding: 0px 0px 0px 0px!important; }
    aside.leftCol .leftColWrapper { width: 300px; }
    aside.leftCol .leftMenu { width: 300px; }
    aside.leftCol .leftMenu ul li a { background: #f8f8f8; border-right: none; }

    #main .mainContent .my-account .data-table th { font-size: 10px; line-height: 11px; }
    #main .mainContent .my-account .data-table td { font-size: 9px; line-height: 10px; }
    #main .mainContent .my-account .box-title a { font-size: 11px; }
    #main .mainContent .my-account .box-head a { font-size: 11px; }
    #main .mainContent .my-account input.input-text { width: 300px; }
    #main .mainContent .my-account h2 { font-size: 16px; }
    #main .mainContent .my-account .box-head h2 { font-size: 16px; line-height: 18px; }
    .customer-account-edit li.control { width: auto; }
    .customer-account-edit .my-account .customer-name { width: auto; }
    .customer-address-form .fieldset select { width: 300px; }
    .customer-address-form #main .mainContent .my-account .buttons-set p.back-link { margin: 16px 0px 16px 0px; }
    .wishlist-index-share #main .mainContent .my-account textarea { width: 300px; }
    .wishlist-index-share #main .mainContent .my-account p.back-link { margin: 7px 0px 0px 0px }
    .sales-order-view #main .mainContent .my-account .data-table td h3.product-name { width: 140px; }
    #main .mainContent .my-account .data-table td h3.product-name { font-size: 12px; }
    .customer-account-edit button.button { width: 300px; }
    .customer-address-form button.button { width: 300px; }
    .customer-address-form .fieldset { width: 300px; }
    .customer-address-form .fieldset.personal-info li.fields .field { width: 100%; }
    .my-account ul.form-list li.fields.econt-door-address-fields ul li.fields .field{ width: 100%; }
    .my-account ul.form-list li.fields.econt-office-address-fields ul li.fields .field {width: 100%;}
    .my-account .fields.addressDetails .field { width: 100%; }
    .delivery-to-wrapper #extensa_econt-form .officeLocator button.button { margin:5px 0 10px 0; }
    .my-account .delivery-to-wrapper { padding: 0; background: none; }

    .wishlist-index-index #main .mainContent .my-account .data-table td a.product-image { width: 50px; height: 50px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td a.product-image img { width: 50px; height: 50px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td h3.product-name { width: 100px; font-size: 11px; line-height: 13px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td h3.product-name a { font-size: 11px; line-height: 13px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .description .inner { font-size: 11px; line-height: 13px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td textarea { width: 94px; height: 60px; font-weight: normal; font-size: 12px; line-height: 14px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .cart-cell { width: 110px; padding: 0px; margin: 0px 0px 0px 3px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .cart-cell .price-box .regular-price span.price { font-size: 11px; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .cart-cell input.qty { width: 45px; margin: 0px 0px 5px 0px; }
    .wishlist-index-index #main .mainContent .my-account .my-wishlist table#wishlist-table button.button { width: 110px; min-width: 110px; font-size: 9px; line-height: 10px; padding: 0px; clear: both; }
    .wishlist-index-index #main .mainContent .my-account .data-table td .btn-remove { margin: -30px 0px 0px -51px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set.buttons-set2 { margin-top: 0px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set button.button.btn-share { width: 240px; font-size: 14px; margin: -10px 0px 10px 45px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set button.btn-add { width: 300px; font-size: 11px; padding-left: 10px; padding-right: 10px; }
    .wishlist-index-index #main .mainContent .my-account .buttons-set button.button.btn-update { min-width: 194px; }

    .cms-index-noroute .textPage { width: 300px; }
    .cms-index-noroute .textPage img { width: 100%; height: auto; }
    .cms-index-noroute .textPage h2 { font-size: 20px; line-height: 22px; }
    .cms-index-noroute .textPage { margin: 0px 0px 0px 0px; }

    .shopsListingWrapper .shopsListing { width: 300px; margin: 0px 0px 0px 0px; }
    .shopsListingWrapper .shopsListingGoogleMap { width: 300px; }
    .shopsListingWrapper .shopsListingGoogleMap #gmapContent { width: 300px; height: 300px; }

    .responsiveBackToTop { display: block; }

    .shopsInnerWrapper .shopImage { display: none; }
    .shopsInnerWrapper .shopImageResponsive { display: block; }
    .shopsInnerWrapper .shopInfo { width: 100%; padding: 0px 0px 0px 0px; }
    .shopsInnerWrapper .shopsInnerGoogleMap #gmapContent { height: 300px; }
    .shopsInnerWrapper .shopsBackBtn { width: 100%; padding: 0px 0px 0px 0px; margin: 0px 0px 20px 0px; text-align: center; }
    .shopsInnerWrapper .shopsShare { float: left; }

    .opc-wrapper-opc { width: 100%; }
    .opc-wrapper-opc .opc-col-left .wide { width: 100%; }
    .opc-wrapper-opc .opc-col-left .control { width: 100%; }
    .opc-wrapper-opc .opc-col-left .fields .field { width: 100%; margin: 0px 0px 0px 0px; }
    .opc-wrapper-opc .validation-advice { float: left; width: 100%; }
    .opc-wrapper-opc .commentAndAgreements { width: 280px; margin: 10px 0px 0px 0px }
    .opc-wrapper-opc .comment-block { width: 100%; }
    .opc-review-actions .opcReviewContent .grandTotalBox { width: 280px; margin: 8px -2px 10px 0px; }
    #checkout-review-submit.opc-review-actions .opcReviewContent form#checkout-agreements label.checkboxLabel { width: 212px; font-size: 12px; line-height: 14px; }
    .opc-review-actions .opcReviewContent button.btn-checkout.opc-btn-checkout { width: 280px; margin: 0px -2px 0px 0px; }
    .opc-wrapper-opc #checkout-review-table.opc-data-table tbody td img { width: 30px; margin: 0px 0px 20px 0px; }
    #checkout-review-table-wrapper { padding: 0px 0px 0px 0px; }
    .opc-wrapper-opc .opc-data-table thead th { font-size: 10px; padding: 5px 5px 5px 5px; }
    .opc-wrapper-opc .opc-data-table thead tr th:last-child { padding-right: 5px; }
    .opc-wrapper-opc .opc-data-table thead th.a-left { padding-left: 5px; }
    .opc-wrapper-opc .opc-data-table tbody td { font-size: 10px; padding: 5px 5px 5px 5px; }
    .opc-wrapper-opc .opc-data-table td.last { padding-right: 5px; }
    .opc-wrapper-opc .opc-data-table tbody td h3 { font-size: 10px; }
    .opc-wrapper-opc .opc-data-table tbody td .price { font-size: 10px; }
    .opc-wrapper-opc .opc-menu .checkoutSocialLoginWrapper { margin: 18px 0px 0px 0px; }
    #main .checkoutSocialLoginWrapper .facebookLoginBtn { margin: 0px 0px 0px 0px; }
    #main .checkoutSocialLoginWrapper .gPlusLoginBtn { margin: 12px 0px 0px 0px; }
    .opcColsWrapper #opc-address-form-billing ul.form-list { padding: 10px 20px 10px 20px; }
    .opc-wrapper-opc .shipping-block #shipping-block-methods { padding: 10px 20px 10px 20px; }
    .opc-wrapper-opc .shipping-block #checkout-shipping-method-load dl.sp-methods { padding: 0px 0px 0px 0px; }
    .opc-wrapper-opc .shipping-block dl dd { margin: 0px 0px 0px 0px; }
    .opc-wrapper-opc .payment-block form#co-payment-form fieldset { padding: 0px 0px 10px 0px; }
    .opc-wrapper-opc .payment-block #checkout-payment-method-load dt input.radio { margin: 7px 7px 0px 10px!important; }
    .opc-wrapper-opc .opc-col-left .wide.loggedIn { margin-top: 0px; }
    .opc-wrapper-opc .opc-col-left li#register-customer-password { width: 258px; }
    .opc-wrapper-opc .opc-col-left li#register-customer-password .field { width: 100%; }
    .opc-wrapper-opc .opc-col-left li#register-customer-password .field .input-box input.input-text { width: 220px; }
    .opc-wrapper-opc .opc-col-right .leaseCalculator { width: 300px; margin-left: -13px; }
    .opc-wrapper-opc .opc-col-right .leaseCalculator .form-list .field { width: 265px; }
    .payment-block .leasing-calculator { width: 300px; margin-left: -13px; }
    .payment-block .leasing-calculator .form-list .field { width: 265px; }
    .payment-block .leasing-calculator .recalc-loader { left: 250px; }

    .stenik-checkout-top-login { width: 100%; }
    .stenik-checkout-top-login .social-login { height: auto; }
    .stenik-checkout-top-login p { display: none; margin: 0 5px 0 0 }
    .stenik-checkout-top-login .button.open-popup { margin: 0 5px 10px 0; }
    #main .stenik-checkout-top-login .facebookLoginBtn { margin: 0 5px 10px 0;  }
    #main .stenik-checkout-top-login .gPlusLoginBtn { margin: 0 5px 10px 0;  }
    .stenik-checkout .col-xs-8 { width: 100%; }
    .stenik-checkout.must-login-checkout .col-xs-4.new-users { width: 100%; min-height: 20px; }
    .stenik-checkout.must-login-checkout .login-first-msg { margin-bottom: 0px; }
    .stenik-checkout.must-login-checkout .col-xs-8.registered-users:after { display: none; }
    .stenik-checkout.must-login-checkout .social-login-checkout { width: 100%; }
    .stenik-checkout .coupon-review-col { position: relative !important; width: 100%; right: auto; top: auto; }
    .stenik-checkout.must-login-checkout .coupon-review-col { margin-bottom: 40px; }
    .stenik-checkout .checkout-review-box .review-items-box .review-item .cart-price { float: left; }
    .stenik-checkout .officeLocator button.button { display: none; }
    .stenik-checkout .stenik-onepage-section { padding: 10px 15px; }
    .stenik-checkout .delivery-to-wrapper::before{ width: 300px; left: -21px; }
    .stenik-checkout .fields .field{ width: 100%; }
    .stenik-checkout .create-account-checkbox .label-checkbox { width: 90%; }
    .stenik-checkout .checkout-review-box .review-items-box .review-item .price-box { width: 100%; text-align: left; }
    .stenik-checkout .buttons-set .button.checkout-color { width: 100%; }
    .checkout-popup-login { width: 260px; padding: 10px; }
    .checkout-popup-login .button { min-width: 70px; padding: 10px; }
    .checkout-popup-login .forgotpassword { margin-left: 5px; }

    .compareProductsWrapper { max-width: 300px; }
    table.compareProductsTable td, table.compareProductsTable th { min-width: 114px; padding: 8px 8px 8px 8px; }
    table.compareProductsTable .compareImg { width: 96px; height: 96px; }
    table.compareProductsTable th { font-size: 14px; line-height: 16px; }
    table.compareProductsTable td { font-size: 14px; line-height: 16px; }
    table.compareProductsTable td .compareImg img { width: 96px; height: 96px; }
    table.compareProductsTable td .deleteItem { top: 10px; right: 10px; }
    table.compareProductsTable td .compareTitle { font-size: 14px; line-height: 16px; }
    table.compareProductsTable td .price-box .regular-price .price { font-size: 14px; line-height: 16px; }
    table.compareProductsTable td .buy { font-size: 16px; line-height: 48px; }

    footer .footerPaymentContent .footerPaymentInfo { width: 300px; }
    footer .footerPaymentContent .footerPaymentBox { width: 300px; margin: 16px 0px 0px 0px; text-align: center; }
    footer .footerPaymentContent .footerPaymentBox p img { float: none; }
    footer .footerNewsLetterContent { background-image: none; background-color: #cc1156; height: auto; padding: 12px 0px 12px 0px;  }
    footer .newsLetterFotoer .newsLetterInfo { width: 100%; padding: 0px 0px 0px 0px; box-sizing: border-box; }
    footer .newsLetterFotoer .newsletterForm { width: 100%; padding: 0px 0px 0px 0px; margin: 12px 0px 0px 0px; box-sizing: border-box; }
    footer .newsLetterFotoer .newsLetterInfo .title { font-size: 24px; line-height: 26px; }
    footer .newsLetterFotoer .newsLetterInfo p { font-size: 14px; line-height: 16px; }
    footer .newsLetterFotoer .newsletterForm input.newsletterInput { width: 174px; }
    footer .newsLetterFotoer .newsletterForm button.newsLetterBtn { width: 114px; font-size: 13px; }
    footer .newsLetterFotoer .newsletterForm .validation-advice { left: 0px; width: 266px; }
    footer .footerContent .footerCol { width: 100%; margin: 0px 0px 12px 0px; padding: 0px 0px 0px 0px; box-sizing: border-box; }
    footer .footerContent .footerCol .title { margin: 0px 0px 12px 0px; }
    footer .footerBottom .copy { width: 100%; height: 54px; text-align: center; }
    footer .footerBottom .copy a { margin: 0px 0px 0px 0px; }
    footer .footerBottom .stenik { width: 100%; text-align: center; }

    #back-top { right: 2%; margin: 0px 0px 0px 0px; }

    .searchautocomplete { width: 240px; }
    .searchautocomplete:hover { width: 240px; }
    .searchautocomplete .nav { width: 214px; border-bottom: solid 1px #cacaca; }
    .searchautocomplete:hover .nav { width: 214px; border-bottom: solid 1px #cacaca; }
    .searchautocomplete:after { width: 0px; }

    .reviewsWrapper .commentsFormWrapper { width: 100%; padding: 0px 0px 0px 0px; margin: 0px 0px 18px 0px; }
    .reviewsWrapper .commentsByUsers { width: 100%; padding: 0px 0px 0px 0px; }
    .reviewsWrapper .noReviewsWrapper { width: 100%; padding: 0px 0px 0px 0px; }
    .reviewsWrapper .commentsFormWrapper button.button.addCommentBtn { margin: 16px 0px 0px 0px; }
    .reviewsWrapper .commentsFormWrapper .validation-advice { width: 288px; }
    .reviewsWrapper .commentsFormWrapper .captcha .validation-advice { width: 290px; }
    .reviewsWrapper .titleSection { font-size: 30px; line-height: 32px; }
    .reviewsWrapper .titleSection:before { width: 300px; margin: 0px 0px 0px -150px; top: 40px; }
    .reviewsWrapper .titleSection:after { top: 36px;}

    ul.messages { margin: 10px 0px 10px 0px!important; }
    .productViewTabsContnt .productsTabInfo .tabs-nav-leasing{ width: 100%; padding: 0; }
    .productViewTabsContnt .productsTabInfo .leasing-tabs ul { margin: 0 0 30px 0; }
    .productViewTabsContnt .productsTabInfo .leasing-tabs li { width: 50%; }
    .productViewTabsContnt .productsTabInfo .tabs-content { width: 100%; }
    .leasing-calculator .downpayment-content input.input-text.downpayment { width: 100px; }
    .leasing-calculator .downpayment-content .button.recalc { padding: 0 10px; min-width: 50px; }
    .leaseCalculator input.downpayment { width: 120px; }
    .leaseCalculator.tbiLeasing .recalcBtn { width: 120px; }
    .productViewTabsContnt .productsTabInfo .leasing-tabs ul.messages li { width: 100%; }

    .personalized-label-section { width: 300px; margin-left: -55px; }

    .shopsListingWrapper .shopsListingGoogleMap #gmapIframes p:first-child iframe{ height: 300px; }
    .shopsListingWrapper .shopsListingGoogleMap #gmapIframes iframe{ height: 300px; }
    .shopsInnerWrapper .gmapIframeView iframe{ height: 300px; }

    .newpay-popup-info-container { padding: 40px 20px 20px 20px !important; }
    .newpay-poppup-close { margin-top: -27px !important; margin-right: 5px !important; }

}



