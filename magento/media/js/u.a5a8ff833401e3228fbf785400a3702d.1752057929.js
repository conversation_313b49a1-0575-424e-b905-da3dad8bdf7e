

/* FILE: prototype.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/*  Prototype JavaScript framework, version 1.7
 *  (c) 2005-2010 Sam Stephenson
 *
 *  Prototype is freely distributable under the terms of an MIT-style license.
 *  For details, see the Prototype web site: http://www.prototypejs.org/
 *
 *--------------------------------------------------------------------------*/

var Prototype = {

  Version: '1.7',

  Browser: (function () {
    var ua = navigator.userAgent
    var isOpera = Object.prototype.toString.call(window.opera) == '[object Opera]'
    return {
      IE: !!window.attachEvent && !isOpera,
      Opera: isOpera,
      WebKit: ua.indexOf('AppleWebKit/') > -1,
      Gecko: ua.indexOf('Gecko') > -1 && ua.indexOf('KHTML') === -1,
      MobileSafari: /Apple.*Mobile/.test(ua)
    }
  })(),

  BrowserFeatures: {
    XPath: !!document.evaluate,

    SelectorsAPI: !!document.querySelector,

    ElementExtensions: (function () {
      var constructor = window.Element || window.HTMLElement
      return !!(constructor && constructor.prototype)
    })(),
    SpecificElementExtensions: (function () {
      if (typeof window.HTMLDivElement !== 'undefined')
        return true

      var div = document.createElement('div'),
        form = document.createElement('form'),
        isSupported = false

      if (div['__proto__'] && (div['__proto__'] !== form['__proto__'])) {
        isSupported = true
      }

      div = form = null

      return isSupported
    })()
  },

  ScriptFragment: '<script[^>]*>([\\S\\s]*?)<\/script>',
  JSONFilter: /^\/\*-secure-([\s\S]*)\*\/\s*$/,

  emptyFunction: function () {
  },

  K: function (x) {
    return x
  }
}

if (Prototype.Browser.MobileSafari)
  Prototype.BrowserFeatures.SpecificElementExtensions = false


var Abstract = {}


var Try = {
  these: function () {
    var returnValue

    for (var i = 0, length = arguments.length; i < length; i++) {
      var lambda = arguments[i]
      try {
        returnValue = lambda()
        break
      } catch (e) {
      }
    }

    return returnValue
  }
}

/* Based on Alex Arnell's inheritance implementation. */

var Class = (function () {

  var IS_DONTENUM_BUGGY = (function () {
    for (var p in {toString: 1}) {
      if (p === 'toString') return false
    }
    return true
  })()

  function subclass() {
  }

  function create() {
    var parent = null, properties = $A(arguments)
    if (Object.isFunction(properties[0]))
      parent = properties.shift()

    function klass() {
      this.initialize.apply(this, arguments)
    }

    Object.extend(klass, Class.Methods)
    klass.superclass = parent
    klass.subclasses = []

    if (parent) {
      subclass.prototype = parent.prototype
      klass.prototype = new subclass
      parent.subclasses.push(klass)
    }

    for (var i = 0, length = properties.length; i < length; i++)
      klass.addMethods(properties[i])

    if (!klass.prototype.initialize)
      klass.prototype.initialize = Prototype.emptyFunction

    klass.prototype.constructor = klass
    return klass
  }

  function addMethods(source) {
    var ancestor = this.superclass && this.superclass.prototype,
      properties = Object.keys(source)

    if (IS_DONTENUM_BUGGY) {
      if (source.toString != Object.prototype.toString)
        properties.push("toString")
      if (source.valueOf != Object.prototype.valueOf)
        properties.push("valueOf")
    }

    for (var i = 0, length = properties.length; i < length; i++) {
      var property = properties[i], value = source[property]
      if (ancestor && Object.isFunction(value) &&
        value.argumentNames()[0] == "$super") {
        var method = value
        value = (function (m) {
          return function () {
            return ancestor[m].apply(this, arguments)
          }
        })(property).wrap(method)

        value.valueOf = method.valueOf.bind(method)
        value.toString = method.toString.bind(method)
      }
      this.prototype[property] = value
    }

    return this
  }

  return {
    create: create,
    Methods: {
      addMethods: addMethods
    }
  }
})();
(function () {

  var _toString = Object.prototype.toString,
    NULL_TYPE = 'Null',
    UNDEFINED_TYPE = 'Undefined',
    BOOLEAN_TYPE = 'Boolean',
    NUMBER_TYPE = 'Number',
    STRING_TYPE = 'String',
    OBJECT_TYPE = 'Object',
    FUNCTION_CLASS = '[object Function]',
    BOOLEAN_CLASS = '[object Boolean]',
    NUMBER_CLASS = '[object Number]',
    STRING_CLASS = '[object String]',
    ARRAY_CLASS = '[object Array]',
    DATE_CLASS = '[object Date]',
    NATIVE_JSON_STRINGIFY_SUPPORT = window.JSON &&
      typeof JSON.stringify === 'function' &&
      JSON.stringify(0) === '0' &&
      typeof JSON.stringify(Prototype.K) === 'undefined'

  function Type(o) {
    switch (o) {
      case null:
        return NULL_TYPE
      case (void 0):
        return UNDEFINED_TYPE
    }
    var type = typeof o
    switch (type) {
      case 'boolean':
        return BOOLEAN_TYPE
      case 'number':
        return NUMBER_TYPE
      case 'string':
        return STRING_TYPE
    }
    return OBJECT_TYPE
  }

  function extend(destination, source) {
    for (var property in source)
      destination[property] = source[property]
    return destination
  }

  function inspect(object) {
    try {
      if (isUndefined(object)) return 'undefined'
      if (object === null) return 'null'
      return object.inspect ? object.inspect() : String(object)
    } catch (e) {
      if (e instanceof RangeError) return '...'
      throw e
    }
  }

  function toJSON(value) {
    return Str('', {'': value}, [])
  }

  function Str(key, holder, stack) {
    var value = holder[key],
      type = typeof value

    if (Type(value) === OBJECT_TYPE && typeof value.toJSON === 'function') {
      value = value.toJSON(key)
    }

    var _class = _toString.call(value)

    switch (_class) {
      case NUMBER_CLASS:
      case BOOLEAN_CLASS:
      case STRING_CLASS:
        value = value.valueOf()
    }

    switch (value) {
      case null:
        return 'null'
      case true:
        return 'true'
      case false:
        return 'false'
    }

    type = typeof value
    switch (type) {
      case 'string':
        return value.inspect(true)
      case 'number':
        return isFinite(value) ? String(value) : 'null'
      case 'object':

        for (var i = 0, length = stack.length; i < length; i++) {
          if (stack[i] === value) {
            throw new TypeError()
          }
        }
        stack.push(value)

        var partial = []
        if (_class === ARRAY_CLASS) {
          for (var i = 0, length = value.length; i < length; i++) {
            var str = Str(i, value, stack)
            partial.push(typeof str === 'undefined' ? 'null' : str)
          }
          partial = '[' + partial.join(',') + ']'
        } else {
          var keys = Object.keys(value)
          for (var i = 0, length = keys.length; i < length; i++) {
            var key = keys[i], str = Str(key, value, stack)
            if (typeof str !== "undefined") {
              partial.push(key.inspect(true) + ':' + str)
            }
          }
          partial = '{' + partial.join(',') + '}'
        }
        stack.pop()
        return partial
    }
  }

  function stringify(object) {
    return JSON.stringify(object)
  }

  function toQueryString(object) {
    return $H(object).toQueryString()
  }

  function toHTML(object) {
    return object && object.toHTML ? object.toHTML() : String.interpret(object)
  }

  function keys(object) {
    if (Type(object) !== OBJECT_TYPE) {
      throw new TypeError()
    }
    var results = []
    for (var property in object) {
      if (object.hasOwnProperty(property)) {
        results.push(property)
      }
    }
    return results
  }

  function values(object) {
    var results = []
    for (var property in object)
      results.push(object[property])
    return results
  }

  function clone(object) {
    return extend({}, object)
  }

  function isElement(object) {
    return !!(object && object.nodeType == 1)
  }

  function isArray(object) {
    return _toString.call(object) === ARRAY_CLASS
  }

  var hasNativeIsArray = (typeof Array.isArray == 'function')
    && Array.isArray([]) && !Array.isArray({})

  if (hasNativeIsArray) {
    isArray = Array.isArray
  }

  function isHash(object) {
    return object instanceof Hash
  }

  function isFunction(object) {
    return _toString.call(object) === FUNCTION_CLASS
  }

  function isString(object) {
    return _toString.call(object) === STRING_CLASS
  }

  function isNumber(object) {
    return _toString.call(object) === NUMBER_CLASS
  }

  function isDate(object) {
    return _toString.call(object) === DATE_CLASS
  }

  function isUndefined(object) {
    return typeof object === "undefined"
  }

  extend(Object, {
    extend: extend,
    inspect: inspect,
    toJSON: NATIVE_JSON_STRINGIFY_SUPPORT ? stringify : toJSON,
    toQueryString: toQueryString,
    toHTML: toHTML,
    keys: Object.keys || keys,
    values: values,
    clone: clone,
    isElement: isElement,
    isArray: isArray,
    isHash: isHash,
    isFunction: isFunction,
    isString: isString,
    isNumber: isNumber,
    isDate: isDate,
    isUndefined: isUndefined
  })
})()
Object.extend(Function.prototype, (function () {
  var slice = Array.prototype.slice

  function update(array, args) {
    var arrayLength = array.length, length = args.length
    while (length--) array[arrayLength + length] = args[length]
    return array
  }

  function merge(array, args) {
    array = slice.call(array, 0)
    return update(array, args)
  }

  function argumentNames() {
    var names = this.toString().match(/^[\s\(]*function[^(]*\(([^)]*)\)/)[1]
      .replace(/\/\/.*?[\r\n]|\/\*(?:.|[\r\n])*?\*\//g, '')
      .replace(/\s+/g, '').split(',')
    return names.length == 1 && !names[0] ? [] : names
  }

  function bind(context) {
    if (arguments.length < 2 && Object.isUndefined(arguments[0])) return this
    var __method = this, args = slice.call(arguments, 1)
    return function () {
      var a = merge(args, arguments)
      return __method.apply(context, a)
    }
  }

  function bindAsEventListener(context) {
    var __method = this, args = slice.call(arguments, 1)
    return function (event) {
      var a = update([event || window.event], args)
      return __method.apply(context, a)
    }
  }

  function curry() {
    if (!arguments.length) return this
    var __method = this, args = slice.call(arguments, 0)
    return function () {
      var a = merge(args, arguments)
      return __method.apply(this, a)
    }
  }

  function delay(timeout) {
    var __method = this, args = slice.call(arguments, 1)
    timeout = timeout * 1000
    return window.setTimeout(function () {
      return __method.apply(__method, args)
    }, timeout)
  }

  function defer() {
    var args = update([0.01], arguments)
    return this.delay.apply(this, args)
  }

  function wrap(wrapper) {
    var __method = this
    return function () {
      var a = update([__method.bind(this)], arguments)
      return wrapper.apply(this, a)
    }
  }

  function methodize() {
    if (this._methodized) return this._methodized
    var __method = this
    return this._methodized = function () {
      var a = update([this], arguments)
      return __method.apply(null, a)
    }
  }

  return {
    argumentNames: argumentNames,
    bind: bind,
    bindAsEventListener: bindAsEventListener,
    curry: curry,
    delay: delay,
    defer: defer,
    wrap: wrap,
    methodize: methodize
  }
})());


(function (proto) {


  function toISOString() {
    return this.getUTCFullYear() + '-' +
      (this.getUTCMonth() + 1).toPaddedString(2) + '-' +
      this.getUTCDate().toPaddedString(2) + 'T' +
      this.getUTCHours().toPaddedString(2) + ':' +
      this.getUTCMinutes().toPaddedString(2) + ':' +
      this.getUTCSeconds().toPaddedString(2) + 'Z'
  }


  function toJSON() {
    return this.toISOString()
  }

  if (!proto.toISOString) proto.toISOString = toISOString
  if (!proto.toJSON) proto.toJSON = toJSON

})(Date.prototype)


RegExp.prototype.match = RegExp.prototype.test

RegExp.escape = function (str) {
  return String(str).replace(/([.*+?^=!:${}()|[\]\/\\])/g, '\\$1')
}
var PeriodicalExecuter = Class.create({
  initialize: function (callback, frequency) {
    this.callback = callback
    this.frequency = frequency
    this.currentlyExecuting = false

    this.registerCallback()
  },

  registerCallback: function () {
    this.timer = setInterval(this.onTimerEvent.bind(this), this.frequency * 1000)
  },

  execute: function () {
    this.callback(this)
  },

  stop: function () {
    if (!this.timer) return
    clearInterval(this.timer)
    this.timer = null
  },

  onTimerEvent: function () {
    if (!this.currentlyExecuting) {
      try {
        this.currentlyExecuting = true
        this.execute()
        this.currentlyExecuting = false
      } catch (e) {
        this.currentlyExecuting = false
        throw e
      }
    }
  }
})
Object.extend(String, {
  interpret: function (value) {
    return value == null ? '' : String(value)
  },
  specialChar: {
    '\b': '\\b',
    '\t': '\\t',
    '\n': '\\n',
    '\f': '\\f',
    '\r': '\\r',
    '\\': '\\\\'
  }
})

Object.extend(String.prototype, (function () {
  var NATIVE_JSON_PARSE_SUPPORT = window.JSON &&
    typeof JSON.parse === 'function' &&
    JSON.parse('{"test": true}').test

  function prepareReplacement(replacement) {
    if (Object.isFunction(replacement)) return replacement
    var template = new Template(replacement)
    return function (match) {
      return template.evaluate(match)
    }
  }

  function gsub(pattern, replacement) {
    var result = '', source = this, match
    replacement = prepareReplacement(replacement)

    if (Object.isString(pattern))
      pattern = RegExp.escape(pattern)

    if (!(pattern.length || pattern.source)) {
      replacement = replacement('')
      return replacement + source.split('').join(replacement) + replacement
    }

    while (source.length > 0) {
      if (match = source.match(pattern)) {
        result += source.slice(0, match.index)
        result += String.interpret(replacement(match))
        source = source.slice(match.index + match[0].length)
      } else {
        result += source, source = ''
      }
    }
    return result
  }

  function sub(pattern, replacement, count) {
    replacement = prepareReplacement(replacement)
    count = Object.isUndefined(count) ? 1 : count

    return this.gsub(pattern, function (match) {
      if (--count < 0) return match[0]
      return replacement(match)
    })
  }

  function scan(pattern, iterator) {
    this.gsub(pattern, iterator)
    return String(this)
  }

  function truncate(length, truncation) {
    length = length || 30
    truncation = Object.isUndefined(truncation) ? '...' : truncation
    return this.length > length ?
      this.slice(0, length - truncation.length) + truncation : String(this)
  }

  function strip() {
    return this.replace(/^\s+/, '').replace(/\s+$/, '')
  }

  function stripTags() {
    return this.replace(/<\w+(\s+("[^"]*"|'[^']*'|[^>])+)?>|<\/\w+>/gi, '')
  }

  function stripScripts() {
    return this.replace(new RegExp(Prototype.ScriptFragment, 'img'), '')
  }

  function extractScripts() {
    var matchAll = new RegExp(Prototype.ScriptFragment, 'img'),
      matchOne = new RegExp(Prototype.ScriptFragment, 'im')
    return (this.match(matchAll) || []).map(function (scriptTag) {
      return (scriptTag.match(matchOne) || ['', ''])[1]
    })
  }

  function evalScripts() {
    return this.extractScripts().map(function (script) {
      return eval(script)
    })
  }

  function escapeHTML() {
    return this.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')
  }

  function unescapeHTML() {
    return this.stripTags().replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
  }


  function toQueryParams(separator) {
    var match = this.strip().match(/([^?#]*)(#.*)?$/)
    if (!match) return {}

    return match[1].split(separator || '&').inject({}, function (hash, pair) {
      if ((pair = pair.split('='))[0]) {
        var key = decodeURIComponent(pair.shift()),
          value = pair.length > 1 ? pair.join('=') : pair[0]

        if (value != undefined) value = decodeURIComponent(value)

        if (key in hash) {
          if (!Object.isArray(hash[key])) hash[key] = [hash[key]]
          hash[key].push(value)
        } else hash[key] = value
      }
      return hash
    })
  }

  function toArray() {
    return this.split('')
  }

  function succ() {
    return this.slice(0, this.length - 1) +
      String.fromCharCode(this.charCodeAt(this.length - 1) + 1)
  }

  function times(count) {
    return count < 1 ? '' : new Array(count + 1).join(this)
  }

  function camelize() {
    return this.replace(/-+(.)?/g, function (match, chr) {
      return chr ? chr.toUpperCase() : ''
    })
  }

  function capitalize() {
    return this.charAt(0).toUpperCase() + this.substring(1).toLowerCase()
  }

  function underscore() {
    return this.replace(/::/g, '/')
      .replace(/([A-Z]+)([A-Z][a-z])/g, '$1_$2')
      .replace(/([a-z\d])([A-Z])/g, '$1_$2')
      .replace(/-/g, '_')
      .toLowerCase()
  }

  function dasherize() {
    return this.replace(/_/g, '-')
  }

  function inspect(useDoubleQuotes) {
    var escapedString = this.replace(/[\x00-\x1f\\]/g, function (character) {
      if (character in String.specialChar) {
        return String.specialChar[character]
      }
      return '\\u00' + character.charCodeAt().toPaddedString(2, 16)
    })
    if (useDoubleQuotes) return '"' + escapedString.replace(/"/g, '\\"') + '"'
    return "'" + escapedString.replace(/'/g, '\\\'') + "'"
  }

  function unfilterJSON(filter) {
    return this.replace(filter || Prototype.JSONFilter, '$1')
  }

  function isJSON() {
    var str = this
    if (str.blank()) return false
    str = str.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g, '@')
    str = str.replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']')
    str = str.replace(/(?:^|:|,)(?:\s*\[)+/g, '')
    return (/^[\],:{}\s]*$/).test(str)
  }

  function evalJSON(sanitize) {
    var json = this.unfilterJSON(),
      cx = /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g
    if (cx.test(json)) {
      json = json.replace(cx, function (a) {
        return '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4)
      })
    }
    try {
      if (!sanitize || json.isJSON()) return eval('(' + json + ')')
    } catch (e) {
    }
    throw new SyntaxError('Badly formed JSON string: ' + this.inspect())
  }

  function parseJSON() {
    var json = this.unfilterJSON()
    return JSON.parse(json)
  }

  function include(pattern) {
    return this.indexOf(pattern) > -1
  }

  function startsWith(pattern) {
    return this.lastIndexOf(pattern, 0) === 0
  }

  function endsWith(pattern) {
    var d = this.length - pattern.length
    return d >= 0 && this.indexOf(pattern, d) === d
  }

  function empty() {
    return this == ''
  }

  function blank() {
    return /^\s*$/.test(this)
  }

  function interpolate(object, pattern) {
    return new Template(this, pattern).evaluate(object)
  }

  return {
    gsub: gsub,
    sub: sub,
    scan: scan,
    truncate: truncate,
    strip: String.prototype.trim || strip,
    stripTags: stripTags,
    stripScripts: stripScripts,
    extractScripts: extractScripts,
    evalScripts: evalScripts,
    escapeHTML: escapeHTML,
    unescapeHTML: unescapeHTML,
    toQueryParams: toQueryParams,
    parseQuery: toQueryParams,
    toArray: toArray,
    succ: succ,
    times: times,
    camelize: camelize,
    capitalize: capitalize,
    underscore: underscore,
    dasherize: dasherize,
    inspect: inspect,
    unfilterJSON: unfilterJSON,
    isJSON: isJSON,
    evalJSON: NATIVE_JSON_PARSE_SUPPORT ? parseJSON : evalJSON,
    include: include,
    startsWith: startsWith,
    endsWith: endsWith,
    empty: empty,
    blank: blank,
    interpolate: interpolate
  }
})())

var Template = Class.create({
  initialize: function (template, pattern) {
    this.template = template.toString()
    this.pattern = pattern || Template.Pattern
  },

  evaluate: function (object) {
    if (object && Object.isFunction(object.toTemplateReplacements))
      object = object.toTemplateReplacements()

    return this.template.gsub(this.pattern, function (match) {
      if (object == null) return (match[1] + '')

      var before = match[1] || ''
      if (before == '\\') return match[2]

      var ctx = object, expr = match[3],
        pattern = /^([^.[]+|\[((?:.*?[^\\])?)\])(\.|\[|$)/

      match = pattern.exec(expr)
      if (match == null) return before

      while (match != null) {
        var comp = match[1].startsWith('[') ? match[2].replace(/\\\\]/g, ']') : match[1]
        ctx = ctx[comp]
        if (null == ctx || '' == match[3]) break
        expr = expr.substring('[' == match[3] ? match[1].length : match[0].length)
        match = pattern.exec(expr)
      }

      return before + String.interpret(ctx)
    })
  }
})
Template.Pattern = /(^|.|\r|\n)(#\{(.*?)\})/

var $break = {}

var Enumerable = (function () {
  function each(iterator, context) {
    var index = 0
    try {
      this._each(function (value) {
        iterator.call(context, value, index++)
      })
    } catch (e) {
      if (e != $break) throw e
    }
    return this
  }

  function eachSlice(number, iterator, context) {
    var index = -number, slices = [], array = this.toArray()
    if (number < 1) return array
    while ((index += number) < array.length)
      slices.push(array.slice(index, index + number))
    return slices.collect(iterator, context)
  }

  function all(iterator, context) {
    iterator = iterator || Prototype.K
    var result = true
    this.each(function (value, index) {
      result = result && !!iterator.call(context, value, index)
      if (!result) throw $break
    })
    return result
  }

  function any(iterator, context) {
    iterator = iterator || Prototype.K
    var result = false
    this.each(function (value, index) {
      if (result = !!iterator.call(context, value, index))
        throw $break
    })
    return result
  }

  function collect(iterator, context) {
    iterator = iterator || Prototype.K
    var results = []
    this.each(function (value, index) {
      results.push(iterator.call(context, value, index))
    })
    return results
  }

  function detect(iterator, context) {
    var result
    this.each(function (value, index) {
      if (iterator.call(context, value, index)) {
        result = value
        throw $break
      }
    })
    return result
  }

  function findAll(iterator, context) {
    var results = []
    this.each(function (value, index) {
      if (iterator.call(context, value, index))
        results.push(value)
    })
    return results
  }

  function grep(filter, iterator, context) {
    iterator = iterator || Prototype.K
    var results = []

    if (Object.isString(filter))
      filter = new RegExp(RegExp.escape(filter))

    this.each(function (value, index) {
      if (filter.match(value))
        results.push(iterator.call(context, value, index))
    })
    return results
  }

  function include(object) {
    if (Object.isFunction(this.indexOf))
      if (this.indexOf(object) != -1) return true

    var found = false
    this.each(function (value) {
      if (value == object) {
        found = true
        throw $break
      }
    })
    return found
  }

  function inGroupsOf(number, fillWith) {
    fillWith = Object.isUndefined(fillWith) ? null : fillWith
    return this.eachSlice(number, function (slice) {
      while (slice.length < number) slice.push(fillWith)
      return slice
    })
  }

  function inject(memo, iterator, context) {
    this.each(function (value, index) {
      memo = iterator.call(context, memo, value, index)
    })
    return memo
  }

  function invoke(method) {
    var args = $A(arguments).slice(1)
    return this.map(function (value) {
      return value[method].apply(value, args)
    })
  }

  function max(iterator, context) {
    iterator = iterator || Prototype.K
    var result
    this.each(function (value, index) {
      value = iterator.call(context, value, index)
      if (result == null || value >= result)
        result = value
    })
    return result
  }

  function min(iterator, context) {
    iterator = iterator || Prototype.K
    var result
    this.each(function (value, index) {
      value = iterator.call(context, value, index)
      if (result == null || value < result)
        result = value
    })
    return result
  }

  function partition(iterator, context) {
    iterator = iterator || Prototype.K
    var trues = [], falses = []
    this.each(function (value, index) {
      (iterator.call(context, value, index) ?
        trues : falses).push(value)
    })
    return [trues, falses]
  }

  function pluck(property) {
    var results = []
    this.each(function (value) {
      results.push(value[property])
    })
    return results
  }

  function reject(iterator, context) {
    var results = []
    this.each(function (value, index) {
      if (!iterator.call(context, value, index))
        results.push(value)
    })
    return results
  }

  function sortBy(iterator, context) {
    return this.map(function (value, index) {
      return {
        value: value,
        criteria: iterator.call(context, value, index)
      }
    }).sort(function (left, right) {
      var a = left.criteria, b = right.criteria
      return a < b ? -1 : a > b ? 1 : 0
    }).pluck('value')
  }

  function toArray() {
    return this.map()
  }

  function zip() {
    var iterator = Prototype.K, args = $A(arguments)
    if (Object.isFunction(args.last()))
      iterator = args.pop()

    var collections = [this].concat(args).map($A)
    return this.map(function (value, index) {
      return iterator(collections.pluck(index))
    })
  }

  function size() {
    return this.toArray().length
  }

  function inspect() {
    return '#<Enumerable:' + this.toArray().inspect() + '>'
  }


  return {
    each: each,
    eachSlice: eachSlice,
    all: all,
    every: all,
    any: any,
    some: any,
    collect: collect,
    map: collect,
    detect: detect,
    findAll: findAll,
    select: findAll,
    filter: findAll,
    grep: grep,
    include: include,
    member: include,
    inGroupsOf: inGroupsOf,
    inject: inject,
    invoke: invoke,
    max: max,
    min: min,
    partition: partition,
    pluck: pluck,
    reject: reject,
    sortBy: sortBy,
    toArray: toArray,
    entries: toArray,
    zip: zip,
    size: size,
    inspect: inspect,
    find: detect
  }
})()

function $A(iterable) {
  if (!iterable) return []
  if ('toArray' in Object(iterable)) return iterable.toArray()
  var length = iterable.length || 0, results = new Array(length)
  while (length--) results[length] = iterable[length]
  return results
}


function $w(string) {
  if (!Object.isString(string)) return []
  string = string.strip()
  return string ? string.split(/\s+/) : []
}

// FIX for:
// This site overrides Array.from() with an implementation that doesn't support iterables,
// which could cause Google Maps JavaScript API v3 to not work correctly.
function isIterable(obj) {
  // checks for null and undefined
  if (obj == null) {
    return false
  }
  return typeof obj[Symbol.iterator] === 'function'
}

if (isIterable($A)) {
  Array.from = $A
}

(function () {
  var arrayProto = Array.prototype,
    slice = arrayProto.slice,
    _each = arrayProto.forEach // use native browser JS 1.6 implementation if available

  function each(iterator, context) {
    for (var i = 0, length = this.length >>> 0; i < length; i++) {
      if (i in this) iterator.call(context, this[i], i, this)
    }
  }

  if (!_each) _each = each

  function clear() {
    this.length = 0
    return this
  }

  function first() {
    return this[0]
  }

  function last() {
    return this[this.length - 1]
  }

  function compact() {
    return this.select(function (value) {
      return value != null
    })
  }

  function flatten() {
    return this.inject([], function (array, value) {
      if (Object.isArray(value))
        return array.concat(value.flatten())
      array.push(value)
      return array
    })
  }

  function without() {
    var values = slice.call(arguments, 0)
    return this.select(function (value) {
      return !values.include(value)
    })
  }

  function reverse(inline) {
    return (inline === false ? this.toArray() : this)._reverse()
  }

  function uniq(sorted) {
    return this.inject([], function (array, value, index) {
      if (0 == index || (sorted ? array.last() != value : !array.include(value)))
        array.push(value)
      return array
    })
  }

  function intersect(array) {
    return this.uniq().findAll(function (item) {
      return array.detect(function (value) {
        return item === value
      })
    })
  }


  function clone() {
    return slice.call(this, 0)
  }

  function size() {
    return this.length
  }

  function inspect() {
    return '[' + this.map(Object.inspect).join(', ') + ']'
  }

  function indexOf(item, i) {
    i || (i = 0)
    var length = this.length
    if (i < 0) i = length + i
    for (; i < length; i++)
      if (this[i] === item) return i
    return -1
  }

  function lastIndexOf(item, i) {
    i = isNaN(i) ? this.length : (i < 0 ? this.length + i : i) + 1
    var n = this.slice(0, i).reverse().indexOf(item)
    return (n < 0) ? n : i - n - 1
  }

  function concat() {
    var array = slice.call(this, 0), item
    for (var i = 0, length = arguments.length; i < length; i++) {
      item = arguments[i]
      if (Object.isArray(item) && !('callee' in item)) {
        for (var j = 0, arrayLength = item.length; j < arrayLength; j++)
          array.push(item[j])
      } else {
        array.push(item)
      }
    }
    return array
  }

  Object.extend(arrayProto, Enumerable)

  if (!arrayProto._reverse)
    arrayProto._reverse = arrayProto.reverse

  Object.extend(arrayProto, {
    _each: _each,
    clear: clear,
    first: first,
    last: last,
    compact: compact,
    flatten: flatten,
    without: without,
    reverse: reverse,
    uniq: uniq,
    intersect: intersect,
    clone: clone,
    toArray: clone,
    size: size,
    inspect: inspect
  })

  var CONCAT_ARGUMENTS_BUGGY = (function () {
    return [].concat(arguments)[0][0] !== 1
  })(1, 2)

  if (CONCAT_ARGUMENTS_BUGGY) arrayProto.concat = concat

  if (!arrayProto.indexOf) arrayProto.indexOf = indexOf
  if (!arrayProto.lastIndexOf) arrayProto.lastIndexOf = lastIndexOf
})()

function $H(object) {
  return new Hash(object)
}

var Hash = Class.create(Enumerable, (function () {
  function initialize(object) {
    this._object = Object.isHash(object) ? object.toObject() : Object.clone(object)
  }


  function _each(iterator) {
    for (var key in this._object) {
      var value = this._object[key], pair = [key, value]
      pair.key = key
      pair.value = value
      iterator(pair)
    }
  }

  function set(key, value) {
    return this._object[key] = value
  }

  function get(key) {
    if (this._object[key] !== Object.prototype[key])
      return this._object[key]
  }

  function unset(key) {
    var value = this._object[key]
    delete this._object[key]
    return value
  }

  function toObject() {
    return Object.clone(this._object)
  }


  function keys() {
    return this.pluck('key')
  }

  function values() {
    return this.pluck('value')
  }

  function index(value) {
    var match = this.detect(function (pair) {
      return pair.value === value
    })
    return match && match.key
  }

  function merge(object) {
    return this.clone().update(object)
  }

  function update(object) {
    return new Hash(object).inject(this, function (result, pair) {
      result.set(pair.key, pair.value)
      return result
    })
  }

  function toQueryPair(key, value) {
    if (Object.isUndefined(value)) return key
    return key + '=' + encodeURIComponent(String.interpret(value))
  }

  function toQueryString() {
    return this.inject([], function (results, pair) {
      var key = encodeURIComponent(pair.key), values = pair.value

      if (values && typeof values == 'object') {
        if (Object.isArray(values)) {
          var queryValues = []
          for (var i = 0, len = values.length, value; i < len; i++) {
            value = values[i]
            queryValues.push(toQueryPair(key, value))
          }
          return results.concat(queryValues)
        }
      } else results.push(toQueryPair(key, values))
      return results
    }).join('&')
  }

  function inspect() {
    return '#<Hash:{' + this.map(function (pair) {
      return pair.map(Object.inspect).join(': ')
    }).join(', ') + '}>'
  }

  function clone() {
    return new Hash(this)
  }

  return {
    initialize: initialize,
    _each: _each,
    set: set,
    get: get,
    unset: unset,
    toObject: toObject,
    toTemplateReplacements: toObject,
    keys: keys,
    values: values,
    index: index,
    merge: merge,
    update: update,
    toQueryString: toQueryString,
    inspect: inspect,
    toJSON: toObject,
    clone: clone
  }
})())

Hash.from = $H
Object.extend(Number.prototype, (function () {
  function toColorPart() {
    return this.toPaddedString(2, 16)
  }

  function succ() {
    return this + 1
  }

  function times(iterator, context) {
    $R(0, this, true).each(iterator, context)
    return this
  }

  function toPaddedString(length, radix) {
    var string = this.toString(radix || 10)
    return '0'.times(length - string.length) + string
  }

  function abs() {
    return Math.abs(this)
  }

  function round() {
    return Math.round(this)
  }

  function ceil() {
    return Math.ceil(this)
  }

  function floor() {
    return Math.floor(this)
  }

  return {
    toColorPart: toColorPart,
    succ: succ,
    times: times,
    toPaddedString: toPaddedString,
    abs: abs,
    round: round,
    ceil: ceil,
    floor: floor
  }
})())

function $R(start, end, exclusive) {
  return new ObjectRange(start, end, exclusive)
}

var ObjectRange = Class.create(Enumerable, (function () {
  function initialize(start, end, exclusive) {
    this.start = start
    this.end = end
    this.exclusive = exclusive
  }

  function _each(iterator) {
    var value = this.start
    while (this.include(value)) {
      iterator(value)
      value = value.succ()
    }
  }

  function include(value) {
    if (value < this.start)
      return false
    if (this.exclusive)
      return value < this.end
    return value <= this.end
  }

  return {
    initialize: initialize,
    _each: _each,
    include: include
  }
})())


var Ajax = {
  getTransport: function () {
    return Try.these(
      function () {
        return new XMLHttpRequest()
      },
      function () {
        return new ActiveXObject('Msxml2.XMLHTTP')
      },
      function () {
        return new ActiveXObject('Microsoft.XMLHTTP')
      }
    ) || false
  },

  activeRequestCount: 0
}

Ajax.Responders = {
  responders: [],

  _each: function (iterator) {
    this.responders._each(iterator)
  },

  register: function (responder) {
    if (!this.include(responder))
      this.responders.push(responder)
  },

  unregister: function (responder) {
    this.responders = this.responders.without(responder)
  },

  dispatch: function (callback, request, transport, json) {
    this.each(function (responder) {
      if (Object.isFunction(responder[callback])) {
        try {
          responder[callback].apply(responder, [request, transport, json])
        } catch (e) {
        }
      }
    })
  }
}

Object.extend(Ajax.Responders, Enumerable)

Ajax.Responders.register({
  onCreate: function () {
    Ajax.activeRequestCount++
  },
  onComplete: function () {
    Ajax.activeRequestCount--
  }
})
Ajax.Base = Class.create({
  initialize: function (options) {
    this.options = {
      method: 'post',
      asynchronous: true,
      contentType: 'application/x-www-form-urlencoded',
      encoding: 'UTF-8',
      parameters: '',
      evalJSON: true,
      evalJS: true
    }
    Object.extend(this.options, options || {})

    this.options.method = this.options.method.toLowerCase()

    if (Object.isHash(this.options.parameters))
      this.options.parameters = this.options.parameters.toObject()
  }
})
Ajax.Request = Class.create(Ajax.Base, {
  _complete: false,

  initialize: function ($super, url, options) {
    $super(options)
    this.transport = Ajax.getTransport()
    this.request(url)
  },

  request: function (url) {
    this.url = url
    this.method = this.options.method
    var params = Object.isString(this.options.parameters) ?
      this.options.parameters :
      Object.toQueryString(this.options.parameters)

    if (!['get', 'post'].include(this.method)) {
      params += (params ? '&' : '') + "_method=" + this.method
      this.method = 'post'
    }

    if (params && this.method === 'get') {
      this.url += (this.url.include('?') ? '&' : '?') + params
    }

    this.parameters = params.toQueryParams()

    try {
      var response = new Ajax.Response(this)
      if (this.options.onCreate) this.options.onCreate(response)
      Ajax.Responders.dispatch('onCreate', this, response)

      this.transport.open(this.method.toUpperCase(), this.url,
        this.options.asynchronous)

      if (this.options.asynchronous) this.respondToReadyState.bind(this).defer(1)

      this.transport.onreadystatechange = this.onStateChange.bind(this)
      this.setRequestHeaders()

      this.body = this.method == 'post' ? (this.options.postBody || params) : null
      this.transport.send(this.body)

      /* Force Firefox to handle ready state 4 for synchronous requests */
      if (!this.options.asynchronous && this.transport.overrideMimeType)
        this.onStateChange()

    } catch (e) {
      this.dispatchException(e)
    }
  },

  onStateChange: function () {
    var readyState = this.transport.readyState
    if (readyState > 1 && !((readyState == 4) && this._complete))
      this.respondToReadyState(this.transport.readyState)
  },

  setRequestHeaders: function () {
    var headers = {
      'X-Requested-With': 'XMLHttpRequest',
      'X-Prototype-Version': Prototype.Version,
      'Accept': 'text/javascript, text/html, application/xml, text/xml, */*'
    }

    if (this.method == 'post') {
      headers['Content-type'] = this.options.contentType +
        (this.options.encoding ? '; charset=' + this.options.encoding : '')

      /* Force "Connection: close" for older Mozilla browsers to work
       * around a bug where XMLHttpRequest sends an incorrect
       * Content-length header. See Mozilla Bugzilla #246651.
       */
      if (this.transport.overrideMimeType &&
        (navigator.userAgent.match(/Gecko\/(\d{4})/) || [0, 2005])[1] < 2005)
        headers['Connection'] = 'close'
    }

    if (typeof this.options.requestHeaders == 'object') {
      var extras = this.options.requestHeaders

      if (Object.isFunction(extras.push))
        for (var i = 0, length = extras.length; i < length; i += 2)
          headers[extras[i]] = extras[i + 1]
      else
        $H(extras).each(function (pair) {
          headers[pair.key] = pair.value
        })
    }

    for (var name in headers)
      this.transport.setRequestHeader(name, headers[name])
  },

  success: function () {
    var status = this.getStatus()
    return !status || (status >= 200 && status < 300) || status == 304
  },

  getStatus: function () {
    try {
      if (this.transport.status === 1223) return 204
      return this.transport.status || 0
    } catch (e) {
      return 0
    }
  },

  respondToReadyState: function (readyState) {
    var state = Ajax.Request.Events[readyState], response = new Ajax.Response(this)

    if (state == 'Complete') {
      try {
        this._complete = true;
        (this.options['on' + response.status]
          || this.options['on' + (this.success() ? 'Success' : 'Failure')]
          || Prototype.emptyFunction)(response, response.headerJSON)
      } catch (e) {
        this.dispatchException(e)
      }

      var contentType = response.getHeader('Content-type')
      if (this.options.evalJS == 'force'
        || (this.options.evalJS && this.isSameOrigin() && contentType
          && contentType.match(/^\s*(text|application)\/(x-)?(java|ecma)script(;.*)?\s*$/i)))
        this.evalResponse()
    }

    try {
      (this.options['on' + state] || Prototype.emptyFunction)(response, response.headerJSON)
      Ajax.Responders.dispatch('on' + state, this, response, response.headerJSON)
    } catch (e) {
      this.dispatchException(e)
    }

    if (state == 'Complete') {
      this.transport.onreadystatechange = Prototype.emptyFunction
    }
  },

  isSameOrigin: function () {
    var m = this.url.match(/^\s*https?:\/\/[^\/]*/)
    return !m || (m[0] == '#{protocol}//#{domain}#{port}'.interpolate({
      protocol: location.protocol,
      domain: document.domain,
      port: location.port ? ':' + location.port : ''
    }))
  },

  getHeader: function (name) {
    try {
      return this.transport.getResponseHeader(name) || null
    } catch (e) {
      return null
    }
  },

  evalResponse: function () {
    try {
      return eval((this.transport.responseText || '').unfilterJSON())
    } catch (e) {
      this.dispatchException(e)
    }
  },

  dispatchException: function (exception) {
    (this.options.onException || Prototype.emptyFunction)(this, exception)
    Ajax.Responders.dispatch('onException', this, exception)
  }
})

Ajax.Request.Events =
  ['Uninitialized', 'Loading', 'Loaded', 'Interactive', 'Complete']


Ajax.Response = Class.create({
  initialize: function (request) {
    this.request = request
    var transport = this.transport = request.transport,
      readyState = this.readyState = transport.readyState

    if ((readyState > 2 && !Prototype.Browser.IE) || readyState == 4) {
      this.status = this.getStatus()
      this.statusText = this.getStatusText()
      this.responseText = String.interpret(transport.responseText)
      this.headerJSON = this._getHeaderJSON()
    }

    if (readyState == 4) {
      var xml = transport.responseXML
      this.responseXML = Object.isUndefined(xml) ? null : xml
      this.responseJSON = this._getResponseJSON()
    }
  },

  status: 0,

  statusText: '',

  getStatus: Ajax.Request.prototype.getStatus,

  getStatusText: function () {
    try {
      return this.transport.statusText || ''
    } catch (e) {
      return ''
    }
  },

  getHeader: Ajax.Request.prototype.getHeader,

  getAllHeaders: function () {
    try {
      return this.getAllResponseHeaders()
    } catch (e) {
      return null
    }
  },

  getResponseHeader: function (name) {
    return this.transport.getResponseHeader(name)
  },

  getAllResponseHeaders: function () {
    return this.transport.getAllResponseHeaders()
  },

  _getHeaderJSON: function () {
    var json = this.getHeader('X-JSON')
    if (!json) return null
    json = decodeURIComponent(escape(json))
    try {
      return json.evalJSON(this.request.options.sanitizeJSON ||
        !this.request.isSameOrigin())
    } catch (e) {
      this.request.dispatchException(e)
    }
  },

  _getResponseJSON: function () {
    var options = this.request.options
    if (!options.evalJSON || (options.evalJSON != 'force' &&
        !(this.getHeader('Content-type') || '').include('application/json')) ||
      this.responseText.blank())
      return null
    try {
      return this.responseText.evalJSON(options.sanitizeJSON ||
        !this.request.isSameOrigin())
    } catch (e) {
      this.request.dispatchException(e)
    }
  }
})

Ajax.Updater = Class.create(Ajax.Request, {
  initialize: function ($super, container, url, options) {
    this.container = {
      success: (container.success || container),
      failure: (container.failure || (container.success ? null : container))
    }

    options = Object.clone(options)
    var onComplete = options.onComplete
    options.onComplete = (function (response, json) {
      this.updateContent(response.responseText)
      if (Object.isFunction(onComplete)) onComplete(response, json)
    }).bind(this)

    $super(url, options)
  },

  updateContent: function (responseText) {
    var receiver = this.container[this.success() ? 'success' : 'failure'],
      options = this.options

    if (!options.evalScripts) responseText = responseText.stripScripts()

    if (receiver = $(receiver)) {
      if (options.insertion) {
        if (Object.isString(options.insertion)) {
          var insertion = {}
          insertion[options.insertion] = responseText
          receiver.insert(insertion)
        } else options.insertion(receiver, responseText)
      } else receiver.update(responseText)
    }
  }
})

Ajax.PeriodicalUpdater = Class.create(Ajax.Base, {
  initialize: function ($super, container, url, options) {
    $super(options)
    this.onComplete = this.options.onComplete

    this.frequency = (this.options.frequency || 2)
    this.decay = (this.options.decay || 1)

    this.updater = {}
    this.container = container
    this.url = url

    this.start()
  },

  start: function () {
    this.options.onComplete = this.updateComplete.bind(this)
    this.onTimerEvent()
  },

  stop: function () {
    this.updater.options.onComplete = undefined
    clearTimeout(this.timer);
    (this.onComplete || Prototype.emptyFunction).apply(this, arguments)
  },

  updateComplete: function (response) {
    if (this.options.decay) {
      this.decay = (response.responseText == this.lastText ?
        this.decay * this.options.decay : 1)

      this.lastText = response.responseText
    }
    this.timer = this.onTimerEvent.bind(this).delay(this.decay * this.frequency)
  },

  onTimerEvent: function () {
    this.updater = new Ajax.Updater(this.container, this.url, this.options)
  }
})


function $(element) {
  if (arguments.length > 1) {
    for (var i = 0, elements = [], length = arguments.length; i < length; i++)
      elements.push($(arguments[i]))
    return elements
  }
  if (Object.isString(element))
    element = document.getElementById(element)
  return Element.extend(element)
}

if (Prototype.BrowserFeatures.XPath) {
  document._getElementsByXPath = function (expression, parentElement) {
    var results = []
    var query = document.evaluate(expression, $(parentElement) || document,
      null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null)
    for (var i = 0, length = query.snapshotLength; i < length; i++)
      results.push(Element.extend(query.snapshotItem(i)))
    return results
  }
}

/*--------------------------------------------------------------------------*/

if (!Node) var Node = {}

if (!Node.ELEMENT_NODE) {
  Object.extend(Node, {
    ELEMENT_NODE: 1,
    ATTRIBUTE_NODE: 2,
    TEXT_NODE: 3,
    CDATA_SECTION_NODE: 4,
    ENTITY_REFERENCE_NODE: 5,
    ENTITY_NODE: 6,
    PROCESSING_INSTRUCTION_NODE: 7,
    COMMENT_NODE: 8,
    DOCUMENT_NODE: 9,
    DOCUMENT_TYPE_NODE: 10,
    DOCUMENT_FRAGMENT_NODE: 11,
    NOTATION_NODE: 12
  })
}


(function (global) {
  function shouldUseCache(tagName, attributes) {
    if (tagName === 'select') return false
    if ('type' in attributes) return false
    return true
  }

  var HAS_EXTENDED_CREATE_ELEMENT_SYNTAX = (function () {
    try {
      var el = document.createElement('<input name="x">')
      return el.tagName.toLowerCase() === 'input' && el.name === 'x'
    } catch (err) {
      return false
    }
  })()

  var element = global.Element

  global.Element = function (tagName, attributes) {
    attributes = attributes || {}
    tagName = tagName.toLowerCase()
    var cache = Element.cache

    if (HAS_EXTENDED_CREATE_ELEMENT_SYNTAX && attributes.name) {
      tagName = '<' + tagName + ' name="' + attributes.name + '">'
      delete attributes.name
      return Element.writeAttribute(document.createElement(tagName), attributes)
    }

    if (!cache[tagName]) cache[tagName] = Element.extend(document.createElement(tagName))

    var node = shouldUseCache(tagName, attributes) ?
      cache[tagName].cloneNode(false) : document.createElement(tagName)

    return Element.writeAttribute(node, attributes)
  }

  Object.extend(global.Element, element || {})
  if (element) global.Element.prototype = element.prototype

})(this)

Element.idCounter = 1
Element.cache = {}

Element._purgeElement = function (element) {
  var uid = element._prototypeUID
  if (uid) {
    Element.stopObserving(element)
    element._prototypeUID = void 0
    delete Element.Storage[uid]
  }
}

Element.Methods = {
  visible: function (element) {
    return $(element).style.display != 'none'
  },

  toggle: function (element) {
    element = $(element)
    Element[Element.visible(element) ? 'hide' : 'show'](element)
    return element
  },

  hide: function (element) {
    element = $(element)
    element.style.display = 'none'
    return element
  },

  show: function (element) {
    element = $(element)
    element.style.display = ''
    return element
  },

  remove: function (element) {
    element = $(element)
    element.parentNode.removeChild(element)
    return element
  },

  update: (function () {

    var SELECT_ELEMENT_INNERHTML_BUGGY = (function () {
      var el = document.createElement("select"),
        isBuggy = true
      el.innerHTML = "<option value=\"test\">test</option>"
      if (el.options && el.options[0]) {
        isBuggy = el.options[0].nodeName.toUpperCase() !== "OPTION"
      }
      el = null
      return isBuggy
    })()

    var TABLE_ELEMENT_INNERHTML_BUGGY = (function () {
      try {
        var el = document.createElement("table")
        if (el && el.tBodies) {
          el.innerHTML = "<tbody><tr><td>test</td></tr></tbody>"
          var isBuggy = typeof el.tBodies[0] == "undefined"
          el = null
          return isBuggy
        }
      } catch (e) {
        return true
      }
    })()

    var LINK_ELEMENT_INNERHTML_BUGGY = (function () {
      try {
        var el = document.createElement('div')
        el.innerHTML = "<link>"
        var isBuggy = (el.childNodes.length === 0)
        el = null
        return isBuggy
      } catch (e) {
        return true
      }
    })()

    var ANY_INNERHTML_BUGGY = SELECT_ELEMENT_INNERHTML_BUGGY ||
      TABLE_ELEMENT_INNERHTML_BUGGY || LINK_ELEMENT_INNERHTML_BUGGY

    var SCRIPT_ELEMENT_REJECTS_TEXTNODE_APPENDING = (function () {
      var s = document.createElement("script"),
        isBuggy = false
      try {
        s.appendChild(document.createTextNode(""))
        isBuggy = !s.firstChild ||
          s.firstChild && s.firstChild.nodeType !== 3
      } catch (e) {
        isBuggy = true
      }
      s = null
      return isBuggy
    })()


    function update(element, content) {
      element = $(element)
      var purgeElement = Element._purgeElement

      var descendants = element.getElementsByTagName('*'),
        i = descendants.length
      while (i--) purgeElement(descendants[i])

      if (content && content.toElement)
        content = content.toElement()

      if (Object.isElement(content))
        return element.update().insert(content)

      content = Object.toHTML(content)

      var tagName = element.tagName.toUpperCase()

      if (tagName === 'SCRIPT' && SCRIPT_ELEMENT_REJECTS_TEXTNODE_APPENDING) {
        element.text = content
        return element
      }

      if (ANY_INNERHTML_BUGGY) {
        if (tagName in Element._insertionTranslations.tags) {
          while (element.firstChild) {
            element.removeChild(element.firstChild)
          }
          Element._getContentFromAnonymousElement(tagName, content.stripScripts())
            .each(function (node) {
              element.appendChild(node)
            })
        } else if (LINK_ELEMENT_INNERHTML_BUGGY && Object.isString(content) && content.indexOf('<link') > -1) {
          while (element.firstChild) {
            element.removeChild(element.firstChild)
          }
          var nodes = Element._getContentFromAnonymousElement(tagName, content.stripScripts(), true)
          nodes.each(function (node) {
            element.appendChild(node)
          })
        } else {
          element.innerHTML = content.stripScripts()
        }
      } else {
        element.innerHTML = content.stripScripts()
      }

      content.evalScripts.bind(content).defer()
      return element
    }

    return update
  })(),

  replace: function (element, content) {
    element = $(element)
    if (content && content.toElement) content = content.toElement()
    else if (!Object.isElement(content)) {
      content = Object.toHTML(content)
      var range = element.ownerDocument.createRange()
      range.selectNode(element)
      content.evalScripts.bind(content).defer()
      content = range.createContextualFragment(content.stripScripts())
    }
    element.parentNode.replaceChild(content, element)
    return element
  },

  insert: function (element, insertions) {
    element = $(element)

    if (Object.isString(insertions) || Object.isNumber(insertions) ||
      Object.isElement(insertions) || (insertions && (insertions.toElement || insertions.toHTML)))
      insertions = {bottom: insertions}

    var content, insert, tagName, childNodes

    for (var position in insertions) {
      content = insertions[position]
      position = position.toLowerCase()
      insert = Element._insertionTranslations[position]

      if (content && content.toElement) content = content.toElement()
      if (Object.isElement(content)) {
        insert(element, content)
        continue
      }

      content = Object.toHTML(content)

      tagName = ((position == 'before' || position == 'after')
        ? element.parentNode : element).tagName.toUpperCase()

      childNodes = Element._getContentFromAnonymousElement(tagName, content.stripScripts())

      if (position == 'top' || position == 'after') childNodes.reverse()
      childNodes.each(insert.curry(element))

      content.evalScripts.bind(content).defer()
    }

    return element
  },

  wrap: function (element, wrapper, attributes) {
    element = $(element)
    if (Object.isElement(wrapper))
      $(wrapper).writeAttribute(attributes || {})
    else if (Object.isString(wrapper)) wrapper = new Element(wrapper, attributes)
    else wrapper = new Element('div', wrapper)
    if (element.parentNode)
      element.parentNode.replaceChild(wrapper, element)
    wrapper.appendChild(element)
    return wrapper
  },

  inspect: function (element) {
    element = $(element)
    var result = '<' + element.tagName.toLowerCase()
    $H({'id': 'id', 'className': 'class'}).each(function (pair) {
      var property = pair.first(),
        attribute = pair.last(),
        value = (element[property] || '').toString()
      if (value) result += ' ' + attribute + '=' + value.inspect(true)
    })
    return result + '>'
  },

  recursivelyCollect: function (element, property, maximumLength) {
    element = $(element)
    maximumLength = maximumLength || -1
    var elements = []

    while (element = element[property]) {
      if (element.nodeType == 1)
        elements.push(Element.extend(element))
      if (elements.length == maximumLength)
        break
    }

    return elements
  },

  ancestors: function (element) {
    return Element.recursivelyCollect(element, 'parentNode')
  },

  descendants: function (element) {
    return Element.select(element, "*")
  },

  firstDescendant: function (element) {
    element = $(element).firstChild
    while (element && element.nodeType != 1) element = element.nextSibling
    return $(element)
  },

  immediateDescendants: function (element) {
    var results = [], child = $(element).firstChild
    while (child) {
      if (child.nodeType === 1) {
        results.push(Element.extend(child))
      }
      child = child.nextSibling
    }
    return results
  },

  previousSiblings: function (element, maximumLength) {
    return Element.recursivelyCollect(element, 'previousSibling')
  },

  nextSiblings: function (element) {
    return Element.recursivelyCollect(element, 'nextSibling')
  },

  siblings: function (element) {
    element = $(element)
    return Element.previousSiblings(element).reverse()
      .concat(Element.nextSiblings(element))
  },

  match: function (element, selector) {
    element = $(element)
    if (Object.isString(selector))
      return Prototype.Selector.match(element, selector)
    return selector.match(element)
  },

  up: function (element, expression, index) {
    element = $(element)
    if (arguments.length == 1) return $(element.parentNode)
    var ancestors = Element.ancestors(element)
    return Object.isNumber(expression) ? ancestors[expression] :
      Prototype.Selector.find(ancestors, expression, index)
  },

  down: function (element, expression, index) {
    element = $(element)
    if (arguments.length == 1) return Element.firstDescendant(element)
    return Object.isNumber(expression) ? Element.descendants(element)[expression] :
      Element.select(element, expression)[index || 0]
  },

  previous: function (element, expression, index) {
    element = $(element)
    if (Object.isNumber(expression)) index = expression, expression = false
    if (!Object.isNumber(index)) index = 0

    if (expression) {
      return Prototype.Selector.find(element.previousSiblings(), expression, index)
    } else {
      return element.recursivelyCollect("previousSibling", index + 1)[index]
    }
  },

  next: function (element, expression, index) {
    element = $(element)
    if (Object.isNumber(expression)) index = expression, expression = false
    if (!Object.isNumber(index)) index = 0

    if (expression) {
      return Prototype.Selector.find(element.nextSiblings(), expression, index)
    } else {
      var maximumLength = Object.isNumber(index) ? index + 1 : 1
      return element.recursivelyCollect("nextSibling", index + 1)[index]
    }
  },


  select: function (element) {
    element = $(element)
    var expressions = Array.prototype.slice.call(arguments, 1).join(', ')
    return Prototype.Selector.select(expressions, element)
  },

  adjacent: function (element) {
    element = $(element)
    var expressions = Array.prototype.slice.call(arguments, 1).join(', ')
    return Prototype.Selector.select(expressions, element.parentNode).without(element)
  },

  identify: function (element) {
    element = $(element)
    var id = Element.readAttribute(element, 'id')
    if (id) return id
    do {
      id = 'anonymous_element_' + Element.idCounter++
    } while ($(id))
    Element.writeAttribute(element, 'id', id)
    return id
  },

  readAttribute: function (element, name) {
    element = $(element)
    if (Prototype.Browser.IE) {
      var t = Element._attributeTranslations.read
      if (t.values[name]) return t.values[name](element, name)
      if (t.names[name]) name = t.names[name]
      if (name.include(':')) {
        return (!element.attributes || !element.attributes[name]) ? null :
          element.attributes[name].value
      }
    }
    return element.getAttribute(name)
  },

  writeAttribute: function (element, name, value) {
    element = $(element)
    var attributes = {}, t = Element._attributeTranslations.write

    if (typeof name == 'object') attributes = name
    else attributes[name] = Object.isUndefined(value) ? true : value

    for (var attr in attributes) {
      name = t.names[attr] || attr
      value = attributes[attr]
      if (t.values[attr]) name = t.values[attr](element, value)
      if (value === false || value === null)
        element.removeAttribute(name)
      else if (value === true)
        element.setAttribute(name, name)
      else element.setAttribute(name, value)
    }
    return element
  },

  getHeight: function (element) {
    return Element.getDimensions(element).height
  },

  getWidth: function (element) {
    return Element.getDimensions(element).width
  },

  classNames: function (element) {
    return new Element.ClassNames(element)
  },

  hasClassName: function (element, className) {
    if (!(element = $(element))) return
    var elementClassName = element.className
    return (elementClassName.length > 0 && (elementClassName == className ||
      new RegExp("(^|\\s)" + className + "(\\s|$)").test(elementClassName)))
  },

  addClassName: function (element, className) {
    if (!(element = $(element))) return
    if (!Element.hasClassName(element, className))
      element.className += (element.className ? ' ' : '') + className
    return element
  },

  removeClassName: function (element, className) {
    if (!(element = $(element))) return
    element.className = element.className.replace(
      new RegExp("(^|\\s+)" + className + "(\\s+|$)"), ' ').strip()
    return element
  },

  toggleClassName: function (element, className) {
    if (!(element = $(element))) return
    return Element[Element.hasClassName(element, className) ?
      'removeClassName' : 'addClassName'](element, className)
  },

  cleanWhitespace: function (element) {
    element = $(element)
    var node = element.firstChild
    while (node) {
      var nextNode = node.nextSibling
      if (node.nodeType == 3 && !/\S/.test(node.nodeValue))
        element.removeChild(node)
      node = nextNode
    }
    return element
  },

  empty: function (element) {
    return $(element).innerHTML.blank()
  },

  descendantOf: function (element, ancestor) {
    element = $(element), ancestor = $(ancestor)

    if (element.compareDocumentPosition)
      return (element.compareDocumentPosition(ancestor) & 8) === 8

    if (ancestor.contains)
      return ancestor.contains(element) && ancestor !== element

    while (element = element.parentNode)
      if (element == ancestor) return true

    return false
  },

  scrollTo: function (element) {
    element = $(element)
    var pos = Element.cumulativeOffset(element)
    window.scrollTo(pos[0], pos[1])
    return element
  },

  getStyle: function (element, style) {
    element = $(element)
    style = style == 'float' ? 'cssFloat' : style.camelize()
    var value = element.style[style]
    if (!value || value == 'auto') {
      var css = document.defaultView.getComputedStyle(element, null)
      value = css ? css[style] : null
    }
    if (style == 'opacity') return value ? parseFloat(value) : 1.0
    return value == 'auto' ? null : value
  },

  getOpacity: function (element) {
    return $(element).getStyle('opacity')
  },

  setStyle: function (element, styles) {
    element = $(element)
    var elementStyle = element.style, match
    if (Object.isString(styles)) {
      element.style.cssText += ';' + styles
      return styles.include('opacity') ?
        element.setOpacity(styles.match(/opacity:\s*(\d?\.?\d*)/)[1]) : element
    }
    for (var property in styles)
      if (property == 'opacity') element.setOpacity(styles[property])
      else
        elementStyle[(property == 'float' || property == 'cssFloat') ?
          (Object.isUndefined(elementStyle.styleFloat) ? 'cssFloat' : 'styleFloat') :
          property] = styles[property]

    return element
  },

  setOpacity: function (element, value) {
    element = $(element)
    element.style.opacity = (value == 1 || value === '') ? '' :
      (value < 0.00001) ? 0 : value
    return element
  },

  makePositioned: function (element) {
    element = $(element)
    var pos = Element.getStyle(element, 'position')
    if (pos == 'static' || !pos) {
      element._madePositioned = true
      element.style.position = 'relative'
      if (Prototype.Browser.Opera) {
        element.style.top = 0
        element.style.left = 0
      }
    }
    return element
  },

  undoPositioned: function (element) {
    element = $(element)
    if (element._madePositioned) {
      element._madePositioned = undefined
      element.style.position =
        element.style.top =
          element.style.left =
            element.style.bottom =
              element.style.right = ''
    }
    return element
  },

  makeClipping: function (element) {
    element = $(element)
    if (element._overflow) return element
    element._overflow = Element.getStyle(element, 'overflow') || 'auto'
    if (element._overflow !== 'hidden')
      element.style.overflow = 'hidden'
    return element
  },

  undoClipping: function (element) {
    element = $(element)
    if (!element._overflow) return element
    element.style.overflow = element._overflow == 'auto' ? '' : element._overflow
    element._overflow = null
    return element
  },

  clonePosition: function (element, source) {
    var options = Object.extend({
      setLeft: true,
      setTop: true,
      setWidth: true,
      setHeight: true,
      offsetTop: 0,
      offsetLeft: 0
    }, arguments[2] || {})

    source = $(source)
    var p = Element.viewportOffset(source), delta = [0, 0], parent = null

    element = $(element)

    if (Element.getStyle(element, 'position') == 'absolute') {
      parent = Element.getOffsetParent(element)
      delta = Element.viewportOffset(parent)
    }

    if (parent == document.body) {
      delta[0] -= document.body.offsetLeft
      delta[1] -= document.body.offsetTop
    }

    if (options.setLeft) element.style.left = (p[0] - delta[0] + options.offsetLeft) + 'px'
    if (options.setTop) element.style.top = (p[1] - delta[1] + options.offsetTop) + 'px'
    if (options.setWidth) element.style.width = source.offsetWidth + 'px'
    if (options.setHeight) element.style.height = source.offsetHeight + 'px'
    return element
  }
}

Object.extend(Element.Methods, {
  getElementsBySelector: Element.Methods.select,

  childElements: Element.Methods.immediateDescendants
})

Element._attributeTranslations = {
  write: {
    names: {
      className: 'class',
      htmlFor: 'for'
    },
    values: {}
  }
}

if (Prototype.Browser.Opera) {
  Element.Methods.getStyle = Element.Methods.getStyle.wrap(
    function (proceed, element, style) {
      switch (style) {
        case 'height':
        case 'width':
          if (!Element.visible(element)) return null

          var dim = parseInt(proceed(element, style), 10)

          if (dim !== element['offset' + style.capitalize()])
            return dim + 'px'

          var properties
          if (style === 'height') {
            properties = ['border-top-width', 'padding-top',
              'padding-bottom', 'border-bottom-width']
          } else {
            properties = ['border-left-width', 'padding-left',
              'padding-right', 'border-right-width']
          }
          return properties.inject(dim, function (memo, property) {
            var val = proceed(element, property)
            return val === null ? memo : memo - parseInt(val, 10)
          }) + 'px'
        default:
          return proceed(element, style)
      }
    }
  )

  Element.Methods.readAttribute = Element.Methods.readAttribute.wrap(
    function (proceed, element, attribute) {
      if (attribute === 'title') return element.title
      return proceed(element, attribute)
    }
  )
} else if (Prototype.Browser.IE) {
  Element.Methods.getStyle = function (element, style) {
    element = $(element)
    style = (style == 'float' || style == 'cssFloat') ? 'styleFloat' : style.camelize()
    var value = element.style[style]
    if (!value && element.currentStyle) value = element.currentStyle[style]

    if (style == 'opacity') {
      if (value = (element.getStyle('filter') || '').match(/alpha\(opacity=(.*)\)/))
        if (value[1]) return parseFloat(value[1]) / 100
      return 1.0
    }

    if (value == 'auto') {
      if ((style == 'width' || style == 'height') && (element.getStyle('display') != 'none'))
        return element['offset' + style.capitalize()] + 'px'
      return null
    }
    return value
  }

  Element.Methods.setOpacity = function (element, value) {
    function stripAlpha(filter) {
      return filter.replace(/alpha\([^\)]*\)/gi, '')
    }

    element = $(element)
    var currentStyle = element.currentStyle
    if ((currentStyle && !currentStyle.hasLayout) ||
      (!currentStyle && element.style.zoom == 'normal'))
      element.style.zoom = 1

    var filter = element.getStyle('filter'), style = element.style
    if (value == 1 || value === '') {
      (filter = stripAlpha(filter)) ?
        style.filter = filter : style.removeAttribute('filter')
      return element
    } else if (value < 0.00001) value = 0
    style.filter = stripAlpha(filter) +
      'alpha(opacity=' + (value * 100) + ')'
    return element
  }

  Element._attributeTranslations = (function () {

    var classProp = 'className',
      forProp = 'for',
      el = document.createElement('div')

    el.setAttribute(classProp, 'x')

    if (el.className !== 'x') {
      el.setAttribute('class', 'x')
      if (el.className === 'x') {
        classProp = 'class'
      }
    }
    el = null

    el = document.createElement('label')
    el.setAttribute(forProp, 'x')
    if (el.htmlFor !== 'x') {
      el.setAttribute('htmlFor', 'x')
      if (el.htmlFor === 'x') {
        forProp = 'htmlFor'
      }
    }
    el = null

    return {
      read: {
        names: {
          'class': classProp,
          'className': classProp,
          'for': forProp,
          'htmlFor': forProp
        },
        values: {
          _getAttr: function (element, attribute) {
            return element.getAttribute(attribute)
          },
          _getAttr2: function (element, attribute) {
            return element.getAttribute(attribute, 2)
          },
          _getAttrNode: function (element, attribute) {
            var node = element.getAttributeNode(attribute)
            return node ? node.value : ""
          },
          _getEv: (function () {

            var el = document.createElement('div'), f
            el.onclick = Prototype.emptyFunction
            var value = el.getAttribute('onclick')

            if (String(value).indexOf('{') > -1) {
              f = function (element, attribute) {
                attribute = element.getAttribute(attribute)
                if (!attribute) return null
                attribute = attribute.toString()
                attribute = attribute.split('{')[1]
                attribute = attribute.split('}')[0]
                return attribute.strip()
              }
            } else if (value === '') {
              f = function (element, attribute) {
                attribute = element.getAttribute(attribute)
                if (!attribute) return null
                return attribute.strip()
              }
            }
            el = null
            return f
          })(),
          _flag: function (element, attribute) {
            return $(element).hasAttribute(attribute) ? attribute : null
          },
          style: function (element) {
            return element.style.cssText.toLowerCase()
          },
          title: function (element) {
            return element.title
          }
        }
      }
    }
  })()

  Element._attributeTranslations.write = {
    names: Object.extend({
      cellpadding: 'cellPadding',
      cellspacing: 'cellSpacing'
    }, Element._attributeTranslations.read.names),
    values: {
      checked: function (element, value) {
        element.checked = !!value
      },

      style: function (element, value) {
        element.style.cssText = value ? value : ''
      }
    }
  }

  Element._attributeTranslations.has = {}

  $w('colSpan rowSpan vAlign dateTime accessKey tabIndex ' +
    'encType maxLength readOnly longDesc frameBorder').each(function (attr) {
    Element._attributeTranslations.write.names[attr.toLowerCase()] = attr
    Element._attributeTranslations.has[attr.toLowerCase()] = attr
  });

  (function (v) {
    Object.extend(v, {
      href: v._getAttr2,
      src: v._getAttr2,
      type: v._getAttr,
      action: v._getAttrNode,
      disabled: v._flag,
      checked: v._flag,
      readonly: v._flag,
      multiple: v._flag,
      onload: v._getEv,
      onunload: v._getEv,
      onclick: v._getEv,
      ondblclick: v._getEv,
      onmousedown: v._getEv,
      onmouseup: v._getEv,
      onmouseover: v._getEv,
      onmousemove: v._getEv,
      onmouseout: v._getEv,
      onfocus: v._getEv,
      onblur: v._getEv,
      onkeypress: v._getEv,
      onkeydown: v._getEv,
      onkeyup: v._getEv,
      onsubmit: v._getEv,
      onreset: v._getEv,
      onselect: v._getEv,
      onchange: v._getEv
    })
  })(Element._attributeTranslations.read.values)

  if (Prototype.BrowserFeatures.ElementExtensions) {
    (function () {
      function _descendants(element) {
        var nodes = element.getElementsByTagName('*'), results = []
        for (var i = 0, node; node = nodes[i]; i++)
          if (node.tagName !== "!") // Filter out comment nodes.
            results.push(node)
        return results
      }

      Element.Methods.down = function (element, expression, index) {
        element = $(element)
        if (arguments.length == 1) return element.firstDescendant()
        return Object.isNumber(expression) ? _descendants(element)[expression] :
          Element.select(element, expression)[index || 0]
      }
    })()
  }

} else if (Prototype.Browser.Gecko && /rv:1\.8\.0/.test(navigator.userAgent)) {
  Element.Methods.setOpacity = function (element, value) {
    element = $(element)
    element.style.opacity = (value == 1) ? 0.999999 :
      (value === '') ? '' : (value < 0.00001) ? 0 : value
    return element
  }
} else if (Prototype.Browser.WebKit) {
  Element.Methods.setOpacity = function (element, value) {
    element = $(element)
    element.style.opacity = (value == 1 || value === '') ? '' :
      (value < 0.00001) ? 0 : value

    if (value == 1)
      if (element.tagName.toUpperCase() == 'IMG' && element.width) {
        element.width++
        element.width--
      } else try {
        var n = document.createTextNode(' ')
        element.appendChild(n)
        element.removeChild(n)
      } catch (e) {
      }

    return element
  }
}

if ('outerHTML' in document.documentElement) {
  Element.Methods.replace = function (element, content) {
    element = $(element)

    if (content && content.toElement) content = content.toElement()
    if (Object.isElement(content)) {
      element.parentNode.replaceChild(content, element)
      return element
    }

    content = Object.toHTML(content)
    var parent = element.parentNode, tagName = parent.tagName.toUpperCase()

    if (Element._insertionTranslations.tags[tagName]) {
      var nextSibling = element.next(),
        fragments = Element._getContentFromAnonymousElement(tagName, content.stripScripts())
      parent.removeChild(element)
      if (nextSibling)
        fragments.each(function (node) {
          parent.insertBefore(node, nextSibling)
        })
      else
        fragments.each(function (node) {
          parent.appendChild(node)
        })
    } else element.outerHTML = content.stripScripts()

    content.evalScripts.bind(content).defer()
    return element
  }
}

Element._returnOffset = function (l, t) {
  var result = [l, t]
  result.left = l
  result.top = t
  return result
}

Element._getContentFromAnonymousElement = function (tagName, html, force) {
  var div = new Element('div'),
    t = Element._insertionTranslations.tags[tagName]

  var workaround = false
  if (t) workaround = true
  else if (force) {
    workaround = true
    t = ['', '', 0]
  }

  if (workaround) {
    div.innerHTML = '&nbsp;' + t[0] + html + t[1]
    div.removeChild(div.firstChild)
    for (var i = t[2]; i--;) {
      div = div.firstChild
    }
  } else {
    div.innerHTML = html
  }
  return $A(div.childNodes)
}

Element._insertionTranslations = {
  before: function (element, node) {
    element.parentNode.insertBefore(node, element)
  },
  top: function (element, node) {
    element.insertBefore(node, element.firstChild)
  },
  bottom: function (element, node) {
    element.appendChild(node)
  },
  after: function (element, node) {
    element.parentNode.insertBefore(node, element.nextSibling)
  },
  tags: {
    TABLE: ['<table>', '</table>', 1],
    TBODY: ['<table><tbody>', '</tbody></table>', 2],
    TR: ['<table><tbody><tr>', '</tr></tbody></table>', 3],
    TD: ['<table><tbody><tr><td>', '</td></tr></tbody></table>', 4],
    SELECT: ['<select>', '</select>', 1]
  }
};

(function () {
  var tags = Element._insertionTranslations.tags
  Object.extend(tags, {
    THEAD: tags.TBODY,
    TFOOT: tags.TBODY,
    TH: tags.TD
  })
})()

Element.Methods.Simulated = {
  hasAttribute: function (element, attribute) {
    attribute = Element._attributeTranslations.has[attribute] || attribute
    var node = $(element).getAttributeNode(attribute)
    return !!(node && node.specified)
  }
}

Element.Methods.ByTag = {}

Object.extend(Element, Element.Methods);

(function (div) {

  if (!Prototype.BrowserFeatures.ElementExtensions && div['__proto__']) {
    window.HTMLElement = {}
    window.HTMLElement.prototype = div['__proto__']
    Prototype.BrowserFeatures.ElementExtensions = true
  }

  div = null

})(document.createElement('div'))

Element.extend = (function () {

  function checkDeficiency(tagName) {
    if (typeof window.Element != 'undefined') {
      var proto = window.Element.prototype
      if (proto) {
        var id = '_' + (Math.random() + '').slice(2),
          el = document.createElement(tagName)
        proto[id] = 'x'
        var isBuggy = (el[id] !== 'x')
        delete proto[id]
        el = null
        return isBuggy
      }
    }
    return false
  }

  function extendElementWith(element, methods) {
    for (var property in methods) {
      var value = methods[property]
      if (Object.isFunction(value) && !(property in element))
        element[property] = value.methodize()
    }
  }

  var HTMLOBJECTELEMENT_PROTOTYPE_BUGGY = checkDeficiency('object')

  if (Prototype.BrowserFeatures.SpecificElementExtensions) {
    if (HTMLOBJECTELEMENT_PROTOTYPE_BUGGY) {
      return function (element) {
        if (element && typeof element._extendedByPrototype == 'undefined') {
          var t = element.tagName
          if (t && (/^(?:object|applet|embed)$/i.test(t))) {
            extendElementWith(element, Element.Methods)
            extendElementWith(element, Element.Methods.Simulated)
            extendElementWith(element, Element.Methods.ByTag[t.toUpperCase()])
          }
        }
        return element
      }
    }
    return Prototype.K
  }

  var Methods = {}, ByTag = Element.Methods.ByTag

  var extend = Object.extend(function (element) {
    if (!element || typeof element._extendedByPrototype != 'undefined' ||
      element.nodeType != 1 || element == window) return element

    var methods = Object.clone(Methods),
      tagName = element.tagName.toUpperCase()

    if (ByTag[tagName]) Object.extend(methods, ByTag[tagName])

    extendElementWith(element, methods)

    element._extendedByPrototype = Prototype.emptyFunction
    return element

  }, {
    refresh: function () {
      if (!Prototype.BrowserFeatures.ElementExtensions) {
        Object.extend(Methods, Element.Methods)
        Object.extend(Methods, Element.Methods.Simulated)
      }
    }
  })

  extend.refresh()
  return extend
})()

if (document.documentElement.hasAttribute) {
  Element.hasAttribute = function (element, attribute) {
    return element.hasAttribute(attribute)
  }
} else {
  Element.hasAttribute = Element.Methods.Simulated.hasAttribute
}

Element.addMethods = function (methods) {
  var F = Prototype.BrowserFeatures, T = Element.Methods.ByTag

  if (!methods) {
    Object.extend(Form, Form.Methods)
    Object.extend(Form.Element, Form.Element.Methods)
    Object.extend(Element.Methods.ByTag, {
      "FORM": Object.clone(Form.Methods),
      "INPUT": Object.clone(Form.Element.Methods),
      "SELECT": Object.clone(Form.Element.Methods),
      "TEXTAREA": Object.clone(Form.Element.Methods),
      "BUTTON": Object.clone(Form.Element.Methods)
    })
  }

  if (arguments.length == 2) {
    var tagName = methods
    methods = arguments[1]
  }

  if (!tagName) Object.extend(Element.Methods, methods || {})
  else {
    if (Object.isArray(tagName)) tagName.each(extend)
    else extend(tagName)
  }

  function extend(tagName) {
    tagName = tagName.toUpperCase()
    if (!Element.Methods.ByTag[tagName])
      Element.Methods.ByTag[tagName] = {}
    Object.extend(Element.Methods.ByTag[tagName], methods)
  }

  function copy(methods, destination, onlyIfAbsent) {
    onlyIfAbsent = onlyIfAbsent || false
    for (var property in methods) {
      var value = methods[property]
      if (!Object.isFunction(value)) continue
      if (!onlyIfAbsent || !(property in destination))
        destination[property] = value.methodize()
    }
  }

  function findDOMClass(tagName) {
    var klass
    var trans = {
      "OPTGROUP": "OptGroup", "TEXTAREA": "TextArea", "P": "Paragraph",
      "FIELDSET": "FieldSet", "UL": "UList", "OL": "OList", "DL": "DList",
      "DIR": "Directory", "H1": "Heading", "H2": "Heading", "H3": "Heading",
      "H4": "Heading", "H5": "Heading", "H6": "Heading", "Q": "Quote",
      "INS": "Mod", "DEL": "Mod", "A": "Anchor", "IMG": "Image", "CAPTION":
        "TableCaption", "COL": "TableCol", "COLGROUP": "TableCol", "THEAD":
        "TableSection", "TFOOT": "TableSection", "TBODY": "TableSection", "TR":
        "TableRow", "TH": "TableCell", "TD": "TableCell", "FRAMESET":
        "FrameSet", "IFRAME": "IFrame"
    }
    if (trans[tagName]) klass = 'HTML' + trans[tagName] + 'Element'
    if (window[klass]) return window[klass]
    klass = 'HTML' + tagName + 'Element'
    if (window[klass]) return window[klass]
    klass = 'HTML' + tagName.capitalize() + 'Element'
    if (window[klass]) return window[klass]

    var element = document.createElement(tagName),
      proto = element['__proto__'] || element.constructor.prototype

    element = null
    return proto
  }

  var elementPrototype = window.HTMLElement ? HTMLElement.prototype :
    Element.prototype

  if (F.ElementExtensions) {
    copy(Element.Methods, elementPrototype)
    copy(Element.Methods.Simulated, elementPrototype, true)
  }

  if (F.SpecificElementExtensions) {
    for (var tag in Element.Methods.ByTag) {
      var klass = findDOMClass(tag)
      if (Object.isUndefined(klass)) continue
      copy(T[tag], klass.prototype)
    }
  }

  Object.extend(Element, Element.Methods)
  delete Element.ByTag

  if (Element.extend.refresh) Element.extend.refresh()
  Element.cache = {}
}


document.viewport = {

  getDimensions: function () {
    return {width: this.getWidth(), height: this.getHeight()}
  },

  getScrollOffsets: function () {
    return Element._returnOffset(
      window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft,
      window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop)
  }
};

(function (viewport) {
  var B = Prototype.Browser, doc = document, element, property = {}

  function getRootElement() {
    if (B.WebKit && !doc.evaluate)
      return document

    if (B.Opera && window.parseFloat(window.opera.version()) < 9.5)
      return document.body

    return document.documentElement
  }

  function define(D) {
    if (!element) element = getRootElement()

    property[D] = 'client' + D

    viewport['get' + D] = function () {
      return element[property[D]]
    }
    return viewport['get' + D]()
  }

  viewport.getWidth = define.curry('Width')

  viewport.getHeight = define.curry('Height')
})(document.viewport)


Element.Storage = {
  UID: 1
}

Element.addMethods({
  getStorage: function (element) {
    if (!(element = $(element))) return

    var uid
    if (element === window) {
      uid = 0
    } else {
      if (typeof element._prototypeUID === "undefined")
        element._prototypeUID = Element.Storage.UID++
      uid = element._prototypeUID
    }

    if (!Element.Storage[uid])
      Element.Storage[uid] = $H()

    return Element.Storage[uid]
  },

  store: function (element, key, value) {
    if (!(element = $(element))) return

    if (arguments.length === 2) {
      Element.getStorage(element).update(key)
    } else {
      Element.getStorage(element).set(key, value)
    }

    return element
  },

  retrieve: function (element, key, defaultValue) {
    if (!(element = $(element))) return
    var hash = Element.getStorage(element), value = hash.get(key)

    if (Object.isUndefined(value)) {
      hash.set(key, defaultValue)
      value = defaultValue
    }

    return value
  },

  clone: function (element, deep) {
    if (!(element = $(element))) return
    var clone = element.cloneNode(deep)
    clone._prototypeUID = void 0
    if (deep) {
      var descendants = Element.select(clone, '*'),
        i = descendants.length
      while (i--) {
        descendants[i]._prototypeUID = void 0
      }
    }
    return Element.extend(clone)
  },

  purge: function (element) {
    if (!(element = $(element))) return
    var purgeElement = Element._purgeElement

    purgeElement(element)

    var descendants = element.getElementsByTagName('*'),
      i = descendants.length

    while (i--) purgeElement(descendants[i])

    return null
  }
});

(function () {

  function toDecimal(pctString) {
    var match = pctString.match(/^(\d+)%?$/i)
    if (!match) return null
    return (Number(match[1]) / 100)
  }

  function getPixelValue(value, property, context) {
    var element = null
    if (Object.isElement(value)) {
      element = value
      value = element.getStyle(property)
    }

    if (value === null) {
      return null
    }

    if ((/^(?:-)?\d+(\.\d+)?(px)?$/i).test(value)) {
      return window.parseFloat(value)
    }

    var isPercentage = value.include('%'), isViewport = (context === document.viewport)

    if (/\d/.test(value) && element && element.runtimeStyle && !(isPercentage && isViewport)) {
      var style = element.style.left, rStyle = element.runtimeStyle.left
      element.runtimeStyle.left = element.currentStyle.left
      element.style.left = value || 0
      value = element.style.pixelLeft
      element.style.left = style
      element.runtimeStyle.left = rStyle

      return value
    }

    if (element && isPercentage) {
      context = context || element.parentNode
      var decimal = toDecimal(value)
      var whole = null
      var position = element.getStyle('position')

      var isHorizontal = property.include('left') || property.include('right') ||
        property.include('width')

      var isVertical = property.include('top') || property.include('bottom') ||
        property.include('height')

      if (context === document.viewport) {
        if (isHorizontal) {
          whole = document.viewport.getWidth()
        } else if (isVertical) {
          whole = document.viewport.getHeight()
        }
      } else {
        if (isHorizontal) {
          whole = $(context).measure('width')
        } else if (isVertical) {
          whole = $(context).measure('height')
        }
      }

      return (whole === null) ? 0 : whole * decimal
    }

    return 0
  }

  function toCSSPixels(number) {
    if (Object.isString(number) && number.endsWith('px')) {
      return number
    }
    return number + 'px'
  }

  function isDisplayed(element) {
    var originalElement = element
    while (element && element.parentNode) {
      var display = element.getStyle('display')
      if (display === 'none') {
        return false
      }
      element = $(element.parentNode)
    }
    return true
  }

  var hasLayout = Prototype.K
  if ('currentStyle' in document.documentElement) {
    hasLayout = function (element) {
      if (!element.currentStyle.hasLayout) {
        element.style.zoom = 1
      }
      return element
    }
  }

  function cssNameFor(key) {
    if (key.include('border')) key = key + '-width'
    return key.camelize()
  }

  Element.Layout = Class.create(Hash, {
    initialize: function ($super, element, preCompute) {
      $super()
      this.element = $(element)

      Element.Layout.PROPERTIES.each(function (property) {
        this._set(property, null)
      }, this)

      if (preCompute) {
        this._preComputing = true
        this._begin()
        Element.Layout.PROPERTIES.each(this._compute, this)
        this._end()
        this._preComputing = false
      }
    },

    _set: function (property, value) {
      return Hash.prototype.set.call(this, property, value)
    },

    set: function (property, value) {
      throw "Properties of Element.Layout are read-only."
    },

    get: function ($super, property) {
      var value = $super(property)
      return value === null ? this._compute(property) : value
    },

    _begin: function () {
      if (this._prepared) return

      var element = this.element
      if (isDisplayed(element)) {
        this._prepared = true
        return
      }

      var originalStyles = {
        position: element.style.position || '',
        width: element.style.width || '',
        visibility: element.style.visibility || '',
        display: element.style.display || ''
      }

      element.store('prototype_original_styles', originalStyles)

      var position = element.getStyle('position'),
        width = element.getStyle('width')

      if (width === "0px" || width === null) {
        element.style.display = 'block'
        width = element.getStyle('width')
      }

      var context = (position === 'fixed') ? document.viewport :
        element.parentNode

      element.setStyle({
        position: 'absolute',
        visibility: 'hidden',
        display: 'block'
      })

      var positionedWidth = element.getStyle('width')

      var newWidth
      if (width && (positionedWidth === width)) {
        newWidth = getPixelValue(element, 'width', context)
      } else if (position === 'absolute' || position === 'fixed') {
        newWidth = getPixelValue(element, 'width', context)
      } else {
        var parent = element.parentNode, pLayout = $(parent).getLayout()

        newWidth = pLayout.get('width') -
          this.get('margin-left') -
          this.get('border-left') -
          this.get('padding-left') -
          this.get('padding-right') -
          this.get('border-right') -
          this.get('margin-right')
      }

      element.setStyle({width: newWidth + 'px'})

      this._prepared = true
    },

    _end: function () {
      var element = this.element
      var originalStyles = element.retrieve('prototype_original_styles')
      element.store('prototype_original_styles', null)
      element.setStyle(originalStyles)
      this._prepared = false
    },

    _compute: function (property) {
      var COMPUTATIONS = Element.Layout.COMPUTATIONS
      if (!(property in COMPUTATIONS)) {
        throw "Property not found."
      }

      return this._set(property, COMPUTATIONS[property].call(this, this.element))
    },

    toObject: function () {
      var args = $A(arguments)
      var keys = (args.length === 0) ? Element.Layout.PROPERTIES :
        args.join(' ').split(' ')
      var obj = {}
      keys.each(function (key) {
        if (!Element.Layout.PROPERTIES.include(key)) return
        var value = this.get(key)
        if (value != null) obj[key] = value
      }, this)
      return obj
    },

    toHash: function () {
      var obj = this.toObject.apply(this, arguments)
      return new Hash(obj)
    },

    toCSS: function () {
      var args = $A(arguments)
      var keys = (args.length === 0) ? Element.Layout.PROPERTIES :
        args.join(' ').split(' ')
      var css = {}

      keys.each(function (key) {
        if (!Element.Layout.PROPERTIES.include(key)) return
        if (Element.Layout.COMPOSITE_PROPERTIES.include(key)) return

        var value = this.get(key)
        if (value != null) css[cssNameFor(key)] = value + 'px'
      }, this)
      return css
    },

    inspect: function () {
      return "#<Element.Layout>"
    }
  })

  Object.extend(Element.Layout, {
    PROPERTIES: $w('height width top left right bottom border-left border-right border-top border-bottom padding-left padding-right padding-top padding-bottom margin-top margin-bottom margin-left margin-right padding-box-width padding-box-height border-box-width border-box-height margin-box-width margin-box-height'),

    COMPOSITE_PROPERTIES: $w('padding-box-width padding-box-height margin-box-width margin-box-height border-box-width border-box-height'),

    COMPUTATIONS: {
      'height': function (element) {
        if (!this._preComputing) this._begin()

        var bHeight = this.get('border-box-height')
        if (bHeight <= 0) {
          if (!this._preComputing) this._end()
          return 0
        }

        var bTop = this.get('border-top'),
          bBottom = this.get('border-bottom')

        var pTop = this.get('padding-top'),
          pBottom = this.get('padding-bottom')

        if (!this._preComputing) this._end()

        return bHeight - bTop - bBottom - pTop - pBottom
      },

      'width': function (element) {
        if (!this._preComputing) this._begin()

        var bWidth = this.get('border-box-width')
        if (bWidth <= 0) {
          if (!this._preComputing) this._end()
          return 0
        }

        var bLeft = this.get('border-left'),
          bRight = this.get('border-right')

        var pLeft = this.get('padding-left'),
          pRight = this.get('padding-right')

        if (!this._preComputing) this._end()

        return bWidth - bLeft - bRight - pLeft - pRight
      },

      'padding-box-height': function (element) {
        var height = this.get('height'),
          pTop = this.get('padding-top'),
          pBottom = this.get('padding-bottom')

        return height + pTop + pBottom
      },

      'padding-box-width': function (element) {
        var width = this.get('width'),
          pLeft = this.get('padding-left'),
          pRight = this.get('padding-right')

        return width + pLeft + pRight
      },

      'border-box-height': function (element) {
        if (!this._preComputing) this._begin()
        var height = element.offsetHeight
        if (!this._preComputing) this._end()
        return height
      },

      'border-box-width': function (element) {
        if (!this._preComputing) this._begin()
        var width = element.offsetWidth
        if (!this._preComputing) this._end()
        return width
      },

      'margin-box-height': function (element) {
        var bHeight = this.get('border-box-height'),
          mTop = this.get('margin-top'),
          mBottom = this.get('margin-bottom')

        if (bHeight <= 0) return 0

        return bHeight + mTop + mBottom
      },

      'margin-box-width': function (element) {
        var bWidth = this.get('border-box-width'),
          mLeft = this.get('margin-left'),
          mRight = this.get('margin-right')

        if (bWidth <= 0) return 0

        return bWidth + mLeft + mRight
      },

      'top': function (element) {
        var offset = element.positionedOffset()
        return offset.top
      },

      'bottom': function (element) {
        var offset = element.positionedOffset(),
          parent = element.getOffsetParent(),
          pHeight = parent.measure('height')

        var mHeight = this.get('border-box-height')

        return pHeight - mHeight - offset.top
      },

      'left': function (element) {
        var offset = element.positionedOffset()
        return offset.left
      },

      'right': function (element) {
        var offset = element.positionedOffset(),
          parent = element.getOffsetParent(),
          pWidth = parent.measure('width')

        var mWidth = this.get('border-box-width')

        return pWidth - mWidth - offset.left
      },

      'padding-top': function (element) {
        return getPixelValue(element, 'paddingTop')
      },

      'padding-bottom': function (element) {
        return getPixelValue(element, 'paddingBottom')
      },

      'padding-left': function (element) {
        return getPixelValue(element, 'paddingLeft')
      },

      'padding-right': function (element) {
        return getPixelValue(element, 'paddingRight')
      },

      'border-top': function (element) {
        return getPixelValue(element, 'borderTopWidth')
      },

      'border-bottom': function (element) {
        return getPixelValue(element, 'borderBottomWidth')
      },

      'border-left': function (element) {
        return getPixelValue(element, 'borderLeftWidth')
      },

      'border-right': function (element) {
        return getPixelValue(element, 'borderRightWidth')
      },

      'margin-top': function (element) {
        return getPixelValue(element, 'marginTop')
      },

      'margin-bottom': function (element) {
        return getPixelValue(element, 'marginBottom')
      },

      'margin-left': function (element) {
        return getPixelValue(element, 'marginLeft')
      },

      'margin-right': function (element) {
        return getPixelValue(element, 'marginRight')
      }
    }
  })

  if ('getBoundingClientRect' in document.documentElement) {
    Object.extend(Element.Layout.COMPUTATIONS, {
      'right': function (element) {
        var parent = hasLayout(element.getOffsetParent())
        var rect = element.getBoundingClientRect(),
          pRect = parent.getBoundingClientRect()

        return (pRect.right - rect.right).round()
      },

      'bottom': function (element) {
        var parent = hasLayout(element.getOffsetParent())
        var rect = element.getBoundingClientRect(),
          pRect = parent.getBoundingClientRect()

        return (pRect.bottom - rect.bottom).round()
      }
    })
  }

  Element.Offset = Class.create({
    initialize: function (left, top) {
      this.left = left.round()
      this.top = top.round()

      this[0] = this.left
      this[1] = this.top
    },

    relativeTo: function (offset) {
      return new Element.Offset(
        this.left - offset.left,
        this.top - offset.top
      )
    },

    inspect: function () {
      return "#<Element.Offset left: #{left} top: #{top}>".interpolate(this)
    },

    toString: function () {
      return "[#{left}, #{top}]".interpolate(this)
    },

    toArray: function () {
      return [this.left, this.top]
    }
  })

  function getLayout(element, preCompute) {
    return new Element.Layout(element, preCompute)
  }

  function measure(element, property) {
    return $(element).getLayout().get(property)
  }

  function getDimensions(element) {
    element = $(element)
    var display = Element.getStyle(element, 'display')

    if (display && display !== 'none') {
      return {width: element.offsetWidth, height: element.offsetHeight}
    }

    var style = element.style
    var originalStyles = {
      visibility: style.visibility,
      position: style.position,
      display: style.display
    }

    var newStyles = {
      visibility: 'hidden',
      display: 'block'
    }

    if (originalStyles.position !== 'fixed')
      newStyles.position = 'absolute'

    Element.setStyle(element, newStyles)

    var dimensions = {
      width: element.offsetWidth,
      height: element.offsetHeight
    }

    Element.setStyle(element, originalStyles)

    return dimensions
  }

  function getOffsetParent(element) {
    element = $(element)

    if (isDocument(element) || isDetached(element) || isBody(element) || isHtml(element))
      return $(document.body)

    var isInline = (Element.getStyle(element, 'display') === 'inline')
    if (!isInline && element.offsetParent) return $(element.offsetParent)

    while ((element = element.parentNode) && element !== document.body) {
      if (Element.getStyle(element, 'position') !== 'static') {
        return isHtml(element) ? $(document.body) : $(element)
      }
    }

    return $(document.body)
  }


  function cumulativeOffset(element) {
    element = $(element)
    var valueT = 0, valueL = 0
    if (element.parentNode) {
      do {
        valueT += element.offsetTop || 0
        valueL += element.offsetLeft || 0
        element = element.offsetParent
      } while (element)
    }
    return new Element.Offset(valueL, valueT)
  }

  function positionedOffset(element) {
    element = $(element)

    var layout = element.getLayout()

    var valueT = 0, valueL = 0
    do {
      valueT += element.offsetTop || 0
      valueL += element.offsetLeft || 0
      element = element.offsetParent
      if (element) {
        if (isBody(element)) break
        var p = Element.getStyle(element, 'position')
        if (p !== 'static') break
      }
    } while (element)

    valueL -= layout.get('margin-top')
    valueT -= layout.get('margin-left')

    return new Element.Offset(valueL, valueT)
  }

  function cumulativeScrollOffset(element) {
    var valueT = 0, valueL = 0
    do {
      valueT += element.scrollTop || 0
      valueL += element.scrollLeft || 0
      element = element.parentNode
    } while (element)
    return new Element.Offset(valueL, valueT)
  }

  function viewportOffset(forElement) {
    element = $(element)
    var valueT = 0, valueL = 0, docBody = document.body

    var element = forElement
    do {
      valueT += element.offsetTop || 0
      valueL += element.offsetLeft || 0
      if (element.offsetParent == docBody &&
        Element.getStyle(element, 'position') == 'absolute') break
    } while (element = element.offsetParent)

    element = forElement
    do {
      if (element != docBody) {
        valueT -= element.scrollTop || 0
        valueL -= element.scrollLeft || 0
      }
    } while (element = element.parentNode)
    return new Element.Offset(valueL, valueT)
  }

  function absolutize(element) {
    element = $(element)

    if (Element.getStyle(element, 'position') === 'absolute') {
      return element
    }

    var offsetParent = getOffsetParent(element)
    var eOffset = element.viewportOffset(),
      pOffset = offsetParent.viewportOffset()

    var offset = eOffset.relativeTo(pOffset)
    var layout = element.getLayout()

    element.store('prototype_absolutize_original_styles', {
      left: element.getStyle('left'),
      top: element.getStyle('top'),
      width: element.getStyle('width'),
      height: element.getStyle('height')
    })

    element.setStyle({
      position: 'absolute',
      top: offset.top + 'px',
      left: offset.left + 'px',
      width: layout.get('width') + 'px',
      height: layout.get('height') + 'px'
    })

    return element
  }

  function relativize(element) {
    element = $(element)
    if (Element.getStyle(element, 'position') === 'relative') {
      return element
    }

    var originalStyles =
      element.retrieve('prototype_absolutize_original_styles')

    if (originalStyles) element.setStyle(originalStyles)
    return element
  }

  if (Prototype.Browser.IE) {
    getOffsetParent = getOffsetParent.wrap(
      function (proceed, element) {
        element = $(element)

        if (isDocument(element) || isDetached(element) || isBody(element) || isHtml(element))
          return $(document.body)

        var position = element.getStyle('position')
        if (position !== 'static') return proceed(element)

        element.setStyle({position: 'relative'})
        var value = proceed(element)
        element.setStyle({position: position})
        return value
      }
    )

    positionedOffset = positionedOffset.wrap(function (proceed, element) {
      element = $(element)
      if (!element.parentNode) return new Element.Offset(0, 0)
      var position = element.getStyle('position')
      if (position !== 'static') return proceed(element)

      var offsetParent = element.getOffsetParent()
      if (offsetParent && offsetParent.getStyle('position') === 'fixed')
        hasLayout(offsetParent)

      element.setStyle({position: 'relative'})
      var value = proceed(element)
      element.setStyle({position: position})
      return value
    })
  } else if (Prototype.Browser.Webkit) {
    cumulativeOffset = function (element) {
      element = $(element)
      var valueT = 0, valueL = 0
      do {
        valueT += element.offsetTop || 0
        valueL += element.offsetLeft || 0
        if (element.offsetParent == document.body)
          if (Element.getStyle(element, 'position') == 'absolute') break

        element = element.offsetParent
      } while (element)

      return new Element.Offset(valueL, valueT)
    }
  }


  Element.addMethods({
    getLayout: getLayout,
    measure: measure,
    getDimensions: getDimensions,
    getOffsetParent: getOffsetParent,
    cumulativeOffset: cumulativeOffset,
    positionedOffset: positionedOffset,
    cumulativeScrollOffset: cumulativeScrollOffset,
    viewportOffset: viewportOffset,
    absolutize: absolutize,
    relativize: relativize
  })

  function isBody(element) {
    return element.nodeName.toUpperCase() === 'BODY'
  }

  function isHtml(element) {
    return element.nodeName.toUpperCase() === 'HTML'
  }

  function isDocument(element) {
    return element.nodeType === Node.DOCUMENT_NODE
  }

  function isDetached(element) {
    return element !== document.body &&
      !Element.descendantOf(element, document.body)
  }

  if ('getBoundingClientRect' in document.documentElement) {
    Element.addMethods({
      viewportOffset: function (element) {
        element = $(element)
        if (isDetached(element)) return new Element.Offset(0, 0)

        var rect = element.getBoundingClientRect(),
          docEl = document.documentElement
        return new Element.Offset(rect.left - docEl.clientLeft,
          rect.top - docEl.clientTop)
      }
    })
  }
})()
window.$$ = function () {
  var expression = $A(arguments).join(', ')
  return Prototype.Selector.select(expression, document)
}

Prototype.Selector = (function () {

  function select() {
    throw new Error('Method "Prototype.Selector.select" must be defined.')
  }

  function match() {
    throw new Error('Method "Prototype.Selector.match" must be defined.')
  }

  function find(elements, expression, index) {
    index = index || 0
    var match = Prototype.Selector.match, length = elements.length, matchIndex = 0, i

    for (i = 0; i < length; i++) {
      if (match(elements[i], expression) && index == matchIndex++) {
        return Element.extend(elements[i])
      }
    }
  }

  function extendElements(elements) {
    for (var i = 0, length = elements.length; i < length; i++) {
      Element.extend(elements[i])
    }
    return elements
  }


  var K = Prototype.K

  return {
    select: select,
    match: match,
    find: find,
    extendElements: (Element.extend === K) ? K : extendElements,
    extendElement: Element.extend
  }
})()
Prototype._original_property = window.Sizzle;
/*!
 * Sizzle CSS Selector Engine - v1.0
 *  Copyright 2009, The Dojo Foundation
 *  Released under the MIT, BSD, and GPL Licenses.
 *  More information: http://sizzlejs.com/
 */
(function () {

  var chunker = /((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^[\]]*\]|['"][^'"]*['"]|[^[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,
    done = 0,
    toString = Object.prototype.toString,
    hasDuplicate = false,
    baseHasDuplicate = true;

  [0, 0].sort(function () {
    baseHasDuplicate = false
    return 0
  })

  var Sizzle = function (selector, context, results, seed) {
    results = results || []
    var origContext = context = context || document

    if (context.nodeType !== 1 && context.nodeType !== 9) {
      return []
    }

    if (!selector || typeof selector !== "string") {
      return results
    }

    var parts = [], m, set, checkSet, check, mode, extra, prune = true, contextXML = isXML(context),
      soFar = selector

    while ((chunker.exec(""), m = chunker.exec(soFar)) !== null) {
      soFar = m[3]

      parts.push(m[1])

      if (m[2]) {
        extra = m[3]
        break
      }
    }

    if (parts.length > 1 && origPOS.exec(selector)) {
      if (parts.length === 2 && Expr.relative[parts[0]]) {
        set = posProcess(parts[0] + parts[1], context)
      } else {
        set = Expr.relative[parts[0]] ?
          [context] :
          Sizzle(parts.shift(), context)

        while (parts.length) {
          selector = parts.shift()

          if (Expr.relative[selector])
            selector += parts.shift()

          set = posProcess(selector, set)
        }
      }
    } else {
      if (!seed && parts.length > 1 && context.nodeType === 9 && !contextXML &&
        Expr.match.ID.test(parts[0]) && !Expr.match.ID.test(parts[parts.length - 1])) {
        var ret = Sizzle.find(parts.shift(), context, contextXML)
        context = ret.expr ? Sizzle.filter(ret.expr, ret.set)[0] : ret.set[0]
      }

      if (context) {
        var ret = seed ?
          {expr: parts.pop(), set: makeArray(seed)} :
          Sizzle.find(parts.pop(), parts.length === 1 && (parts[0] === "~" || parts[0] === "+") && context.parentNode ? context.parentNode : context, contextXML)
        set = ret.expr ? Sizzle.filter(ret.expr, ret.set) : ret.set

        if (parts.length > 0) {
          checkSet = makeArray(set)
        } else {
          prune = false
        }

        while (parts.length) {
          var cur = parts.pop(), pop = cur

          if (!Expr.relative[cur]) {
            cur = ""
          } else {
            pop = parts.pop()
          }

          if (pop == null) {
            pop = context
          }

          Expr.relative[cur](checkSet, pop, contextXML)
        }
      } else {
        checkSet = parts = []
      }
    }

    if (!checkSet) {
      checkSet = set
    }

    if (!checkSet) {
      throw "Syntax error, unrecognized expression: " + (cur || selector)
    }

    if (toString.call(checkSet) === "[object Array]") {
      if (!prune) {
        results.push.apply(results, checkSet)
      } else if (context && context.nodeType === 1) {
        for (var i = 0; checkSet[i] != null; i++) {
          if (checkSet[i] && (checkSet[i] === true || checkSet[i].nodeType === 1 && contains(context, checkSet[i]))) {
            results.push(set[i])
          }
        }
      } else {
        for (var i = 0; checkSet[i] != null; i++) {
          if (checkSet[i] && checkSet[i].nodeType === 1) {
            results.push(set[i])
          }
        }
      }
    } else {
      makeArray(checkSet, results)
    }

    if (extra) {
      Sizzle(extra, origContext, results, seed)
      Sizzle.uniqueSort(results)
    }

    return results
  }

  Sizzle.uniqueSort = function (results) {
    if (sortOrder) {
      hasDuplicate = baseHasDuplicate
      results.sort(sortOrder)

      if (hasDuplicate) {
        for (var i = 1; i < results.length; i++) {
          if (results[i] === results[i - 1]) {
            results.splice(i--, 1)
          }
        }
      }
    }

    return results
  }

  Sizzle.matches = function (expr, set) {
    return Sizzle(expr, null, null, set)
  }

  Sizzle.find = function (expr, context, isXML) {
    var set, match

    if (!expr) {
      return []
    }

    for (var i = 0, l = Expr.order.length; i < l; i++) {
      var type = Expr.order[i], match

      if ((match = Expr.leftMatch[type].exec(expr))) {
        var left = match[1]
        match.splice(1, 1)

        if (left.substr(left.length - 1) !== "\\") {
          match[1] = (match[1] || "").replace(/\\/g, "")
          set = Expr.find[type](match, context, isXML)
          if (set != null) {
            expr = expr.replace(Expr.match[type], "")
            break
          }
        }
      }
    }

    if (!set) {
      set = context.getElementsByTagName("*")
    }

    return {set: set, expr: expr}
  }

  Sizzle.filter = function (expr, set, inplace, not) {
    var old = expr, result = [], curLoop = set, match, anyFound,
      isXMLFilter = set && set[0] && isXML(set[0])

    while (expr && set.length) {
      for (var type in Expr.filter) {
        if ((match = Expr.match[type].exec(expr)) != null) {
          var filter = Expr.filter[type], found, item
          anyFound = false

          if (curLoop == result) {
            result = []
          }

          if (Expr.preFilter[type]) {
            match = Expr.preFilter[type](match, curLoop, inplace, result, not, isXMLFilter)

            if (!match) {
              anyFound = found = true
            } else if (match === true) {
              continue
            }
          }

          if (match) {
            for (var i = 0; (item = curLoop[i]) != null; i++) {
              if (item) {
                found = filter(item, match, i, curLoop)
                var pass = not ^ !!found

                if (inplace && found != null) {
                  if (pass) {
                    anyFound = true
                  } else {
                    curLoop[i] = false
                  }
                } else if (pass) {
                  result.push(item)
                  anyFound = true
                }
              }
            }
          }

          if (found !== undefined) {
            if (!inplace) {
              curLoop = result
            }

            expr = expr.replace(Expr.match[type], "")

            if (!anyFound) {
              return []
            }

            break
          }
        }
      }

      if (expr == old) {
        if (anyFound == null) {
          throw "Syntax error, unrecognized expression: " + expr
        } else {
          break
        }
      }

      old = expr
    }

    return curLoop
  }

  var Expr = Sizzle.selectors = {
    order: ["ID", "NAME", "TAG"],
    match: {
      ID: /#((?:[\w\u00c0-\uFFFF-]|\\.)+)/,
      CLASS: /\.((?:[\w\u00c0-\uFFFF-]|\\.)+)/,
      NAME: /\[name=['"]*((?:[\w\u00c0-\uFFFF-]|\\.)+)['"]*\]/,
      ATTR: /\[\s*((?:[\w\u00c0-\uFFFF-]|\\.)+)\s*(?:(\S?=)\s*(['"]*)(.*?)\3|)\s*\]/,
      TAG: /^((?:[\w\u00c0-\uFFFF\*-]|\\.)+)/,
      CHILD: /:(only|nth|last|first)-child(?:\((even|odd|[\dn+-]*)\))?/,
      POS: /:(nth|eq|gt|lt|first|last|even|odd)(?:\((\d*)\))?(?=[^-]|$)/,
      PSEUDO: /:((?:[\w\u00c0-\uFFFF-]|\\.)+)(?:\((['"]*)((?:\([^\)]+\)|[^\2\(\)]*)+)\2\))?/
    },
    leftMatch: {},
    attrMap: {
      "class": "className",
      "for": "htmlFor"
    },
    attrHandle: {
      href: function (elem) {
        return elem.getAttribute("href")
      }
    },
    relative: {
      "+": function (checkSet, part, isXML) {
        var isPartStr = typeof part === "string",
          isTag = isPartStr && !/\W/.test(part),
          isPartStrNotTag = isPartStr && !isTag

        if (isTag && !isXML) {
          part = part.toUpperCase()
        }

        for (var i = 0, l = checkSet.length, elem; i < l; i++) {
          if ((elem = checkSet[i])) {
            while ((elem = elem.previousSibling) && elem.nodeType !== 1) {
            }

            checkSet[i] = isPartStrNotTag || elem && elem.nodeName === part ?
              elem || false :
              elem === part
          }
        }

        if (isPartStrNotTag) {
          Sizzle.filter(part, checkSet, true)
        }
      },
      ">": function (checkSet, part, isXML) {
        var isPartStr = typeof part === "string"

        if (isPartStr && !/\W/.test(part)) {
          part = isXML ? part : part.toUpperCase()

          for (var i = 0, l = checkSet.length; i < l; i++) {
            var elem = checkSet[i]
            if (elem) {
              var parent = elem.parentNode
              checkSet[i] = parent.nodeName === part ? parent : false
            }
          }
        } else {
          for (var i = 0, l = checkSet.length; i < l; i++) {
            var elem = checkSet[i]
            if (elem) {
              checkSet[i] = isPartStr ?
                elem.parentNode :
                elem.parentNode === part
            }
          }

          if (isPartStr) {
            Sizzle.filter(part, checkSet, true)
          }
        }
      },
      "": function (checkSet, part, isXML) {
        var doneName = done++, checkFn = dirCheck

        if (!/\W/.test(part)) {
          var nodeCheck = part = isXML ? part : part.toUpperCase()
          checkFn = dirNodeCheck
        }

        checkFn("parentNode", part, doneName, checkSet, nodeCheck, isXML)
      },
      "~": function (checkSet, part, isXML) {
        var doneName = done++, checkFn = dirCheck

        if (typeof part === "string" && !/\W/.test(part)) {
          var nodeCheck = part = isXML ? part : part.toUpperCase()
          checkFn = dirNodeCheck
        }

        checkFn("previousSibling", part, doneName, checkSet, nodeCheck, isXML)
      }
    },
    find: {
      ID: function (match, context, isXML) {
        if (typeof context.getElementById !== "undefined" && !isXML) {
          var m = context.getElementById(match[1])
          return m ? [m] : []
        }
      },
      NAME: function (match, context, isXML) {
        if (typeof context.getElementsByName !== "undefined") {
          var ret = [], results = context.getElementsByName(match[1])

          for (var i = 0, l = results.length; i < l; i++) {
            if (results[i].getAttribute("name") === match[1]) {
              ret.push(results[i])
            }
          }

          return ret.length === 0 ? null : ret
        }
      },
      TAG: function (match, context) {
        return context.getElementsByTagName(match[1])
      }
    },
    preFilter: {
      CLASS: function (match, curLoop, inplace, result, not, isXML) {
        match = " " + match[1].replace(/\\/g, "") + " "

        if (isXML) {
          return match
        }

        for (var i = 0, elem; (elem = curLoop[i]) != null; i++) {
          if (elem) {
            if (not ^ (elem.className && (" " + elem.className + " ").indexOf(match) >= 0)) {
              if (!inplace)
                result.push(elem)
            } else if (inplace) {
              curLoop[i] = false
            }
          }
        }

        return false
      },
      ID: function (match) {
        return match[1].replace(/\\/g, "")
      },
      TAG: function (match, curLoop) {
        for (var i = 0; curLoop[i] === false; i++) {
        }
        return curLoop[i] && isXML(curLoop[i]) ? match[1] : match[1].toUpperCase()
      },
      CHILD: function (match) {
        if (match[1] == "nth") {
          var test = /(-?)(\d*)n((?:\+|-)?\d*)/.exec(
            match[2] == "even" && "2n" || match[2] == "odd" && "2n+1" ||
            !/\D/.test(match[2]) && "0n+" + match[2] || match[2])

          match[2] = (test[1] + (test[2] || 1)) - 0
          match[3] = test[3] - 0
        }

        match[0] = done++

        return match
      },
      ATTR: function (match, curLoop, inplace, result, not, isXML) {
        var name = match[1].replace(/\\/g, "")

        if (!isXML && Expr.attrMap[name]) {
          match[1] = Expr.attrMap[name]
        }

        if (match[2] === "~=") {
          match[4] = " " + match[4] + " "
        }

        return match
      },
      PSEUDO: function (match, curLoop, inplace, result, not) {
        if (match[1] === "not") {
          if ((chunker.exec(match[3]) || "").length > 1 || /^\w/.test(match[3])) {
            match[3] = Sizzle(match[3], null, null, curLoop)
          } else {
            var ret = Sizzle.filter(match[3], curLoop, inplace, true ^ not)
            if (!inplace) {
              result.push.apply(result, ret)
            }
            return false
          }
        } else if (Expr.match.POS.test(match[0]) || Expr.match.CHILD.test(match[0])) {
          return true
        }

        return match
      },
      POS: function (match) {
        match.unshift(true)
        return match
      }
    },
    filters: {
      enabled: function (elem) {
        return elem.disabled === false && elem.type !== "hidden"
      },
      disabled: function (elem) {
        return elem.disabled === true
      },
      checked: function (elem) {
        return elem.checked === true
      },
      selected: function (elem) {
        elem.parentNode.selectedIndex
        return elem.selected === true
      },
      parent: function (elem) {
        return !!elem.firstChild
      },
      empty: function (elem) {
        return !elem.firstChild
      },
      has: function (elem, i, match) {
        return !!Sizzle(match[3], elem).length
      },
      header: function (elem) {
        return /h\d/i.test(elem.nodeName)
      },
      text: function (elem) {
        return "text" === elem.type
      },
      radio: function (elem) {
        return "radio" === elem.type
      },
      checkbox: function (elem) {
        return "checkbox" === elem.type
      },
      file: function (elem) {
        return "file" === elem.type
      },
      password: function (elem) {
        return "password" === elem.type
      },
      submit: function (elem) {
        return "submit" === elem.type
      },
      image: function (elem) {
        return "image" === elem.type
      },
      reset: function (elem) {
        return "reset" === elem.type
      },
      button: function (elem) {
        return "button" === elem.type || elem.nodeName.toUpperCase() === "BUTTON"
      },
      input: function (elem) {
        return /input|select|textarea|button/i.test(elem.nodeName)
      }
    },
    setFilters: {
      first: function (elem, i) {
        return i === 0
      },
      last: function (elem, i, match, array) {
        return i === array.length - 1
      },
      even: function (elem, i) {
        return i % 2 === 0
      },
      odd: function (elem, i) {
        return i % 2 === 1
      },
      lt: function (elem, i, match) {
        return i < match[3] - 0
      },
      gt: function (elem, i, match) {
        return i > match[3] - 0
      },
      nth: function (elem, i, match) {
        return match[3] - 0 == i
      },
      eq: function (elem, i, match) {
        return match[3] - 0 == i
      }
    },
    filter: {
      PSEUDO: function (elem, match, i, array) {
        var name = match[1], filter = Expr.filters[name]

        if (filter) {
          return filter(elem, i, match, array)
        } else if (name === "contains") {
          return (elem.textContent || elem.innerText || "").indexOf(match[3]) >= 0
        } else if (name === "not") {
          var not = match[3]

          for (var i = 0, l = not.length; i < l; i++) {
            if (not[i] === elem) {
              return false
            }
          }

          return true
        }
      },
      CHILD: function (elem, match) {
        var type = match[1], node = elem
        switch (type) {
          case 'only':
          case 'first':
            while ((node = node.previousSibling)) {
              if (node.nodeType === 1) return false
            }
            if (type == 'first') return true
            node = elem
          case 'last':
            while ((node = node.nextSibling)) {
              if (node.nodeType === 1) return false
            }
            return true
          case 'nth':
            var first = match[2], last = match[3]

            if (first == 1 && last == 0) {
              return true
            }

            var doneName = match[0],
              parent = elem.parentNode

            if (parent && (parent.sizcache !== doneName || !elem.nodeIndex)) {
              var count = 0
              for (node = parent.firstChild; node; node = node.nextSibling) {
                if (node.nodeType === 1) {
                  node.nodeIndex = ++count
                }
              }
              parent.sizcache = doneName
            }

            var diff = elem.nodeIndex - last
            if (first == 0) {
              return diff == 0
            } else {
              return (diff % first == 0 && diff / first >= 0)
            }
        }
      },
      ID: function (elem, match) {
        return elem.nodeType === 1 && elem.getAttribute("id") === match
      },
      TAG: function (elem, match) {
        return (match === "*" && elem.nodeType === 1) || elem.nodeName === match
      },
      CLASS: function (elem, match) {
        return (" " + (elem.className || elem.getAttribute("class")) + " ")
          .indexOf(match) > -1
      },
      ATTR: function (elem, match) {
        var name = match[1],
          result = Expr.attrHandle[name] ?
            Expr.attrHandle[name](elem) :
            elem[name] != null ?
              elem[name] :
              elem.getAttribute(name),
          value = result + "",
          type = match[2],
          check = match[4]

        return result == null ?
          type === "!=" :
          type === "=" ?
            value === check :
            type === "*=" ?
              value.indexOf(check) >= 0 :
              type === "~=" ?
                (" " + value + " ").indexOf(check) >= 0 :
                !check ?
                  value && result !== false :
                  type === "!=" ?
                    value != check :
                    type === "^=" ?
                      value.indexOf(check) === 0 :
                      type === "$=" ?
                        value.substr(value.length - check.length) === check :
                        type === "|=" ?
                          value === check || value.substr(0, check.length + 1) === check + "-" :
                          false
      },
      POS: function (elem, match, i, array) {
        var name = match[2], filter = Expr.setFilters[name]

        if (filter) {
          return filter(elem, i, match, array)
        }
      }
    }
  }

  var origPOS = Expr.match.POS

  for (var type in Expr.match) {
    Expr.match[type] = new RegExp(Expr.match[type].source + /(?![^\[]*\])(?![^\(]*\))/.source)
    Expr.leftMatch[type] = new RegExp(/(^(?:.|\r|\n)*?)/.source + Expr.match[type].source)
  }

  var makeArray = function (array, results) {
    array = Array.prototype.slice.call(array, 0)

    if (results) {
      results.push.apply(results, array)
      return results
    }

    return array
  }

  try {
    Array.prototype.slice.call(document.documentElement.childNodes, 0)

  } catch (e) {
    makeArray = function (array, results) {
      var ret = results || []

      if (toString.call(array) === "[object Array]") {
        Array.prototype.push.apply(ret, array)
      } else {
        if (typeof array.length === "number") {
          for (var i = 0, l = array.length; i < l; i++) {
            ret.push(array[i])
          }
        } else {
          for (var i = 0; array[i]; i++) {
            ret.push(array[i])
          }
        }
      }

      return ret
    }
  }

  var sortOrder

  if (document.documentElement.compareDocumentPosition) {
    sortOrder = function (a, b) {
      if (!a.compareDocumentPosition || !b.compareDocumentPosition) {
        if (a == b) {
          hasDuplicate = true
        }
        return 0
      }

      var ret = a.compareDocumentPosition(b) & 4 ? -1 : a === b ? 0 : 1
      if (ret === 0) {
        hasDuplicate = true
      }
      return ret
    }
  } else if ("sourceIndex" in document.documentElement) {
    sortOrder = function (a, b) {
      if (!a.sourceIndex || !b.sourceIndex) {
        if (a == b) {
          hasDuplicate = true
        }
        return 0
      }

      var ret = a.sourceIndex - b.sourceIndex
      if (ret === 0) {
        hasDuplicate = true
      }
      return ret
    }
  } else if (document.createRange) {
    sortOrder = function (a, b) {
      if (!a.ownerDocument || !b.ownerDocument) {
        if (a == b) {
          hasDuplicate = true
        }
        return 0
      }

      var aRange = a.ownerDocument.createRange(), bRange = b.ownerDocument.createRange()
      aRange.setStart(a, 0)
      aRange.setEnd(a, 0)
      bRange.setStart(b, 0)
      bRange.setEnd(b, 0)
      var ret = aRange.compareBoundaryPoints(Range.START_TO_END, bRange)
      if (ret === 0) {
        hasDuplicate = true
      }
      return ret
    }
  }

  (function () {
    var form = document.createElement("div"),
      id = "script" + (new Date).getTime()
    form.innerHTML = "<a name='" + id + "'/>"

    var root = document.documentElement
    root.insertBefore(form, root.firstChild)

    if (!!document.getElementById(id)) {
      Expr.find.ID = function (match, context, isXML) {
        if (typeof context.getElementById !== "undefined" && !isXML) {
          var m = context.getElementById(match[1])
          return m ? m.id === match[1] || typeof m.getAttributeNode !== "undefined" && m.getAttributeNode("id").nodeValue === match[1] ? [m] : undefined : []
        }
      }

      Expr.filter.ID = function (elem, match) {
        var node = typeof elem.getAttributeNode !== "undefined" && elem.getAttributeNode("id")
        return elem.nodeType === 1 && node && node.nodeValue === match
      }
    }

    root.removeChild(form)
    root = form = null // release memory in IE
  })();

  (function () {

    var div = document.createElement("div")
    div.appendChild(document.createComment(""))

    if (div.getElementsByTagName("*").length > 0) {
      Expr.find.TAG = function (match, context) {
        var results = context.getElementsByTagName(match[1])

        if (match[1] === "*") {
          var tmp = []

          for (var i = 0; results[i]; i++) {
            if (results[i].nodeType === 1) {
              tmp.push(results[i])
            }
          }

          results = tmp
        }

        return results
      }
    }

    div.innerHTML = "<a href='#'></a>"
    if (div.firstChild && typeof div.firstChild.getAttribute !== "undefined" &&
      div.firstChild.getAttribute("href") !== "#") {
      Expr.attrHandle.href = function (elem) {
        return elem.getAttribute("href", 2)
      }
    }

    div = null // release memory in IE
  })()

  if (document.querySelectorAll) (function () {
    var oldSizzle = Sizzle, div = document.createElement("div")
    div.innerHTML = "<p class='TEST'></p>"

    if (div.querySelectorAll && div.querySelectorAll(".TEST").length === 0) {
      return
    }

    Sizzle = function (query, context, extra, seed) {
      context = context || document

      if (!seed && context.nodeType === 9 && !isXML(context)) {
        try {
          return makeArray(context.querySelectorAll(query), extra)
        } catch (e) {
        }
      }

      return oldSizzle(query, context, extra, seed)
    }

    for (var prop in oldSizzle) {
      Sizzle[prop] = oldSizzle[prop]
    }

    div = null // release memory in IE
  })()

  if (document.getElementsByClassName && document.documentElement.getElementsByClassName) (function () {
    var div = document.createElement("div")
    div.innerHTML = "<div class='test e'></div><div class='test'></div>"

    if (div.getElementsByClassName("e").length === 0)
      return

    div.lastChild.className = "e"

    if (div.getElementsByClassName("e").length === 1)
      return

    Expr.order.splice(1, 0, "CLASS")
    Expr.find.CLASS = function (match, context, isXML) {
      if (typeof context.getElementsByClassName !== "undefined" && !isXML) {
        return context.getElementsByClassName(match[1])
      }
    }

    div = null // release memory in IE
  })()

  function dirNodeCheck(dir, cur, doneName, checkSet, nodeCheck, isXML) {
    var sibDir = dir == "previousSibling" && !isXML
    for (var i = 0, l = checkSet.length; i < l; i++) {
      var elem = checkSet[i]
      if (elem) {
        if (sibDir && elem.nodeType === 1) {
          elem.sizcache = doneName
          elem.sizset = i
        }
        elem = elem[dir]
        var match = false

        while (elem) {
          if (elem.sizcache === doneName) {
            match = checkSet[elem.sizset]
            break
          }

          if (elem.nodeType === 1 && !isXML) {
            elem.sizcache = doneName
            elem.sizset = i
          }

          if (elem.nodeName === cur) {
            match = elem
            break
          }

          elem = elem[dir]
        }

        checkSet[i] = match
      }
    }
  }

  function dirCheck(dir, cur, doneName, checkSet, nodeCheck, isXML) {
    var sibDir = dir == "previousSibling" && !isXML
    for (var i = 0, l = checkSet.length; i < l; i++) {
      var elem = checkSet[i]
      if (elem) {
        if (sibDir && elem.nodeType === 1) {
          elem.sizcache = doneName
          elem.sizset = i
        }
        elem = elem[dir]
        var match = false

        while (elem) {
          if (elem.sizcache === doneName) {
            match = checkSet[elem.sizset]
            break
          }

          if (elem.nodeType === 1) {
            if (!isXML) {
              elem.sizcache = doneName
              elem.sizset = i
            }
            if (typeof cur !== "string") {
              if (elem === cur) {
                match = true
                break
              }

            } else if (Sizzle.filter(cur, [elem]).length > 0) {
              match = elem
              break
            }
          }

          elem = elem[dir]
        }

        checkSet[i] = match
      }
    }
  }

  var contains = document.compareDocumentPosition ? function (a, b) {
    return a.compareDocumentPosition(b) & 16
  } : function (a, b) {
    return a !== b && (a.contains ? a.contains(b) : true)
  }

  var isXML = function (elem) {
    return elem.nodeType === 9 && elem.documentElement.nodeName !== "HTML" ||
      !!elem.ownerDocument && elem.ownerDocument.documentElement.nodeName !== "HTML"
  }

  var posProcess = function (selector, context) {
    var tmpSet = [], later = "", match,
      root = context.nodeType ? [context] : context

    while ((match = Expr.match.PSEUDO.exec(selector))) {
      later += match[0]
      selector = selector.replace(Expr.match.PSEUDO, "")
    }

    selector = Expr.relative[selector] ? selector + "*" : selector

    for (var i = 0, l = root.length; i < l; i++) {
      Sizzle(selector, root[i], tmpSet)
    }

    return Sizzle.filter(later, tmpSet)
  }


  window.Sizzle = Sizzle

})()

;(function (engine) {
  var extendElements = Prototype.Selector.extendElements

  function select(selector, scope) {
    return extendElements(engine(selector, scope || document))
  }

  function match(element, selector) {
    return engine.matches(selector, [element]).length == 1
  }

  Prototype.Selector.engine = engine
  Prototype.Selector.select = select
  Prototype.Selector.match = match
})(Sizzle)

window.Sizzle = Prototype._original_property
delete Prototype._original_property

var Form = {
  reset: function (form) {
    form = $(form)
    form.reset()
    return form
  },

  serializeElements: function (elements, options) {
    if (typeof options != 'object') options = {hash: !!options}
    else if (Object.isUndefined(options.hash)) options.hash = true
    var key, value, submitted = false, submit = options.submit, accumulator, initial

    if (options.hash) {
      initial = {}
      accumulator = function (result, key, value) {
        if (key in result) {
          if (!Object.isArray(result[key])) result[key] = [result[key]]
          result[key].push(value)
        } else result[key] = value
        return result
      }
    } else {
      initial = ''
      accumulator = function (result, key, value) {
        return result + (result ? '&' : '') + encodeURIComponent(key) + '=' + encodeURIComponent(value)
      }
    }

    return elements.inject(initial, function (result, element) {
      if (!element.disabled && element.name) {
        key = element.name
        value = $(element).getValue()
        if (value != null && element.type != 'file' && (element.type != 'submit' || (!submitted &&
          submit !== false && (!submit || key == submit) && (submitted = true)))) {
          result = accumulator(result, key, value)
        }
      }
      return result
    })
  }
}

Form.Methods = {
  serialize: function (form, options) {
    return Form.serializeElements(Form.getElements(form), options)
  },

  getElements: function (form) {
    var elements = $(form).getElementsByTagName('*'),
      element,
      arr = [],
      serializers = Form.Element.Serializers
    for (var i = 0; element = elements[i]; i++) {
      arr.push(element)
    }
    return arr.inject([], function (elements, child) {
      if (serializers[child.tagName.toLowerCase()])
        elements.push(Element.extend(child))
      return elements
    })
  },

  getInputs: function (form, typeName, name) {
    form = $(form)
    var inputs = form.getElementsByTagName('input')

    if (!typeName && !name) return $A(inputs).map(Element.extend)

    for (var i = 0, matchingInputs = [], length = inputs.length; i < length; i++) {
      var input = inputs[i]
      if ((typeName && input.type != typeName) || (name && input.name != name))
        continue
      matchingInputs.push(Element.extend(input))
    }

    return matchingInputs
  },

  disable: function (form) {
    form = $(form)
    Form.getElements(form).invoke('disable')
    return form
  },

  enable: function (form) {
    form = $(form)
    Form.getElements(form).invoke('enable')
    return form
  },

  findFirstElement: function (form) {
    var elements = $(form).getElements().findAll(function (element) {
      return 'hidden' != element.type && !element.disabled
    })
    var firstByIndex = elements.findAll(function (element) {
      return element.hasAttribute('tabIndex') && element.tabIndex >= 0
    }).sortBy(function (element) {
      return element.tabIndex
    }).first()

    return firstByIndex ? firstByIndex : elements.find(function (element) {
      return /^(?:input|select|textarea)$/i.test(element.tagName)
    })
  },

  focusFirstElement: function (form) {
    form = $(form)
    var element = form.findFirstElement()
    if (element) element.activate()
    return form
  },

  request: function (form, options) {
    form = $(form), options = Object.clone(options || {})

    var params = options.parameters, action = form.readAttribute('action') || ''
    if (action.blank()) action = window.location.href
    options.parameters = form.serialize(true)

    if (params) {
      if (Object.isString(params)) params = params.toQueryParams()
      Object.extend(options.parameters, params)
    }

    if (form.hasAttribute('method') && !options.method)
      options.method = form.method

    return new Ajax.Request(action, options)
  }
}

/*--------------------------------------------------------------------------*/


Form.Element = {
  focus: function (element) {
    $(element).focus()
    return element
  },

  select: function (element) {
    $(element).select()
    return element
  }
}

Form.Element.Methods = {

  serialize: function (element) {
    element = $(element)
    if (!element.disabled && element.name) {
      var value = element.getValue()
      if (value != undefined) {
        var pair = {}
        pair[element.name] = value
        return Object.toQueryString(pair)
      }
    }
    return ''
  },

  getValue: function (element) {
    element = $(element)
    var method = element.tagName.toLowerCase()
    return Form.Element.Serializers[method](element)
  },

  setValue: function (element, value) {
    element = $(element)
    var method = element.tagName.toLowerCase()
    Form.Element.Serializers[method](element, value)
    return element
  },

  clear: function (element) {
    $(element).value = ''
    return element
  },

  present: function (element) {
    return $(element).value != ''
  },

  activate: function (element) {
    element = $(element)
    try {
      element.focus()
      if (element.select && (element.tagName.toLowerCase() != 'input' ||
        !(/^(?:button|reset|submit)$/i.test(element.type))))
        element.select()
    } catch (e) {
    }
    return element
  },

  disable: function (element) {
    element = $(element)
    element.disabled = true
    return element
  },

  enable: function (element) {
    element = $(element)
    element.disabled = false
    return element
  }
}

/*--------------------------------------------------------------------------*/

var Field = Form.Element

var $F = Form.Element.Methods.getValue

/*--------------------------------------------------------------------------*/

Form.Element.Serializers = (function () {
  function input(element, value) {
    switch (element.type.toLowerCase()) {
      case 'checkbox':
      case 'radio':
        return inputSelector(element, value)
      default:
        return valueSelector(element, value)
    }
  }

  function inputSelector(element, value) {
    if (Object.isUndefined(value))
      return element.checked ? element.value : null
    else element.checked = !!value
  }

  function valueSelector(element, value) {
    if (Object.isUndefined(value)) return element.value
    else element.value = value
  }

  function select(element, value) {
    if (Object.isUndefined(value))
      return (element.type === 'select-one' ? selectOne : selectMany)(element)

    var opt, currentValue, single = !Object.isArray(value)
    for (var i = 0, length = element.length; i < length; i++) {
      opt = element.options[i]
      currentValue = this.optionValue(opt)
      if (single) {
        if (currentValue == value) {
          opt.selected = true
          return
        }
      } else opt.selected = value.include(currentValue)
    }
  }

  function selectOne(element) {
    var index = element.selectedIndex
    return index >= 0 ? optionValue(element.options[index]) : null
  }

  function selectMany(element) {
    var values, length = element.length
    if (!length) return null

    for (var i = 0, values = []; i < length; i++) {
      var opt = element.options[i]
      if (opt.selected) values.push(optionValue(opt))
    }
    return values
  }

  function optionValue(opt) {
    return Element.hasAttribute(opt, 'value') ? opt.value : opt.text
  }

  return {
    input: input,
    inputSelector: inputSelector,
    textarea: valueSelector,
    select: select,
    selectOne: selectOne,
    selectMany: selectMany,
    optionValue: optionValue,
    button: valueSelector
  }
})()

/*--------------------------------------------------------------------------*/


Abstract.TimedObserver = Class.create(PeriodicalExecuter, {
  initialize: function ($super, element, frequency, callback) {
    $super(callback, frequency)
    this.element = $(element)
    this.lastValue = this.getValue()
  },

  execute: function () {
    var value = this.getValue()
    if (Object.isString(this.lastValue) && Object.isString(value) ?
      this.lastValue != value : String(this.lastValue) != String(value)) {
      this.callback(this.element, value)
      this.lastValue = value
    }
  }
})

Form.Element.Observer = Class.create(Abstract.TimedObserver, {
  getValue: function () {
    return Form.Element.getValue(this.element)
  }
})

Form.Observer = Class.create(Abstract.TimedObserver, {
  getValue: function () {
    return Form.serialize(this.element)
  }
})

/*--------------------------------------------------------------------------*/

Abstract.EventObserver = Class.create({
  initialize: function (element, callback) {
    this.element = $(element)
    this.callback = callback

    this.lastValue = this.getValue()
    if (this.element.tagName.toLowerCase() == 'form')
      this.registerFormCallbacks()
    else
      this.registerCallback(this.element)
  },

  onElementEvent: function () {
    var value = this.getValue()
    if (this.lastValue != value) {
      this.callback(this.element, value)
      this.lastValue = value
    }
  },

  registerFormCallbacks: function () {
    Form.getElements(this.element).each(this.registerCallback, this)
  },

  registerCallback: function (element) {
    if (element.type) {
      switch (element.type.toLowerCase()) {
        case 'checkbox':
        case 'radio':
          Event.observe(element, 'click', this.onElementEvent.bind(this))
          break
        default:
          Event.observe(element, 'change', this.onElementEvent.bind(this))
          break
      }
    }
  }
})

Form.Element.EventObserver = Class.create(Abstract.EventObserver, {
  getValue: function () {
    return Form.Element.getValue(this.element)
  }
})

Form.EventObserver = Class.create(Abstract.EventObserver, {
  getValue: function () {
    return Form.serialize(this.element)
  }
});
(function () {

  var Event = {
    KEY_BACKSPACE: 8,
    KEY_TAB: 9,
    KEY_RETURN: 13,
    KEY_ESC: 27,
    KEY_LEFT: 37,
    KEY_UP: 38,
    KEY_RIGHT: 39,
    KEY_DOWN: 40,
    KEY_DELETE: 46,
    KEY_HOME: 36,
    KEY_END: 35,
    KEY_PAGEUP: 33,
    KEY_PAGEDOWN: 34,
    KEY_INSERT: 45,

    cache: {}
  }

  var docEl = document.documentElement
  var MOUSEENTER_MOUSELEAVE_EVENTS_SUPPORTED = 'onmouseenter' in docEl
    && 'onmouseleave' in docEl


  var isIELegacyEvent = function (event) {
    return false
  }

  if (window.attachEvent) {
    if (window.addEventListener) {
      isIELegacyEvent = function (event) {
        return !(event instanceof window.Event)
      }
    } else {
      isIELegacyEvent = function (event) {
        return true
      }
    }
  }

  var _isButton

  function _isButtonForDOMEvents(event, code) {
    return event.which ? (event.which === code + 1) : (event.button === code)
  }

  var legacyButtonMap = {0: 1, 1: 4, 2: 2}

  function _isButtonForLegacyEvents(event, code) {
    return event.button === legacyButtonMap[code]
  }

  function _isButtonForWebKit(event, code) {
    switch (code) {
      case 0:
        return event.which == 1 && !event.metaKey
      case 1:
        return event.which == 2 || (event.which == 1 && event.metaKey)
      case 2:
        return event.which == 3
      default:
        return false
    }
  }

  if (window.attachEvent) {
    if (!window.addEventListener) {
      _isButton = _isButtonForLegacyEvents
    } else {
      _isButton = function (event, code) {
        return isIELegacyEvent(event) ? _isButtonForLegacyEvents(event, code) :
          _isButtonForDOMEvents(event, code)
      }
    }
  } else if (Prototype.Browser.WebKit) {
    _isButton = _isButtonForWebKit
  } else {
    _isButton = _isButtonForDOMEvents
  }

  function isLeftClick(event) {
    return _isButton(event, 0)
  }

  function isMiddleClick(event) {
    return _isButton(event, 1)
  }

  function isRightClick(event) {
    return _isButton(event, 2)
  }

  function element(event) {
    event = Event.extend(event)

    var node = event.target, type = event.type,
      currentTarget = event.currentTarget

    if (currentTarget && currentTarget.tagName) {
      if (type === 'load' || type === 'error' ||
        (type === 'click' && currentTarget.tagName.toLowerCase() === 'input'
          && currentTarget.type === 'radio'))
        node = currentTarget
    }

    if (node.nodeType == Node.TEXT_NODE)
      node = node.parentNode

    return Element.extend(node)
  }

  function findElement(event, expression) {
    var element = Event.element(event)

    if (!expression) return element
    while (element) {
      if (Object.isElement(element) && Prototype.Selector.match(element, expression)) {
        return Element.extend(element)
      }
      element = element.parentNode
    }
  }

  function pointer(event) {
    return {x: pointerX(event), y: pointerY(event)}
  }

  function pointerX(event) {
    var docElement = document.documentElement,
      body = document.body || {scrollLeft: 0}

    return event.pageX || (event.clientX +
      (docElement.scrollLeft || body.scrollLeft) -
      (docElement.clientLeft || 0))
  }

  function pointerY(event) {
    var docElement = document.documentElement,
      body = document.body || {scrollTop: 0}

    return event.pageY || (event.clientY +
      (docElement.scrollTop || body.scrollTop) -
      (docElement.clientTop || 0))
  }


  function stop(event) {
    Event.extend(event)
    event.preventDefault()
    event.stopPropagation()

    event.stopped = true
  }


  Event.Methods = {
    isLeftClick: isLeftClick,
    isMiddleClick: isMiddleClick,
    isRightClick: isRightClick,

    element: element,
    findElement: findElement,

    pointer: pointer,
    pointerX: pointerX,
    pointerY: pointerY,

    stop: stop
  }

  var methods = Object.keys(Event.Methods).inject({}, function (m, name) {
    m[name] = Event.Methods[name].methodize()
    return m
  })

  if (window.attachEvent) {
    function _relatedTarget(event) {
      var element
      switch (event.type) {
        case 'mouseover':
        case 'mouseenter':
          element = event.fromElement
          break
        case 'mouseout':
        case 'mouseleave':
          element = event.toElement
          break
        default:
          return null
      }
      return Element.extend(element)
    }

    var additionalMethods = {
      stopPropagation: function () {
        this.cancelBubble = true
      },
      preventDefault: function () {
        this.returnValue = false
      },
      inspect: function () {
        return '[object Event]'
      }
    }

    Event.extend = function (event, element) {
      if (!event) return false

      if (!isIELegacyEvent(event)) return event

      if (event._extendedByPrototype) return event
      event._extendedByPrototype = Prototype.emptyFunction

      var pointer = Event.pointer(event)

      Object.extend(event, {
        target: event.srcElement || element,
        relatedTarget: _relatedTarget(event),
        pageX: pointer.x,
        pageY: pointer.y
      })

      Object.extend(event, methods)
      Object.extend(event, additionalMethods)

      return event
    }
  } else {
    Event.extend = Prototype.K
  }

  if (window.addEventListener) {
    Event.prototype = window.Event.prototype || document.createEvent('HTMLEvents').__proto__
    Object.extend(Event.prototype, methods)
  }

  function _createResponder(element, eventName, handler) {
    var registry = Element.retrieve(element, 'prototype_event_registry')

    if (Object.isUndefined(registry)) {
      CACHE.push(element)
      registry = Element.retrieve(element, 'prototype_event_registry', $H())
    }

    var respondersForEvent = registry.get(eventName)
    if (Object.isUndefined(respondersForEvent)) {
      respondersForEvent = []
      registry.set(eventName, respondersForEvent)
    }

    if (respondersForEvent.pluck('handler').include(handler)) return false

    var responder
    if (eventName.include(":")) {
      responder = function (event) {
        if (Object.isUndefined(event.eventName))
          return false

        if (event.eventName !== eventName)
          return false

        Event.extend(event, element)
        handler.call(element, event)
      }
    } else {
      if (!MOUSEENTER_MOUSELEAVE_EVENTS_SUPPORTED &&
        (eventName === "mouseenter" || eventName === "mouseleave")) {
        if (eventName === "mouseenter" || eventName === "mouseleave") {
          responder = function (event) {
            Event.extend(event, element)

            var parent = event.relatedTarget
            while (parent && parent !== element) {
              try {
                parent = parent.parentNode
              } catch (e) {
                parent = element
              }
            }

            if (parent === element) return

            handler.call(element, event)
          }
        }
      } else {
        responder = function (event) {
          Event.extend(event, element)
          handler.call(element, event)
        }
      }
    }

    responder.handler = handler
    respondersForEvent.push(responder)
    return responder
  }

  function _destroyCache() {
    for (var i = 0, length = CACHE.length; i < length; i++) {
      Event.stopObserving(CACHE[i])
      CACHE[i] = null
    }
  }

  var CACHE = []

  if (Prototype.Browser.IE)
    window.attachEvent('onunload', _destroyCache)

  if (Prototype.Browser.WebKit)
    window.addEventListener('unload', Prototype.emptyFunction, false)


  var _getDOMEventName = Prototype.K,
    translations = {mouseenter: "mouseover", mouseleave: "mouseout"}

  if (!MOUSEENTER_MOUSELEAVE_EVENTS_SUPPORTED) {
    _getDOMEventName = function (eventName) {
      return (translations[eventName] || eventName)
    }
  }

  function observe(element, eventName, handler) {
    element = $(element)

    var responder = _createResponder(element, eventName, handler)

    if (!responder) return element

    if (eventName.include(':')) {
      if (element.addEventListener)
        element.addEventListener("dataavailable", responder, false)
      else {
        element.attachEvent("ondataavailable", responder)
        element.attachEvent("onlosecapture", responder)
      }
    } else {
      var actualEventName = _getDOMEventName(eventName)

      if (element.addEventListener)
        element.addEventListener(actualEventName, responder, false)
      else
        element.attachEvent("on" + actualEventName, responder)
    }

    return element
  }

  function stopObserving(element, eventName, handler) {
    element = $(element)

    var registry = Element.retrieve(element, 'prototype_event_registry')
    if (!registry) return element

    if (!eventName) {
      registry.each(function (pair) {
        var eventName = pair.key
        stopObserving(element, eventName)
      })
      return element
    }

    var responders = registry.get(eventName)
    if (!responders) return element

    if (!handler) {
      responders.each(function (r) {
        stopObserving(element, eventName, r.handler)
      })
      return element
    }

    var i = responders.length, responder
    while (i--) {
      if (responders[i].handler === handler) {
        responder = responders[i]
        break
      }
    }
    if (!responder) return element

    if (eventName.include(':')) {
      if (element.removeEventListener)
        element.removeEventListener("dataavailable", responder, false)
      else {
        element.detachEvent("ondataavailable", responder)
        element.detachEvent("onlosecapture", responder)
      }
    } else {
      var actualEventName = _getDOMEventName(eventName)
      if (element.removeEventListener)
        element.removeEventListener(actualEventName, responder, false)
      else
        element.detachEvent('on' + actualEventName, responder)
    }

    registry.set(eventName, responders.without(responder))

    return element
  }

  function fire(element, eventName, memo, bubble) {
    element = $(element)

    if (Object.isUndefined(bubble))
      bubble = true

    if (element == document && document.createEvent && !element.dispatchEvent)
      element = document.documentElement

    var event
    if (document.createEvent) {
      event = document.createEvent('HTMLEvents')
      event.initEvent('dataavailable', bubble, true)
    } else {
      event = document.createEventObject()
      event.eventType = bubble ? 'ondataavailable' : 'onlosecapture'
    }

    event.eventName = eventName
    event.memo = memo || {}

    if (document.createEvent)
      element.dispatchEvent(event)
    else
      element.fireEvent(event.eventType, event)

    return Event.extend(event)
  }

  Event.Handler = Class.create({
    initialize: function (element, eventName, selector, callback) {
      this.element = $(element)
      this.eventName = eventName
      this.selector = selector
      this.callback = callback
      this.handler = this.handleEvent.bind(this)
    },

    start: function () {
      Event.observe(this.element, this.eventName, this.handler)
      return this
    },

    stop: function () {
      Event.stopObserving(this.element, this.eventName, this.handler)
      return this
    },

    handleEvent: function (event) {
      var element = Event.findElement(event, this.selector)
      if (element) this.callback.call(this.element, event, element)
    }
  })

  function on(element, eventName, selector, callback) {
    element = $(element)
    if (Object.isFunction(selector) && Object.isUndefined(callback)) {
      callback = selector, selector = null
    }

    return new Event.Handler(element, eventName, selector, callback).start()
  }

  Object.extend(Event, Event.Methods)

  Object.extend(Event, {
    fire: fire,
    observe: observe,
    stopObserving: stopObserving,
    on: on
  })

  Element.addMethods({
    fire: fire,

    observe: observe,

    stopObserving: stopObserving,

    on: on
  })

  Object.extend(document, {
    fire: fire.methodize(),

    observe: observe.methodize(),

    stopObserving: stopObserving.methodize(),

    on: on.methodize(),

    loaded: false
  })

  if (window.Event) Object.extend(window.Event, Event)
  else window.Event = Event
})();

(function () {
  /* Support for the DOMContentLoaded event is based on work by Dan Webb,
     Matthias Miller, Dean Edwards, John Resig, and Diego Perini. */

  var timer

  function fireContentLoadedEvent() {
    if (document.loaded) return
    if (timer) window.clearTimeout(timer)
    document.loaded = true
    document.fire('dom:loaded')
  }

  function checkReadyState() {
    if (document.readyState === 'complete') {
      document.stopObserving('readystatechange', checkReadyState)
      fireContentLoadedEvent()
    }
  }

  function pollDoScroll() {
    try {
      document.documentElement.doScroll('left')
    } catch (e) {
      timer = pollDoScroll.defer()
      return
    }
    fireContentLoadedEvent()
  }

  if (document.addEventListener) {
    document.addEventListener('DOMContentLoaded', fireContentLoadedEvent, false)
  } else {
    document.observe('readystatechange', checkReadyState)
    if (window == top)
      timer = pollDoScroll.defer()
  }

  Event.observe(window, 'load', fireContentLoadedEvent)
})()

Element.addMethods()

/*------------------------------- DEPRECATED -------------------------------*/

Hash.toQueryString = Object.toQueryString

var Toggle = {display: Element.toggle}

Element.Methods.childOf = Element.Methods.descendantOf

var Insertion = {
  Before: function (element, content) {
    return Element.insert(element, {before: content})
  },

  Top: function (element, content) {
    return Element.insert(element, {top: content})
  },

  Bottom: function (element, content) {
    return Element.insert(element, {bottom: content})
  },

  After: function (element, content) {
    return Element.insert(element, {after: content})
  }
}

var $continue = new Error('"throw $continue" is deprecated, use "return" instead')

var Position = {
  includeScrollOffsets: false,

  prepare: function () {
    this.deltaX = window.pageXOffset
      || document.documentElement.scrollLeft
      || document.body.scrollLeft
      || 0
    this.deltaY = window.pageYOffset
      || document.documentElement.scrollTop
      || document.body.scrollTop
      || 0
  },

  within: function (element, x, y) {
    if (this.includeScrollOffsets)
      return this.withinIncludingScrolloffsets(element, x, y)
    this.xcomp = x
    this.ycomp = y
    this.offset = Element.cumulativeOffset(element)

    return (y >= this.offset[1] &&
      y < this.offset[1] + element.offsetHeight &&
      x >= this.offset[0] &&
      x < this.offset[0] + element.offsetWidth)
  },

  withinIncludingScrolloffsets: function (element, x, y) {
    var offsetcache = Element.cumulativeScrollOffset(element)

    this.xcomp = x + offsetcache[0] - this.deltaX
    this.ycomp = y + offsetcache[1] - this.deltaY
    this.offset = Element.cumulativeOffset(element)

    return (this.ycomp >= this.offset[1] &&
      this.ycomp < this.offset[1] + element.offsetHeight &&
      this.xcomp >= this.offset[0] &&
      this.xcomp < this.offset[0] + element.offsetWidth)
  },

  overlap: function (mode, element) {
    if (!mode) return 0
    if (mode == 'vertical')
      return ((this.offset[1] + element.offsetHeight) - this.ycomp) /
        element.offsetHeight
    if (mode == 'horizontal')
      return ((this.offset[0] + element.offsetWidth) - this.xcomp) /
        element.offsetWidth
  },


  cumulativeOffset: Element.Methods.cumulativeOffset,

  positionedOffset: Element.Methods.positionedOffset,

  absolutize: function (element) {
    Position.prepare()
    return Element.absolutize(element)
  },

  relativize: function (element) {
    Position.prepare()
    return Element.relativize(element)
  },

  realOffset: Element.Methods.cumulativeScrollOffset,

  offsetParent: Element.Methods.getOffsetParent,

  page: Element.Methods.viewportOffset,

  clone: function (source, target, options) {
    options = options || {}
    return Element.clonePosition(target, source, options)
  }
}

/*--------------------------------------------------------------------------*/

if (!document.getElementsByClassName) document.getElementsByClassName = function (instanceMethods) {
  function iter(name) {
    return name.blank() ? null : "[contains(concat(' ', @class, ' '), ' " + name + " ')]"
  }

  instanceMethods.getElementsByClassName = Prototype.BrowserFeatures.XPath ?
    function (element, className) {
      className = className.toString().strip()
      var cond = /\s/.test(className) ? $w(className).map(iter).join('') : iter(className)
      return cond ? document._getElementsByXPath('.//*' + cond, element) : []
    } : function (element, className) {
      className = className.toString().strip()
      var elements = [], classNames = (/\s/.test(className) ? $w(className) : null)
      if (!classNames && !className) return elements

      var nodes = $(element).getElementsByTagName('*')
      className = ' ' + className + ' '

      for (var i = 0, child, cn; child = nodes[i]; i++) {
        if (child.className && (cn = ' ' + child.className + ' ') && (cn.include(className) ||
          (classNames && classNames.all(function (name) {
            return !name.toString().blank() && cn.include(' ' + name + ' ')
          }))))
          elements.push(Element.extend(child))
      }
      return elements
    }

  return function (className, parentElement) {
    return $(parentElement || document.body).getElementsByClassName(className)
  }
}(Element.Methods)

/*--------------------------------------------------------------------------*/

Element.ClassNames = Class.create()
Element.ClassNames.prototype = {
  initialize: function (element) {
    this.element = $(element)
  },

  _each: function (iterator) {
    this.element.className.split(/\s+/).select(function (name) {
      return name.length > 0
    })._each(iterator)
  },

  set: function (className) {
    this.element.className = className
  },

  add: function (classNameToAdd) {
    if (this.include(classNameToAdd)) return
    this.set($A(this).concat(classNameToAdd).join(' '))
  },

  remove: function (classNameToRemove) {
    if (!this.include(classNameToRemove)) return
    this.set($A(this).without(classNameToRemove).join(' '))
  },

  toString: function () {
    return $A(this).join(' ')
  }
}

Object.extend(Element.ClassNames.prototype, Enumerable);

/*--------------------------------------------------------------------------*/

(function () {
  window.Selector = Class.create({
    initialize: function (expression) {
      this.expression = expression.strip()
    },

    findElements: function (rootElement) {
      return Prototype.Selector.select(this.expression, rootElement)
    },

    match: function (element) {
      return Prototype.Selector.match(element, this.expression)
    },

    toString: function () {
      return this.expression
    },

    inspect: function () {
      return "#<Selector: " + this.expression + ">"
    }
  })

  Object.extend(Selector, {
    matchElements: function (elements, expression) {
      var match = Prototype.Selector.match,
        results = []

      for (var i = 0, length = elements.length; i < length; i++) {
        var element = elements[i]
        if (match(element, expression)) {
          results.push(Element.extend(element))
        }
      }
      return results
    },

    findElement: function (elements, expression, index) {
      index = index || 0
      var matchIndex = 0, element
      for (var i = 0, length = elements.length; i < length; i++) {
        element = elements[i]
        if (Prototype.Selector.match(element, expression) && index === matchIndex++) {
          return Element.extend(element)
        }
      }
    },

    findChildElements: function (element, expressions) {
      var selector = expressions.toArray().join(', ')
      return Prototype.Selector.select(selector, element || document)
    }
  })
})()



/* FILE: ccard.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
// Credit Card Validation Javascript
// copyright 12th May 2003, by Stephen Chapman, Felgall Pty Ltd

// You have permission to copy and use this javascript provided that
// the content of the script is not changed in any way.

function validateCreditCard(s) {
    // remove non-numerics
    var v = "0123456789";
    var w = "";
    for (i=0; i < s.length; i++) {
        x = s.charAt(i);
        if (v.indexOf(x,0) != -1)
        w += x;
    }
    // validate number
    j = w.length / 2;
    k = Math.floor(j);
    m = Math.ceil(j) - k;
    c = 0;
    for (i=0; i<k; i++) {
        a = w.charAt(i*2+m) * 2;
        c += a > 9 ? Math.floor(a/10 + a%10) : a;
    }
    for (i=0; i<k+m; i++) c += w.charAt(i*2+1-m) * 1;
    return (c%10 == 0);
}




/* FILE: validation.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/*
* Really easy field validation with Prototype
* http://tetlaw.id.au/view/javascript/really-easy-field-validation
* Andrew Tetlaw
* Version ******* (2007-01-05)
*
* Copyright (c) 2007 Andrew Tetlaw
* Permission is hereby granted, free of charge, to any person
* obtaining a copy of this software and associated documentation
* files (the "Software"), to deal in the Software without
* restriction, including without limitation the rights to use, copy,
* modify, merge, publish, distribute, sublicense, and/or sell copies
* of the Software, and to permit persons to whom the Software is
* furnished to do so, subject to the following conditions:
*
* The above copyright notice and this permission notice shall be
* included in all copies or substantial portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
* BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
* ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
* CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
* SOFTWARE.
*
*/
var Validator = Class.create();

Validator.prototype = {
    initialize : function(className, error, test, options) {
        if(typeof test == 'function'){
            this.options = $H(options);
            this._test = test;
        } else {
            this.options = $H(test);
            this._test = function(){return true};
        }
        this.error = error || 'Validation failed.';
        this.className = className;
    },
    test : function(v, elm) {
        return (this._test(v,elm) && this.options.all(function(p){
            return Validator.methods[p.key] ? Validator.methods[p.key](v,elm,p.value) : true;
        }));
    }
}
Validator.methods = {
    pattern : function(v,elm,opt) {return Validation.get('IsEmpty').test(v) || opt.test(v)},
    minLength : function(v,elm,opt) {return v.length >= opt},
    maxLength : function(v,elm,opt) {return v.length <= opt},
    min : function(v,elm,opt) {return v >= parseFloat(opt)},
    max : function(v,elm,opt) {return v <= parseFloat(opt)},
    notOneOf : function(v,elm,opt) {return $A(opt).all(function(value) {
        return v != value;
    })},
    oneOf : function(v,elm,opt) {return $A(opt).any(function(value) {
        return v == value;
    })},
    is : function(v,elm,opt) {return v == opt},
    isNot : function(v,elm,opt) {return v != opt},
    equalToField : function(v,elm,opt) {return v == $F(opt)},
    notEqualToField : function(v,elm,opt) {return v != $F(opt)},
    include : function(v,elm,opt) {return $A(opt).all(function(value) {
        return Validation.get(value).test(v,elm);
    })}
}

var Validation = Class.create();
Validation.defaultOptions = {
    onSubmit : true,
    stopOnFirst : false,
    immediate : false,
    focusOnError : true,
    useTitles : false,
    addClassNameToContainer: false,
    containerClassName: '.input-box',
    onFormValidate : function(result, form) {},
    onElementValidate : function(result, elm) {}
};

Validation.prototype = {
    initialize : function(form, options){
        this.form = $(form);
        if (!this.form) {
            return;
        }
        this.options = Object.extend({
            onSubmit : Validation.defaultOptions.onSubmit,
            stopOnFirst : Validation.defaultOptions.stopOnFirst,
            immediate : Validation.defaultOptions.immediate,
            focusOnError : Validation.defaultOptions.focusOnError,
            useTitles : Validation.defaultOptions.useTitles,
            onFormValidate : Validation.defaultOptions.onFormValidate,
            onElementValidate : Validation.defaultOptions.onElementValidate
        }, options || {});
        if(this.options.onSubmit) Event.observe(this.form,'submit',this.onSubmit.bind(this),false);
        if(this.options.immediate) {
            Form.getElements(this.form).each(function(input) { // Thanks Mike!
                if (input.tagName.toLowerCase() == 'select') {
                    Event.observe(input, 'blur', this.onChange.bindAsEventListener(this));
                }
                if (input.type.toLowerCase() == 'radio' || input.type.toLowerCase() == 'checkbox') {
                    Event.observe(input, 'click', this.onChange.bindAsEventListener(this));
                } else {
                    Event.observe(input, 'change', this.onChange.bindAsEventListener(this));
                }
            }, this);
        }
    },
    onChange : function (ev) {
        Validation.isOnChange = true;
        Validation.validate(Event.element(ev),{
                useTitle : this.options.useTitles,
                onElementValidate : this.options.onElementValidate
        });
        Validation.isOnChange = false;
    },
    onSubmit :  function(ev){
        if(!this.validate()) Event.stop(ev);
    },
    validate : function() {
        var result = false;
        var useTitles = this.options.useTitles;
        var callback = this.options.onElementValidate;
        try {
            if(this.options.stopOnFirst) {
                result = Form.getElements(this.form).all(function(elm) {
                    if (elm.hasClassName('local-validation') && !this.isElementInForm(elm, this.form)) {
                        return true;
                    }
                    return Validation.validate(elm,{useTitle : useTitles, onElementValidate : callback});
                }, this);
            } else {
                result = Form.getElements(this.form).collect(function(elm) {
                    if (elm.hasClassName('local-validation') && !this.isElementInForm(elm, this.form)) {
                        return true;
                    }
                    return Validation.validate(elm,{useTitle : useTitles, onElementValidate : callback});
                }, this).all();
            }
        } catch (e) {
        }
        if(!result && this.options.focusOnError) {
            try{
                Form.getElements(this.form).findAll(function(elm){return $(elm).hasClassName('validation-failed')}).first().focus()
            }
            catch(e){
            }
        }
        this.options.onFormValidate(result, this.form);
        return result;
    },
    reset : function() {
        Form.getElements(this.form).each(Validation.reset);
    },
    isElementInForm : function(elm, form) {
        var domForm = elm.up('form');
        if (domForm == form) {
            return true;
        }
        return false;
    }
}

Object.extend(Validation, {
    validate : function(elm, options){
        options = Object.extend({
            useTitle : false,
            onElementValidate : function(result, elm) {}
        }, options || {});
        elm = $(elm);

        var cn = $w(elm.className);
        return result = cn.all(function(value) {
            var test = Validation.test(value,elm,options.useTitle);
            options.onElementValidate(test, elm);
            return test;
        });
    },
    insertAdvice : function(elm, advice){
        var container = $(elm).up('.field-row');
        if(container){
            Element.insert(container, {after: advice});
        } else if (elm.up('td.value')) {
            elm.up('td.value').insert({bottom: advice});
        } else if (elm.advaiceContainer && $(elm.advaiceContainer)) {
            $(elm.advaiceContainer).update(advice);
        }
        else {
            switch (elm.type.toLowerCase()) {
                case 'checkbox':
                case 'radio':
                    var p = elm.parentNode;
                    if(p) {
                        Element.insert(p, {'bottom': advice});
                    } else {
                        Element.insert(elm, {'after': advice});
                    }
                    break;
                default:
                    Element.insert(elm, {'after': advice});
            }
        }
    },
    showAdvice : function(elm, advice, adviceName){
        if(!elm.advices){
            elm.advices = new Hash();
        }
        else{
            elm.advices.each(function(pair){
                if (!advice || pair.value.id != advice.id) {
                    // hide non-current advice after delay
                    this.hideAdvice(elm, pair.value);
                }
            }.bind(this));
        }
        elm.advices.set(adviceName, advice);
        if(typeof Effect == 'undefined') {
            advice.style.display = 'block';
        } else {
            if(!advice._adviceAbsolutize) {
                new Effect.Appear(advice, {duration : 1 });
            } else {
                Position.absolutize(advice);
                advice.show();
                advice.setStyle({
                    'top':advice._adviceTop,
                    'left': advice._adviceLeft,
                    'width': advice._adviceWidth,
                    'z-index': 1000
                });
                advice.addClassName('advice-absolute');
            }
        }
    },
    hideAdvice : function(elm, advice){
        if (advice != null) {
            new Effect.Fade(advice, {duration : 1, afterFinishInternal : function() {advice.hide();}});
        }
    },
    updateCallback : function(elm, status) {
        if (typeof elm.callbackFunction != 'undefined') {
            eval(elm.callbackFunction+'(\''+elm.id+'\',\''+status+'\')');
        }
    },
    ajaxError : function(elm, errorMsg) {
        var name = 'validate-ajax';
        var advice = Validation.getAdvice(name, elm);
        if (advice == null) {
            advice = this.createAdvice(name, elm, false, errorMsg);
        }
        this.showAdvice(elm, advice, 'validate-ajax');
        this.updateCallback(elm, 'failed');

        elm.addClassName('validation-failed');
        elm.addClassName('validate-ajax');
        if (Validation.defaultOptions.addClassNameToContainer && Validation.defaultOptions.containerClassName != '') {
            var container = elm.up(Validation.defaultOptions.containerClassName);
            if (container && this.allowContainerClassName(elm)) {
                container.removeClassName('validation-passed');
                container.addClassName('validation-error');
            }
        }
    },
    allowContainerClassName: function (elm) {
        if (elm.type == 'radio' || elm.type == 'checkbox') {
            return elm.hasClassName('change-container-classname');
        }

        return true;
    },
    test : function(name, elm, useTitle) {
        var v = Validation.get(name);
        var prop = '__advice'+name.camelize();
        try {
        if(Validation.isVisible(elm) && !v.test($F(elm), elm)) {
            //if(!elm[prop]) {
                var advice = Validation.getAdvice(name, elm);
                if (advice == null) {
                    advice = this.createAdvice(name, elm, useTitle);
                }
                this.showAdvice(elm, advice, name);
                this.updateCallback(elm, 'failed');
            //}
            elm[prop] = 1;
            if (!elm.advaiceContainer) {
                elm.removeClassName('validation-passed');
                elm.addClassName('validation-failed');
            }

           if (Validation.defaultOptions.addClassNameToContainer && Validation.defaultOptions.containerClassName != '') {
                var container = elm.up(Validation.defaultOptions.containerClassName);
                if (container && this.allowContainerClassName(elm)) {
                    container.removeClassName('validation-passed');
                    container.addClassName('validation-error');
                }
            }
            return false;
        } else {
            var advice = Validation.getAdvice(name, elm);
            this.hideAdvice(elm, advice);
            this.updateCallback(elm, 'passed');
            elm[prop] = '';
            elm.removeClassName('validation-failed');
            elm.addClassName('validation-passed');
            if (Validation.defaultOptions.addClassNameToContainer && Validation.defaultOptions.containerClassName != '') {
                var container = elm.up(Validation.defaultOptions.containerClassName);
                if (container && !container.down('.validation-failed') && this.allowContainerClassName(elm)) {
                    if (!Validation.get('IsEmpty').test(elm.value) || !this.isVisible(elm)) {
                        container.addClassName('validation-passed');
                    } else {
                        container.removeClassName('validation-passed');
                    }
                    container.removeClassName('validation-error');
                }
            }
            return true;
        }
        } catch(e) {
            throw(e)
        }
    },
    isVisible : function(elm) {
        while(elm.tagName != 'BODY') {
            if(!$(elm).visible()) return false;
            elm = elm.parentNode;
        }
        return true;
    },
    getAdvice : function(name, elm) {
        return $('advice-' + name + '-' + Validation.getElmID(elm)) || $('advice-' + Validation.getElmID(elm));
    },
    createAdvice : function(name, elm, useTitle, customError) {
        var v = Validation.get(name);
        var errorMsg = useTitle ? ((elm && elm.title) ? elm.title : v.error) : v.error;
        if (customError) {
            errorMsg = customError;
        }
        try {
            if (Translator){
                errorMsg = Translator.translate(errorMsg);
            }
        }
        catch(e){}

        advice = '<div class="validation-advice" id="advice-' + name + '-' + Validation.getElmID(elm) +'" style="display:none">' + errorMsg + '</div>'


        Validation.insertAdvice(elm, advice);
        advice = Validation.getAdvice(name, elm);
        if($(elm).hasClassName('absolute-advice')) {
            var dimensions = $(elm).getDimensions();
            var originalPosition = Position.cumulativeOffset(elm);

            advice._adviceTop = (originalPosition[1] + dimensions.height) + 'px';
            advice._adviceLeft = (originalPosition[0])  + 'px';
            advice._adviceWidth = (dimensions.width)  + 'px';
            advice._adviceAbsolutize = true;
        }
        return advice;
    },
    getElmID : function(elm) {
        return elm.id ? elm.id : elm.name;
    },
    reset : function(elm) {
        elm = $(elm);
        var cn = $w(elm.className);
        cn.each(function(value) {
            var prop = '__advice'+value.camelize();
            if(elm[prop]) {
                var advice = Validation.getAdvice(value, elm);
                if (advice) {
                    advice.hide();
                }
                elm[prop] = '';
            }
            elm.removeClassName('validation-failed');
            elm.removeClassName('validation-passed');
            if (Validation.defaultOptions.addClassNameToContainer && Validation.defaultOptions.containerClassName != '') {
                var container = elm.up(Validation.defaultOptions.containerClassName);
                if (container) {
                    container.removeClassName('validation-passed');
                    container.removeClassName('validation-error');
                }
            }
        });
    },
    add : function(className, error, test, options) {
        var nv = {};
        nv[className] = new Validator(className, error, test, options);
        Object.extend(Validation.methods, nv);
    },
    addAllThese : function(validators) {
        var nv = {};
        $A(validators).each(function(value) {
                nv[value[0]] = new Validator(value[0], value[1], value[2], (value.length > 3 ? value[3] : {}));
            });
        Object.extend(Validation.methods, nv);
    },
    get : function(name) {
        return  Validation.methods[name] ? Validation.methods[name] : Validation.methods['_LikeNoIDIEverSaw_'];
    },
    methods : {
        '_LikeNoIDIEverSaw_' : new Validator('_LikeNoIDIEverSaw_','',{})
    }
});

Validation.add('IsEmpty', '', function(v) {
    return  (v == '' || (v == null) || (v.length == 0) || /^\s+$/.test(v));
});

Validation.addAllThese([
    ['validate-no-html-tags', 'HTML tags are not allowed', function(v) {
				return !/<(\/)?\w+/.test(v);
			}],
	['validate-select', 'Please select an option.', function(v) {
                return ((v != "none") && (v != null) && (v.length != 0));
            }],
    ['required-entry', 'This is a required field.', function(v) {
                return !Validation.get('IsEmpty').test(v);
            }],
    ['validate-number', 'Please enter a valid number in this field.', function(v) {
                return Validation.get('IsEmpty').test(v)
                    || (!isNaN(parseNumber(v)) && /^\s*-?\d*(\.\d*)?\s*$/.test(v));
            }],
    ['validate-number-range', 'The value is not within the specified range.', function(v, elm) {
                if (Validation.get('IsEmpty').test(v)) {
                    return true;
                }

                var numValue = parseNumber(v);
                if (isNaN(numValue)) {
                    return false;
                }

                var reRange = /^number-range-(-?[\d.,]+)?-(-?[\d.,]+)?$/,
                    result = true;

                $w(elm.className).each(function(name) {
                    var m = reRange.exec(name);
                    if (m) {
                        result = result
                            && (m[1] == null || m[1] == '' || numValue >= parseNumber(m[1]))
                            && (m[2] == null || m[2] == '' || numValue <= parseNumber(m[2]));
                    }
                });

                return result;
            }],
    ['validate-digits', 'Please use numbers only in this field. Please avoid spaces or other characters such as dots or commas.', function(v) {
                return Validation.get('IsEmpty').test(v) ||  !/[^\d]/.test(v);
            }],
    ['validate-digits-range', 'The value is not within the specified range.', function(v, elm) {
                if (Validation.get('IsEmpty').test(v)) {
                    return true;
                }

                var numValue = parseNumber(v);
                if (isNaN(numValue)) {
                    return false;
                }

                var reRange = /^digits-range-(-?\d+)?-(-?\d+)?$/,
                    result = true;

                $w(elm.className).each(function(name) {
                    var m = reRange.exec(name);
                    if (m) {
                        result = result
                            && (m[1] == null || m[1] == '' || numValue >= parseNumber(m[1]))
                            && (m[2] == null || m[2] == '' || numValue <= parseNumber(m[2]));
                    }
                });

                return result;
            }],
    ['validate-alpha', 'Please use letters only (a-z or A-Z) in this field.', function (v) {
                return Validation.get('IsEmpty').test(v) ||  /^[a-zA-Z]+$/.test(v)
            }],
    ['validate-code', 'Please use only letters (a-z), numbers (0-9) or underscore(_) in this field, first character should be a letter.', function (v) {
                return Validation.get('IsEmpty').test(v) ||  /^[a-z]+[a-z0-9_]+$/.test(v)
            }],
    ['validate-code-event', 'Please do not use "event" for an attribute code.', function (v) {
        return Validation.get('IsEmpty').test(v) || !/^(event)$/.test(v)
            }],
    ['validate-alphanum', 'Please use only letters (a-z or A-Z) or numbers (0-9) only in this field. No spaces or other characters are allowed.', function(v) {
                return Validation.get('IsEmpty').test(v) || /^[a-zA-Z0-9]+$/.test(v)
            }],
    ['validate-alphanum-with-spaces', 'Please use only letters (a-z or A-Z), numbers (0-9) or spaces only in this field.', function(v) {
                    return Validation.get('IsEmpty').test(v) || /^[a-zA-Z0-9 ]+$/.test(v)
            }],
    ['validate-street', 'Please use only letters (a-z or A-Z) or numbers (0-9) or spaces and # only in this field.', function(v) {
                return Validation.get('IsEmpty').test(v) ||  /^[ \w]{3,}([A-Za-z]\.)?([ \w]*\#\d+)?(\r\n| )[ \w]{3,}/.test(v)
            }],
    ['validate-phoneStrict', 'Please enter a valid phone number. For example (************* or ************.', function(v) {
                return Validation.get('IsEmpty').test(v) || /^(\()?\d{3}(\))?(-|\s)?\d{3}(-|\s)\d{4}$/.test(v);
            }],
    ['validate-phoneLax', 'Please enter a valid phone number. For example (************* or ************.', function(v) {
                return Validation.get('IsEmpty').test(v) || /^((\d[-. ]?)?((\(\d{3}\))|\d{3}))?[-. ]?\d{3}[-. ]?\d{4}$/.test(v);
            }],
    ['validate-fax', 'Please enter a valid fax number. For example (************* or ************.', function(v) {
                return Validation.get('IsEmpty').test(v) || /^(\()?\d{3}(\))?(-|\s)?\d{3}(-|\s)\d{4}$/.test(v);
            }],
    ['validate-date', 'Please enter a valid date.', function(v) {
                var test = new Date(v);
                return Validation.get('IsEmpty').test(v) || !isNaN(test);
            }],
    ['validate-date-range', 'The From Date value should be less than or equal to the To Date value.', function(v, elm) {
            var m = /\bdate-range-(\w+)-(\w+)\b/.exec(elm.className);
            if (!m || m[2] == 'to' || Validation.get('IsEmpty').test(v)) {
                return true;
            }

            var currentYear = new Date().getFullYear() + '';
            var normalizedTime = function(v) {
                v = v.split(/[.\/]/);
                if (v[2] && v[2].length < 4) {
                    v[2] = currentYear.substr(0, v[2].length) + v[2];
                }
                return new Date(v.join('/')).getTime();
            };

            var dependentElements = Element.select(elm.form, '.validate-date-range.date-range-' + m[1] + '-to');
            return !dependentElements.length || Validation.get('IsEmpty').test(dependentElements[0].value)
                || normalizedTime(v) <= normalizedTime(dependentElements[0].value);
        }],
    ['validate-email', 'Please enter a valid email address. <NAME_EMAIL>.', function (v) {
                //return Validation.get('IsEmpty').test(v) || /\w{1,}[@][\w\-]{1,}([.]([\w\-]{1,})){1,3}$/.test(v)
                //return Validation.get('IsEmpty').test(v) || /^[\!\#$%\*/?|\^\{\}`~&\'\+\-=_a-z0-9][\!\#$%\*/?|\^\{\}`~&\'\+\-=_a-z0-9\.]{1,30}[\!\#$%\*/?|\^\{\}`~&\'\+\-=_a-z0-9]@([a-z0-9_-]{1,30}\.){1,5}[a-z]{2,4}$/i.test(v)
                return Validation.get('IsEmpty').test(v) || /^([a-z0-9,!\#\$%&'\*\+\/=\?\^_`\{\|\}~-]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z0-9,!\#\$%&'\*\+\/=\?\^_`\{\|\}~-]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*@([a-z0-9-]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z0-9-]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*\.(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]){2,})$/i.test(v)
            }],
    ['validate-emailSender', 'Please use only visible characters and spaces.', function (v) {
                return Validation.get('IsEmpty').test(v) ||  /^[\S ]+$/.test(v)
                    }],
    ['validate-password', 'Please enter more characters or clean leading or trailing spaces.', function(v, elm) {
                var pass=v.strip(); /*strip leading and trailing spaces*/
                var reMin = new RegExp(/^min-pass-length-[0-9]+$/);
                var minLength = 7;
                $w(elm.className).each(function(name, index) {
                    if (name.match(reMin)) {
                        minLength = name.split('-')[3];
                    }
                });
                return (!(v.length > 0 && v.length < minLength) && v.length == pass.length);
            }],
    ['validate-admin-password', 'Please enter more characters. Password should contain both numeric and alphabetic characters.', function(v, elm) {
                var pass=v.strip();
                if (0 == pass.length) {
                    return true;
                }
                if (!(/[a-z]/i.test(v)) || !(/[0-9]/.test(v))) {
                    return false;
                }
                var reMin = new RegExp(/^min-admin-pass-length-[0-9]+$/);
                var minLength = 7;
                $w(elm.className).each(function(name, index) {
                    if (name.match(reMin)) {
                        minLength = name.split('-')[4];
                    }
                });
                return !(pass.length < minLength);
            }],
    ['validate-cpassword', 'Please make sure your passwords match.', function(v) {
                var conf = $('confirmation') ? $('confirmation') : $$('.validate-cpassword')[0];
                var pass = false;
                if ($('password')) {
                    pass = $('password');
                }
                var passwordElements = $$('.validate-password');
                for (var i = 0; i < passwordElements.size(); i++) {
                    var passwordElement = passwordElements[i];
                    if (passwordElement.up('form').id == conf.up('form').id) {
                        pass = passwordElement;
                    }
                }
                if ($$('.validate-admin-password').size()) {
                    pass = $$('.validate-admin-password')[0];
                }
                return (pass.value == conf.value);
            }],
    ['validate-both-passwords', 'Please make sure your passwords match.', function(v, input) {
                var dependentInput = $(input.form[input.name == 'password' ? 'confirmation' : 'password']),
                    isEqualValues  = input.value == dependentInput.value;

                if (isEqualValues && dependentInput.hasClassName('validation-failed')) {
                    Validation.test(this.className, dependentInput);
                }

                return dependentInput.value == '' || isEqualValues;
            }],
    ['validate-url', 'Please enter a valid URL. Protocol is required (http://, https:// or ftp://)', function (v) {
                v = (v || '').replace(/^\s+/, '').replace(/\s+$/, '');
                return Validation.get('IsEmpty').test(v) || /^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(v)
            }],
    ['validate-clean-url', 'Please enter a valid URL. For example http://www.example.com or www.example.com', function (v) {
                return Validation.get('IsEmpty').test(v) || /^(http|https|ftp):\/\/(([A-Z0-9][A-Z0-9_-]*)(\.[A-Z0-9][A-Z0-9_-]*)+.(com|org|net|dk|at|us|tv|info|uk|co.uk|biz|se)$)(:(\d+))?\/?/i.test(v) || /^(www)((\.[A-Z0-9][A-Z0-9_-]*)+.(com|org|net|dk|at|us|tv|info|uk|co.uk|biz|se)$)(:(\d+))?\/?/i.test(v)
            }],
    ['validate-identifier', 'Please enter a valid URL Key. For example "example-page", "example-page.html" or "anotherlevel/example-page".', function (v) {
                return Validation.get('IsEmpty').test(v) || /^[a-z0-9][a-z0-9_\/-]+(\.[a-z0-9_-]+)?$/.test(v)
            }],
    ['validate-xml-identifier', 'Please enter a valid XML-identifier. For example something_1, block5, id-4.', function (v) {
                return Validation.get('IsEmpty').test(v) || /^[A-Z][A-Z0-9_\/-]*$/i.test(v)
            }],
    ['validate-ssn', 'Please enter a valid social security number. For example ***********.', function(v) {
            return Validation.get('IsEmpty').test(v) || /^\d{3}-?\d{2}-?\d{4}$/.test(v);
            }],
    ['validate-zip', 'Please enter a valid zip code. For example 90602 or 90602-1234.', function(v) {
            return Validation.get('IsEmpty').test(v) || /(^\d{5}$)|(^\d{5}-\d{4}$)/.test(v);
            }],
    ['validate-zip-international', 'Please enter a valid zip code.', function(v) {
            //return Validation.get('IsEmpty').test(v) || /(^[A-z0-9]{2,10}([\s]{0,1}|[\-]{0,1})[A-z0-9]{2,10}$)/.test(v);
            return true;
            }],
    ['validate-date-au', 'Please use this date format: dd/mm/yyyy. For example 17/03/2006 for the 17th of March, 2006.', function(v) {
                if(Validation.get('IsEmpty').test(v)) return true;
                var regex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
                if(!regex.test(v)) return false;
                var d = new Date(v.replace(regex, '$2/$1/$3'));
                return ( parseInt(RegExp.$2, 10) == (1+d.getMonth()) ) &&
                            (parseInt(RegExp.$1, 10) == d.getDate()) &&
                            (parseInt(RegExp.$3, 10) == d.getFullYear() );
            }],
    ['validate-currency-dollar', 'Please enter a valid $ amount. For example $100.00.', function(v) {
                // [$]1[##][,###]+[.##]
                // [$]1###+[.##]
                // [$]0.##
                // [$].##
                return Validation.get('IsEmpty').test(v) ||  /^\$?\-?([1-9]{1}[0-9]{0,2}(\,[0-9]{3})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/.test(v)
            }],
    ['validate-one-required', 'Please select one of the above options.', function (v,elm) {
                var p = elm.parentNode;
                var options = p.getElementsByTagName('INPUT');
                return $A(options).any(function(elm) {
                    return $F(elm);
                });
            }],
    ['validate-one-required-by-name', 'Please select one of the options.', function (v,elm) {
                var inputs = $$('input[name="' + elm.name.replace(/([\\"])/g, '\\$1') + '"]');

                var error = 1;
                for(var i=0;i<inputs.length;i++) {
                    if((inputs[i].type == 'checkbox' || inputs[i].type == 'radio') && inputs[i].checked == true) {
                        error = 0;
                    }

                    if(Validation.isOnChange && (inputs[i].type == 'checkbox' || inputs[i].type == 'radio')) {
                        Validation.reset(inputs[i]);
                    }
                }

                if( error == 0 ) {
                    return true;
                } else {
                    return false;
                }
            }],
    ['validate-not-negative-number', 'Please enter a number 0 or greater in this field.', function(v) {
                if (Validation.get('IsEmpty').test(v)) {
                    return true;
                }
                v = parseNumber(v);
                return !isNaN(v) && v >= 0;
            }],
    ['validate-zero-or-greater', 'Please enter a number 0 or greater in this field.', function(v) {
            return Validation.get('validate-not-negative-number').test(v);
        }],
    ['validate-greater-than-zero', 'Please enter a number greater than 0 in this field.', function(v) {
            if (Validation.get('IsEmpty').test(v)) {
                return true;
            }
            v = parseNumber(v);
            return !isNaN(v) && v > 0;
        }],

    ['validate-special-price', 'The Special Price is active only when lower than the Actual Price.', function(v) {
        var priceInput = $('price');
        var priceType = $('price_type');
        var priceValue = parseFloat(v);

        // Passed on non-related validators conditions (to not change order of validation)
        if(
            !priceInput
            || Validation.get('IsEmpty').test(v)
            || !Validation.get('validate-number').test(v)
        ) {
            return true;
        }
        if(priceType) {
            return (priceType && priceValue <= 99.99);
        }
        return priceValue < parseFloat($F(priceInput));
    }],
    ['validate-state', 'Please select State/Province.', function(v) {
                return (v!=0 || v == '');
            }],
    ['validate-new-password', 'Please enter more characters or clean leading or trailing spaces.', function(v, elm) {
                if (!Validation.get('validate-password').test(v, elm)) return false;
                if (Validation.get('IsEmpty').test(v) && v != '') return false;
                return true;
            }],
    ['validate-cc-number', 'Please enter a valid credit card number.', function(v, elm) {
                // remove non-numerics
                var ccTypeContainer = $(elm.id.substr(0,elm.id.indexOf('_cc_number')) + '_cc_type');
                if (ccTypeContainer && typeof Validation.creditCartTypes.get(ccTypeContainer.value) != 'undefined'
                        && Validation.creditCartTypes.get(ccTypeContainer.value)[2] == false) {
                    if (!Validation.get('IsEmpty').test(v) && Validation.get('validate-digits').test(v)) {
                        return true;
                    } else {
                        return false;
                    }
                }
                return validateCreditCard(v);
            }],
    ['validate-cc-type', 'Credit card number does not match credit card type.', function(v, elm) {
                // remove credit card number delimiters such as "-" and space
                elm.value = removeDelimiters(elm.value);
                v         = removeDelimiters(v);

                var ccTypeContainer = $(elm.id.substr(0,elm.id.indexOf('_cc_number')) + '_cc_type');
                if (!ccTypeContainer) {
                    return true;
                }
                var ccType = ccTypeContainer.value;

                if (typeof Validation.creditCartTypes.get(ccType) == 'undefined') {
                    return false;
                }

                // Other card type or switch or solo card
                if (Validation.creditCartTypes.get(ccType)[0]==false) {
                    return true;
                }

                var validationFailure = false;
                Validation.creditCartTypes.each(function (pair) {
                    if (pair.key == ccType) {
                        if (pair.value[0] && !v.match(pair.value[0])) {
                            validationFailure = true;
                        }
                        throw $break;
                    }
                });

                if (validationFailure) {
                    return false;
                }

                if (ccTypeContainer.hasClassName('validation-failed') && Validation.isOnChange) {
                    Validation.validate(ccTypeContainer);
                }

                return true;
            }],
     ['validate-cc-type-select', 'Card type does not match credit card number.', function(v, elm) {
                var ccNumberContainer = $(elm.id.substr(0,elm.id.indexOf('_cc_type')) + '_cc_number');
                if (Validation.isOnChange && Validation.get('IsEmpty').test(ccNumberContainer.value)) {
                    return true;
                }
                if (Validation.get('validate-cc-type').test(ccNumberContainer.value, ccNumberContainer)) {
                    Validation.validate(ccNumberContainer);
                }
                return Validation.get('validate-cc-type').test(ccNumberContainer.value, ccNumberContainer);
            }],
     ['validate-cc-exp', 'Incorrect credit card expiration date.', function(v, elm) {
                var ccExpMonth   = v;
                var ccExpYear    = $(elm.id.substr(0,elm.id.indexOf('_expiration')) + '_expiration_yr').value;
                var currentTime  = new Date();
                var currentMonth = currentTime.getMonth() + 1;
                var currentYear  = currentTime.getFullYear();
                if (ccExpMonth < currentMonth && ccExpYear == currentYear) {
                    return false;
                }
                return true;
            }],
     ['validate-cc-cvn', 'Please enter a valid credit card verification number.', function(v, elm) {
                var ccTypeContainer = $(elm.id.substr(0,elm.id.indexOf('_cc_cid')) + '_cc_type');
                if (!ccTypeContainer) {
                    return true;
                }
                var ccType = ccTypeContainer.value;

                if (typeof Validation.creditCartTypes.get(ccType) == 'undefined') {
                    return false;
                }

                var re = Validation.creditCartTypes.get(ccType)[1];

                if (v.match(re)) {
                    return true;
                }

                return false;
            }],
     ['validate-ajax', '', function(v, elm) { return true; }],
     ['validate-data', 'Please use only letters (a-z or A-Z), numbers (0-9) or underscore(_) in this field, first character should be a letter.', function (v) {
                if(v != '' && v) {
                    return /^[A-Za-z]+[A-Za-z0-9_]+$/.test(v);
                }
                return true;
            }],
     ['validate-css-length', 'Please input a valid CSS-length. For example 100px or 77pt or 20em or .5ex or 50%.', function (v) {
                if (v != '' && v) {
                    return /^[0-9\.]+(px|pt|em|ex|%)?$/.test(v) && (!(/\..*\./.test(v))) && !(/\.$/.test(v));
                }
                return true;
            }],
     ['validate-length', 'Text length does not satisfy specified text range.', function (v, elm) {
                var reMax = new RegExp(/^maximum-length-[0-9]+$/);
                var reMin = new RegExp(/^minimum-length-[0-9]+$/);
                var result = true;
                $w(elm.className).each(function(name, index) {
                    if (name.match(reMax) && result) {
                       var length = name.split('-')[2];
                       result = (v.length <= length);
                    }
                    if (name.match(reMin) && result && !Validation.get('IsEmpty').test(v)) {
                        var length = name.split('-')[2];
                        result = (v.length >= length);
                    }
                });
                return result;
            }],
     ['validate-percents', 'Please enter a number lower than 100.', {max:100}],
     ['required-file', 'Please select a file', function(v, elm) {
         var result = !Validation.get('IsEmpty').test(v);
         if (result === false) {
             ovId = elm.id + '_value';
             if ($(ovId)) {
                 result = !Validation.get('IsEmpty').test($(ovId).value);
             }
         }
         return result;
     }],
     ['validate-cc-ukss', 'Please enter issue number or start date for switch/solo card type.', function(v,elm) {
         var endposition;

         if (elm.id.match(/(.)+_cc_issue$/)) {
             endposition = elm.id.indexOf('_cc_issue');
         } else if (elm.id.match(/(.)+_start_month$/)) {
             endposition = elm.id.indexOf('_start_month');
         } else {
             endposition = elm.id.indexOf('_start_year');
         }

         var prefix = elm.id.substr(0,endposition);

         var ccTypeContainer = $(prefix + '_cc_type');

         if (!ccTypeContainer) {
               return true;
         }
         var ccType = ccTypeContainer.value;

         if(['SS','SM','SO'].indexOf(ccType) == -1){
             return true;
         }

         $(prefix + '_cc_issue').advaiceContainer
           = $(prefix + '_start_month').advaiceContainer
           = $(prefix + '_start_year').advaiceContainer
           = $(prefix + '_cc_type_ss_div').down('ul li.adv-container');

         var ccIssue   =  $(prefix + '_cc_issue').value;
         var ccSMonth  =  $(prefix + '_start_month').value;
         var ccSYear   =  $(prefix + '_start_year').value;

         var ccStartDatePresent = (ccSMonth && ccSYear) ? true : false;

         if (!ccStartDatePresent && !ccIssue){
             return false;
         }
         return true;
     }]
]);

function removeDelimiters (v) {
    v = v.replace(/\s/g, '');
    v = v.replace(/\-/g, '');
    return v;
}

function parseNumber(v)
{
    if (typeof v != 'string') {
        return parseFloat(v);
    }

    var isDot  = v.indexOf('.');
    var isComa = v.indexOf(',');

    if (isDot != -1 && isComa != -1) {
        if (isComa > isDot) {
            v = v.replace('.', '').replace(',', '.');
        }
        else {
            v = v.replace(',', '');
        }
    }
    else if (isComa != -1) {
        v = v.replace(',', '.');
    }

    return parseFloat(v);
}

/**
 * Hash with credit card types which can be simply extended in payment modules
 * 0 - regexp for card number
 * 1 - regexp for cvn
 * 2 - check or not credit card number trough Luhn algorithm by
 *     function validateCreditCard which you can find above in this file
 */
Validation.creditCartTypes = $H({
//    'SS': [new RegExp('^((6759[0-9]{12})|(5018|5020|5038|6304|6759|6761|6763[0-9]{12,19})|(49[013][1356][0-9]{12})|(6333[0-9]{12})|(6334[0-4]\d{11})|(633110[0-9]{10})|(564182[0-9]{10}))([0-9]{2,3})?$'), new RegExp('^([0-9]{3}|[0-9]{4})?$'), true],
    'SO': [new RegExp('^(6334[5-9]([0-9]{11}|[0-9]{13,14}))|(6767([0-9]{12}|[0-9]{14,15}))$'), new RegExp('^([0-9]{3}|[0-9]{4})?$'), true],
    'VI': [new RegExp('^4[0-9]{12}([0-9]{3})?$'), new RegExp('^[0-9]{3}$'), true],
    'MC': [new RegExp('^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$'), new RegExp('^[0-9]{3}$'), true],
    'AE': [new RegExp('^3[47][0-9]{13}$'), new RegExp('^[0-9]{4}$'), true],
    'DI': [new RegExp('^(30[0-5][0-9]{13}|3095[0-9]{12}|35(2[8-9][0-9]{12}|[3-8][0-9]{13})|36[0-9]{12}|3[8-9][0-9]{14}|6011(0[0-9]{11}|[2-4][0-9]{11}|74[0-9]{10}|7[7-9][0-9]{10}|8[6-9][0-9]{10}|9[0-9]{11})|62(2(12[6-9][0-9]{10}|1[3-9][0-9]{11}|[2-8][0-9]{12}|9[0-1][0-9]{11}|92[0-5][0-9]{10})|[4-6][0-9]{13}|8[2-8][0-9]{12})|6(4[4-9][0-9]{13}|5[0-9]{14}))$'), new RegExp('^[0-9]{3}$'), true],
    'JCB': [new RegExp('^(30[0-5][0-9]{13}|3095[0-9]{12}|35(2[8-9][0-9]{12}|[3-8][0-9]{13})|36[0-9]{12}|3[8-9][0-9]{14}|6011(0[0-9]{11}|[2-4][0-9]{11}|74[0-9]{10}|7[7-9][0-9]{10}|8[6-9][0-9]{10}|9[0-9]{11})|62(2(12[6-9][0-9]{10}|1[3-9][0-9]{11}|[2-8][0-9]{12}|9[0-1][0-9]{11}|92[0-5][0-9]{10})|[4-6][0-9]{13}|8[2-8][0-9]{12})|6(4[4-9][0-9]{13}|5[0-9]{14}))$'), new RegExp('^[0-9]{3,4}$'), true],
    'DICL': [new RegExp('^(30[0-5][0-9]{13}|3095[0-9]{12}|35(2[8-9][0-9]{12}|[3-8][0-9]{13})|36[0-9]{12}|3[8-9][0-9]{14}|6011(0[0-9]{11}|[2-4][0-9]{11}|74[0-9]{10}|7[7-9][0-9]{10}|8[6-9][0-9]{10}|9[0-9]{11})|62(2(12[6-9][0-9]{10}|1[3-9][0-9]{11}|[2-8][0-9]{12}|9[0-1][0-9]{11}|92[0-5][0-9]{10})|[4-6][0-9]{13}|8[2-8][0-9]{12})|6(4[4-9][0-9]{13}|5[0-9]{14}))$'), new RegExp('^[0-9]{3}$'), true],
    'SM': [new RegExp('(^(5[0678])[0-9]{11,18}$)|(^(6[^05])[0-9]{11,18}$)|(^(601)[^1][0-9]{9,16}$)|(^(6011)[0-9]{9,11}$)|(^(6011)[0-9]{13,16}$)|(^(65)[0-9]{11,13}$)|(^(65)[0-9]{15,18}$)|(^(49030)[2-9]([0-9]{10}$|[0-9]{12,13}$))|(^(49033)[5-9]([0-9]{10}$|[0-9]{12,13}$))|(^(49110)[1-2]([0-9]{10}$|[0-9]{12,13}$))|(^(49117)[4-9]([0-9]{10}$|[0-9]{12,13}$))|(^(49118)[0-2]([0-9]{10}$|[0-9]{12,13}$))|(^(4936)([0-9]{12}$|[0-9]{14,15}$))'), new RegExp('^([0-9]{3}|[0-9]{4})?$'), true],
    'OT': [false, new RegExp('^([0-9]{3}|[0-9]{4})?$'), false]
});



/* FILE: builder.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
// script.aculo.us builder.js v1.8.2, Tue Nov 18 18:30:58 +0100 2008

// Copyright (c) 2005-2008 Thomas Fuchs (http://script.aculo.us, http://mir.aculo.us)
//
// script.aculo.us is freely distributable under the terms of an MIT-style license.
// For details, see the script.aculo.us web site: http://script.aculo.us/

var Builder = {
  NODEMAP: {
    AREA: 'map',
    CAPTION: 'table',
    COL: 'table',
    COLGROUP: 'table',
    LEGEND: 'fieldset',
    OPTGROUP: 'select',
    OPTION: 'select',
    PARAM: 'object',
    TBODY: 'table',
    TD: 'table',
    TFOOT: 'table',
    TH: 'table',
    THEAD: 'table',
    TR: 'table'
  },
  // note: For Firefox < 1.5, OPTION and OPTGROUP tags are currently broken,
  //       due to a Firefox bug
  node: function(elementName) {
    elementName = elementName.toUpperCase();

    // try innerHTML approach
    var parentTag = this.NODEMAP[elementName] || 'div';
    var parentElement = document.createElement(parentTag);
    try { // prevent IE "feature": http://dev.rubyonrails.org/ticket/2707
      parentElement.innerHTML = "<" + elementName + "></" + elementName + ">";
    } catch(e) {}
    var element = parentElement.firstChild || null;

    // see if browser added wrapping tags
    if(element && (element.tagName.toUpperCase() != elementName))
      element = element.getElementsByTagName(elementName)[0];

    // fallback to createElement approach
    if(!element) element = document.createElement(elementName);

    // abort if nothing could be created
    if(!element) return;

    // attributes (or text)
    if(arguments[1])
      if(this._isStringOrNumber(arguments[1]) ||
        (arguments[1] instanceof Array) ||
        arguments[1].tagName) {
          this._children(element, arguments[1]);
        } else {
          var attrs = this._attributes(arguments[1]);
          if(attrs.length) {
            try { // prevent IE "feature": http://dev.rubyonrails.org/ticket/2707
              parentElement.innerHTML = "<" +elementName + " " +
                attrs + "></" + elementName + ">";
            } catch(e) {}
            element = parentElement.firstChild || null;
            // workaround firefox 1.0.X bug
            if(!element) {
              element = document.createElement(elementName);
              for(attr in arguments[1])
                element[attr == 'class' ? 'className' : attr] = arguments[1][attr];
            }
            if(element.tagName.toUpperCase() != elementName)
              element = parentElement.getElementsByTagName(elementName)[0];
          }
        }

    // text, or array of children
    if(arguments[2])
      this._children(element, arguments[2]);

     return $(element);
  },
  _text: function(text) {
     return document.createTextNode(text);
  },

  ATTR_MAP: {
    'className': 'class',
    'htmlFor': 'for'
  },

  _attributes: function(attributes) {
    var attrs = [];
    for(attribute in attributes)
      attrs.push((attribute in this.ATTR_MAP ? this.ATTR_MAP[attribute] : attribute) +
          '="' + attributes[attribute].toString().escapeHTML().gsub(/"/,'&quot;') + '"');
    return attrs.join(" ");
  },
  _children: function(element, children) {
    if(children.tagName) {
      element.appendChild(children);
      return;
    }
    if(typeof children=='object') { // array can hold nodes and text
      children.flatten().each( function(e) {
        if(typeof e=='object')
          element.appendChild(e);
        else
          if(Builder._isStringOrNumber(e))
            element.appendChild(Builder._text(e));
      });
    } else
      if(Builder._isStringOrNumber(children))
        element.appendChild(Builder._text(children));
  },
  _isStringOrNumber: function(param) {
    return(typeof param=='string' || typeof param=='number');
  },
  build: function(html) {
    var element = this.node('div');
    $(element).update(html.strip());
    return element.down();
  },
  dump: function(scope) {
    if(typeof scope != 'object' && typeof scope != 'function') scope = window; //global scope

    var tags = ("A ABBR ACRONYM ADDRESS APPLET AREA B BASE BASEFONT BDO BIG BLOCKQUOTE BODY " +
      "BR BUTTON CAPTION CENTER CITE CODE COL COLGROUP DD DEL DFN DIR DIV DL DT EM FIELDSET " +
      "FONT FORM FRAME FRAMESET H1 H2 H3 H4 H5 H6 HEAD HR HTML I IFRAME IMG INPUT INS ISINDEX "+
      "KBD LABEL LEGEND LI LINK MAP MENU META NOFRAMES NOSCRIPT OBJECT OL OPTGROUP OPTION P "+
      "PARAM PRE Q S SAMP SCRIPT SELECT SMALL SPAN STRIKE STRONG STYLE SUB SUP TABLE TBODY TD "+
      "TEXTAREA TFOOT TH THEAD TITLE TR TT U UL VAR").split(/\s+/);

    tags.each( function(tag){
      scope[tag] = function() {
        return Builder.node.apply(Builder, [tag].concat($A(arguments)));
      };
    });
  }
};


/* FILE: effects.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
// script.aculo.us effects.js v1.8.2, Tue Nov 18 18:30:58 +0100 2008

// Copyright (c) 2005-2008 Thomas Fuchs (http://script.aculo.us, http://mir.aculo.us)
// Contributors:
//  Justin Palmer (http://encytemedia.com/)
//  Mark Pilgrim (http://diveintomark.org/)
//  Martin Bialasinki
//
// script.aculo.us is freely distributable under the terms of an MIT-style license.
// For details, see the script.aculo.us web site: http://script.aculo.us/

// converts rgb() and #xxx to #xxxxxx format,
// returns self (or first argument) if not convertable
String.prototype.parseColor = function() {
  var color = '#';
  if (this.slice(0,4) == 'rgb(') {
    var cols = this.slice(4,this.length-1).split(',');
    var i=0; do { color += parseInt(cols[i]).toColorPart() } while (++i<3);
  } else {
    if (this.slice(0,1) == '#') {
      if (this.length==4) for(var i=1;i<4;i++) color += (this.charAt(i) + this.charAt(i)).toLowerCase();
      if (this.length==7) color = this.toLowerCase();
    }
  }
  return (color.length==7 ? color : (arguments[0] || this));
};

/*--------------------------------------------------------------------------*/

Element.collectTextNodes = function(element) {
  return $A($(element).childNodes).collect( function(node) {
    return (node.nodeType==3 ? node.nodeValue :
      (node.hasChildNodes() ? Element.collectTextNodes(node) : ''));
  }).flatten().join('');
};

Element.collectTextNodesIgnoreClass = function(element, className) {
  return $A($(element).childNodes).collect( function(node) {
    return (node.nodeType==3 ? node.nodeValue :
      ((node.hasChildNodes() && !Element.hasClassName(node,className)) ?
        Element.collectTextNodesIgnoreClass(node, className) : ''));
  }).flatten().join('');
};

Element.setContentZoom = function(element, percent) {
  element = $(element);
  element.setStyle({fontSize: (percent/100) + 'em'});
  if (Prototype.Browser.WebKit) window.scrollBy(0,0);
  return element;
};

Element.getInlineOpacity = function(element){
  return $(element).style.opacity || '';
};

Element.forceRerendering = function(element) {
  try {
    element = $(element);
    var n = document.createTextNode(' ');
    element.appendChild(n);
    element.removeChild(n);
  } catch(e) { }
};

/*--------------------------------------------------------------------------*/

var Effect = {
  _elementDoesNotExistError: {
    name: 'ElementDoesNotExistError',
    message: 'The specified DOM element does not exist, but is required for this effect to operate'
  },
  Transitions: {
    linear: Prototype.K,
    sinoidal: function(pos) {
      return (-Math.cos(pos*Math.PI)/2) + .5;
    },
    reverse: function(pos) {
      return 1-pos;
    },
    flicker: function(pos) {
      var pos = ((-Math.cos(pos*Math.PI)/4) + .75) + Math.random()/4;
      return pos > 1 ? 1 : pos;
    },
    wobble: function(pos) {
      return (-Math.cos(pos*Math.PI*(9*pos))/2) + .5;
    },
    pulse: function(pos, pulses) {
      return (-Math.cos((pos*((pulses||5)-.5)*2)*Math.PI)/2) + .5;
    },
    spring: function(pos) {
      return 1 - (Math.cos(pos * 4.5 * Math.PI) * Math.exp(-pos * 6));
    },
    none: function(pos) {
      return 0;
    },
    full: function(pos) {
      return 1;
    }
  },
  DefaultOptions: {
    duration:   1.0,   // seconds
    fps:        100,   // 100= assume 66fps max.
    sync:       false, // true for combining
    from:       0.0,
    to:         1.0,
    delay:      0.0,
    queue:      'parallel'
  },
  tagifyText: function(element) {
    var tagifyStyle = 'position:relative';
    if (Prototype.Browser.IE) tagifyStyle += ';zoom:1';

    element = $(element);
    $A(element.childNodes).each( function(child) {
      if (child.nodeType==3) {
        child.nodeValue.toArray().each( function(character) {
          element.insertBefore(
            new Element('span', {style: tagifyStyle}).update(
              character == ' ' ? String.fromCharCode(160) : character),
              child);
        });
        Element.remove(child);
      }
    });
  },
  multiple: function(element, effect) {
    var elements;
    if (((typeof element == 'object') ||
        Object.isFunction(element)) &&
       (element.length))
      elements = element;
    else
      elements = $(element).childNodes;

    var options = Object.extend({
      speed: 0.1,
      delay: 0.0
    }, arguments[2] || { });
    var masterDelay = options.delay;

    $A(elements).each( function(element, index) {
      new effect(element, Object.extend(options, { delay: index * options.speed + masterDelay }));
    });
  },
  PAIRS: {
    'slide':  ['SlideDown','SlideUp'],
    'blind':  ['BlindDown','BlindUp'],
    'appear': ['Appear','Fade']
  },
  toggle: function(element, effect) {
    element = $(element);
    effect = (effect || 'appear').toLowerCase();
    var options = Object.extend({
      queue: { position:'end', scope:(element.id || 'global'), limit: 1 }
    }, arguments[2] || { });
    Effect[element.visible() ?
      Effect.PAIRS[effect][1] : Effect.PAIRS[effect][0]](element, options);
  }
};

Effect.DefaultOptions.transition = Effect.Transitions.sinoidal;

/* ------------- core effects ------------- */

Effect.ScopedQueue = Class.create(Enumerable, {
  initialize: function() {
    this.effects  = [];
    this.interval = null;
  },
  _each: function(iterator) {
    this.effects._each(iterator);
  },
  add: function(effect) {
    var timestamp = new Date().getTime();

    var position = Object.isString(effect.options.queue) ?
      effect.options.queue : effect.options.queue.position;

    switch(position) {
      case 'front':
        // move unstarted effects after this effect
        this.effects.findAll(function(e){ return e.state=='idle' }).each( function(e) {
            e.startOn  += effect.finishOn;
            e.finishOn += effect.finishOn;
          });
        break;
      case 'with-last':
        timestamp = this.effects.pluck('startOn').max() || timestamp;
        break;
      case 'end':
        // start effect after last queued effect has finished
        timestamp = this.effects.pluck('finishOn').max() || timestamp;
        break;
    }

    effect.startOn  += timestamp;
    effect.finishOn += timestamp;

    if (!effect.options.queue.limit || (this.effects.length < effect.options.queue.limit))
      this.effects.push(effect);

    if (!this.interval)
      this.interval = setInterval(this.loop.bind(this), 15);
  },
  remove: function(effect) {
    this.effects = this.effects.reject(function(e) { return e==effect });
    if (this.effects.length == 0) {
      clearInterval(this.interval);
      this.interval = null;
    }
  },
  loop: function() {
    var timePos = new Date().getTime();
    for(var i=0, len=this.effects.length;i<len;i++)
      this.effects[i] && this.effects[i].loop(timePos);
  }
});

Effect.Queues = {
  instances: $H(),
  get: function(queueName) {
    if (!Object.isString(queueName)) return queueName;

    return this.instances.get(queueName) ||
      this.instances.set(queueName, new Effect.ScopedQueue());
  }
};
Effect.Queue = Effect.Queues.get('global');

Effect.Base = Class.create({
  position: null,
  start: function(options) {
    function codeForEvent(options,eventName){
      return (
        (options[eventName+'Internal'] ? 'this.options.'+eventName+'Internal(this);' : '') +
        (options[eventName] ? 'this.options.'+eventName+'(this);' : '')
      );
    }
    if (options && options.transition === false) options.transition = Effect.Transitions.linear;
    this.options      = Object.extend(Object.extend({ },Effect.DefaultOptions), options || { });
    this.currentFrame = 0;
    this.state        = 'idle';
    this.startOn      = this.options.delay*1000;
    this.finishOn     = this.startOn+(this.options.duration*1000);
    this.fromToDelta  = this.options.to-this.options.from;
    this.totalTime    = this.finishOn-this.startOn;
    this.totalFrames  = this.options.fps*this.options.duration;

    this.render = (function() {
      function dispatch(effect, eventName) {
        if (effect.options[eventName + 'Internal'])
          effect.options[eventName + 'Internal'](effect);
        if (effect.options[eventName])
          effect.options[eventName](effect);
      }

      return function(pos) {
        if (this.state === "idle") {
          this.state = "running";
          dispatch(this, 'beforeSetup');
          if (this.setup) this.setup();
          dispatch(this, 'afterSetup');
        }
        if (this.state === "running") {
          pos = (this.options.transition(pos) * this.fromToDelta) + this.options.from;
          this.position = pos;
          dispatch(this, 'beforeUpdate');
          if (this.update) this.update(pos);
          dispatch(this, 'afterUpdate');
        }
      };
    })();

    this.event('beforeStart');
    if (!this.options.sync)
      Effect.Queues.get(Object.isString(this.options.queue) ?
        'global' : this.options.queue.scope).add(this);
  },
  loop: function(timePos) {
    if (timePos >= this.startOn) {
      if (timePos >= this.finishOn) {
        this.render(1.0);
        this.cancel();
        this.event('beforeFinish');
        if (this.finish) this.finish();
        this.event('afterFinish');
        return;
      }
      var pos   = (timePos - this.startOn) / this.totalTime,
          frame = (pos * this.totalFrames).round();
      if (frame > this.currentFrame) {
        this.render(pos);
        this.currentFrame = frame;
      }
    }
  },
  cancel: function() {
    if (!this.options.sync)
      Effect.Queues.get(Object.isString(this.options.queue) ?
        'global' : this.options.queue.scope).remove(this);
    this.state = 'finished';
  },
  event: function(eventName) {
    if (this.options[eventName + 'Internal']) this.options[eventName + 'Internal'](this);
    if (this.options[eventName]) this.options[eventName](this);
  },
  inspect: function() {
    var data = $H();
    for(property in this)
      if (!Object.isFunction(this[property])) data.set(property, this[property]);
    return '#<Effect:' + data.inspect() + ',options:' + $H(this.options).inspect() + '>';
  }
});

Effect.Parallel = Class.create(Effect.Base, {
  initialize: function(effects) {
    this.effects = effects || [];
    this.start(arguments[1]);
  },
  update: function(position) {
    this.effects.invoke('render', position);
  },
  finish: function(position) {
    this.effects.each( function(effect) {
      effect.render(1.0);
      effect.cancel();
      effect.event('beforeFinish');
      if (effect.finish) effect.finish(position);
      effect.event('afterFinish');
    });
  }
});

Effect.Tween = Class.create(Effect.Base, {
  initialize: function(object, from, to) {
    object = Object.isString(object) ? $(object) : object;
    var args = $A(arguments), method = args.last(),
      options = args.length == 5 ? args[3] : null;
    this.method = Object.isFunction(method) ? method.bind(object) :
      Object.isFunction(object[method]) ? object[method].bind(object) :
      function(value) { object[method] = value };
    this.start(Object.extend({ from: from, to: to }, options || { }));
  },
  update: function(position) {
    this.method(position);
  }
});

Effect.Event = Class.create(Effect.Base, {
  initialize: function() {
    this.start(Object.extend({ duration: 0 }, arguments[0] || { }));
  },
  update: Prototype.emptyFunction
});

Effect.Opacity = Class.create(Effect.Base, {
  initialize: function(element) {
    this.element = $(element);
    if (!this.element) throw(Effect._elementDoesNotExistError);
    // make this work on IE on elements without 'layout'
    if (Prototype.Browser.IE && (!this.element.currentStyle.hasLayout))
      this.element.setStyle({zoom: 1});
    var options = Object.extend({
      from: this.element.getOpacity() || 0.0,
      to:   1.0
    }, arguments[1] || { });
    this.start(options);
  },
  update: function(position) {
    this.element.setOpacity(position);
  }
});

Effect.Move = Class.create(Effect.Base, {
  initialize: function(element) {
    this.element = $(element);
    if (!this.element) throw(Effect._elementDoesNotExistError);
    var options = Object.extend({
      x:    0,
      y:    0,
      mode: 'relative'
    }, arguments[1] || { });
    this.start(options);
  },
  setup: function() {
    this.element.makePositioned();
    this.originalLeft = parseFloat(this.element.getStyle('left') || '0');
    this.originalTop  = parseFloat(this.element.getStyle('top')  || '0');
    if (this.options.mode == 'absolute') {
      this.options.x = this.options.x - this.originalLeft;
      this.options.y = this.options.y - this.originalTop;
    }
  },
  update: function(position) {
    this.element.setStyle({
      left: (this.options.x  * position + this.originalLeft).round() + 'px',
      top:  (this.options.y  * position + this.originalTop).round()  + 'px'
    });
  }
});

// for backwards compatibility
Effect.MoveBy = function(element, toTop, toLeft) {
  return new Effect.Move(element,
    Object.extend({ x: toLeft, y: toTop }, arguments[3] || { }));
};

Effect.Scale = Class.create(Effect.Base, {
  initialize: function(element, percent) {
    this.element = $(element);
    if (!this.element) throw(Effect._elementDoesNotExistError);
    var options = Object.extend({
      scaleX: true,
      scaleY: true,
      scaleContent: true,
      scaleFromCenter: false,
      scaleMode: 'box',        // 'box' or 'contents' or { } with provided values
      scaleFrom: 100.0,
      scaleTo:   percent
    }, arguments[2] || { });
    this.start(options);
  },
  setup: function() {
    this.restoreAfterFinish = this.options.restoreAfterFinish || false;
    this.elementPositioning = this.element.getStyle('position');

    this.originalStyle = { };
    ['top','left','width','height','fontSize'].each( function(k) {
      this.originalStyle[k] = this.element.style[k];
    }.bind(this));

    this.originalTop  = this.element.offsetTop;
    this.originalLeft = this.element.offsetLeft;

    var fontSize = this.element.getStyle('font-size') || '100%';
    ['em','px','%','pt'].each( function(fontSizeType) {
      if (fontSize.indexOf(fontSizeType)>0) {
        this.fontSize     = parseFloat(fontSize);
        this.fontSizeType = fontSizeType;
      }
    }.bind(this));

    this.factor = (this.options.scaleTo - this.options.scaleFrom)/100;

    this.dims = null;
    if (this.options.scaleMode=='box')
      this.dims = [this.element.offsetHeight, this.element.offsetWidth];
    if (/^content/.test(this.options.scaleMode))
      this.dims = [this.element.scrollHeight, this.element.scrollWidth];
    if (!this.dims)
      this.dims = [this.options.scaleMode.originalHeight,
                   this.options.scaleMode.originalWidth];
  },
  update: function(position) {
    var currentScale = (this.options.scaleFrom/100.0) + (this.factor * position);
    if (this.options.scaleContent && this.fontSize)
      this.element.setStyle({fontSize: this.fontSize * currentScale + this.fontSizeType });
    this.setDimensions(this.dims[0] * currentScale, this.dims[1] * currentScale);
  },
  finish: function(position) {
    if (this.restoreAfterFinish) this.element.setStyle(this.originalStyle);
  },
  setDimensions: function(height, width) {
    var d = { };
    if (this.options.scaleX) d.width = width.round() + 'px';
    if (this.options.scaleY) d.height = height.round() + 'px';
    if (this.options.scaleFromCenter) {
      var topd  = (height - this.dims[0])/2;
      var leftd = (width  - this.dims[1])/2;
      if (this.elementPositioning == 'absolute') {
        if (this.options.scaleY) d.top = this.originalTop-topd + 'px';
        if (this.options.scaleX) d.left = this.originalLeft-leftd + 'px';
      } else {
        if (this.options.scaleY) d.top = -topd + 'px';
        if (this.options.scaleX) d.left = -leftd + 'px';
      }
    }
    this.element.setStyle(d);
  }
});

Effect.Highlight = Class.create(Effect.Base, {
  initialize: function(element) {
    this.element = $(element);
    if (!this.element) throw(Effect._elementDoesNotExistError);
    var options = Object.extend({ startcolor: '#ffff99' }, arguments[1] || { });
    this.start(options);
  },
  setup: function() {
    // Prevent executing on elements not in the layout flow
    if (this.element.getStyle('display')=='none') { this.cancel(); return; }
    // Disable background image during the effect
    this.oldStyle = { };
    if (!this.options.keepBackgroundImage) {
      this.oldStyle.backgroundImage = this.element.getStyle('background-image');
      this.element.setStyle({backgroundImage: 'none'});
    }
    if (!this.options.endcolor)
      this.options.endcolor = this.element.getStyle('background-color').parseColor('#ffffff');
    if (!this.options.restorecolor)
      this.options.restorecolor = this.element.getStyle('background-color');
    // init color calculations
    this._base  = $R(0,2).map(function(i){ return parseInt(this.options.startcolor.slice(i*2+1,i*2+3),16) }.bind(this));
    this._delta = $R(0,2).map(function(i){ return parseInt(this.options.endcolor.slice(i*2+1,i*2+3),16)-this._base[i] }.bind(this));
  },
  update: function(position) {
    this.element.setStyle({backgroundColor: $R(0,2).inject('#',function(m,v,i){
      return m+((this._base[i]+(this._delta[i]*position)).round().toColorPart()); }.bind(this)) });
  },
  finish: function() {
    this.element.setStyle(Object.extend(this.oldStyle, {
      backgroundColor: this.options.restorecolor
    }));
  }
});

Effect.ScrollTo = function(element) {
  var options = arguments[1] || { },
  scrollOffsets = document.viewport.getScrollOffsets(),
  elementOffsets = $(element).cumulativeOffset();

  if (options.offset) elementOffsets[1] += options.offset;

  return new Effect.Tween(null,
    scrollOffsets.top,
    elementOffsets[1],
    options,
    function(p){ scrollTo(scrollOffsets.left, p.round()); }
  );
};

/* ------------- combination effects ------------- */

Effect.Fade = function(element) {
  element = $(element);
  var oldOpacity = element.getInlineOpacity();
  var options = Object.extend({
    from: element.getOpacity() || 1.0,
    to:   0.0,
    afterFinishInternal: function(effect) {
      if (effect.options.to!=0) return;
      effect.element.hide().setStyle({opacity: oldOpacity});
    }
  }, arguments[1] || { });
  return new Effect.Opacity(element,options);
};

Effect.Appear = function(element) {
  element = $(element);
  var options = Object.extend({
  from: (element.getStyle('display') == 'none' ? 0.0 : element.getOpacity() || 0.0),
  to:   1.0,
  // force Safari to render floated elements properly
  afterFinishInternal: function(effect) {
    effect.element.forceRerendering();
  },
  beforeSetup: function(effect) {
    effect.element.setOpacity(effect.options.from).show();
  }}, arguments[1] || { });
  return new Effect.Opacity(element,options);
};

Effect.Puff = function(element) {
  element = $(element);
  var oldStyle = {
    opacity: element.getInlineOpacity(),
    position: element.getStyle('position'),
    top:  element.style.top,
    left: element.style.left,
    width: element.style.width,
    height: element.style.height
  };
  return new Effect.Parallel(
   [ new Effect.Scale(element, 200,
      { sync: true, scaleFromCenter: true, scaleContent: true, restoreAfterFinish: true }),
     new Effect.Opacity(element, { sync: true, to: 0.0 } ) ],
     Object.extend({ duration: 1.0,
      beforeSetupInternal: function(effect) {
        Position.absolutize(effect.effects[0].element);
      },
      afterFinishInternal: function(effect) {
         effect.effects[0].element.hide().setStyle(oldStyle); }
     }, arguments[1] || { })
   );
};

Effect.BlindUp = function(element) {
  element = $(element);
  element.makeClipping();
  return new Effect.Scale(element, 0,
    Object.extend({ scaleContent: false,
      scaleX: false,
      restoreAfterFinish: true,
      afterFinishInternal: function(effect) {
        effect.element.hide().undoClipping();
      }
    }, arguments[1] || { })
  );
};

Effect.BlindDown = function(element) {
  element = $(element);
  var elementDimensions = element.getDimensions();
  return new Effect.Scale(element, 100, Object.extend({
    scaleContent: false,
    scaleX: false,
    scaleFrom: 0,
    scaleMode: {originalHeight: elementDimensions.height, originalWidth: elementDimensions.width},
    restoreAfterFinish: true,
    afterSetup: function(effect) {
      effect.element.makeClipping().setStyle({height: '0px'}).show();
    },
    afterFinishInternal: function(effect) {
      effect.element.undoClipping();
    }
  }, arguments[1] || { }));
};

Effect.SwitchOff = function(element) {
  element = $(element);
  var oldOpacity = element.getInlineOpacity();
  return new Effect.Appear(element, Object.extend({
    duration: 0.4,
    from: 0,
    transition: Effect.Transitions.flicker,
    afterFinishInternal: function(effect) {
      new Effect.Scale(effect.element, 1, {
        duration: 0.3, scaleFromCenter: true,
        scaleX: false, scaleContent: false, restoreAfterFinish: true,
        beforeSetup: function(effect) {
          effect.element.makePositioned().makeClipping();
        },
        afterFinishInternal: function(effect) {
          effect.element.hide().undoClipping().undoPositioned().setStyle({opacity: oldOpacity});
        }
      });
    }
  }, arguments[1] || { }));
};

Effect.DropOut = function(element) {
  element = $(element);
  var oldStyle = {
    top: element.getStyle('top'),
    left: element.getStyle('left'),
    opacity: element.getInlineOpacity() };
  return new Effect.Parallel(
    [ new Effect.Move(element, {x: 0, y: 100, sync: true }),
      new Effect.Opacity(element, { sync: true, to: 0.0 }) ],
    Object.extend(
      { duration: 0.5,
        beforeSetup: function(effect) {
          effect.effects[0].element.makePositioned();
        },
        afterFinishInternal: function(effect) {
          effect.effects[0].element.hide().undoPositioned().setStyle(oldStyle);
        }
      }, arguments[1] || { }));
};

Effect.Shake = function(element) {
  element = $(element);
  var options = Object.extend({
    distance: 20,
    duration: 0.5
  }, arguments[1] || {});
  var distance = parseFloat(options.distance);
  var split = parseFloat(options.duration) / 10.0;
  var oldStyle = {
    top: element.getStyle('top'),
    left: element.getStyle('left') };
    return new Effect.Move(element,
      { x:  distance, y: 0, duration: split, afterFinishInternal: function(effect) {
    new Effect.Move(effect.element,
      { x: -distance*2, y: 0, duration: split*2,  afterFinishInternal: function(effect) {
    new Effect.Move(effect.element,
      { x:  distance*2, y: 0, duration: split*2,  afterFinishInternal: function(effect) {
    new Effect.Move(effect.element,
      { x: -distance*2, y: 0, duration: split*2,  afterFinishInternal: function(effect) {
    new Effect.Move(effect.element,
      { x:  distance*2, y: 0, duration: split*2,  afterFinishInternal: function(effect) {
    new Effect.Move(effect.element,
      { x: -distance, y: 0, duration: split, afterFinishInternal: function(effect) {
        effect.element.undoPositioned().setStyle(oldStyle);
  }}); }}); }}); }}); }}); }});
};

Effect.SlideDown = function(element) {
  element = $(element).cleanWhitespace();
  // SlideDown need to have the content of the element wrapped in a container element with fixed height!
  var oldInnerBottom = element.down().getStyle('bottom');
  var elementDimensions = element.getDimensions();
  return new Effect.Scale(element, 100, Object.extend({
    scaleContent: false,
    scaleX: false,
    scaleFrom: window.opera ? 0 : 1,
    scaleMode: {originalHeight: elementDimensions.height, originalWidth: elementDimensions.width},
    restoreAfterFinish: true,
    afterSetup: function(effect) {
      effect.element.makePositioned();
      effect.element.down().makePositioned();
      if (window.opera) effect.element.setStyle({top: ''});
      effect.element.makeClipping().setStyle({height: '0px'}).show();
    },
    afterUpdateInternal: function(effect) {
      effect.element.down().setStyle({bottom:
        (effect.dims[0] - effect.element.clientHeight) + 'px' });
    },
    afterFinishInternal: function(effect) {
      effect.element.undoClipping().undoPositioned();
      effect.element.down().undoPositioned().setStyle({bottom: oldInnerBottom}); }
    }, arguments[1] || { })
  );
};

Effect.SlideUp = function(element) {
  element = $(element).cleanWhitespace();
  var oldInnerBottom = element.down().getStyle('bottom');
  var elementDimensions = element.getDimensions();
  return new Effect.Scale(element, window.opera ? 0 : 1,
   Object.extend({ scaleContent: false,
    scaleX: false,
    scaleMode: 'box',
    scaleFrom: 100,
    scaleMode: {originalHeight: elementDimensions.height, originalWidth: elementDimensions.width},
    restoreAfterFinish: true,
    afterSetup: function(effect) {
      effect.element.makePositioned();
      effect.element.down().makePositioned();
      if (window.opera) effect.element.setStyle({top: ''});
      effect.element.makeClipping().show();
    },
    afterUpdateInternal: function(effect) {
      effect.element.down().setStyle({bottom:
        (effect.dims[0] - effect.element.clientHeight) + 'px' });
    },
    afterFinishInternal: function(effect) {
      effect.element.hide().undoClipping().undoPositioned();
      effect.element.down().undoPositioned().setStyle({bottom: oldInnerBottom});
    }
   }, arguments[1] || { })
  );
};

// Bug in opera makes the TD containing this element expand for a instance after finish
Effect.Squish = function(element) {
  return new Effect.Scale(element, window.opera ? 1 : 0, {
    restoreAfterFinish: true,
    beforeSetup: function(effect) {
      effect.element.makeClipping();
    },
    afterFinishInternal: function(effect) {
      effect.element.hide().undoClipping();
    }
  });
};

Effect.Grow = function(element) {
  element = $(element);
  var options = Object.extend({
    direction: 'center',
    moveTransition: Effect.Transitions.sinoidal,
    scaleTransition: Effect.Transitions.sinoidal,
    opacityTransition: Effect.Transitions.full
  }, arguments[1] || { });
  var oldStyle = {
    top: element.style.top,
    left: element.style.left,
    height: element.style.height,
    width: element.style.width,
    opacity: element.getInlineOpacity() };

  var dims = element.getDimensions();
  var initialMoveX, initialMoveY;
  var moveX, moveY;

  switch (options.direction) {
    case 'top-left':
      initialMoveX = initialMoveY = moveX = moveY = 0;
      break;
    case 'top-right':
      initialMoveX = dims.width;
      initialMoveY = moveY = 0;
      moveX = -dims.width;
      break;
    case 'bottom-left':
      initialMoveX = moveX = 0;
      initialMoveY = dims.height;
      moveY = -dims.height;
      break;
    case 'bottom-right':
      initialMoveX = dims.width;
      initialMoveY = dims.height;
      moveX = -dims.width;
      moveY = -dims.height;
      break;
    case 'center':
      initialMoveX = dims.width / 2;
      initialMoveY = dims.height / 2;
      moveX = -dims.width / 2;
      moveY = -dims.height / 2;
      break;
  }

  return new Effect.Move(element, {
    x: initialMoveX,
    y: initialMoveY,
    duration: 0.01,
    beforeSetup: function(effect) {
      effect.element.hide().makeClipping().makePositioned();
    },
    afterFinishInternal: function(effect) {
      new Effect.Parallel(
        [ new Effect.Opacity(effect.element, { sync: true, to: 1.0, from: 0.0, transition: options.opacityTransition }),
          new Effect.Move(effect.element, { x: moveX, y: moveY, sync: true, transition: options.moveTransition }),
          new Effect.Scale(effect.element, 100, {
            scaleMode: { originalHeight: dims.height, originalWidth: dims.width },
            sync: true, scaleFrom: window.opera ? 1 : 0, transition: options.scaleTransition, restoreAfterFinish: true})
        ], Object.extend({
             beforeSetup: function(effect) {
               effect.effects[0].element.setStyle({height: '0px'}).show();
             },
             afterFinishInternal: function(effect) {
               effect.effects[0].element.undoClipping().undoPositioned().setStyle(oldStyle);
             }
           }, options)
      );
    }
  });
};

Effect.Shrink = function(element) {
  element = $(element);
  var options = Object.extend({
    direction: 'center',
    moveTransition: Effect.Transitions.sinoidal,
    scaleTransition: Effect.Transitions.sinoidal,
    opacityTransition: Effect.Transitions.none
  }, arguments[1] || { });
  var oldStyle = {
    top: element.style.top,
    left: element.style.left,
    height: element.style.height,
    width: element.style.width,
    opacity: element.getInlineOpacity() };

  var dims = element.getDimensions();
  var moveX, moveY;

  switch (options.direction) {
    case 'top-left':
      moveX = moveY = 0;
      break;
    case 'top-right':
      moveX = dims.width;
      moveY = 0;
      break;
    case 'bottom-left':
      moveX = 0;
      moveY = dims.height;
      break;
    case 'bottom-right':
      moveX = dims.width;
      moveY = dims.height;
      break;
    case 'center':
      moveX = dims.width / 2;
      moveY = dims.height / 2;
      break;
  }

  return new Effect.Parallel(
    [ new Effect.Opacity(element, { sync: true, to: 0.0, from: 1.0, transition: options.opacityTransition }),
      new Effect.Scale(element, window.opera ? 1 : 0, { sync: true, transition: options.scaleTransition, restoreAfterFinish: true}),
      new Effect.Move(element, { x: moveX, y: moveY, sync: true, transition: options.moveTransition })
    ], Object.extend({
         beforeStartInternal: function(effect) {
           effect.effects[0].element.makePositioned().makeClipping();
         },
         afterFinishInternal: function(effect) {
           effect.effects[0].element.hide().undoClipping().undoPositioned().setStyle(oldStyle); }
       }, options)
  );
};

Effect.Pulsate = function(element) {
  element = $(element);
  var options    = arguments[1] || { },
    oldOpacity = element.getInlineOpacity(),
    transition = options.transition || Effect.Transitions.linear,
    reverser   = function(pos){
      return 1 - transition((-Math.cos((pos*(options.pulses||5)*2)*Math.PI)/2) + .5);
    };

  return new Effect.Opacity(element,
    Object.extend(Object.extend({  duration: 2.0, from: 0,
      afterFinishInternal: function(effect) { effect.element.setStyle({opacity: oldOpacity}); }
    }, options), {transition: reverser}));
};

Effect.Fold = function(element) {
  element = $(element);
  var oldStyle = {
    top: element.style.top,
    left: element.style.left,
    width: element.style.width,
    height: element.style.height };
  element.makeClipping();
  return new Effect.Scale(element, 5, Object.extend({
    scaleContent: false,
    scaleX: false,
    afterFinishInternal: function(effect) {
    new Effect.Scale(element, 1, {
      scaleContent: false,
      scaleY: false,
      afterFinishInternal: function(effect) {
        effect.element.hide().undoClipping().setStyle(oldStyle);
      } });
  }}, arguments[1] || { }));
};

Effect.Morph = Class.create(Effect.Base, {
  initialize: function(element) {
    this.element = $(element);
    if (!this.element) throw(Effect._elementDoesNotExistError);
    var options = Object.extend({
      style: { }
    }, arguments[1] || { });

    if (!Object.isString(options.style)) this.style = $H(options.style);
    else {
      if (options.style.include(':'))
        this.style = options.style.parseStyle();
      else {
        this.element.addClassName(options.style);
        this.style = $H(this.element.getStyles());
        this.element.removeClassName(options.style);
        var css = this.element.getStyles();
        this.style = this.style.reject(function(style) {
          return style.value == css[style.key];
        });
        options.afterFinishInternal = function(effect) {
          effect.element.addClassName(effect.options.style);
          effect.transforms.each(function(transform) {
            effect.element.style[transform.style] = '';
          });
        };
      }
    }
    this.start(options);
  },

  setup: function(){
    function parseColor(color){
      if (!color || ['rgba(0, 0, 0, 0)','transparent'].include(color)) color = '#ffffff';
      color = color.parseColor();
      return $R(0,2).map(function(i){
        return parseInt( color.slice(i*2+1,i*2+3), 16 );
      });
    }
    this.transforms = this.style.map(function(pair){
      var property = pair[0], value = pair[1], unit = null;

      if (value.parseColor('#zzzzzz') != '#zzzzzz') {
        value = value.parseColor();
        unit  = 'color';
      } else if (property == 'opacity') {
        value = parseFloat(value);
        if (Prototype.Browser.IE && (!this.element.currentStyle.hasLayout))
          this.element.setStyle({zoom: 1});
      } else if (Element.CSS_LENGTH.test(value)) {
          var components = value.match(/^([\+\-]?[0-9\.]+)(.*)$/);
          value = parseFloat(components[1]);
          unit = (components.length == 3) ? components[2] : null;
      }

      var originalValue = this.element.getStyle(property);
      return {
        style: property.camelize(),
        originalValue: unit=='color' ? parseColor(originalValue) : parseFloat(originalValue || 0),
        targetValue: unit=='color' ? parseColor(value) : value,
        unit: unit
      };
    }.bind(this)).reject(function(transform){
      return (
        (transform.originalValue == transform.targetValue) ||
        (
          transform.unit != 'color' &&
          (isNaN(transform.originalValue) || isNaN(transform.targetValue))
        )
      );
    });
  },
  update: function(position) {
    var style = { }, transform, i = this.transforms.length;
    while(i--)
      style[(transform = this.transforms[i]).style] =
        transform.unit=='color' ? '#'+
          (Math.round(transform.originalValue[0]+
            (transform.targetValue[0]-transform.originalValue[0])*position)).toColorPart() +
          (Math.round(transform.originalValue[1]+
            (transform.targetValue[1]-transform.originalValue[1])*position)).toColorPart() +
          (Math.round(transform.originalValue[2]+
            (transform.targetValue[2]-transform.originalValue[2])*position)).toColorPart() :
        (transform.originalValue +
          (transform.targetValue - transform.originalValue) * position).toFixed(3) +
            (transform.unit === null ? '' : transform.unit);
    this.element.setStyle(style, true);
  }
});

Effect.Transform = Class.create({
  initialize: function(tracks){
    this.tracks  = [];
    this.options = arguments[1] || { };
    this.addTracks(tracks);
  },
  addTracks: function(tracks){
    tracks.each(function(track){
      track = $H(track);
      var data = track.values().first();
      this.tracks.push($H({
        ids:     track.keys().first(),
        effect:  Effect.Morph,
        options: { style: data }
      }));
    }.bind(this));
    return this;
  },
  play: function(){
    return new Effect.Parallel(
      this.tracks.map(function(track){
        var ids = track.get('ids'), effect = track.get('effect'), options = track.get('options');
        var elements = [$(ids) || $$(ids)].flatten();
        return elements.map(function(e){ return new effect(e, Object.extend({ sync:true }, options)) });
      }).flatten(),
      this.options
    );
  }
});

Element.CSS_PROPERTIES = $w(
  'backgroundColor backgroundPosition borderBottomColor borderBottomStyle ' +
  'borderBottomWidth borderLeftColor borderLeftStyle borderLeftWidth ' +
  'borderRightColor borderRightStyle borderRightWidth borderSpacing ' +
  'borderTopColor borderTopStyle borderTopWidth bottom clip color ' +
  'fontSize fontWeight height left letterSpacing lineHeight ' +
  'marginBottom marginLeft marginRight marginTop markerOffset maxHeight '+
  'maxWidth minHeight minWidth opacity outlineColor outlineOffset ' +
  'outlineWidth paddingBottom paddingLeft paddingRight paddingTop ' +
  'right textIndent top width wordSpacing zIndex');

Element.CSS_LENGTH = /^(([\+\-]?[0-9\.]+)(em|ex|px|in|cm|mm|pt|pc|\%))|0$/;

String.__parseStyleElement = document.createElement('div');
String.prototype.parseStyle = function(){
  var style, styleRules = $H();
  if (Prototype.Browser.WebKit)
    style = new Element('div',{style:this}).style;
  else {
    String.__parseStyleElement.innerHTML = '<div style="' + this + '"></div>';
    style = String.__parseStyleElement.childNodes[0].style;
  }

  Element.CSS_PROPERTIES.each(function(property){
    if (style[property]) styleRules.set(property, style[property]);
  });

  if (Prototype.Browser.IE && this.include('opacity'))
    styleRules.set('opacity', this.match(/opacity:\s*((?:0|1)?(?:\.\d*)?)/)[1]);

  return styleRules;
};

if (document.defaultView && document.defaultView.getComputedStyle) {
  Element.getStyles = function(element) {
    var css = document.defaultView.getComputedStyle($(element), null);
    return Element.CSS_PROPERTIES.inject({ }, function(styles, property) {
      styles[property] = css[property];
      return styles;
    });
  };
} else {
  Element.getStyles = function(element) {
    element = $(element);
    var css = element.currentStyle, styles;
    styles = Element.CSS_PROPERTIES.inject({ }, function(results, property) {
      results[property] = css[property];
      return results;
    });
    if (!styles.opacity) styles.opacity = element.getOpacity();
    return styles;
  };
}

Effect.Methods = {
  morph: function(element, style) {
    element = $(element);
    new Effect.Morph(element, Object.extend({ style: style }, arguments[2] || { }));
    return element;
  },
  visualEffect: function(element, effect, options) {
    element = $(element);
    var s = effect.dasherize().camelize(), klass = s.charAt(0).toUpperCase() + s.substring(1);
    new Effect[klass](element, options);
    return element;
  },
  highlight: function(element, options) {
    element = $(element);
    new Effect.Highlight(element, options);
    return element;
  }
};

$w('fade appear grow shrink fold blindUp blindDown slideUp slideDown '+
  'pulsate shake puff squish switchOff dropOut').each(
  function(effect) {
    Effect.Methods[effect] = function(element, options){
      element = $(element);
      Effect[effect.charAt(0).toUpperCase() + effect.substring(1)](element, options);
      return element;
    };
  }
);

$w('getInlineOpacity forceRerendering setContentZoom collectTextNodes collectTextNodesIgnoreClass getStyles').each(
  function(f) { Effect.Methods[f] = Element[f]; }
);

Element.addMethods(Effect.Methods);


/* FILE: dragdrop.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
// script.aculo.us dragdrop.js v1.9.0, Thu Dec 23 16:54:48 -0500 2010

// Copyright (c) 2005-2010 Thomas Fuchs (http://script.aculo.us, http://mir.aculo.us)
//
// script.aculo.us is freely distributable under the terms of an MIT-style license.
// For details, see the script.aculo.us web site: http://script.aculo.us/

if(Object.isUndefined(Effect))
  throw("dragdrop.js requires including script.aculo.us' effects.js library");

var Droppables = {
  drops: [],

  remove: function(element) {
    this.drops = this.drops.reject(function(d) { return d.element==$(element) });
  },

  add: function(element) {
    element = $(element);
    var options = Object.extend({
      greedy:     true,
      hoverclass: null,
      tree:       false
    }, arguments[1] || { });

    // cache containers
    if(options.containment) {
      options._containers = [];
      var containment = options.containment;
      if(Object.isArray(containment)) {
        containment.each( function(c) { options._containers.push($(c)) });
      } else {
        options._containers.push($(containment));
      }
    }

    if(options.accept) options.accept = [options.accept].flatten();

    Element.makePositioned(element); // fix IE
    options.element = element;

    this.drops.push(options);
  },

  findDeepestChild: function(drops) {
    deepest = drops[0];

    for (i = 1; i < drops.length; ++i)
      if (Element.isParent(drops[i].element, deepest.element))
        deepest = drops[i];

    return deepest;
  },

  isContained: function(element, drop) {
    var containmentNode;
    if(drop.tree) {
      containmentNode = element.treeNode;
    } else {
      containmentNode = element.parentNode;
    }
    return drop._containers.detect(function(c) { return containmentNode == c });
  },

  isAffected: function(point, element, drop) {
    return (
      (drop.element!=element) &&
      ((!drop._containers) ||
        this.isContained(element, drop)) &&
      ((!drop.accept) ||
        (Element.classNames(element).detect(
          function(v) { return drop.accept.include(v) } ) )) &&
      Position.within(drop.element, point[0], point[1]) );
  },

  deactivate: function(drop) {
    if(drop.hoverclass)
      Element.removeClassName(drop.element, drop.hoverclass);
    this.last_active = null;
  },

  activate: function(drop) {
    if(drop.hoverclass)
      Element.addClassName(drop.element, drop.hoverclass);
    this.last_active = drop;
  },

  show: function(point, element) {
    if(!this.drops.length) return;
    var drop, affected = [];

    this.drops.each( function(drop) {
      if(Droppables.isAffected(point, element, drop))
        affected.push(drop);
    });

    if(affected.length>0)
      drop = Droppables.findDeepestChild(affected);

    if(this.last_active && this.last_active != drop) this.deactivate(this.last_active);
    if (drop) {
      Position.within(drop.element, point[0], point[1]);
      if(drop.onHover)
        drop.onHover(element, drop.element, Position.overlap(drop.overlap, drop.element));

      if (drop != this.last_active) Droppables.activate(drop);
    }
  },

  fire: function(event, element) {
    if(!this.last_active) return;
    Position.prepare();

    if (this.isAffected([Event.pointerX(event), Event.pointerY(event)], element, this.last_active))
      if (this.last_active.onDrop) {
        this.last_active.onDrop(element, this.last_active.element, event);
        return true;
      }
  },

  reset: function() {
    if(this.last_active)
      this.deactivate(this.last_active);
  }
};

var Draggables = {
  drags: [],
  observers: [],

  register: function(draggable) {
    if(this.drags.length == 0) {
      this.eventMouseUp   = this.endDrag.bindAsEventListener(this);
      this.eventMouseMove = this.updateDrag.bindAsEventListener(this);
      this.eventKeypress  = this.keyPress.bindAsEventListener(this);

      Event.observe(document, "mouseup", this.eventMouseUp);
      Event.observe(document, "mousemove", this.eventMouseMove);
      Event.observe(document, "keypress", this.eventKeypress);
    }
    this.drags.push(draggable);
  },

  unregister: function(draggable) {
    this.drags = this.drags.reject(function(d) { return d==draggable });
    if(this.drags.length == 0) {
      Event.stopObserving(document, "mouseup", this.eventMouseUp);
      Event.stopObserving(document, "mousemove", this.eventMouseMove);
      Event.stopObserving(document, "keypress", this.eventKeypress);
    }
  },

  activate: function(draggable) {
    if(draggable.options.delay) {
      this._timeout = setTimeout(function() {
        Draggables._timeout = null;
        window.focus();
        Draggables.activeDraggable = draggable;
      }.bind(this), draggable.options.delay);
    } else {
      window.focus(); // allows keypress events if window isn't currently focused, fails for Safari
      this.activeDraggable = draggable;
    }
  },

  deactivate: function() {
    this.activeDraggable = null;
  },

  updateDrag: function(event) {
    if(!this.activeDraggable) return;
    var pointer = [Event.pointerX(event), Event.pointerY(event)];
    // Mozilla-based browsers fire successive mousemove events with
    // the same coordinates, prevent needless redrawing (moz bug?)
    if(this._lastPointer && (this._lastPointer.inspect() == pointer.inspect())) return;
    this._lastPointer = pointer;

    this.activeDraggable.updateDrag(event, pointer);
  },

  endDrag: function(event) {
    if(this._timeout) {
      clearTimeout(this._timeout);
      this._timeout = null;
    }
    if(!this.activeDraggable) return;
    this._lastPointer = null;
    this.activeDraggable.endDrag(event);
    this.activeDraggable = null;
  },

  keyPress: function(event) {
    if(this.activeDraggable)
      this.activeDraggable.keyPress(event);
  },

  addObserver: function(observer) {
    this.observers.push(observer);
    this._cacheObserverCallbacks();
  },

  removeObserver: function(element) {  // element instead of observer fixes mem leaks
    this.observers = this.observers.reject( function(o) { return o.element==element });
    this._cacheObserverCallbacks();
  },

  notify: function(eventName, draggable, event) {  // 'onStart', 'onEnd', 'onDrag'
    if(this[eventName+'Count'] > 0)
      this.observers.each( function(o) {
        if(o[eventName]) o[eventName](eventName, draggable, event);
      });
    if(draggable.options[eventName]) draggable.options[eventName](draggable, event);
  },

  _cacheObserverCallbacks: function() {
    ['onStart','onEnd','onDrag'].each( function(eventName) {
      Draggables[eventName+'Count'] = Draggables.observers.select(
        function(o) { return o[eventName]; }
      ).length;
    });
  }
};

/*--------------------------------------------------------------------------*/

var Draggable = Class.create({
  initialize: function(element) {
    var defaults = {
      handle: false,
      reverteffect: function(element, top_offset, left_offset) {
        var dur = Math.sqrt(Math.abs(top_offset^2)+Math.abs(left_offset^2))*0.02;
        new Effect.Move(element, { x: -left_offset, y: -top_offset, duration: dur,
          queue: {scope:'_draggable', position:'end'}
        });
      },
      endeffect: function(element) {
        var toOpacity = Object.isNumber(element._opacity) ? element._opacity : 1.0;
        new Effect.Opacity(element, {duration:0.2, from:0.7, to:toOpacity,
          queue: {scope:'_draggable', position:'end'},
          afterFinish: function(){
            Draggable._dragging[element] = false
          }
        });
      },
      zindex: 1000,
      revert: false,
      quiet: false,
      scroll: false,
      scrollSensitivity: 20,
      scrollSpeed: 15,
      snap: false,  // false, or xy or [x,y] or function(x,y){ return [x,y] }
      delay: 0
    };

    if(!arguments[1] || Object.isUndefined(arguments[1].endeffect))
      Object.extend(defaults, {
        starteffect: function(element) {
          element._opacity = Element.getOpacity(element);
          Draggable._dragging[element] = true;
          new Effect.Opacity(element, {duration:0.2, from:element._opacity, to:0.7});
        }
      });

    var options = Object.extend(defaults, arguments[1] || { });

    this.element = $(element);

    if(options.handle && Object.isString(options.handle))
      this.handle = this.element.down('.'+options.handle, 0);

    if(!this.handle) this.handle = $(options.handle);
    if(!this.handle) this.handle = this.element;

    if(options.scroll && !options.scroll.scrollTo && !options.scroll.outerHTML) {
      options.scroll = $(options.scroll);
      this._isScrollChild = Element.childOf(this.element, options.scroll);
    }

    Element.makePositioned(this.element); // fix IE

    this.options  = options;
    this.dragging = false;

    this.eventMouseDown = this.initDrag.bindAsEventListener(this);
    Event.observe(this.handle, "mousedown", this.eventMouseDown);

    Draggables.register(this);
  },

  destroy: function() {
    Event.stopObserving(this.handle, "mousedown", this.eventMouseDown);
    Draggables.unregister(this);
  },

  currentDelta: function() {
    return([
      parseInt(Element.getStyle(this.element,'left') || '0'),
      parseInt(Element.getStyle(this.element,'top') || '0')]);
  },

  initDrag: function(event) {
    if(!Object.isUndefined(Draggable._dragging[this.element]) &&
      Draggable._dragging[this.element]) return;
    if(Event.isLeftClick(event)) {
      // abort on form elements, fixes a Firefox issue
      var src = Event.element(event);
      if((tag_name = src.tagName.toUpperCase()) && (
        tag_name=='INPUT' ||
        tag_name=='SELECT' ||
        tag_name=='OPTION' ||
        tag_name=='BUTTON' ||
        tag_name=='TEXTAREA')) return;

      var pointer = [Event.pointerX(event), Event.pointerY(event)];
      var pos     = this.element.cumulativeOffset();
      this.offset = [0,1].map( function(i) { return (pointer[i] - pos[i]) });

      Draggables.activate(this);
      Event.stop(event);
    }
  },

  startDrag: function(event) {
    this.dragging = true;
    if(!this.delta)
      this.delta = this.currentDelta();

    if(this.options.zindex) {
      this.originalZ = parseInt(Element.getStyle(this.element,'z-index') || 0);
      this.element.style.zIndex = this.options.zindex;
    }

    if(this.options.ghosting) {
      this._clone = this.element.cloneNode(true);
      this._originallyAbsolute = (this.element.getStyle('position') == 'absolute');
      if (!this._originallyAbsolute)
        Position.absolutize(this.element);
      this.element.parentNode.insertBefore(this._clone, this.element);
    }

    if(this.options.scroll) {
      if (this.options.scroll == window) {
        var where = this._getWindowScroll(this.options.scroll);
        this.originalScrollLeft = where.left;
        this.originalScrollTop = where.top;
      } else {
        this.originalScrollLeft = this.options.scroll.scrollLeft;
        this.originalScrollTop = this.options.scroll.scrollTop;
      }
    }

    Draggables.notify('onStart', this, event);

    if(this.options.starteffect) this.options.starteffect(this.element);
  },

  updateDrag: function(event, pointer) {
    if(!this.dragging) this.startDrag(event);

    if(!this.options.quiet){
      Position.prepare();
      Droppables.show(pointer, this.element);
    }

    Draggables.notify('onDrag', this, event);

    this.draw(pointer);
    if(this.options.change) this.options.change(this);

    if(this.options.scroll) {
      this.stopScrolling();

      var p;
      if (this.options.scroll == window) {
        with(this._getWindowScroll(this.options.scroll)) { p = [ left, top, left+width, top+height ]; }
      } else {
        p = Position.page(this.options.scroll).toArray();
        p[0] += this.options.scroll.scrollLeft + Position.deltaX;
        p[1] += this.options.scroll.scrollTop + Position.deltaY;
        p.push(p[0]+this.options.scroll.offsetWidth);
        p.push(p[1]+this.options.scroll.offsetHeight);
      }
      var speed = [0,0];
      if(pointer[0] < (p[0]+this.options.scrollSensitivity)) speed[0] = pointer[0]-(p[0]+this.options.scrollSensitivity);
      if(pointer[1] < (p[1]+this.options.scrollSensitivity)) speed[1] = pointer[1]-(p[1]+this.options.scrollSensitivity);
      if(pointer[0] > (p[2]-this.options.scrollSensitivity)) speed[0] = pointer[0]-(p[2]-this.options.scrollSensitivity);
      if(pointer[1] > (p[3]-this.options.scrollSensitivity)) speed[1] = pointer[1]-(p[3]-this.options.scrollSensitivity);
      this.startScrolling(speed);
    }

    // fix AppleWebKit rendering
    if(Prototype.Browser.WebKit) window.scrollBy(0,0);

    Event.stop(event);
  },

  finishDrag: function(event, success) {
    this.dragging = false;

    if(this.options.quiet){
      Position.prepare();
      var pointer = [Event.pointerX(event), Event.pointerY(event)];
      Droppables.show(pointer, this.element);
    }

    if(this.options.ghosting) {
      if (!this._originallyAbsolute)
        Position.relativize(this.element);
      delete this._originallyAbsolute;
      Element.remove(this._clone);
      this._clone = null;
    }

    var dropped = false;
    if(success) {
      dropped = Droppables.fire(event, this.element);
      if (!dropped) dropped = false;
    }
    if(dropped && this.options.onDropped) this.options.onDropped(this.element);
    Draggables.notify('onEnd', this, event);

    var revert = this.options.revert;
    if(revert && Object.isFunction(revert)) revert = revert(this.element);

    var d = this.currentDelta();
    if(revert && this.options.reverteffect) {
      if (dropped == 0 || revert != 'failure')
        this.options.reverteffect(this.element,
          d[1]-this.delta[1], d[0]-this.delta[0]);
    } else {
      this.delta = d;
    }

    if(this.options.zindex)
      this.element.style.zIndex = this.originalZ;

    if(this.options.endeffect)
      this.options.endeffect(this.element);

    Draggables.deactivate(this);
    Droppables.reset();
  },

  keyPress: function(event) {
    if(event.keyCode!=Event.KEY_ESC) return;
    this.finishDrag(event, false);
    Event.stop(event);
  },

  endDrag: function(event) {
    if(!this.dragging) return;
    this.stopScrolling();
    this.finishDrag(event, true);
    Event.stop(event);
  },

  draw: function(point) {
    var pos = this.element.cumulativeOffset();
    if(this.options.ghosting) {
      var r   = Position.realOffset(this.element);
      pos[0] += r[0] - Position.deltaX; pos[1] += r[1] - Position.deltaY;
    }

    var d = this.currentDelta();
    pos[0] -= d[0]; pos[1] -= d[1];

    if(this.options.scroll && (this.options.scroll != window && this._isScrollChild)) {
      pos[0] -= this.options.scroll.scrollLeft-this.originalScrollLeft;
      pos[1] -= this.options.scroll.scrollTop-this.originalScrollTop;
    }

    var p = [0,1].map(function(i){
      return (point[i]-pos[i]-this.offset[i])
    }.bind(this));

    if(this.options.snap) {
      if(Object.isFunction(this.options.snap)) {
        p = this.options.snap(p[0],p[1],this);
      } else {
      if(Object.isArray(this.options.snap)) {
        p = p.map( function(v, i) {
          return (v/this.options.snap[i]).round()*this.options.snap[i] }.bind(this));
      } else {
        p = p.map( function(v) {
          return (v/this.options.snap).round()*this.options.snap }.bind(this));
      }
    }}

    var style = this.element.style;
    if((!this.options.constraint) || (this.options.constraint=='horizontal'))
      style.left = p[0] + "px";
    if((!this.options.constraint) || (this.options.constraint=='vertical'))
      style.top  = p[1] + "px";

    if(style.visibility=="hidden") style.visibility = ""; // fix gecko rendering
  },

  stopScrolling: function() {
    if(this.scrollInterval) {
      clearInterval(this.scrollInterval);
      this.scrollInterval = null;
      Draggables._lastScrollPointer = null;
    }
  },

  startScrolling: function(speed) {
    if(!(speed[0] || speed[1])) return;
    this.scrollSpeed = [speed[0]*this.options.scrollSpeed,speed[1]*this.options.scrollSpeed];
    this.lastScrolled = new Date();
    this.scrollInterval = setInterval(this.scroll.bind(this), 10);
  },

  scroll: function() {
    var current = new Date();
    var delta = current - this.lastScrolled;
    this.lastScrolled = current;
    if(this.options.scroll == window) {
      with (this._getWindowScroll(this.options.scroll)) {
        if (this.scrollSpeed[0] || this.scrollSpeed[1]) {
          var d = delta / 1000;
          this.options.scroll.scrollTo( left + d*this.scrollSpeed[0], top + d*this.scrollSpeed[1] );
        }
      }
    } else {
      this.options.scroll.scrollLeft += this.scrollSpeed[0] * delta / 1000;
      this.options.scroll.scrollTop  += this.scrollSpeed[1] * delta / 1000;
    }

    Position.prepare();
    Droppables.show(Draggables._lastPointer, this.element);
    Draggables.notify('onDrag', this);
    if (this._isScrollChild) {
      Draggables._lastScrollPointer = Draggables._lastScrollPointer || $A(Draggables._lastPointer);
      Draggables._lastScrollPointer[0] += this.scrollSpeed[0] * delta / 1000;
      Draggables._lastScrollPointer[1] += this.scrollSpeed[1] * delta / 1000;
      if (Draggables._lastScrollPointer[0] < 0)
        Draggables._lastScrollPointer[0] = 0;
      if (Draggables._lastScrollPointer[1] < 0)
        Draggables._lastScrollPointer[1] = 0;
      this.draw(Draggables._lastScrollPointer);
    }

    if(this.options.change) this.options.change(this);
  },

  _getWindowScroll: function(w) {
    var T, L, W, H;
    with (w.document) {
      if (w.document.documentElement && documentElement.scrollTop) {
        T = documentElement.scrollTop;
        L = documentElement.scrollLeft;
      } else if (w.document.body) {
        T = body.scrollTop;
        L = body.scrollLeft;
      }
      if (w.innerWidth) {
        W = w.innerWidth;
        H = w.innerHeight;
      } else if (w.document.documentElement && documentElement.clientWidth) {
        W = documentElement.clientWidth;
        H = documentElement.clientHeight;
      } else {
        W = body.offsetWidth;
        H = body.offsetHeight;
      }
    }
    return { top: T, left: L, width: W, height: H };
  }
});

Draggable._dragging = { };

/*--------------------------------------------------------------------------*/

var SortableObserver = Class.create({
  initialize: function(element, observer) {
    this.element   = $(element);
    this.observer  = observer;
    this.lastValue = Sortable.serialize(this.element);
  },

  onStart: function() {
    this.lastValue = Sortable.serialize(this.element);
  },

  onEnd: function() {
    Sortable.unmark();
    if(this.lastValue != Sortable.serialize(this.element))
      this.observer(this.element)
  }
});

var Sortable = {
  SERIALIZE_RULE: /^[^_\-](?:[A-Za-z0-9\-\_]*)[_](.*)$/,

  sortables: { },

  _findRootElement: function(element) {
    while (element.tagName.toUpperCase() != "BODY") {
      if(element.id && Sortable.sortables[element.id]) return element;
      element = element.parentNode;
    }
  },

  options: function(element) {
    element = Sortable._findRootElement($(element));
    if(!element) return;
    return Sortable.sortables[element.id];
  },

  destroy: function(element){
    element = $(element);
    var s = Sortable.sortables[element.id];

    if(s) {
      Draggables.removeObserver(s.element);
      s.droppables.each(function(d){ Droppables.remove(d) });
      s.draggables.invoke('destroy');

      delete Sortable.sortables[s.element.id];
    }
  },

  create: function(element) {
    element = $(element);
    var options = Object.extend({
      element:     element,
      tag:         'li',       // assumes li children, override with tag: 'tagname'
      dropOnEmpty: false,
      tree:        false,
      treeTag:     'ul',
      overlap:     'vertical', // one of 'vertical', 'horizontal'
      constraint:  'vertical', // one of 'vertical', 'horizontal', false
      containment: element,    // also takes array of elements (or id's); or false
      handle:      false,      // or a CSS class
      only:        false,
      delay:       0,
      hoverclass:  null,
      ghosting:    false,
      quiet:       false,
      scroll:      false,
      scrollSensitivity: 20,
      scrollSpeed: 15,
      format:      this.SERIALIZE_RULE,

      // these take arrays of elements or ids and can be
      // used for better initialization performance
      elements:    false,
      handles:     false,

      onChange:    Prototype.emptyFunction,
      onUpdate:    Prototype.emptyFunction
    }, arguments[1] || { });

    // clear any old sortable with same element
    this.destroy(element);

    // build options for the draggables
    var options_for_draggable = {
      revert:      true,
      quiet:       options.quiet,
      scroll:      options.scroll,
      scrollSpeed: options.scrollSpeed,
      scrollSensitivity: options.scrollSensitivity,
      delay:       options.delay,
      ghosting:    options.ghosting,
      constraint:  options.constraint,
      handle:      options.handle };

    if(options.starteffect)
      options_for_draggable.starteffect = options.starteffect;

    if(options.reverteffect)
      options_for_draggable.reverteffect = options.reverteffect;
    else
      if(options.ghosting) options_for_draggable.reverteffect = function(element) {
        element.style.top  = 0;
        element.style.left = 0;
      };

    if(options.endeffect)
      options_for_draggable.endeffect = options.endeffect;

    if(options.zindex)
      options_for_draggable.zindex = options.zindex;

    // build options for the droppables
    var options_for_droppable = {
      overlap:     options.overlap,
      containment: options.containment,
      tree:        options.tree,
      hoverclass:  options.hoverclass,
      onHover:     Sortable.onHover
    };

    var options_for_tree = {
      onHover:      Sortable.onEmptyHover,
      overlap:      options.overlap,
      containment:  options.containment,
      hoverclass:   options.hoverclass
    };

    // fix for gecko engine
    Element.cleanWhitespace(element);

    options.draggables = [];
    options.droppables = [];

    // drop on empty handling
    if(options.dropOnEmpty || options.tree) {
      Droppables.add(element, options_for_tree);
      options.droppables.push(element);
    }

    (options.elements || this.findElements(element, options) || []).each( function(e,i) {
      var handle = options.handles ? $(options.handles[i]) :
        (options.handle ? $(e).select('.' + options.handle)[0] : e);
      options.draggables.push(
        new Draggable(e, Object.extend(options_for_draggable, { handle: handle })));
      Droppables.add(e, options_for_droppable);
      if(options.tree) e.treeNode = element;
      options.droppables.push(e);
    });

    if(options.tree) {
      (Sortable.findTreeElements(element, options) || []).each( function(e) {
        Droppables.add(e, options_for_tree);
        e.treeNode = element;
        options.droppables.push(e);
      });
    }

    // keep reference
    this.sortables[element.identify()] = options;

    // for onupdate
    Draggables.addObserver(new SortableObserver(element, options.onUpdate));

  },

  // return all suitable-for-sortable elements in a guaranteed order
  findElements: function(element, options) {
    return Element.findChildren(
      element, options.only, options.tree ? true : false, options.tag);
  },

  findTreeElements: function(element, options) {
    return Element.findChildren(
      element, options.only, options.tree ? true : false, options.treeTag);
  },

  onHover: function(element, dropon, overlap) {
    if(Element.isParent(dropon, element)) return;

    if(overlap > .33 && overlap < .66 && Sortable.options(dropon).tree) {
      return;
    } else if(overlap>0.5) {
      Sortable.mark(dropon, 'before');
      if(dropon.previousSibling != element) {
        var oldParentNode = element.parentNode;
        element.style.visibility = "hidden"; // fix gecko rendering
        dropon.parentNode.insertBefore(element, dropon);
        if(dropon.parentNode!=oldParentNode)
          Sortable.options(oldParentNode).onChange(element);
        Sortable.options(dropon.parentNode).onChange(element);
      }
    } else {
      Sortable.mark(dropon, 'after');
      var nextElement = dropon.nextSibling || null;
      if(nextElement != element) {
        var oldParentNode = element.parentNode;
        element.style.visibility = "hidden"; // fix gecko rendering
        dropon.parentNode.insertBefore(element, nextElement);
        if(dropon.parentNode!=oldParentNode)
          Sortable.options(oldParentNode).onChange(element);
        Sortable.options(dropon.parentNode).onChange(element);
      }
    }
  },

  onEmptyHover: function(element, dropon, overlap) {
    var oldParentNode = element.parentNode;
    var droponOptions = Sortable.options(dropon);

    if(!Element.isParent(dropon, element)) {
      var index;

      var children = Sortable.findElements(dropon, {tag: droponOptions.tag, only: droponOptions.only});
      var child = null;

      if(children) {
        var offset = Element.offsetSize(dropon, droponOptions.overlap) * (1.0 - overlap);

        for (index = 0; index < children.length; index += 1) {
          if (offset - Element.offsetSize (children[index], droponOptions.overlap) >= 0) {
            offset -= Element.offsetSize (children[index], droponOptions.overlap);
          } else if (offset - (Element.offsetSize (children[index], droponOptions.overlap) / 2) >= 0) {
            child = index + 1 < children.length ? children[index + 1] : null;
            break;
          } else {
            child = children[index];
            break;
          }
        }
      }

      dropon.insertBefore(element, child);

      Sortable.options(oldParentNode).onChange(element);
      droponOptions.onChange(element);
    }
  },

  unmark: function() {
    if(Sortable._marker) Sortable._marker.hide();
  },

  mark: function(dropon, position) {
    // mark on ghosting only
    var sortable = Sortable.options(dropon.parentNode);
    if(sortable && !sortable.ghosting) return;

    if(!Sortable._marker) {
      Sortable._marker =
        ($('dropmarker') || Element.extend(document.createElement('DIV'))).
          hide().addClassName('dropmarker').setStyle({position:'absolute'});
      document.getElementsByTagName("body").item(0).appendChild(Sortable._marker);
    }
    var offsets = dropon.cumulativeOffset();
    Sortable._marker.setStyle({left: offsets[0]+'px', top: offsets[1] + 'px'});

    if(position=='after')
      if(sortable.overlap == 'horizontal')
        Sortable._marker.setStyle({left: (offsets[0]+dropon.clientWidth) + 'px'});
      else
        Sortable._marker.setStyle({top: (offsets[1]+dropon.clientHeight) + 'px'});

    Sortable._marker.show();
  },

  _tree: function(element, options, parent) {
    var children = Sortable.findElements(element, options) || [];

    for (var i = 0; i < children.length; ++i) {
      var match = children[i].id.match(options.format);

      if (!match) continue;

      var child = {
        id: encodeURIComponent(match ? match[1] : null),
        element: element,
        parent: parent,
        children: [],
        position: parent.children.length,
        container: $(children[i]).down(options.treeTag)
      };

      /* Get the element containing the children and recurse over it */
      if (child.container)
        this._tree(child.container, options, child);

      parent.children.push (child);
    }

    return parent;
  },

  tree: function(element) {
    element = $(element);
    var sortableOptions = this.options(element);
    var options = Object.extend({
      tag: sortableOptions.tag,
      treeTag: sortableOptions.treeTag,
      only: sortableOptions.only,
      name: element.id,
      format: sortableOptions.format
    }, arguments[1] || { });

    var root = {
      id: null,
      parent: null,
      children: [],
      container: element,
      position: 0
    };

    return Sortable._tree(element, options, root);
  },

  /* Construct a [i] index for a particular node */
  _constructIndex: function(node) {
    var index = '';
    do {
      if (node.id) index = '[' + node.position + ']' + index;
    } while ((node = node.parent) != null);
    return index;
  },

  sequence: function(element) {
    element = $(element);
    var options = Object.extend(this.options(element), arguments[1] || { });

    return $(this.findElements(element, options) || []).map( function(item) {
      return item.id.match(options.format) ? item.id.match(options.format)[1] : '';
    });
  },

  setSequence: function(element, new_sequence) {
    element = $(element);
    var options = Object.extend(this.options(element), arguments[2] || { });

    var nodeMap = { };
    this.findElements(element, options).each( function(n) {
        if (n.id.match(options.format))
            nodeMap[n.id.match(options.format)[1]] = [n, n.parentNode];
        n.parentNode.removeChild(n);
    });

    new_sequence.each(function(ident) {
      var n = nodeMap[ident];
      if (n) {
        n[1].appendChild(n[0]);
        delete nodeMap[ident];
      }
    });
  },

  serialize: function(element) {
    element = $(element);
    var options = Object.extend(Sortable.options(element), arguments[1] || { });
    var name = encodeURIComponent(
      (arguments[1] && arguments[1].name) ? arguments[1].name : element.id);

    if (options.tree) {
      return Sortable.tree(element, arguments[1]).children.map( function (item) {
        return [name + Sortable._constructIndex(item) + "[id]=" +
                encodeURIComponent(item.id)].concat(item.children.map(arguments.callee));
      }).flatten().join('&');
    } else {
      return Sortable.sequence(element, arguments[1]).map( function(item) {
        return name + "[]=" + encodeURIComponent(item);
      }).join('&');
    }
  }
};

// Returns true if child is contained within element
Element.isParent = function(child, element) {
  if (!child.parentNode || child == element) return false;
  if (child.parentNode == element) return true;
  return Element.isParent(child.parentNode, element);
};

Element.findChildren = function(element, only, recursive, tagName) {
  if(!element.hasChildNodes()) return null;
  tagName = tagName.toUpperCase();
  if(only) only = [only].flatten();
  var elements = [];
  $A(element.childNodes).each( function(e) {
    if(e.tagName && e.tagName.toUpperCase()==tagName &&
      (!only || (Element.classNames(e).detect(function(v) { return only.include(v) }))))
        elements.push(e);
    if(recursive) {
      var grandchildren = Element.findChildren(e, only, recursive, tagName);
      if(grandchildren) elements.push(grandchildren);
    }
  });

  return (elements.length>0 ? elements.flatten() : []);
};

Element.offsetSize = function (element, type) {
  return element['offset' + ((type=='vertical' || type=='height') ? 'Height' : 'Width')];
};


/* FILE: controls.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
// script.aculo.us controls.js v1.8.2, Tue Nov 18 18:30:58 +0100 2008

// Copyright (c) 2005-2008 Thomas Fuchs (http://script.aculo.us, http://mir.aculo.us)
//           (c) 2005-2008 Ivan Krstic (http://blogs.law.harvard.edu/ivan)
//           (c) 2005-2008 Jon Tirsen (http://www.tirsen.com)
// Contributors:
//  Richard Livsey
//  Rahul Bhargava
//  Rob Wills
//
// script.aculo.us is freely distributable under the terms of an MIT-style license.
// For details, see the script.aculo.us web site: http://script.aculo.us/

// Autocompleter.Base handles all the autocompletion functionality
// that's independent of the data source for autocompletion. This
// includes drawing the autocompletion menu, observing keyboard
// and mouse events, and similar.
//
// Specific autocompleters need to provide, at the very least,
// a getUpdatedChoices function that will be invoked every time
// the text inside the monitored textbox changes. This method
// should get the text for which to provide autocompletion by
// invoking this.getToken(), NOT by directly accessing
// this.element.value. This is to allow incremental tokenized
// autocompletion. Specific auto-completion logic (AJAX, etc)
// belongs in getUpdatedChoices.
//
// Tokenized incremental autocompletion is enabled automatically
// when an autocompleter is instantiated with the 'tokens' option
// in the options parameter, e.g.:
// new Ajax.Autocompleter('id','upd', '/url/', { tokens: ',' });
// will incrementally autocomplete with a comma as the token.
// Additionally, ',' in the above example can be replaced with
// a token array, e.g. { tokens: [',', '\n'] } which
// enables autocompletion on multiple tokens. This is most
// useful when one of the tokens is \n (a newline), as it
// allows smart autocompletion after linebreaks.

if(typeof Effect == 'undefined')
  throw("controls.js requires including script.aculo.us' effects.js library");

var Autocompleter = { };
Autocompleter.Base = Class.create({
  baseInitialize: function(element, update, options) {
    element          = $(element);
    this.element     = element;
    this.update      = $(update);
    this.hasFocus    = false;
    this.changed     = false;
    this.active      = false;
    this.index       = 0;
    this.entryCount  = 0;
    this.oldElementValue = this.element.value;

    if(this.setOptions)
      this.setOptions(options);
    else
      this.options = options || { };

    this.options.paramName    = this.options.paramName || this.element.name;
    this.options.tokens       = this.options.tokens || [];
    this.options.frequency    = this.options.frequency || 0.4;
    this.options.minChars     = this.options.minChars || 1;
    this.options.onShow       = this.options.onShow ||
      function(element, update){
        if(!update.style.position || update.style.position=='absolute') {
          update.style.position = 'absolute';
          Position.clone(element, update, {
            setHeight: false,
            offsetTop: element.offsetHeight
          });
        }
        Effect.Appear(update,{duration:0.15});
      };
    this.options.onHide = this.options.onHide ||
      function(element, update){ new Effect.Fade(update,{duration:0.15}) };

    if(typeof(this.options.tokens) == 'string')
      this.options.tokens = new Array(this.options.tokens);
    // Force carriage returns as token delimiters anyway
    if (!this.options.tokens.include('\n'))
      this.options.tokens.push('\n');

    this.observer = null;

    this.element.setAttribute('autocomplete','off');

    Element.hide(this.update);

    Event.observe(this.element, 'blur', this.onBlur.bindAsEventListener(this));
    Event.observe(this.element, 'keydown', this.onKeyPress.bindAsEventListener(this));
  },

  show: function() {
    if(Element.getStyle(this.update, 'display')=='none') this.options.onShow(this.element, this.update);
    if(!this.iefix &&
      (Prototype.Browser.IE) &&
      (Element.getStyle(this.update, 'position')=='absolute')) {
      new Insertion.After(this.update,
       '<iframe id="' + this.update.id + '_iefix" '+
       'style="display:none;position:absolute;filter:progid:DXImageTransform.Microsoft.Alpha(opacity=0);" ' +
       'src="javascript:false;" frameborder="0" scrolling="no"></iframe>');
      this.iefix = $(this.update.id+'_iefix');
    }
    if(this.iefix) setTimeout(this.fixIEOverlapping.bind(this), 50);
  },

  fixIEOverlapping: function() {
    Position.clone(this.update, this.iefix, {setTop:(!this.update.style.height)});
    this.iefix.style.zIndex = 1;
    this.update.style.zIndex = 2;
    Element.show(this.iefix);
  },

  hide: function() {
    this.stopIndicator();
    if(Element.getStyle(this.update, 'display')!='none') this.options.onHide(this.element, this.update);
    if(this.iefix) Element.hide(this.iefix);
  },

  startIndicator: function() {
    if(this.options.indicator) Element.show(this.options.indicator);
  },

  stopIndicator: function() {
    if(this.options.indicator) Element.hide(this.options.indicator);
  },

  onKeyPress: function(event) {
    if(this.active)
      switch(event.keyCode) {
       case Event.KEY_TAB:
       case Event.KEY_RETURN:
         this.selectEntry();
         Event.stop(event);
       case Event.KEY_ESC:
         this.hide();
         this.active = false;
         Event.stop(event);
         return;
       case Event.KEY_LEFT:
       case Event.KEY_RIGHT:
         return;
       case Event.KEY_UP:
         this.markPrevious();
         this.render();
         Event.stop(event);
         return;
       case Event.KEY_DOWN:
         this.markNext();
         this.render();
         Event.stop(event);
         return;
      }
     else
       if(event.keyCode==Event.KEY_TAB || event.keyCode==Event.KEY_RETURN ||
         (Prototype.Browser.WebKit > 0 && event.keyCode == 0)) return;

    this.changed = true;
    this.hasFocus = true;

    if(this.observer) clearTimeout(this.observer);
      this.observer =
        setTimeout(this.onObserverEvent.bind(this), this.options.frequency*1000);
  },

  activate: function() {
    this.changed = false;
    this.hasFocus = true;
    this.getUpdatedChoices();
  },

  onHover: function(event) {
    var element = Event.findElement(event, 'LI');
    if(this.index != element.autocompleteIndex)
    {
        this.index = element.autocompleteIndex;
        this.render();
    }
    Event.stop(event);
  },

  onClick: function(event) {
    var element = Event.findElement(event, 'LI');
    this.index = element.autocompleteIndex;
    this.selectEntry();
    this.hide();
  },

  onBlur: function(event) {
    // needed to make click events working
    setTimeout(this.hide.bind(this), 250);
    this.hasFocus = false;
    this.active = false;
  },

  render: function() {
    if(this.entryCount > 0) {
      for (var i = 0; i < this.entryCount; i++)
        this.index==i ?
          Element.addClassName(this.getEntry(i),"selected") :
          Element.removeClassName(this.getEntry(i),"selected");
      if(this.hasFocus) {
        this.show();
        this.active = true;
      }
    } else {
      this.active = false;
      this.hide();
    }
  },

  markPrevious: function() {
    if(this.index > 0) this.index--;
      else this.index = this.entryCount-1;
    //this.getEntry(this.index).scrollIntoView(true); useless
  },

  markNext: function() {
    if(this.index < this.entryCount-1) this.index++;
      else this.index = 0;
    this.getEntry(this.index).scrollIntoView(false);
  },

  getEntry: function(index) {
    return this.update.firstChild.childNodes[index];
  },

  getCurrentEntry: function() {
    return this.getEntry(this.index);
  },

  selectEntry: function() {
    this.active = false;
    this.updateElement(this.getCurrentEntry());
  },

  updateElement: function(selectedElement) {
    if (this.options.updateElement) {
      this.options.updateElement(selectedElement);
      return;
    }
    var value = '';
    if (this.options.select) {
      var nodes = $(selectedElement).select('.' + this.options.select) || [];
      if(nodes.length>0) value = Element.collectTextNodes(nodes[0], this.options.select);
    } else
      value = Element.collectTextNodesIgnoreClass(selectedElement, 'informal');

    var bounds = this.getTokenBounds();
    if (bounds[0] != -1) {
      var newValue = this.element.value.substr(0, bounds[0]);
      var whitespace = this.element.value.substr(bounds[0]).match(/^\s+/);
      if (whitespace)
        newValue += whitespace[0];
      this.element.value = newValue + value + this.element.value.substr(bounds[1]);
    } else {
      this.element.value = value;
    }
    this.oldElementValue = this.element.value;
    this.element.focus();

    if (this.options.afterUpdateElement)
      this.options.afterUpdateElement(this.element, selectedElement);
  },

  updateChoices: function(choices) {
    if(!this.changed && this.hasFocus) {
      this.update.innerHTML = choices;
      Element.cleanWhitespace(this.update);
      Element.cleanWhitespace(this.update.down());

      if(this.update.firstChild && this.update.down().childNodes) {
        this.entryCount =
          this.update.down().childNodes.length;
        for (var i = 0; i < this.entryCount; i++) {
          var entry = this.getEntry(i);
          entry.autocompleteIndex = i;
          this.addObservers(entry);
        }
      } else {
        this.entryCount = 0;
      }

      this.stopIndicator();
      this.index = 0;

      if(this.entryCount==1 && this.options.autoSelect) {
        this.selectEntry();
        this.hide();
      } else {
        this.render();
      }
    }
  },

  addObservers: function(element) {
    Event.observe(element, "mouseover", this.onHover.bindAsEventListener(this));
    Event.observe(element, "click", this.onClick.bindAsEventListener(this));
  },

  onObserverEvent: function() {
    this.changed = false;
    this.tokenBounds = null;
    if(this.getToken().length>=this.options.minChars) {
      this.getUpdatedChoices();
    } else {
      this.active = false;
      this.hide();
    }
    this.oldElementValue = this.element.value;
  },

  getToken: function() {
    var bounds = this.getTokenBounds();
    return this.element.value.substring(bounds[0], bounds[1]).strip();
  },

  getTokenBounds: function() {
    if (null != this.tokenBounds) return this.tokenBounds;
    var value = this.element.value;
    if (value.strip().empty()) return [-1, 0];
    var diff = arguments.callee.getFirstDifferencePos(value, this.oldElementValue);
    var offset = (diff == this.oldElementValue.length ? 1 : 0);
    var prevTokenPos = -1, nextTokenPos = value.length;
    var tp;
    for (var index = 0, l = this.options.tokens.length; index < l; ++index) {
      tp = value.lastIndexOf(this.options.tokens[index], diff + offset - 1);
      if (tp > prevTokenPos) prevTokenPos = tp;
      tp = value.indexOf(this.options.tokens[index], diff + offset);
      if (-1 != tp && tp < nextTokenPos) nextTokenPos = tp;
    }
    return (this.tokenBounds = [prevTokenPos + 1, nextTokenPos]);
  }
});

Autocompleter.Base.prototype.getTokenBounds.getFirstDifferencePos = function(newS, oldS) {
  var boundary = Math.min(newS.length, oldS.length);
  for (var index = 0; index < boundary; ++index)
    if (newS[index] != oldS[index])
      return index;
  return boundary;
};

Ajax.Autocompleter = Class.create(Autocompleter.Base, {
  initialize: function(element, update, url, options) {
    this.baseInitialize(element, update, options);
    this.options.asynchronous  = true;
    this.options.onComplete    = this.onComplete.bind(this);
    this.options.defaultParams = this.options.parameters || null;
    this.url                   = url;
  },

  getUpdatedChoices: function() {
    this.startIndicator();

    var entry = encodeURIComponent(this.options.paramName) + '=' +
      encodeURIComponent(this.getToken());

    this.options.parameters = this.options.callback ?
      this.options.callback(this.element, entry) : entry;

    if(this.options.defaultParams)
      this.options.parameters += '&' + this.options.defaultParams;

    new Ajax.Request(this.url, this.options);
  },

  onComplete: function(request) {
    this.updateChoices(request.responseText);
  }
});

// The local array autocompleter. Used when you'd prefer to
// inject an array of autocompletion options into the page, rather
// than sending out Ajax queries, which can be quite slow sometimes.
//
// The constructor takes four parameters. The first two are, as usual,
// the id of the monitored textbox, and id of the autocompletion menu.
// The third is the array you want to autocomplete from, and the fourth
// is the options block.
//
// Extra local autocompletion options:
// - choices - How many autocompletion choices to offer
//
// - partialSearch - If false, the autocompleter will match entered
//                    text only at the beginning of strings in the
//                    autocomplete array. Defaults to true, which will
//                    match text at the beginning of any *word* in the
//                    strings in the autocomplete array. If you want to
//                    search anywhere in the string, additionally set
//                    the option fullSearch to true (default: off).
//
// - fullSsearch - Search anywhere in autocomplete array strings.
//
// - partialChars - How many characters to enter before triggering
//                   a partial match (unlike minChars, which defines
//                   how many characters are required to do any match
//                   at all). Defaults to 2.
//
// - ignoreCase - Whether to ignore case when autocompleting.
//                 Defaults to true.
//
// It's possible to pass in a custom function as the 'selector'
// option, if you prefer to write your own autocompletion logic.
// In that case, the other options above will not apply unless
// you support them.

Autocompleter.Local = Class.create(Autocompleter.Base, {
  initialize: function(element, update, array, options) {
    this.baseInitialize(element, update, options);
    this.options.array = array;
  },

  getUpdatedChoices: function() {
    this.updateChoices(this.options.selector(this));
  },

  setOptions: function(options) {
    this.options = Object.extend({
      choices: 10,
      partialSearch: true,
      partialChars: 2,
      ignoreCase: true,
      fullSearch: false,
      selector: function(instance) {
        var ret       = []; // Beginning matches
        var partial   = []; // Inside matches
        var entry     = instance.getToken();
        var count     = 0;

        for (var i = 0; i < instance.options.array.length &&
          ret.length < instance.options.choices ; i++) {

          var elem = instance.options.array[i];
          var foundPos = instance.options.ignoreCase ?
            elem.toLowerCase().indexOf(entry.toLowerCase()) :
            elem.indexOf(entry);

          while (foundPos != -1) {
            if (foundPos == 0 && elem.length != entry.length) {
              ret.push("<li><strong>" + elem.substr(0, entry.length) + "</strong>" +
                elem.substr(entry.length) + "</li>");
              break;
            } else if (entry.length >= instance.options.partialChars &&
              instance.options.partialSearch && foundPos != -1) {
              if (instance.options.fullSearch || /\s/.test(elem.substr(foundPos-1,1))) {
                partial.push("<li>" + elem.substr(0, foundPos) + "<strong>" +
                  elem.substr(foundPos, entry.length) + "</strong>" + elem.substr(
                  foundPos + entry.length) + "</li>");
                break;
              }
            }

            foundPos = instance.options.ignoreCase ?
              elem.toLowerCase().indexOf(entry.toLowerCase(), foundPos + 1) :
              elem.indexOf(entry, foundPos + 1);

          }
        }
        if (partial.length)
          ret = ret.concat(partial.slice(0, instance.options.choices - ret.length));
        return "<ul>" + ret.join('') + "</ul>";
      }
    }, options || { });
  }
});

// AJAX in-place editor and collection editor
// Full rewrite by Christophe Porteneuve <<EMAIL>> (April 2007).

// Use this if you notice weird scrolling problems on some browsers,
// the DOM might be a bit confused when this gets called so do this
// waits 1 ms (with setTimeout) until it does the activation
Field.scrollFreeActivate = function(field) {
  setTimeout(function() {
    Field.activate(field);
  }, 1);
};

Ajax.InPlaceEditor = Class.create({
  initialize: function(element, url, options) {
    this.url = url;
    this.element = element = $(element);
    this.prepareOptions();
    this._controls = { };
    arguments.callee.dealWithDeprecatedOptions(options); // DEPRECATION LAYER!!!
    Object.extend(this.options, options || { });
    if (!this.options.formId && this.element.id) {
      this.options.formId = this.element.id + '-inplaceeditor';
      if ($(this.options.formId))
        this.options.formId = '';
    }
    if (this.options.externalControl)
      this.options.externalControl = $(this.options.externalControl);
    if (!this.options.externalControl)
      this.options.externalControlOnly = false;
    this._originalBackground = this.element.getStyle('background-color') || 'transparent';
    this.element.title = this.options.clickToEditText;
    this._boundCancelHandler = this.handleFormCancellation.bind(this);
    this._boundComplete = (this.options.onComplete || Prototype.emptyFunction).bind(this);
    this._boundFailureHandler = this.handleAJAXFailure.bind(this);
    this._boundSubmitHandler = this.handleFormSubmission.bind(this);
    this._boundWrapperHandler = this.wrapUp.bind(this);
    this.registerListeners();
  },
  checkForEscapeOrReturn: function(e) {
    if (!this._editing || e.ctrlKey || e.altKey || e.shiftKey) return;
    if (Event.KEY_ESC == e.keyCode)
      this.handleFormCancellation(e);
    else if (Event.KEY_RETURN == e.keyCode)
      this.handleFormSubmission(e);
  },
  createControl: function(mode, handler, extraClasses) {
    var control = this.options[mode + 'Control'];
    var text = this.options[mode + 'Text'];
    if ('button' == control) {
      var btn = document.createElement('input');
      btn.type = 'submit';
      btn.value = text;
      btn.className = 'editor_' + mode + '_button';
      if ('cancel' == mode)
        btn.onclick = this._boundCancelHandler;
      this._form.appendChild(btn);
      this._controls[mode] = btn;
    } else if ('link' == control) {
      var link = document.createElement('a');
      link.href = '#';
      link.appendChild(document.createTextNode(text));
      link.onclick = 'cancel' == mode ? this._boundCancelHandler : this._boundSubmitHandler;
      link.className = 'editor_' + mode + '_link';
      if (extraClasses)
        link.className += ' ' + extraClasses;
      this._form.appendChild(link);
      this._controls[mode] = link;
    }
  },
  createEditField: function() {
    var text = (this.options.loadTextURL ? this.options.loadingText : this.getText());
    var fld;
    if (1 >= this.options.rows && !/\r|\n/.test(this.getText())) {
      fld = document.createElement('input');
      fld.type = 'text';
      var size = this.options.size || this.options.cols || 0;
      if (0 < size) fld.size = size;
    } else {
      fld = document.createElement('textarea');
      fld.rows = (1 >= this.options.rows ? this.options.autoRows : this.options.rows);
      fld.cols = this.options.cols || 40;
    }
    fld.name = this.options.paramName;
    fld.value = text; // No HTML breaks conversion anymore
    fld.className = 'editor_field';
    if (this.options.submitOnBlur)
      fld.onblur = this._boundSubmitHandler;
    this._controls.editor = fld;
    if (this.options.loadTextURL)
      this.loadExternalText();
    this._form.appendChild(this._controls.editor);
  },
  createForm: function() {
    var ipe = this;
    function addText(mode, condition) {
      var text = ipe.options['text' + mode + 'Controls'];
      if (!text || condition === false) return;
      ipe._form.appendChild(document.createTextNode(text));
    };
    this._form = $(document.createElement('form'));
    this._form.id = this.options.formId;
    this._form.addClassName(this.options.formClassName);
    this._form.onsubmit = this._boundSubmitHandler;
    this.createEditField();
    if ('textarea' == this._controls.editor.tagName.toLowerCase())
      this._form.appendChild(document.createElement('br'));
    if (this.options.onFormCustomization)
      this.options.onFormCustomization(this, this._form);
    addText('Before', this.options.okControl || this.options.cancelControl);
    this.createControl('ok', this._boundSubmitHandler);
    addText('Between', this.options.okControl && this.options.cancelControl);
    this.createControl('cancel', this._boundCancelHandler, 'editor_cancel');
    addText('After', this.options.okControl || this.options.cancelControl);
  },
  destroy: function() {
    if (this._oldInnerHTML)
      this.element.innerHTML = this._oldInnerHTML;
    this.leaveEditMode();
    this.unregisterListeners();
  },
  enterEditMode: function(e) {
    if (this._saving || this._editing) return;
    this._editing = true;
    this.triggerCallback('onEnterEditMode');
    if (this.options.externalControl)
      this.options.externalControl.hide();
    this.element.hide();
    this.createForm();
    this.element.parentNode.insertBefore(this._form, this.element);
    if (!this.options.loadTextURL)
      this.postProcessEditField();
    if (e) Event.stop(e);
  },
  enterHover: function(e) {
    if (this.options.hoverClassName)
      this.element.addClassName(this.options.hoverClassName);
    if (this._saving) return;
    this.triggerCallback('onEnterHover');
  },
  getText: function() {
    return this.element.innerHTML.unescapeHTML();
  },
  handleAJAXFailure: function(transport) {
    this.triggerCallback('onFailure', transport);
    if (this._oldInnerHTML) {
      this.element.innerHTML = this._oldInnerHTML;
      this._oldInnerHTML = null;
    }
  },
  handleFormCancellation: function(e) {
    this.wrapUp();
    if (e) Event.stop(e);
  },
  handleFormSubmission: function(e) {
    var form = this._form;
    var value = $F(this._controls.editor);
    this.prepareSubmission();
    var params = this.options.callback(form, value) || '';
    if (Object.isString(params))
      params = params.toQueryParams();
    params.editorId = this.element.id;
    if (this.options.htmlResponse) {
      var options = Object.extend({ evalScripts: true }, this.options.ajaxOptions);
      Object.extend(options, {
        parameters: params,
        onComplete: this._boundWrapperHandler,
        onFailure: this._boundFailureHandler
      });
      new Ajax.Updater({ success: this.element }, this.url, options);
    } else {
      var options = Object.extend({ method: 'get' }, this.options.ajaxOptions);
      Object.extend(options, {
        parameters: params,
        onComplete: this._boundWrapperHandler,
        onFailure: this._boundFailureHandler
      });
      new Ajax.Request(this.url, options);
    }
    if (e) Event.stop(e);
  },
  leaveEditMode: function() {
    this.element.removeClassName(this.options.savingClassName);
    this.removeForm();
    this.leaveHover();
    this.element.style.backgroundColor = this._originalBackground;
    this.element.show();
    if (this.options.externalControl)
      this.options.externalControl.show();
    this._saving = false;
    this._editing = false;
    this._oldInnerHTML = null;
    this.triggerCallback('onLeaveEditMode');
  },
  leaveHover: function(e) {
    if (this.options.hoverClassName)
      this.element.removeClassName(this.options.hoverClassName);
    if (this._saving) return;
    this.triggerCallback('onLeaveHover');
  },
  loadExternalText: function() {
    this._form.addClassName(this.options.loadingClassName);
    this._controls.editor.disabled = true;
    var options = Object.extend({ method: 'get' }, this.options.ajaxOptions);
    Object.extend(options, {
      parameters: 'editorId=' + encodeURIComponent(this.element.id),
      onComplete: Prototype.emptyFunction,
      onSuccess: function(transport) {
        this._form.removeClassName(this.options.loadingClassName);
        var text = transport.responseText;
        if (this.options.stripLoadedTextTags)
          text = text.stripTags();
        this._controls.editor.value = text;
        this._controls.editor.disabled = false;
        this.postProcessEditField();
      }.bind(this),
      onFailure: this._boundFailureHandler
    });
    new Ajax.Request(this.options.loadTextURL, options);
  },
  postProcessEditField: function() {
    var fpc = this.options.fieldPostCreation;
    if (fpc)
      $(this._controls.editor)['focus' == fpc ? 'focus' : 'activate']();
  },
  prepareOptions: function() {
    this.options = Object.clone(Ajax.InPlaceEditor.DefaultOptions);
    Object.extend(this.options, Ajax.InPlaceEditor.DefaultCallbacks);
    [this._extraDefaultOptions].flatten().compact().each(function(defs) {
      Object.extend(this.options, defs);
    }.bind(this));
  },
  prepareSubmission: function() {
    this._saving = true;
    this.removeForm();
    this.leaveHover();
    this.showSaving();
  },
  registerListeners: function() {
    this._listeners = { };
    var listener;
    $H(Ajax.InPlaceEditor.Listeners).each(function(pair) {
      listener = this[pair.value].bind(this);
      this._listeners[pair.key] = listener;
      if (!this.options.externalControlOnly)
        this.element.observe(pair.key, listener);
      if (this.options.externalControl)
        this.options.externalControl.observe(pair.key, listener);
    }.bind(this));
  },
  removeForm: function() {
    if (!this._form) return;
    this._form.remove();
    this._form = null;
    this._controls = { };
  },
  showSaving: function() {
    this._oldInnerHTML = this.element.innerHTML;
    this.element.innerHTML = this.options.savingText;
    this.element.addClassName(this.options.savingClassName);
    this.element.style.backgroundColor = this._originalBackground;
    this.element.show();
  },
  triggerCallback: function(cbName, arg) {
    if ('function' == typeof this.options[cbName]) {
      this.options[cbName](this, arg);
    }
  },
  unregisterListeners: function() {
    $H(this._listeners).each(function(pair) {
      if (!this.options.externalControlOnly)
        this.element.stopObserving(pair.key, pair.value);
      if (this.options.externalControl)
        this.options.externalControl.stopObserving(pair.key, pair.value);
    }.bind(this));
  },
  wrapUp: function(transport) {
    this.leaveEditMode();
    // Can't use triggerCallback due to backward compatibility: requires
    // binding + direct element
    this._boundComplete(transport, this.element);
  }
});

Object.extend(Ajax.InPlaceEditor.prototype, {
  dispose: Ajax.InPlaceEditor.prototype.destroy
});

Ajax.InPlaceCollectionEditor = Class.create(Ajax.InPlaceEditor, {
  initialize: function($super, element, url, options) {
    this._extraDefaultOptions = Ajax.InPlaceCollectionEditor.DefaultOptions;
    $super(element, url, options);
  },

  createEditField: function() {
    var list = document.createElement('select');
    list.name = this.options.paramName;
    list.size = 1;
    this._controls.editor = list;
    this._collection = this.options.collection || [];
    if (this.options.loadCollectionURL)
      this.loadCollection();
    else
      this.checkForExternalText();
    this._form.appendChild(this._controls.editor);
  },

  loadCollection: function() {
    this._form.addClassName(this.options.loadingClassName);
    this.showLoadingText(this.options.loadingCollectionText);
    var options = Object.extend({ method: 'get' }, this.options.ajaxOptions);
    Object.extend(options, {
      parameters: 'editorId=' + encodeURIComponent(this.element.id),
      onComplete: Prototype.emptyFunction,
      onSuccess: function(transport) {
        var js = transport.responseText.strip();
        if (!/^\[.*\]$/.test(js)) // TODO: improve sanity check
          throw('Server returned an invalid collection representation.');
        this._collection = eval(js);
        this.checkForExternalText();
      }.bind(this),
      onFailure: this.onFailure
    });
    new Ajax.Request(this.options.loadCollectionURL, options);
  },

  showLoadingText: function(text) {
    this._controls.editor.disabled = true;
    var tempOption = this._controls.editor.firstChild;
    if (!tempOption) {
      tempOption = document.createElement('option');
      tempOption.value = '';
      this._controls.editor.appendChild(tempOption);
      tempOption.selected = true;
    }
    tempOption.update((text || '').stripScripts().stripTags());
  },

  checkForExternalText: function() {
    this._text = this.getText();
    if (this.options.loadTextURL)
      this.loadExternalText();
    else
      this.buildOptionList();
  },

  loadExternalText: function() {
    this.showLoadingText(this.options.loadingText);
    var options = Object.extend({ method: 'get' }, this.options.ajaxOptions);
    Object.extend(options, {
      parameters: 'editorId=' + encodeURIComponent(this.element.id),
      onComplete: Prototype.emptyFunction,
      onSuccess: function(transport) {
        this._text = transport.responseText.strip();
        this.buildOptionList();
      }.bind(this),
      onFailure: this.onFailure
    });
    new Ajax.Request(this.options.loadTextURL, options);
  },

  buildOptionList: function() {
    this._form.removeClassName(this.options.loadingClassName);
    this._collection = this._collection.map(function(entry) {
      return 2 === entry.length ? entry : [entry, entry].flatten();
    });
    var marker = ('value' in this.options) ? this.options.value : this._text;
    var textFound = this._collection.any(function(entry) {
      return entry[0] == marker;
    }.bind(this));
    this._controls.editor.update('');
    var option;
    this._collection.each(function(entry, index) {
      option = document.createElement('option');
      option.value = entry[0];
      option.selected = textFound ? entry[0] == marker : 0 == index;
      option.appendChild(document.createTextNode(entry[1]));
      this._controls.editor.appendChild(option);
    }.bind(this));
    this._controls.editor.disabled = false;
    Field.scrollFreeActivate(this._controls.editor);
  }
});

//**** DEPRECATION LAYER FOR InPlace[Collection]Editor! ****
//**** This only  exists for a while,  in order to  let ****
//**** users adapt to  the new API.  Read up on the new ****
//**** API and convert your code to it ASAP!            ****

Ajax.InPlaceEditor.prototype.initialize.dealWithDeprecatedOptions = function(options) {
  if (!options) return;
  function fallback(name, expr) {
    if (name in options || expr === undefined) return;
    options[name] = expr;
  };
  fallback('cancelControl', (options.cancelLink ? 'link' : (options.cancelButton ? 'button' :
    options.cancelLink == options.cancelButton == false ? false : undefined)));
  fallback('okControl', (options.okLink ? 'link' : (options.okButton ? 'button' :
    options.okLink == options.okButton == false ? false : undefined)));
  fallback('highlightColor', options.highlightcolor);
  fallback('highlightEndColor', options.highlightendcolor);
};

Object.extend(Ajax.InPlaceEditor, {
  DefaultOptions: {
    ajaxOptions: { },
    autoRows: 3,                                // Use when multi-line w/ rows == 1
    cancelControl: 'link',                      // 'link'|'button'|false
    cancelText: 'cancel',
    clickToEditText: 'Click to edit',
    externalControl: null,                      // id|elt
    externalControlOnly: false,
    fieldPostCreation: 'activate',              // 'activate'|'focus'|false
    formClassName: 'inplaceeditor-form',
    formId: null,                               // id|elt
    highlightColor: '#ffff99',
    highlightEndColor: '#ffffff',
    hoverClassName: '',
    htmlResponse: true,
    loadingClassName: 'inplaceeditor-loading',
    loadingText: 'Loading...',
    okControl: 'button',                        // 'link'|'button'|false
    okText: 'ok',
    paramName: 'value',
    rows: 1,                                    // If 1 and multi-line, uses autoRows
    savingClassName: 'inplaceeditor-saving',
    savingText: 'Saving...',
    size: 0,
    stripLoadedTextTags: false,
    submitOnBlur: false,
    textAfterControls: '',
    textBeforeControls: '',
    textBetweenControls: ''
  },
  DefaultCallbacks: {
    callback: function(form) {
      return Form.serialize(form);
    },
    onComplete: function(transport, element) {
      // For backward compatibility, this one is bound to the IPE, and passes
      // the element directly.  It was too often customized, so we don't break it.
      new Effect.Highlight(element, {
        startcolor: this.options.highlightColor, keepBackgroundImage: true });
    },
    onEnterEditMode: null,
    onEnterHover: function(ipe) {
      ipe.element.style.backgroundColor = ipe.options.highlightColor;
      if (ipe._effect)
        ipe._effect.cancel();
    },
    onFailure: function(transport, ipe) {
      alert('Error communication with the server: ' + transport.responseText.stripTags());
    },
    onFormCustomization: null, // Takes the IPE and its generated form, after editor, before controls.
    onLeaveEditMode: null,
    onLeaveHover: function(ipe) {
      ipe._effect = new Effect.Highlight(ipe.element, {
        startcolor: ipe.options.highlightColor, endcolor: ipe.options.highlightEndColor,
        restorecolor: ipe._originalBackground, keepBackgroundImage: true
      });
    }
  },
  Listeners: {
    click: 'enterEditMode',
    keydown: 'checkForEscapeOrReturn',
    mouseover: 'enterHover',
    mouseout: 'leaveHover'
  }
});

Ajax.InPlaceCollectionEditor.DefaultOptions = {
  loadingCollectionText: 'Loading options...'
};

// Delayed observer, like Form.Element.Observer,
// but waits for delay after last key input
// Ideal for live-search fields

Form.Element.DelayedObserver = Class.create({
  initialize: function(element, delay, callback) {
    this.delay     = delay || 0.5;
    this.element   = $(element);
    this.callback  = callback;
    this.timer     = null;
    this.lastValue = $F(this.element);
    Event.observe(this.element,'keyup',this.delayedListener.bindAsEventListener(this));
  },
  delayedListener: function(event) {
    if(this.lastValue == $F(this.element)) return;
    if(this.timer) clearTimeout(this.timer);
    this.timer = setTimeout(this.onTimerEvent.bind(this), this.delay * 1000);
    this.lastValue = $F(this.element);
  },
  onTimerEvent: function() {
    this.timer = null;
    this.callback(this.element, $F(this.element));
  }
});


/* FILE: slider.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
// script.aculo.us slider.js v1.8.2, Tue Nov 18 18:30:58 +0100 2008

// Copyright (c) 2005-2008 Marty Haught, Thomas Fuchs
//
// script.aculo.us is freely distributable under the terms of an MIT-style license.
// For details, see the script.aculo.us web site: http://script.aculo.us/

if (!Control) var Control = { };

// options:
//  axis: 'vertical', or 'horizontal' (default)
//
// callbacks:
//  onChange(value)
//  onSlide(value)
Control.Slider = Class.create({
  initialize: function(handle, track, options) {
    var slider = this;

    if (Object.isArray(handle)) {
      this.handles = handle.collect( function(e) { return $(e) });
    } else {
      this.handles = [$(handle)];
    }

    this.track   = $(track);
    this.options = options || { };

    this.axis      = this.options.axis || 'horizontal';
    this.increment = this.options.increment || 1;
    this.step      = parseInt(this.options.step || '1');
    this.range     = this.options.range || $R(0,1);

    this.value     = 0; // assure backwards compat
    this.values    = this.handles.map( function() { return 0 });
    this.spans     = this.options.spans ? this.options.spans.map(function(s){ return $(s) }) : false;
    this.options.startSpan = $(this.options.startSpan || null);
    this.options.endSpan   = $(this.options.endSpan || null);

    this.restricted = this.options.restricted || false;

    this.maximum   = this.options.maximum || this.range.end;
    this.minimum   = this.options.minimum || this.range.start;

    // Will be used to align the handle onto the track, if necessary
    this.alignX = parseInt(this.options.alignX || '0');
    this.alignY = parseInt(this.options.alignY || '0');

    this.trackLength = this.maximumOffset() - this.minimumOffset();

    this.handleLength = this.isVertical() ?
      (this.handles[0].offsetHeight != 0 ?
        this.handles[0].offsetHeight : this.handles[0].style.height.replace(/px$/,"")) :
      (this.handles[0].offsetWidth != 0 ? this.handles[0].offsetWidth :
        this.handles[0].style.width.replace(/px$/,""));

    this.active   = false;
    this.dragging = false;
    this.disabled = false;

    if (this.options.disabled) this.setDisabled();

    // Allowed values array
    this.allowedValues = this.options.values ? this.options.values.sortBy(Prototype.K) : false;
    if (this.allowedValues) {
      this.minimum = this.allowedValues.min();
      this.maximum = this.allowedValues.max();
    }

    this.eventMouseDown = this.startDrag.bindAsEventListener(this);
    this.eventMouseUp   = this.endDrag.bindAsEventListener(this);
    this.eventMouseMove = this.update.bindAsEventListener(this);

    // Initialize handles in reverse (make sure first handle is active)
    this.handles.each( function(h,i) {
      i = slider.handles.length-1-i;
      slider.setValue(parseFloat(
        (Object.isArray(slider.options.sliderValue) ?
          slider.options.sliderValue[i] : slider.options.sliderValue) ||
         slider.range.start), i);
      h.makePositioned().observe("mousedown", slider.eventMouseDown);
    });

    this.track.observe("mousedown", this.eventMouseDown);
    document.observe("mouseup", this.eventMouseUp);
    $(this.track.parentNode.parentNode).observe("mousemove", this.eventMouseMove);


    this.initialized = true;
  },
  dispose: function() {
    var slider = this;
    Event.stopObserving(this.track, "mousedown", this.eventMouseDown);
    Event.stopObserving(document, "mouseup", this.eventMouseUp);
    Event.stopObserving(this.track.parentNode.parentNode, "mousemove", this.eventMouseMove);
    this.handles.each( function(h) {
      Event.stopObserving(h, "mousedown", slider.eventMouseDown);
    });
  },
  setDisabled: function(){
    this.disabled = true;
    this.track.parentNode.className = this.track.parentNode.className + ' disabled';
  },
  setEnabled: function(){
    this.disabled = false;
  },
  getNearestValue: function(value){
    if (this.allowedValues){
      if (value >= this.allowedValues.max()) return(this.allowedValues.max());
      if (value <= this.allowedValues.min()) return(this.allowedValues.min());

      var offset = Math.abs(this.allowedValues[0] - value);
      var newValue = this.allowedValues[0];
      this.allowedValues.each( function(v) {
        var currentOffset = Math.abs(v - value);
        if (currentOffset <= offset){
          newValue = v;
          offset = currentOffset;
        }
      });
      return newValue;
    }
    if (value > this.range.end) return this.range.end;
    if (value < this.range.start) return this.range.start;
    return value;
  },
  setValue: function(sliderValue, handleIdx){
    if (!this.active) {
      this.activeHandleIdx = handleIdx || 0;
      this.activeHandle    = this.handles[this.activeHandleIdx];
      this.updateStyles();
    }
    handleIdx = handleIdx || this.activeHandleIdx || 0;
    if (this.initialized && this.restricted) {
      if ((handleIdx>0) && (sliderValue<this.values[handleIdx-1]))
        sliderValue = this.values[handleIdx-1];
      if ((handleIdx < (this.handles.length-1)) && (sliderValue>this.values[handleIdx+1]))
        sliderValue = this.values[handleIdx+1];
    }
    sliderValue = this.getNearestValue(sliderValue);
    this.values[handleIdx] = sliderValue;
    this.value = this.values[0]; // assure backwards compat

    this.handles[handleIdx].style[this.isVertical() ? 'top' : 'left'] =
      this.translateToPx(sliderValue);

    this.drawSpans();
    if (!this.dragging || !this.event) this.updateFinished();
  },
  setValueBy: function(delta, handleIdx) {
    this.setValue(this.values[handleIdx || this.activeHandleIdx || 0] + delta,
      handleIdx || this.activeHandleIdx || 0);
  },
  translateToPx: function(value) {
    return Math.round(
      ((this.trackLength-this.handleLength)/(this.range.end-this.range.start)) *
      (value - this.range.start)) + "px";
  },
  translateToValue: function(offset) {
    return ((offset/(this.trackLength-this.handleLength) *
      (this.range.end-this.range.start)) + this.range.start);
  },
  getRange: function(range) {
    var v = this.values.sortBy(Prototype.K);
    range = range || 0;
    return $R(v[range],v[range+1]);
  },
  minimumOffset: function(){
    return(this.isVertical() ? this.alignY : this.alignX);
  },
  maximumOffset: function(){
    return(this.isVertical() ?
      (this.track.offsetHeight != 0 ? this.track.offsetHeight :
        this.track.style.height.replace(/px$/,"")) - this.alignY :
      (this.track.offsetWidth != 0 ? this.track.offsetWidth :
        this.track.style.width.replace(/px$/,"")) - this.alignX);
  },
  isVertical:  function(){
    return (this.axis == 'vertical');
  },
  drawSpans: function() {
    var slider = this;
    if (this.spans)
      $R(0, this.spans.length-1).each(function(r) { slider.setSpan(slider.spans[r], slider.getRange(r)) });
    if (this.options.startSpan)
      this.setSpan(this.options.startSpan,
        $R(0, this.values.length>1 ? this.getRange(0).min() : this.value ));
    if (this.options.endSpan)
      this.setSpan(this.options.endSpan,
        $R(this.values.length>1 ? this.getRange(this.spans.length-1).max() : this.value, this.maximum));
  },
  setSpan: function(span, range) {
    if (this.isVertical()) {
      span.style.top = this.translateToPx(range.start);
      span.style.height = this.translateToPx(range.end - range.start + this.range.start);
    } else {
      span.style.left = this.translateToPx(range.start);
      span.style.width = this.translateToPx(range.end - range.start + this.range.start);
    }
  },
  updateStyles: function() {
    this.handles.each( function(h){ Element.removeClassName(h, 'selected') });
    Element.addClassName(this.activeHandle, 'selected');
  },
  startDrag: function(event) {
    if (Event.isLeftClick(event)) {
      if (!this.disabled){
        this.active = true;

        var handle = Event.element(event);
        var pointer  = [Event.pointerX(event), Event.pointerY(event)];
        var track = handle;
        if (track==this.track) {
          var offsets  = Position.cumulativeOffset(this.track);
          this.event = event;
          this.setValue(this.translateToValue(
           (this.isVertical() ? pointer[1]-offsets[1] : pointer[0]-offsets[0])-(this.handleLength/2)
          ));
          var offsets  = Position.cumulativeOffset(this.activeHandle);
          this.offsetX = (pointer[0] - offsets[0]);
          this.offsetY = (pointer[1] - offsets[1]);
        } else {
          // find the handle (prevents issues with Safari)
          while((this.handles.indexOf(handle) == -1) && handle.parentNode)
            handle = handle.parentNode;

          if (this.handles.indexOf(handle)!=-1) {
            this.activeHandle    = handle;
            this.activeHandleIdx = this.handles.indexOf(this.activeHandle);
            this.updateStyles();

            var offsets  = Position.cumulativeOffset(this.activeHandle);
            this.offsetX = (pointer[0] - offsets[0]);
            this.offsetY = (pointer[1] - offsets[1]);
          }
        }
      }
      Event.stop(event);
    }
  },
  update: function(event) {
   if (this.active) {
      if (!this.dragging) this.dragging = true;
      this.draw(event);
      if (Prototype.Browser.WebKit) window.scrollBy(0,0);
      Event.stop(event);
   }
  },
  draw: function(event) {
    var pointer = [Event.pointerX(event), Event.pointerY(event)];
    var offsets = Position.cumulativeOffset(this.track);
    pointer[0] -= this.offsetX + offsets[0];
    pointer[1] -= this.offsetY + offsets[1];
    this.event = event;
    this.setValue(this.translateToValue( this.isVertical() ? pointer[1] : pointer[0] ));
    if (this.initialized && this.options.onSlide)
      this.options.onSlide(this.values.length>1 ? this.values : this.value, this);
  },
  endDrag: function(event) {
    if (this.active && this.dragging) {
      this.finishDrag(event, true);
      Event.stop(event);
    }
    this.active = false;
    this.dragging = false;
  },
  finishDrag: function(event, success) {
    this.active = false;
    this.dragging = false;
    this.updateFinished();
  },
  updateFinished: function() {
    if (this.initialized && this.options.onChange)
      this.options.onChange(this.values.length>1 ? this.values : this.value, this);
    this.event = null;
  }
});


/* FILE: js.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Varien
 * @package     js
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
function popWin(url,win,para) {
    var win = window.open(url,win,para);
    win.focus();
}

function setLocation(url){
    window.location.href = encodeURI(url);
}

function setPLocation(url, setFocus){
    if( setFocus ) {
        window.opener.focus();
    }
    window.opener.location.href = encodeURI(url);
}

/**
 * @deprecated
 */
function setLanguageCode(code, fromCode){
    //TODO: javascript cookies have different domain and path than php cookies
    var href = window.location.href;
    var after = '', dash;
    if (dash = href.match(/\#(.*)$/)) {
        href = href.replace(/\#(.*)$/, '');
        after = dash[0];
    }

    if (href.match(/[?]/)) {
        var re = /([?&]store=)[a-z0-9_]*/;
        if (href.match(re)) {
            href = href.replace(re, '$1'+code);
        } else {
            href += '&store='+code;
        }

        var re = /([?&]from_store=)[a-z0-9_]*/;
        if (href.match(re)) {
            href = href.replace(re, '');
        }
    } else {
        href += '?store='+code;
    }
    if (typeof(fromCode) != 'undefined') {
        href += '&from_store='+fromCode;
    }
    href += after;

    setLocation(href);
}

/**
 * Add classes to specified elements.
 * Supported classes are: 'odd', 'even', 'first', 'last'
 *
 * @param elements - array of elements to be decorated
 * [@param decorateParams] - array of classes to be set. If omitted, all available will be used
 */
function decorateGeneric(elements, decorateParams)
{
    var allSupportedParams = ['odd', 'even', 'first', 'last'];
    var _decorateParams = {};
    var total = elements.length;

    if (total) {
        // determine params called
        if (typeof(decorateParams) == 'undefined') {
            decorateParams = allSupportedParams;
        }
        if (!decorateParams.length) {
            return;
        }
        for (var k in allSupportedParams) {
            _decorateParams[allSupportedParams[k]] = false;
        }
        for (var k in decorateParams) {
            _decorateParams[decorateParams[k]] = true;
        }

        // decorate elements
        // elements[0].addClassName('first'); // will cause bug in IE (#5587)
        if (_decorateParams.first) {
            Element.addClassName(elements[0], 'first');
        }
        if (_decorateParams.last) {
            Element.addClassName(elements[total-1], 'last');
        }
        for (var i = 0; i < total; i++) {
            if ((i + 1) % 2 == 0) {
                if (_decorateParams.even) {
                    Element.addClassName(elements[i], 'even');
                }
            }
            else {
                if (_decorateParams.odd) {
                    Element.addClassName(elements[i], 'odd');
                }
            }
        }
    }
}

/**
 * Decorate table rows and cells, tbody etc
 * @see decorateGeneric()
 */
function decorateTable(table, options) {
    var table = $(table);
    if (table) {
        // set default options
        var _options = {
            'tbody'    : false,
            'tbody tr' : ['odd', 'even', 'first', 'last'],
            'thead tr' : ['first', 'last'],
            'tfoot tr' : ['first', 'last'],
            'tr td'    : ['last']
        };
        // overload options
        if (typeof(options) != 'undefined') {
            for (var k in options) {
                _options[k] = options[k];
            }
        }
        // decorate
        if (_options['tbody']) {
            decorateGeneric(table.select('tbody'), _options['tbody']);
        }
        if (_options['tbody tr']) {
            decorateGeneric(table.select('tbody tr'), _options['tbody tr']);
        }
        if (_options['thead tr']) {
            decorateGeneric(table.select('thead tr'), _options['thead tr']);
        }
        if (_options['tfoot tr']) {
            decorateGeneric(table.select('tfoot tr'), _options['tfoot tr']);
        }
        if (_options['tr td']) {
            var allRows = table.select('tr');
            if (allRows.length) {
                for (var i = 0; i < allRows.length; i++) {
                    decorateGeneric(allRows[i].getElementsByTagName('TD'), _options['tr td']);
                }
            }
        }
    }
}

/**
 * Set "odd", "even" and "last" CSS classes for list items
 * @see decorateGeneric()
 */
function decorateList(list, nonRecursive) {
    if ($(list)) {
        if (typeof(nonRecursive) == 'undefined') {
            var items = $(list).select('li');
        }
        else {
            var items = $(list).childElements();
        }
        decorateGeneric(items, ['odd', 'even', 'last']);
    }
}

/**
 * Set "odd", "even" and "last" CSS classes for list items
 * @see decorateGeneric()
 */
function decorateDataList(list) {
    list = $(list);
    if (list) {
        decorateGeneric(list.select('dt'), ['odd', 'even', 'last']);
        decorateGeneric(list.select('dd'), ['odd', 'even', 'last']);
    }
}

/**
 * Parse SID and produces the correct URL
 */
function parseSidUrl(baseUrl, urlExt) {
    var sidPos = baseUrl.indexOf('/?SID=');
    var sid = '';
    urlExt = (urlExt != undefined) ? urlExt : '';

    if(sidPos > -1) {
        sid = '?' + baseUrl.substring(sidPos + 2);
        baseUrl = baseUrl.substring(0, sidPos + 1);
    }

    return baseUrl+urlExt+sid;
}

/**
 * Formats currency using patern
 * format - JSON (pattern, decimal, decimalsDelimeter, groupsDelimeter)
 * showPlus - true (always show '+'or '-'),
 *      false (never show '-' even if number is negative)
 *      null (show '-' if number is negative)
 */

function formatCurrency(price, format, showPlus){
    var precision = isNaN(format.precision = Math.abs(format.precision)) ? 2 : format.precision;
    var requiredPrecision = isNaN(format.requiredPrecision = Math.abs(format.requiredPrecision)) ? 2 : format.requiredPrecision;

    //precision = (precision > requiredPrecision) ? precision : requiredPrecision;
    //for now we don't need this difference so precision is requiredPrecision
    precision = requiredPrecision;

    var integerRequired = isNaN(format.integerRequired = Math.abs(format.integerRequired)) ? 1 : format.integerRequired;

    var decimalSymbol = format.decimalSymbol == undefined ? "," : format.decimalSymbol;
    var groupSymbol = format.groupSymbol == undefined ? "." : format.groupSymbol;
    var groupLength = format.groupLength == undefined ? 3 : format.groupLength;

    var s = '';

    if (showPlus == undefined || showPlus == true) {
        s = price < 0 ? "-" : ( showPlus ? "+" : "");
    } else if (showPlus == false) {
        s = '';
    }

    var i = parseInt(price = Math.abs(+price || 0).toFixed(precision)) + "";
    var pad = (i.length < integerRequired) ? (integerRequired - i.length) : 0;
    while (pad) { i = '0' + i; pad--; }
    j = (j = i.length) > groupLength ? j % groupLength : 0;
    re = new RegExp("(\\d{" + groupLength + "})(?=\\d)", "g");

    /**
     * replace(/-/, 0) is only for fixing Safari bug which appears
     * when Math.abs(0).toFixed() executed on "0" number.
     * Result is "0.-0" :(
     */
    var r = (j ? i.substr(0, j) + groupSymbol : "") + i.substr(j).replace(re, "$1" + groupSymbol) + (precision ? decimalSymbol + Math.abs(price - i).toFixed(precision).replace(/-/, 0).slice(2) : "");
    var pattern = '';
    if (format.pattern.indexOf('{sign}') == -1) {
        pattern = s + format.pattern;
    } else {
        pattern = format.pattern.replace('{sign}', s);
    }

    return pattern.replace('%s', r).replace(/^\s\s*/, '').replace(/\s\s*$/, '');
};

function expandDetails(el, childClass) {
    if (Element.hasClassName(el,'show-details')) {
        $$(childClass).each(function(item){
            item.hide();
        });
        Element.removeClassName(el,'show-details');
    }
    else {
        $$(childClass).each(function(item){
            item.show();
        });
        Element.addClassName(el,'show-details');
    }
}

// Version 1.0
var isIE = navigator.appVersion.match(/MSIE/) == "MSIE";

if (!window.Varien)
    var Varien = new Object();

Varien.showLoading = function(){
    var loader = $('loading-process');
    loader && loader.show();
};
Varien.hideLoading = function(){
    var loader = $('loading-process');
    loader && loader.hide();
};
Varien.GlobalHandlers = {
    onCreate: function() {
        Varien.showLoading();
    },

    onComplete: function() {
        if(Ajax.activeRequestCount == 0) {
            Varien.hideLoading();
        }
    }
};

Ajax.Responders.register(Varien.GlobalHandlers);

/**
 * Quick Search form client model
 */
Varien.searchForm = Class.create();
Varien.searchForm.prototype = {
    initialize : function(form, field, emptyText){
        this.form   = $(form);
        this.field  = $(field);
        this.emptyText = emptyText;

        Event.observe(this.form,  'submit', this.submit.bind(this));
        Event.observe(this.field, 'focus', this.focus.bind(this));
        Event.observe(this.field, 'blur', this.blur.bind(this));
        this.blur();
    },

    submit : function(event){
        if (this.field.value == this.emptyText || this.field.value == ''){
            Event.stop(event);
            return false;
        }
        return true;
    },

    focus : function(event){
        if(this.field.value==this.emptyText){
            this.field.value='';
        }

    },

    blur : function(event){
        if(this.field.value==''){
            this.field.value=this.emptyText;
        }
    },

    initAutocomplete : function(url, destinationElement){
        new Ajax.Autocompleter(
            this.field,
            destinationElement,
            url,
            {
                paramName: this.field.name,
                method: 'get',
                minChars: 2,
                updateElement: this._selectAutocompleteItem.bind(this),
                onShow : function(element, update) {
                    if(!update.style.position || update.style.position=='absolute') {
                        update.style.position = 'absolute';
                        Position.clone(element, update, {
                            setHeight: false,
                            offsetTop: element.offsetHeight
                        });
                    }
                    Effect.Appear(update,{duration:0});
                }

            }
        );
    },

    _selectAutocompleteItem : function(element){
        if(element.title){
            this.field.value = element.title;
        }
        this.form.submit();
    }
};

Varien.Tabs = Class.create();
Varien.Tabs.prototype = {
  initialize: function(selector) {
    var self=this;
    $$(selector+' a').each(this.initTab.bind(this));
  },

  initTab: function(el) {
      el.href = 'javascript:void(0)';
      if ($(el.parentNode).hasClassName('active')) {
        this.showContent(el);
      }
      el.observe('click', this.showContent.bind(this, el));
  },

  showContent: function(a) {
    var li = $(a.parentNode), ul = $(li.parentNode);
    ul.getElementsBySelector('li', 'ol').each(function(el){
      var contents = $(el.id+'_contents');
      if (el==li) {
        el.addClassName('active');
        contents.show();
      } else {
        el.removeClassName('active');
        contents.hide();
      }
    });
  }
};

Varien.DateElement = Class.create();
Varien.DateElement.prototype = {
    initialize: function(type, content, required, format) {
        if (type == 'id') {
            // id prefix
            this.day    = $(content + 'day');
            this.month  = $(content + 'month');
            this.year   = $(content + 'year');
            this.full   = $(content + 'full');
            this.advice = $(content + 'date-advice');
        } else if (type == 'container') {
            // content must be container with data
            this.day    = content.day;
            this.month  = content.month;
            this.year   = content.year;
            this.full   = content.full;
            this.advice = content.advice;
        } else {
            return;
        }

        this.required = required;
        this.format   = format;

        this.day.addClassName('validate-custom');
        this.day.validate = this.validate.bind(this);
        this.month.addClassName('validate-custom');
        this.month.validate = this.validate.bind(this);
        this.year.addClassName('validate-custom');
        this.year.validate = this.validate.bind(this);

        this.setDateRange(false, false);
        this.year.setAttribute('autocomplete','off');

        this.advice.hide();

        var date = new Date;
        this.curyear = date.getFullYear();
    },
    validate: function() {
        var error = false,
            day   = parseInt(this.day.value, 10)   || 0,
            month = parseInt(this.month.value, 10) || 0,
            year  = parseInt(this.year.value, 10)  || 0;
        if (this.day.value.strip().empty()
            && this.month.value.strip().empty()
            && this.year.value.strip().empty()
        ) {
            if (this.required) {
                error = 'This date is a required value.';
            } else {
                this.full.value = '';
            }
        } else if (!day || !month || !year) {
            error = 'Please enter a valid full date';
        } else {
            var date = new Date, countDaysInMonth = 0, errorType = null;
            date.setYear(year);date.setMonth(month-1);date.setDate(32);
            countDaysInMonth = 32 - date.getDate();
            if(!countDaysInMonth || countDaysInMonth>31) countDaysInMonth = 31;
            if(year < 1900) error = this.errorTextModifier(this.validateDataErrorText);

            if (day<1 || day>countDaysInMonth) {
                errorType = 'day';
                error = 'Please enter a valid day (1-%d).';
            } else if (month<1 || month>12) {
                errorType = 'month';
                error = 'Please enter a valid month (1-12).';
            } else {
                if(day % 10 == day) this.day.value = '0'+day;
                if(month % 10 == month) this.month.value = '0'+month;
                this.full.value = this.format.replace(/%[mb]/i, this.month.value).replace(/%[de]/i, this.day.value).replace(/%y/i, this.year.value);
                var testFull = this.month.value + '/' + this.day.value + '/'+ this.year.value;
                var test = new Date(testFull);
                if (isNaN(test)) {
                    error = 'Please enter a valid date.';
                } else {
                    this.setFullDate(test);
                }
            }
            var valueError = false;
            if (!error && !this.validateData()){//(year<1900 || year>curyear) {
                errorType = this.validateDataErrorType;//'year';
                valueError = this.validateDataErrorText;//'Please enter a valid year (1900-%d).';
                error = valueError;
            }
        }

        if (error !== false) {
            try {
                error = Translator.translate(error);
            }
            catch (e) {}
            if (!valueError) {
                this.advice.innerHTML = error.replace('%d', countDaysInMonth);
            } else {
                this.advice.innerHTML = this.errorTextModifier(error);
            }
            this.advice.show();
            return false;
        }

        // fixing elements class
        this.day.removeClassName('validation-failed');
        this.month.removeClassName('validation-failed');
        this.year.removeClassName('validation-failed');

        this.advice.hide();
        return true;
    },
    validateData: function() {
        var year = this.fullDate.getFullYear();
        return (year>=1900 && year<=this.curyear);
    },
    validateDataErrorType: 'year',
    validateDataErrorText: 'Please enter a valid year (1900-%d).',
    errorTextModifier: function(text) {
        text = Translator.translate(text);
        return text.replace('%d', this.curyear);
    },
    setDateRange: function(minDate, maxDate) {
        this.minDate = minDate;
        this.maxDate = maxDate;
    },
    setFullDate: function(date) {
        this.fullDate = date;
    }
};

Varien.DOB = Class.create();
Varien.DOB.prototype = {
    initialize: function(selector, required, format) {
        var el = $$(selector)[0];
        var container       = {};
        container.day       = Element.select(el, '.dob-day input')[0];
        container.month     = Element.select(el, '.dob-month input')[0];
        container.year      = Element.select(el, '.dob-year input')[0];
        container.full      = Element.select(el, '.dob-full input')[0];
        container.advice    = Element.select(el, '.validation-advice')[0];

        new Varien.DateElement('container', container, required, format);
    }
};

Varien.dateRangeDate = Class.create();
Varien.dateRangeDate.prototype = Object.extend(new Varien.DateElement(), {
    validateData: function() {
        var validate = true;
        if (this.minDate || this.maxValue) {
            if (this.minDate) {
                this.minDate = new Date(this.minDate);
                this.minDate.setHours(0);
                if (isNaN(this.minDate)) {
                    this.minDate = new Date('1/1/1900');
                }
                validate = validate && (this.fullDate >= this.minDate);
            }
            if (this.maxDate) {
                this.maxDate = new Date(this.maxDate);
                this.minDate.setHours(0);
                if (isNaN(this.maxDate)) {
                    this.maxDate = new Date();
                }
                validate = validate && (this.fullDate <= this.maxDate);
            }
            if (this.maxDate && this.minDate) {
                this.validateDataErrorText = 'Please enter a valid date between %s and %s';
            } else if (this.maxDate) {
                this.validateDataErrorText = 'Please enter a valid date less than or equal to %s';
            } else if (this.minDate) {
                this.validateDataErrorText = 'Please enter a valid date equal to or greater than %s';
            } else {
                this.validateDataErrorText = '';
            }
        }
        return validate;
    },
    validateDataErrorText: 'Date should be between %s and %s',
    errorTextModifier: function(text) {
        if (this.minDate) {
            text = text.sub('%s', this.dateFormat(this.minDate));
        }
        if (this.maxDate) {
            text = text.sub('%s', this.dateFormat(this.maxDate));
        }
        return text;
    },
    dateFormat: function(date) {
        return (date.getMonth() + 1) + '/' + date.getDate() + '/' + date.getFullYear();
    }
});

Varien.FileElement = Class.create();
Varien.FileElement.prototype = {
    initialize: function (id) {
        this.fileElement = $(id);
        this.hiddenElement = $(id + '_value');

        this.fileElement.observe('change', this.selectFile.bind(this));
    },
    selectFile: function(event) {
        this.hiddenElement.value = this.fileElement.getValue();
    }
};

Validation.addAllThese([
    ['validate-custom', ' ', function(v,elm) {
        return elm.validate();
    }]
]);

function truncateOptions() {
    $$('.truncated').each(function(element){
        Event.observe(element, 'mouseover', function(){
            if (element.down('div.truncated_full_value')) {
                element.down('div.truncated_full_value').addClassName('show');
            }
        });
        Event.observe(element, 'mouseout', function(){
            if (element.down('div.truncated_full_value')) {
                element.down('div.truncated_full_value').removeClassName('show');
            }
        });

    });
}
Event.observe(window, 'load', function(){
   truncateOptions();
});

Element.addMethods({
    getInnerText: function(element)
    {
        element = $(element);
        if(element.innerText && !Prototype.Browser.Opera) {
            return element.innerText;
        }
        return element.innerHTML.stripScripts().unescapeHTML().replace(/[\n\r\s]+/g, ' ').strip();
    }
});

/*
if (!("console" in window) || !("firebug" in console))
{
    var names = ["log", "debug", "info", "warn", "error", "assert", "dir", "dirxml",
    "group", "groupEnd", "time", "timeEnd", "count", "trace", "profile", "profileEnd"];

    window.console = {};
    for (var i = 0; i < names.length; ++i)
        window.console[names[i]] = function() {}
}
*/

/**
 * Executes event handler on the element. Works with event handlers attached by Prototype,
 * in a browser-agnostic fashion.
 * @param element The element object
 * @param event Event name, like 'change'
 *
 * @example fireEvent($('my-input', 'click'));
 */
function fireEvent(element, event) {
    if (document.createEvent) {
        // dispatch for all browsers except IE before version 9
        var evt = document.createEvent("HTMLEvents");
        evt.initEvent(event, true, true ); // event type, bubbling, cancelable
        return element.dispatchEvent(evt);
    } else {
        // dispatch for IE before version 9
        var evt = document.createEventObject();
        return element.fireEvent('on' + event, evt);
    }
}

/**
 * Returns more accurate results of floating-point modulo division
 * E.g.:
 * 0.6 % 0.2 = 0.19999999999999996
 * modulo(0.6, 0.2) = 0
 *
 * @param dividend
 * @param divisor
 */
function modulo(dividend, divisor)
{
    var epsilon = divisor / 10000;
    var remainder = dividend % divisor;

    if (Math.abs(remainder - divisor) < epsilon || Math.abs(remainder) < epsilon) {
        remainder = 0;
    }

    return remainder;
}

/**
 * createContextualFragment is not supported in IE9. Adding its support.
 */
if ((typeof Range != "undefined") && !Range.prototype.createContextualFragment)
{
    Range.prototype.createContextualFragment = function(html)
    {
        var frag = document.createDocumentFragment(),
        div = document.createElement("div");
        frag.appendChild(div);
        div.outerHTML = html;
        return frag;
    };
}

/**
 * Create form element. Set parameters into it and send
 *
 * @param url
 * @param parametersArray
 * @param method
 */
Varien.formCreator = Class.create();
Varien.formCreator.prototype = {
    initialize : function(url, parametersArray, method) {
        this.url = url;
        this.parametersArray = JSON.parse(parametersArray);
        this.method = method;
        this.form = '';

        this.createForm();
        this.setFormData();
    },
    createForm : function() {
        this.form = new Element('form', { 'method': this.method, action: this.url });
    },
    setFormData : function () {
        for (var key in this.parametersArray) {
            Element.insert(
                this.form,
                new Element('input', { name: key, value: this.parametersArray[key], type: 'hidden' })
            );
        }
    }
};

function customFormSubmit(url, parametersArray, method) {
    var createdForm = new Varien.formCreator(url, parametersArray, method);
    Element.insert($$('body')[0], createdForm.form);
    createdForm.form.submit();
}

function customFormSubmitToParent(url, parametersArray, method) {
    new Ajax.Request(url, {
        method: method,
        parameters: JSON.parse(parametersArray),
        onSuccess: function (response) {
            var node = document.createElement('div');
            node.innerHTML = response.responseText;
            var responseMessage = node.getElementsByClassName('messages')[0];
            var pageTitle = window.document.body.getElementsByClassName('page-title')[0];
            pageTitle.insertAdjacentHTML('afterend', responseMessage.outerHTML);
            window.opener.focus();
            window.opener.location.href = response.transport.responseURL;
        }
    });
}

function buttonDisabler() {
    const buttons = document.querySelectorAll('button.save');
    buttons.forEach(button => button.disabled = true);
}




/* FILE: form.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Varien
 * @package     js
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
VarienForm = Class.create();
VarienForm.prototype = {
    initialize: function(formId, firstFieldFocus){
        this.form       = $(formId);
        if (!this.form) {
            return;
        }
        this.cache      = $A();
        this.currLoader = false;
        this.currDataIndex = false;
        this.validator  = new Validation(this.form);
        this.elementFocus   = this.elementOnFocus.bindAsEventListener(this);
        this.elementBlur    = this.elementOnBlur.bindAsEventListener(this);
        this.childLoader    = this.onChangeChildLoad.bindAsEventListener(this);
        this.highlightClass = 'highlight';
        this.extraChildParams = '';
        this.firstFieldFocus= firstFieldFocus || false;
        this.bindElements();
        if(this.firstFieldFocus){
            try{
                Form.Element.focus(Form.findFirstElement(this.form));
            }
            catch(e){}
        }
    },

    submit : function(url){
        if(this.validator && this.validator.validate()){
             this.form.submit();
        }
        return false;
    },

    bindElements:function (){
        var elements = Form.getElements(this.form);
        for (var row in elements) {
            if (elements[row].id) {
                Event.observe(elements[row],'focus',this.elementFocus);
                Event.observe(elements[row],'blur',this.elementBlur);
            }
        }
    },

    elementOnFocus: function(event){
        var element = Event.findElement(event, 'fieldset');
        if(element){
            Element.addClassName(element, this.highlightClass);
        }
    },

    elementOnBlur: function(event){
        var element = Event.findElement(event, 'fieldset');
        if(element){
            Element.removeClassName(element, this.highlightClass);
        }
    },

    setElementsRelation: function(parent, child, dataUrl, first){
        if (parent=$(parent)) {
            // TODO: array of relation and caching
            if (!this.cache[parent.id]){
                this.cache[parent.id] = $A();
                this.cache[parent.id]['child']     = child;
                this.cache[parent.id]['dataUrl']   = dataUrl;
                this.cache[parent.id]['data']      = $A();
                this.cache[parent.id]['first']      = first || false;
            }
            Event.observe(parent,'change',this.childLoader);
        }
    },

    onChangeChildLoad: function(event){
        element = Event.element(event);
        this.elementChildLoad(element);
    },

    elementChildLoad: function(element, callback){
        this.callback = callback || false;
        if (element.value) {
            this.currLoader = element.id;
            this.currDataIndex = element.value;
            if (this.cache[element.id]['data'][element.value]) {
                this.setDataToChild(this.cache[element.id]['data'][element.value]);
            }
            else{
                new Ajax.Request(this.cache[this.currLoader]['dataUrl'],{
                        method: 'post',
                        parameters: {"parent":element.value},
                        onComplete: this.reloadChildren.bind(this)
                });
            }
        }
    },

    reloadChildren: function(transport){
        var data = transport.responseJSON || transport.responseText.evalJSON(true) || {};
        this.cache[this.currLoader]['data'][this.currDataIndex] = data;
        this.setDataToChild(data);
    },

    setDataToChild: function(data){
        if (data.length) {
            var child = $(this.cache[this.currLoader]['child']);
            if (child){
                var html = '<select name="'+child.name+'" id="'+child.id+'" class="'+child.className+'" title="'+child.title+'" '+this.extraChildParams+'>';
                if(this.cache[this.currLoader]['first']){
                    html+= '<option value="">'+this.cache[this.currLoader]['first']+'</option>';
                }
                for (var i in data){
                    if(data[i].value) {
                        html+= '<option value="'+data[i].value+'"';
                        if(child.value && (child.value == data[i].value || child.value == data[i].label)){
                            html+= ' selected';
                        }
                        html+='>'+data[i].label+'</option>';
                    }
                }
                html+= '</select>';
                Element.insert(child, {before: html});
                Element.remove(child);
            }
        }
        else{
            var child = $(this.cache[this.currLoader]['child']);
            if (child){
                var html = '<input type="text" name="'+child.name+'" id="'+child.id+'" class="'+child.className+'" title="'+child.title+'" '+this.extraChildParams+'>';
                Element.insert(child, {before: html});
                Element.remove(child);
            }
        }

        this.bindElements();
        if (this.callback) {
            this.callback();
        }
    }
};

RegionUpdater = Class.create();
RegionUpdater.prototype = {
    initialize: function (countryEl, regionTextEl, regionSelectEl, regions, disableAction, zipEl)
    {
        this.countryEl = $(countryEl);
        this.regionTextEl = $(regionTextEl);
        this.regionSelectEl = $(regionSelectEl);
        this.zipEl = $(zipEl);
        this.config = regions['config'];
        delete regions.config;
        this.regions = regions;

        this.disableAction = (typeof disableAction=='undefined') ? 'hide' : disableAction;
        this.zipOptions = (typeof zipOptions=='undefined') ? false : zipOptions;

        if (this.regionSelectEl.options.length<=1) {
            this.update();
        }

        Event.observe(this.countryEl, 'change', this.update.bind(this));
    },

    _checkRegionRequired: function()
    {
        var label, wildCard;
        var elements = [this.regionTextEl, this.regionSelectEl];
        var that = this;
        if (typeof this.config == 'undefined') {
            return;
        }
        var regionRequired = this.config.regions_required.indexOf(this.countryEl.value) >= 0;

        elements.each(function(currentElement) {
            Validation.reset(currentElement);
            label = $$('label[for="' + currentElement.id + '"]')[0];
            if (label) {
                wildCard = label.down('em') || label.down('span.required');
                if (!that.config.show_all_regions) {
                    if (regionRequired) {
                        label.up().show();
                    } else {
                        label.up().hide();
                    }
                }
            }

            if (label && wildCard) {
                if (!regionRequired) {
                    wildCard.hide();
                    if (label.hasClassName('required')) {
                        label.removeClassName('required');
                    }
                } else if (regionRequired) {
                    wildCard.show();
                    if (!label.hasClassName('required')) {
                        label.addClassName('required');
                    }
                }
            }

            if (!regionRequired) {
                if (currentElement.hasClassName('required-entry')) {
                    currentElement.removeClassName('required-entry');
                }
                if ('select' == currentElement.tagName.toLowerCase() &&
                    currentElement.hasClassName('validate-select')) {
                    currentElement.removeClassName('validate-select');
                }
            } else {
                if (!currentElement.hasClassName('required-entry')) {
                    currentElement.addClassName('required-entry');
                }
                if ('select' == currentElement.tagName.toLowerCase() &&
                    !currentElement.hasClassName('validate-select')) {
                    currentElement.addClassName('validate-select');
                }
            }
        });
    },

    update: function()
    {
        if (this.regions[this.countryEl.value]) {
            var i, option, region, def;

            def = this.regionSelectEl.getAttribute('defaultValue');
            if (this.regionTextEl) {
                if (!def) {
                    def = this.regionTextEl.value.toLowerCase();
                }
                this.regionTextEl.value = '';
            }

            this.regionSelectEl.options.length = 1;
            for (regionId in this.regions[this.countryEl.value]) {
                region = this.regions[this.countryEl.value][regionId];

                option = document.createElement('OPTION');
                option.value = regionId;
                option.text = region.name.stripTags();
                option.title = region.name;

                if (this.regionSelectEl.options.add) {
                    this.regionSelectEl.options.add(option);
                } else {
                    this.regionSelectEl.appendChild(option);
                }

                if (regionId == def || (region.name && region.name.toLowerCase() == def)
                    || (region.name && region.code.toLowerCase() == def)
                ) {
                    this.regionSelectEl.value = regionId;
                }
            }
            this.sortSelect();
            if (this.disableAction == 'hide') {
                if (this.regionTextEl) {
                    this.regionTextEl.style.display = 'none';
                }

                this.regionSelectEl.style.display = '';
            } else if (this.disableAction == 'disable') {
                if (this.regionTextEl) {
                    this.regionTextEl.disabled = true;
                }
                this.regionSelectEl.disabled = false;
            }
            this.setMarkDisplay(this.regionSelectEl, true);
        } else {
            this.regionSelectEl.options.length = 1;
            this.sortSelect();
            if (this.disableAction == 'hide') {
                if (this.regionTextEl) {
                    this.regionTextEl.style.display = '';
                }
                this.regionSelectEl.style.display = 'none';
                Validation.reset(this.regionSelectEl);
            } else if (this.disableAction == 'disable') {
                if (this.regionTextEl) {
                    this.regionTextEl.disabled = false;
                }
                this.regionSelectEl.disabled = true;
            } else if (this.disableAction == 'nullify') {
                this.regionSelectEl.options.length = 1;
                this.regionSelectEl.value = '';
                this.regionSelectEl.selectedIndex = 0;
                this.lastCountryId = '';
            }
            this.setMarkDisplay(this.regionSelectEl, false);
        }

        this._checkRegionRequired();
        // Make Zip and its label required/optional
        var zipUpdater = new ZipUpdater(this.countryEl.value, this.zipEl);
        zipUpdater.update();
    },

    setMarkDisplay: function(elem, display){
        elem = $(elem);
        var labelElement = elem.up(0).down('label > span.required') ||
                           elem.up(1).down('label > span.required') ||
                           elem.up(0).down('label.required > em') ||
                           elem.up(1).down('label.required > em');
        if(labelElement) {
            inputElement = labelElement.up().next('input');
            if (display) {
                labelElement.show();
                if (inputElement) {
                    inputElement.addClassName('required-entry');
                }
            } else {
                labelElement.hide();
                if (inputElement) {
                    inputElement.removeClassName('required-entry');
                }
            }
        }
    },
    sortSelect : function () {
        var elem = this.regionSelectEl;
        var tmpArray = new Array();
        var currentVal = $(elem).value;
        for (var i = 0; i < $(elem).options.length; i++) {
            if (i == 0) {
                continue;
            }
            tmpArray[i-1] = new Array();
            tmpArray[i-1][0] = $(elem).options[i].text;
            tmpArray[i-1][1] = $(elem).options[i].value;
        }
        tmpArray.sort();
        for (var i = 1; i <= tmpArray.length; i++) {
            var op = new Option(tmpArray[i-1][0], tmpArray[i-1][1]);
            $(elem).options[i] = op;
        }
        $(elem).value = currentVal;
        return;
    }
};

ZipUpdater = Class.create();
ZipUpdater.prototype = {
    initialize: function(country, zipElement)
    {
        this.country = country;
        this.zipElement = $(zipElement);
    },

    update: function()
    {
        // Country ISO 2-letter codes must be pre-defined
        if (typeof optionalZipCountries == 'undefined') {
            return false;
        }

        // Ajax-request and normal content load compatibility
        if (this.zipElement != undefined) {
            Validation.reset(this.zipElement);
            this._setPostcodeOptional();
        } else {
            Event.observe(window, "load", this._setPostcodeOptional.bind(this));
        }
    },

    _setPostcodeOptional: function()
    {
        this.zipElement = $(this.zipElement);
        if (this.zipElement == undefined) {
            return false;
        }

        // find label
        var label = $$('label[for="' + this.zipElement.id + '"]')[0];
        if (label != undefined) {
            var wildCard = label.down('em') || label.down('span.required');
        }

        // Make Zip and its label required/optional
        if (optionalZipCountries.indexOf(this.country) != -1) {
            while (this.zipElement.hasClassName('required-entry')) {
                this.zipElement.removeClassName('required-entry');
            }
            if (wildCard != undefined) {
                wildCard.hide();
            }
        } else {
            this.zipElement.addClassName('required-entry');
            if (wildCard != undefined) {
                wildCard.show();
            }
        }
    }
};



/* FILE: menu.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Varien
 * @package     js
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * @classDescription simple Navigation with replacing old handlers
 * @param {String} id id of ul element with navigation lists
 * @param {Object} settings object with settings
 */
var mainNav = function() {

    var main = {
        obj_nav :   $(arguments[0]) || $("nav"),

        settings :  {
            show_delay      :   0,
            hide_delay      :   0,
            _ie6            :   /MSIE 6.+Win/.test(navigator.userAgent),
            _ie7            :   /MSIE 7.+Win/.test(navigator.userAgent)
        },

        init :  function(obj, level) {
            obj.lists = obj.childElements();
            obj.lists.each(function(el,ind){
                main.handlNavElement(el);
                if((main.settings._ie6 || main.settings._ie7) && level){
                    main.ieFixZIndex(el, ind, obj.lists.size());
                }
            });
            if(main.settings._ie6 && !level){
                document.execCommand("BackgroundImageCache", false, true);
            }
        },

        handlNavElement :   function(list) {
            if(list !== undefined){
                list.onmouseover = function(){
                    main.fireNavEvent(this,true);
                };
                list.onmouseout = function(){
                    main.fireNavEvent(this,false);
                };
                if(list.down("ul")){
                    main.init(list.down("ul"), true);
                }
            }
        },

        ieFixZIndex : function(el, i, l) {
            if(el.tagName.toString().toLowerCase().indexOf("iframe") == -1){
                el.style.zIndex = l - i;
            } else {
                el.onmouseover = "null";
                el.onmouseout = "null";
            }
        },

        fireNavEvent :  function(elm,ev) {
            if(ev){
                elm.addClassName("over");
                elm.down("a").addClassName("over");
                if (elm.childElements()[1]) {
                    main.show(elm.childElements()[1]);
                }
            } else {
                elm.removeClassName("over");
                elm.down("a").removeClassName("over");
                if (elm.childElements()[1]) {
                    main.hide(elm.childElements()[1]);
                }
            }
        },

        show : function (sub_elm) {
            if (sub_elm.hide_time_id) {
                clearTimeout(sub_elm.hide_time_id);
            }
            sub_elm.show_time_id = setTimeout(function() {
                if (!sub_elm.hasClassName("shown-sub")) {
                    sub_elm.addClassName("shown-sub");
                }
            }, main.settings.show_delay);
        },

        hide : function (sub_elm) {
            if (sub_elm.show_time_id) {
                clearTimeout(sub_elm.show_time_id);
            }
            sub_elm.hide_time_id = setTimeout(function(){
                if (sub_elm.hasClassName("shown-sub")) {
                    sub_elm.removeClassName("shown-sub");
                }
            }, main.settings.hide_delay);
        }

    };
    if (arguments[1]) {
        main.settings = Object.extend(main.settings, arguments[1]);
    }
    if (main.obj_nav) {
        main.init(main.obj_nav, false);
    }
};

document.observe("dom:loaded", function() {
    //run navigation without delays and with default id="#nav"
    //mainNav();

    //run navigation with delays
    mainNav("nav", {"show_delay":"100","hide_delay":"100"});
});



/* FILE: translate.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     js
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

var Translate = Class.create();
Translate.prototype = {
    initialize: function(data){
        this.data = $H(data);
    },

    translate : function(){
        var args = arguments;
        var text = arguments[0];

        if(this.data.get(text)){
            return this.data.get(text);
        }
        return text;
    },
    add : function() {
        if (arguments.length > 1) {
            this.data.set(arguments[0], arguments[1]);
        } else if (typeof arguments[0] =='object') {
            $H(arguments[0]).each(function (pair){
                this.data.set(pair.key, pair.value);
            }.bind(this));
        }
    }
};



/* FILE: cookies.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     js
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
// old school cookie functions grabbed off the web

if (!window.Mage) var Mage = {};

Mage.Cookies = {};
Mage.Cookies.expires  = null;
Mage.Cookies.path     = '/';
Mage.Cookies.domain   = null;
Mage.Cookies.secure   = false;
Mage.Cookies.set = function(name, value){
     var argv = arguments;
     var argc = arguments.length;
     var expires = (argc > 2) ? argv[2] : Mage.Cookies.expires;
     var path = (argc > 3) ? argv[3] : Mage.Cookies.path;
     var domain = (argc > 4) ? argv[4] : Mage.Cookies.domain;
     var secure = (argc > 5) ? argv[5] : Mage.Cookies.secure;
     document.cookie = name + "=" + escape (value) +
       ((expires == null) ? "" : ("; expires=" + expires.toGMTString())) +
       ((path == null) ? "" : ("; path=" + path)) +
       ((domain == null) ? "" : ("; domain=" + domain)) +
       ((secure == true) ? "; secure" : "");
};

Mage.Cookies.get = function(name){
    var arg = name + "=";
    var alen = arg.length;
    var clen = document.cookie.length;
    var i = 0;
    var j = 0;
    while(i < clen){
        j = i + alen;
        if (document.cookie.substring(i, j) == arg)
            return Mage.Cookies.getCookieVal(j);
        i = document.cookie.indexOf(" ", i) + 1;
        if(i == 0)
            break;
    }
    return null;
};

Mage.Cookies.clear = function(name) {
  if(Mage.Cookies.get(name)){
    document.cookie = name + "=" +
    "; expires=Thu, 01-Jan-70 00:00:01 GMT";
  }
};

Mage.Cookies.getCookieVal = function(offset){
   var endstr = document.cookie.indexOf(";", offset);
   if(endstr == -1){
       endstr = document.cookie.length;
   }
   return unescape(document.cookie.substring(offset, endstr));
};



/* FILE: onsale.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
AwOnSale = Class.create();
AwOnSale.prototype = {
    initialize: function(){
        this.helements = new Array();
        this.velements = new Array();
        document.observe("dom:loaded", onPageLoad);
        Event.observe(window, 'load',onPageLoad);
    },
    setVertPos: function(element, position){
        var eH = element.offsetHeight;
        var pH = element.parentNode.offsetHeight;
        var eT = 0;

        if (eH != pH){
            switch (position){
                case 'top':
                    eT = 0;
                    break;
                case 'middle':
                    eT = (pH - eH)/2;
                    break;
                case 'bottom':
                    eT = pH - eH;
                    break;
                default:
                    eT = 0;
            }
        } else if (eH == pH) {
            eT = 0;
        }
        element.style.bottom = null;
        element.style.top = eT + 'px';

    },
    setHorPos: function(element, position){
        var eW = element.offsetWidth;
        var pW = element.parentNode.offsetWidth;
        var eL = 0;

        if (eW != pW){
            switch (position){
                case 'left':
                    eL = 0;
                    break;
                case 'center':
                    eL = (pW - eW)/2;
                    break;
                case 'right':
                    eL = pW - eW;
                    break;
                default:
                    eL = 0;
            }
        } else if (eW == pW) {
            eL = 0;
        }
        element.style.right = null;
        element.style.left = eL + 'px';

    },
    registerVertPosition: function(element, position) {
        element.vposition = position;
        this.velements.push(element);
    },
    registerHorPosition: function(element, position) {
        element.hposition = position;
        this.helements.push(element);
    }
}

var onPageLoad = function(){
    /* Set hor. position to labels */
    if (onsale.helements.length > 0){
        for (var i = 0; i < onsale.helements.length; i++){
            onsale.setHorPos(onsale.helements[i], onsale.helements[i].hposition);
        }
    }
    /* Set vert. position to labels */
    if (onsale.velements.length > 0){
        for (var j = 0; j < onsale.velements.length; j++){
            onsale.setVertPos(onsale.velements[j], onsale.velements[j].vposition);
        }
    }
}

function onsaleinit(id,hpos,vpos) {
    try{
    lcontainer = $('category-container-'+id);
    llabel = $('category-onsale-label-'+id);
    onsale.registerHorPosition(lcontainer, hpos);
    onsale.registerVertPosition(lcontainer, vpos);
    onsale.registerHorPosition(llabel, 'center');
    onsale.registerVertPosition(llabel, 'middle');
    onsale.setHorPos(lcontainer, lcontainer.hposition);
    onsale.setVertPos(lcontainer, lcontainer.vposition);
    onsale.setHorPos(llabel, llabel.hposition);
    onsale.setVertPos(llabel, llabel.vposition);
    }catch(e){}
}

if(typeof onsale=='undefined') {
    var onsale = new AwOnSale();
}


/* FILE: script.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
function sFbTrackAddToCart(data) {
    fbq('track', 'AddToCart', {
        content_ids: [data.ids],
        content_type: 'product',
        value: data.value,
        currency: data.currency,
        content_name: data.name,
    });
}

function sFbTrackAddToWishList(data) {
    fbq('track', 'AddToCart', {
        content_ids: [data.ids],
        value: data.value,
        currency: data.currency,
        content_name: data.name,
        content_category: data.categories
    });
}

function sFbTrackInitCheckout(data) {
    fbq('track', 'InitiateCheckout', {
        content_ids: data.ids,
        content_type: 'product',
        value: data.value,
        currency: data.currency,
        num_items: data.numItems
    });
}

function sFbTrackAddPaymentInfo() {
    fbq('track', 'AddPaymentInfo');
}

function sFbTrackNewsletterSub() {
    fbq('track', 'Lead');
}


/* FILE: modernizr.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
/* Modernizr 2.6.3 (Custom Build) | MIT & BSD
 * Build: http://modernizr.com/download/#-fontface-backgroundsize-borderimage-borderradius-boxshadow-flexbox-hsla-multiplebgs-opacity-rgba-textshadow-cssanimations-csscolumns-generatedcontent-cssgradients-cssreflections-csstransforms-csstransforms3d-csstransitions-applicationcache-canvas-canvastext-draganddrop-hashchange-history-audio-video-indexeddb-input-inputtypes-localstorage-postmessage-sessionstorage-websockets-websqldatabase-webworkers-geolocation-inlinesvg-smil-svg-svgclippaths-touch-webgl-shiv-cssclasses-addtest-prefixed-teststyles-testprop-testallprops-hasevent-prefixes-domprefixes-load
 */
;



window.Modernizr = (function( window, document, undefined ) {

    var version = '2.6.3',

    Modernizr = {},

    enableClasses = true,

    docElement = document.documentElement,

    mod = 'modernizr',
    modElem = document.createElement(mod),
    mStyle = modElem.style,

    inputElem  = document.createElement('input')  ,

    smile = ':)',

    toString = {}.toString,

    prefixes = ' -webkit- -moz- -o- -ms- '.split(' '),



    omPrefixes = 'Webkit Moz O ms',

    cssomPrefixes = omPrefixes.split(' '),

    domPrefixes = omPrefixes.toLowerCase().split(' '),

    ns = {'svg': 'http://www.w3.org/2000/svg'},

    tests = {},
    inputs = {},
    attrs = {},

    classes = [],

    slice = classes.slice,

    featureName, 


    injectElementWithStyles = function( rule, callback, nodes, testnames ) {

      var style, ret, node, docOverflow,
          div = document.createElement('div'),
                body = document.body,
                fakeBody = body || document.createElement('body');

      if ( parseInt(nodes, 10) ) {
                      while ( nodes-- ) {
              node = document.createElement('div');
              node.id = testnames ? testnames[nodes] : mod + (nodes + 1);
              div.appendChild(node);
          }
      }

                style = ['&#173;','<style id="s', mod, '">', rule, '</style>'].join('');
      div.id = mod;
          (body ? div : fakeBody).innerHTML += style;
      fakeBody.appendChild(div);
      if ( !body ) {
                fakeBody.style.background = '';
                fakeBody.style.overflow = 'hidden';
          docOverflow = docElement.style.overflow;
          docElement.style.overflow = 'hidden';
          docElement.appendChild(fakeBody);
      }

      ret = callback(div, rule);
        if ( !body ) {
          fakeBody.parentNode.removeChild(fakeBody);
          docElement.style.overflow = docOverflow;
      } else {
          div.parentNode.removeChild(div);
      }

      return !!ret;

    },



    isEventSupported = (function() {

      var TAGNAMES = {
        'select': 'input', 'change': 'input',
        'submit': 'form', 'reset': 'form',
        'error': 'img', 'load': 'img', 'abort': 'img'
      };

      function isEventSupported( eventName, element ) {

        element = element || document.createElement(TAGNAMES[eventName] || 'div');
        eventName = 'on' + eventName;

            var isSupported = eventName in element;

        if ( !isSupported ) {
                if ( !element.setAttribute ) {
            element = document.createElement('div');
          }
          if ( element.setAttribute && element.removeAttribute ) {
            element.setAttribute(eventName, '');
            isSupported = is(element[eventName], 'function');

                    if ( !is(element[eventName], 'undefined') ) {
              element[eventName] = undefined;
            }
            element.removeAttribute(eventName);
          }
        }

        element = null;
        return isSupported;
      }
      return isEventSupported;
    })(),


    _hasOwnProperty = ({}).hasOwnProperty, hasOwnProp;

    if ( !is(_hasOwnProperty, 'undefined') && !is(_hasOwnProperty.call, 'undefined') ) {
      hasOwnProp = function (object, property) {
        return _hasOwnProperty.call(object, property);
      };
    }
    else {
      hasOwnProp = function (object, property) { 
        return ((property in object) && is(object.constructor.prototype[property], 'undefined'));
      };
    }


    if (!Function.prototype.bind) {
      Function.prototype.bind = function bind(that) {

        var target = this;

        if (typeof target != "function") {
            throw new TypeError();
        }

        var args = slice.call(arguments, 1),
            bound = function () {

            if (this instanceof bound) {

              var F = function(){};
              F.prototype = target.prototype;
              var self = new F();

              var result = target.apply(
                  self,
                  args.concat(slice.call(arguments))
              );
              if (Object(result) === result) {
                  return result;
              }
              return self;

            } else {

              return target.apply(
                  that,
                  args.concat(slice.call(arguments))
              );

            }

        };

        return bound;
      };
    }

    function setCss( str ) {
        mStyle.cssText = str;
    }

    function setCssAll( str1, str2 ) {
        return setCss(prefixes.join(str1 + ';') + ( str2 || '' ));
    }

    function is( obj, type ) {
        return typeof obj === type;
    }

    function contains( str, substr ) {
        return !!~('' + str).indexOf(substr);
    }

    function testProps( props, prefixed ) {
        for ( var i in props ) {
            var prop = props[i];
            if ( !contains(prop, "-") && mStyle[prop] !== undefined ) {
                return prefixed == 'pfx' ? prop : true;
            }
        }
        return false;
    }

    function testDOMProps( props, obj, elem ) {
        for ( var i in props ) {
            var item = obj[props[i]];
            if ( item !== undefined) {

                            if (elem === false) return props[i];

                            if (is(item, 'function')){
                                return item.bind(elem || obj);
                }

                            return item;
            }
        }
        return false;
    }

    function testPropsAll( prop, prefixed, elem ) {

        var ucProp  = prop.charAt(0).toUpperCase() + prop.slice(1),
            props   = (prop + ' ' + cssomPrefixes.join(ucProp + ' ') + ucProp).split(' ');

            if(is(prefixed, "string") || is(prefixed, "undefined")) {
          return testProps(props, prefixed);

            } else {
          props = (prop + ' ' + (domPrefixes).join(ucProp + ' ') + ucProp).split(' ');
          return testDOMProps(props, prefixed, elem);
        }
    }    tests['flexbox'] = function() {
      return testPropsAll('flexWrap');
    };    tests['canvas'] = function() {
        var elem = document.createElement('canvas');
        return !!(elem.getContext && elem.getContext('2d'));
    };

    tests['canvastext'] = function() {
        return !!(Modernizr['canvas'] && is(document.createElement('canvas').getContext('2d').fillText, 'function'));
    };



    tests['webgl'] = function() {
        return !!window.WebGLRenderingContext;
    };


    tests['touch'] = function() {
        var bool;

        if(('ontouchstart' in window) || window.DocumentTouch && document instanceof DocumentTouch) {
          bool = true;
        } else {
          injectElementWithStyles(['@media (',prefixes.join('touch-enabled),('),mod,')','{#modernizr{top:9px;position:absolute}}'].join(''), function( node ) {
            bool = node.offsetTop === 9;
          });
        }

        return bool;
    };



    tests['geolocation'] = function() {
        return 'geolocation' in navigator;
    };


    tests['postmessage'] = function() {
      return !!window.postMessage;
    };


    tests['websqldatabase'] = function() {
      return !!window.openDatabase;
    };

    tests['indexedDB'] = function() {
      return !!testPropsAll("indexedDB", window);
    };

    tests['hashchange'] = function() {
      return isEventSupported('hashchange', window) && (document.documentMode === undefined || document.documentMode > 7);
    };

    tests['history'] = function() {
      return !!(window.history && history.pushState);
    };

    tests['draganddrop'] = function() {
        var div = document.createElement('div');
        return ('draggable' in div) || ('ondragstart' in div && 'ondrop' in div);
    };

    tests['websockets'] = function() {
        return 'WebSocket' in window || 'MozWebSocket' in window;
    };


    tests['rgba'] = function() {
        setCss('background-color:rgba(150,255,150,.5)');

        return contains(mStyle.backgroundColor, 'rgba');
    };

    tests['hsla'] = function() {
            setCss('background-color:hsla(120,40%,100%,.5)');

        return contains(mStyle.backgroundColor, 'rgba') || contains(mStyle.backgroundColor, 'hsla');
    };

    tests['multiplebgs'] = function() {
                setCss('background:url(https://),url(https://),red url(https://)');

            return (/(url\s*\(.*?){3}/).test(mStyle.background);
    };    tests['backgroundsize'] = function() {
        return testPropsAll('backgroundSize');
    };

    tests['borderimage'] = function() {
        return testPropsAll('borderImage');
    };



    tests['borderradius'] = function() {
        return testPropsAll('borderRadius');
    };

    tests['boxshadow'] = function() {
        return testPropsAll('boxShadow');
    };

    tests['textshadow'] = function() {
        return document.createElement('div').style.textShadow === '';
    };


    tests['opacity'] = function() {
                setCssAll('opacity:.55');

                    return (/^0.55$/).test(mStyle.opacity);
    };


    tests['cssanimations'] = function() {
        return testPropsAll('animationName');
    };


    tests['csscolumns'] = function() {
        return testPropsAll('columnCount');
    };


    tests['cssgradients'] = function() {
        var str1 = 'background-image:',
            str2 = 'gradient(linear,left top,right bottom,from(#9f9),to(white));',
            str3 = 'linear-gradient(left top,#9f9, white);';

        setCss(
                       (str1 + '-webkit- '.split(' ').join(str2 + str1) +
                       prefixes.join(str3 + str1)).slice(0, -str1.length)
        );

        return contains(mStyle.backgroundImage, 'gradient');
    };


    tests['cssreflections'] = function() {
        return testPropsAll('boxReflect');
    };


    tests['csstransforms'] = function() {
        return !!testPropsAll('transform');
    };


    tests['csstransforms3d'] = function() {

        var ret = !!testPropsAll('perspective');

                        if ( ret && 'webkitPerspective' in docElement.style ) {

                      injectElementWithStyles('@media (transform-3d),(-webkit-transform-3d){#modernizr{left:9px;position:absolute;height:3px;}}', function( node, rule ) {
            ret = node.offsetLeft === 9 && node.offsetHeight === 3;
          });
        }
        return ret;
    };


    tests['csstransitions'] = function() {
        return testPropsAll('transition');
    };



    tests['fontface'] = function() {
        var bool;

        injectElementWithStyles('@font-face {font-family:"font";src:url("https://")}', function( node, rule ) {
          var style = document.getElementById('smodernizr'),
              sheet = style.sheet || style.styleSheet,
              cssText = sheet ? (sheet.cssRules && sheet.cssRules[0] ? sheet.cssRules[0].cssText : sheet.cssText || '') : '';

          bool = /src/i.test(cssText) && cssText.indexOf(rule.split(' ')[0]) === 0;
        });

        return bool;
    };

    tests['generatedcontent'] = function() {
        var bool;

        injectElementWithStyles(['#',mod,'{font:0/0 a}#',mod,':after{content:"',smile,'";visibility:hidden;font:3px/1 a}'].join(''), function( node ) {
          bool = node.offsetHeight >= 3;
        });

        return bool;
    };
    tests['video'] = function() {
        var elem = document.createElement('video'),
            bool = false;

            try {
            if ( bool = !!elem.canPlayType ) {
                bool      = new Boolean(bool);
                bool.ogg  = elem.canPlayType('video/ogg; codecs="theora"')      .replace(/^no$/,'');

                            bool.h264 = elem.canPlayType('video/mp4; codecs="avc1.42E01E"') .replace(/^no$/,'');

                bool.webm = elem.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,'');
            }

        } catch(e) { }

        return bool;
    };

    tests['audio'] = function() {
        var elem = document.createElement('audio'),
            bool = false;

        try {
            if ( bool = !!elem.canPlayType ) {
                bool      = new Boolean(bool);
                bool.ogg  = elem.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,'');
                bool.mp3  = elem.canPlayType('audio/mpeg;')               .replace(/^no$/,'');

                                                    bool.wav  = elem.canPlayType('audio/wav; codecs="1"')     .replace(/^no$/,'');
                bool.m4a  = ( elem.canPlayType('audio/x-m4a;')            ||
                              elem.canPlayType('audio/aac;'))             .replace(/^no$/,'');
            }
        } catch(e) { }

        return bool;
    };


    tests['localstorage'] = function() {
        try {
            localStorage.setItem(mod, mod);
            localStorage.removeItem(mod);
            return true;
        } catch(e) {
            return false;
        }
    };

    tests['sessionstorage'] = function() {
        try {
            sessionStorage.setItem(mod, mod);
            sessionStorage.removeItem(mod);
            return true;
        } catch(e) {
            return false;
        }
    };


    tests['webworkers'] = function() {
        return !!window.Worker;
    };


    tests['applicationcache'] = function() {
        return !!window.applicationCache;
    };


    tests['svg'] = function() {
        return !!document.createElementNS && !!document.createElementNS(ns.svg, 'svg').createSVGRect;
    };

    tests['inlinesvg'] = function() {
      var div = document.createElement('div');
      div.innerHTML = '<svg/>';
      return (div.firstChild && div.firstChild.namespaceURI) == ns.svg;
    };

    tests['smil'] = function() {
        return !!document.createElementNS && /SVGAnimate/.test(toString.call(document.createElementNS(ns.svg, 'animate')));
    };


    tests['svgclippaths'] = function() {
        return !!document.createElementNS && /SVGClipPath/.test(toString.call(document.createElementNS(ns.svg, 'clipPath')));
    };

    function webforms() {
                                            Modernizr['input'] = (function( props ) {
            for ( var i = 0, len = props.length; i < len; i++ ) {
                attrs[ props[i] ] = !!(props[i] in inputElem);
            }
            if (attrs.list){
                                  attrs.list = !!(document.createElement('datalist') && window.HTMLDataListElement);
            }
            return attrs;
        })('autocomplete autofocus list placeholder max min multiple pattern required step'.split(' '));
                            Modernizr['inputtypes'] = (function(props) {

            for ( var i = 0, bool, inputElemType, defaultView, len = props.length; i < len; i++ ) {

                inputElem.setAttribute('type', inputElemType = props[i]);
                bool = inputElem.type !== 'text';

                                                    if ( bool ) {

                    inputElem.value         = smile;
                    inputElem.style.cssText = 'position:absolute;visibility:hidden;';

                    if ( /^range$/.test(inputElemType) && inputElem.style.WebkitAppearance !== undefined ) {

                      docElement.appendChild(inputElem);
                      defaultView = document.defaultView;

                                        bool =  defaultView.getComputedStyle &&
                              defaultView.getComputedStyle(inputElem, null).WebkitAppearance !== 'textfield' &&
                                                                                  (inputElem.offsetHeight !== 0);

                      docElement.removeChild(inputElem);

                    } else if ( /^(search|tel)$/.test(inputElemType) ){
                                                                                    } else if ( /^(url|email)$/.test(inputElemType) ) {
                                        bool = inputElem.checkValidity && inputElem.checkValidity() === false;

                    } else {
                                        bool = inputElem.value != smile;
                    }
                }

                inputs[ props[i] ] = !!bool;
            }
            return inputs;
        })('search tel url email datetime date month week time datetime-local number range color'.split(' '));
        }
    for ( var feature in tests ) {
        if ( hasOwnProp(tests, feature) ) {
                                    featureName  = feature.toLowerCase();
            Modernizr[featureName] = tests[feature]();

            classes.push((Modernizr[featureName] ? '' : 'no-') + featureName);
        }
    }

    Modernizr.input || webforms();


     Modernizr.addTest = function ( feature, test ) {
       if ( typeof feature == 'object' ) {
         for ( var key in feature ) {
           if ( hasOwnProp( feature, key ) ) {
             Modernizr.addTest( key, feature[ key ] );
           }
         }
       } else {

         feature = feature.toLowerCase();

         if ( Modernizr[feature] !== undefined ) {
                                              return Modernizr;
         }

         test = typeof test == 'function' ? test() : test;

         if (typeof enableClasses !== "undefined" && enableClasses) {
           docElement.className += ' ' + (test ? '' : 'no-') + feature;
         }
         Modernizr[feature] = test;

       }

       return Modernizr; 
     };


    setCss('');
    modElem = inputElem = null;

    ;(function(window, document) {
        var options = window.html5 || {};

        var reSkip = /^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i;

        var saveClones = /^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i;

        var supportsHtml5Styles;

        var expando = '_html5shiv';

        var expanID = 0;

        var expandoData = {};

        var supportsUnknownElements;

      (function() {
        try {
            var a = document.createElement('a');
            a.innerHTML = '<xyz></xyz>';
                    supportsHtml5Styles = ('hidden' in a);

            supportsUnknownElements = a.childNodes.length == 1 || (function() {
                        (document.createElement)('a');
              var frag = document.createDocumentFragment();
              return (
                typeof frag.cloneNode == 'undefined' ||
                typeof frag.createDocumentFragment == 'undefined' ||
                typeof frag.createElement == 'undefined'
              );
            }());
        } catch(e) {
          supportsHtml5Styles = true;
          supportsUnknownElements = true;
        }

      }());        function addStyleSheet(ownerDocument, cssText) {
        var p = ownerDocument.createElement('p'),
            parent = ownerDocument.getElementsByTagName('head')[0] || ownerDocument.documentElement;

        p.innerHTML = 'x<style>' + cssText + '</style>';
        return parent.insertBefore(p.lastChild, parent.firstChild);
      }

        function getElements() {
        var elements = html5.elements;
        return typeof elements == 'string' ? elements.split(' ') : elements;
      }

          function getExpandoData(ownerDocument) {
        var data = expandoData[ownerDocument[expando]];
        if (!data) {
            data = {};
            expanID++;
            ownerDocument[expando] = expanID;
            expandoData[expanID] = data;
        }
        return data;
      }

        function createElement(nodeName, ownerDocument, data){
        if (!ownerDocument) {
            ownerDocument = document;
        }
        if(supportsUnknownElements){
            return ownerDocument.createElement(nodeName);
        }
        if (!data) {
            data = getExpandoData(ownerDocument);
        }
        var node;

        if (data.cache[nodeName]) {
            node = data.cache[nodeName].cloneNode();
        } else if (saveClones.test(nodeName)) {
            node = (data.cache[nodeName] = data.createElem(nodeName)).cloneNode();
        } else {
            node = data.createElem(nodeName);
        }

                                    return node.canHaveChildren && !reSkip.test(nodeName) ? data.frag.appendChild(node) : node;
      }

        function createDocumentFragment(ownerDocument, data){
        if (!ownerDocument) {
            ownerDocument = document;
        }
        if(supportsUnknownElements){
            return ownerDocument.createDocumentFragment();
        }
        data = data || getExpandoData(ownerDocument);
        var clone = data.frag.cloneNode(),
            i = 0,
            elems = getElements(),
            l = elems.length;
        for(;i<l;i++){
            clone.createElement(elems[i]);
        }
        return clone;
      }

        function shivMethods(ownerDocument, data) {
        if (!data.cache) {
            data.cache = {};
            data.createElem = ownerDocument.createElement;
            data.createFrag = ownerDocument.createDocumentFragment;
            data.frag = data.createFrag();
        }


        ownerDocument.createElement = function(nodeName) {
                if (!html5.shivMethods) {
              return data.createElem(nodeName);
          }
          return createElement(nodeName, ownerDocument, data);
        };

        ownerDocument.createDocumentFragment = Function('h,f', 'return function(){' +
          'var n=f.cloneNode(),c=n.createElement;' +
          'h.shivMethods&&(' +
                    getElements().join().replace(/\w+/g, function(nodeName) {
              data.createElem(nodeName);
              data.frag.createElement(nodeName);
              return 'c("' + nodeName + '")';
            }) +
          ');return n}'
        )(html5, data.frag);
      }        function shivDocument(ownerDocument) {
        if (!ownerDocument) {
            ownerDocument = document;
        }
        var data = getExpandoData(ownerDocument);

        if (html5.shivCSS && !supportsHtml5Styles && !data.hasCSS) {
          data.hasCSS = !!addStyleSheet(ownerDocument,
                    'article,aside,figcaption,figure,footer,header,hgroup,nav,section{display:block}' +
                    'mark{background:#FF0;color:#000}'
          );
        }
        if (!supportsUnknownElements) {
          shivMethods(ownerDocument, data);
        }
        return ownerDocument;
      }        var html5 = {

            'elements': options.elements || 'abbr article aside audio bdi canvas data datalist details figcaption figure footer header hgroup mark meter nav output progress section summary time video',

            'shivCSS': (options.shivCSS !== false),

            'supportsUnknownElements': supportsUnknownElements,

            'shivMethods': (options.shivMethods !== false),

            'type': 'default',

            'shivDocument': shivDocument,

            createElement: createElement,

            createDocumentFragment: createDocumentFragment
      };        window.html5 = html5;

        shivDocument(document);

    }(this, document));

    Modernizr._version      = version;

    Modernizr._prefixes     = prefixes;
    Modernizr._domPrefixes  = domPrefixes;
    Modernizr._cssomPrefixes  = cssomPrefixes;


    Modernizr.hasEvent      = isEventSupported;

    Modernizr.testProp      = function(prop){
        return testProps([prop]);
    };

    Modernizr.testAllProps  = testPropsAll;


    Modernizr.testStyles    = injectElementWithStyles;
    Modernizr.prefixed      = function(prop, obj, elem){
      if(!obj) {
        return testPropsAll(prop, 'pfx');
      } else {
            return testPropsAll(prop, obj, elem);
      }
    };


    docElement.className = docElement.className.replace(/(^|\s)no-js(\s|$)/, '$1$2') +

                                                    (enableClasses ? ' js ' + classes.join(' ') : '');

    return Modernizr;

})(this, this.document);
/*yepnope1.5.4|WTFPL*/
(function(a,b,c){function d(a){return"[object Function]"==o.call(a)}function e(a){return"string"==typeof a}function f(){}function g(a){return!a||"loaded"==a||"complete"==a||"uninitialized"==a}function h(){var a=p.shift();q=1,a?a.t?m(function(){("c"==a.t?B.injectCss:B.injectJs)(a.s,0,a.a,a.x,a.e,1)},0):(a(),h()):q=0}function i(a,c,d,e,f,i,j){function k(b){if(!o&&g(l.readyState)&&(u.r=o=1,!q&&h(),l.onload=l.onreadystatechange=null,b)){"img"!=a&&m(function(){t.removeChild(l)},50);for(var d in y[c])y[c].hasOwnProperty(d)&&y[c][d].onload()}}var j=j||B.errorTimeout,l=b.createElement(a),o=0,r=0,u={t:d,s:c,e:f,a:i,x:j};1===y[c]&&(r=1,y[c]=[]),"object"==a?l.data=c:(l.src=c,l.type=a),l.width=l.height="0",l.onerror=l.onload=l.onreadystatechange=function(){k.call(this,r)},p.splice(e,0,u),"img"!=a&&(r||2===y[c]?(t.insertBefore(l,s?null:n),m(k,j)):y[c].push(l))}function j(a,b,c,d,f){return q=0,b=b||"j",e(a)?i("c"==b?v:u,a,b,this.i++,c,d,f):(p.splice(this.i++,0,a),1==p.length&&h()),this}function k(){var a=B;return a.loader={load:j,i:0},a}var l=b.documentElement,m=a.setTimeout,n=b.getElementsByTagName("script")[0],o={}.toString,p=[],q=0,r="MozAppearance"in l.style,s=r&&!!b.createRange().compareNode,t=s?l:n.parentNode,l=a.opera&&"[object Opera]"==o.call(a.opera),l=!!b.attachEvent&&!l,u=r?"object":l?"script":"img",v=l?"script":u,w=Array.isArray||function(a){return"[object Array]"==o.call(a)},x=[],y={},z={timeout:function(a,b){return b.length&&(a.timeout=b[0]),a}},A,B;B=function(a){function b(a){var a=a.split("!"),b=x.length,c=a.pop(),d=a.length,c={url:c,origUrl:c,prefixes:a},e,f,g;for(f=0;f<d;f++)g=a[f].split("="),(e=z[g.shift()])&&(c=e(c,g));for(f=0;f<b;f++)c=x[f](c);return c}function g(a,e,f,g,h){var i=b(a),j=i.autoCallback;i.url.split(".").pop().split("?").shift(),i.bypass||(e&&(e=d(e)?e:e[a]||e[g]||e[a.split("/").pop().split("?")[0]]),i.instead?i.instead(a,e,f,g,h):(y[i.url]?i.noexec=!0:y[i.url]=1,f.load(i.url,i.forceCSS||!i.forceJS&&"css"==i.url.split(".").pop().split("?").shift()?"c":c,i.noexec,i.attrs,i.timeout),(d(e)||d(j))&&f.load(function(){k(),e&&e(i.origUrl,h,g),j&&j(i.origUrl,h,g),y[i.url]=2})))}function h(a,b){function c(a,c){if(a){if(e(a))c||(j=function(){var a=[].slice.call(arguments);k.apply(this,a),l()}),g(a,j,b,0,h);else if(Object(a)===a)for(n in m=function(){var b=0,c;for(c in a)a.hasOwnProperty(c)&&b++;return b}(),a)a.hasOwnProperty(n)&&(!c&&!--m&&(d(j)?j=function(){var a=[].slice.call(arguments);k.apply(this,a),l()}:j[n]=function(a){return function(){var b=[].slice.call(arguments);a&&a.apply(this,b),l()}}(k[n])),g(a[n],j,b,n,h))}else!c&&l()}var h=!!a.test,i=a.load||a.both,j=a.callback||f,k=j,l=a.complete||f,m,n;c(h?a.yep:a.nope,!!i),i&&c(i)}var i,j,l=this.yepnope.loader;if(e(a))g(a,0,l,0);else if(w(a))for(i=0;i<a.length;i++)j=a[i],e(j)?g(j,0,l,0):w(j)?B(j):Object(j)===j&&h(j,l);else Object(a)===a&&h(a,l)},B.addPrefix=function(a,b){z[a]=b},B.addFilter=function(a){x.push(a)},B.errorTimeout=1e4,null==b.readyState&&b.addEventListener&&(b.readyState="loading",b.addEventListener("DOMContentLoaded",A=function(){b.removeEventListener("DOMContentLoaded",A,0),b.readyState="complete"},0)),a.yepnope=k(),a.yepnope.executeStack=h,a.yepnope.injectJs=function(a,c,d,e,i,j){var k=b.createElement("script"),l,o,e=e||B.errorTimeout;k.src=a;for(o in d)k.setAttribute(o,d[o]);c=j?h:c||f,k.onreadystatechange=k.onload=function(){!l&&g(k.readyState)&&(l=1,c(),k.onload=k.onreadystatechange=null)},m(function(){l||(l=1,c(1))},e),i?k.onload():n.parentNode.insertBefore(k,n)},a.yepnope.injectCss=function(a,c,d,e,g,i){var e=b.createElement("link"),j,c=i?h:c||f;e.href=a,e.rel="stylesheet",e.type="text/css";for(j in d)e.setAttribute(j,d[j]);g||(n.parentNode.insertBefore(e,n),m(c,0))}})(this,document);
Modernizr.load=function(){yepnope.apply(window,[].slice.call(arguments,0));};
;


/* FILE: bubblelayer.js */
/* HANDLES: default,STORE_default,THEME_frontend_stenik_default,catalog_category_view,catalog_category_layered,CATEGORY_9,customer_logged_out,MAP_popup,SHORTCUT_popup,SHORTCUT_uk_popup */
var BubbleLayer = Class.create();
BubbleLayer.prototype = {
    initialize: function(options) {
        this.options = Object.extend({
            topSelector:        '.bubble-layer-top',
            leftSelector:       '.col-left',
            rightSelector:      '.col-right',
            contentSelector:    '.category-products',
            scrollSelector:     '.col-left',
            enableAjax:         true,
            enableAutoScroll:   true,
            enableAjaxToolbar:  true,
            enableHistory:      true,
            onUpdateComplete:   function() {}
        }, options || {});

        this.loading = false;

        if (typeof(History) === 'undefined' || !this.options.enableAjax) {
            this.options.enableHistory = false;
        }

        var o = this.options;

        this.handlePriceSliders();

        if (o.enableHistory && this.getLeftElement() && this.getContentElement()) {
            document.observe('dom:loaded', function() {
                var topEl = this.getTopElement();
                var leftEl = this.getLeftElement();
                var rightEl = this.getRightElement();
                var contentEl = this.getContentElement();
                this.saveState(
                    document.location.href,
                    topEl ? topEl.innerHTML : '',
                    leftEl ? leftEl.innerHTML : '',
                    rightEl ? rightEl.innerHTML : '',
                    contentEl ? contentEl.innerHTML : '',
                    document.title
                );
            }.bind(this));
        }

        document.observe('click', function(e) {
            var el = e.element();
            if (el.hasClassName('show-hidden')) {
                e.stop();
                el.up(1).select('li.hideable').invoke('toggleClassName', 'no-display');
                el.up(1).select('.show-hidden').invoke('toggleClassName', 'no-display');
            } else if (e.findElement('.block-layered-nav a') ||
                       e.findElement('.checkbox-filter') ||
                       e.findElement('.toolbar a') && o.enableAjaxToolbar)
            {
                var url;
                if (el.tagName == 'INPUT') {
                    url = el.value;
                } else if (!el.href && el.up('a')) {
                    el = el.up('a');
                }
                if (!url && el.href) {
                    url = el.href;
                    e.stop();
                    if (el.up('li')) {
                        var input = el.up('li').down('input');
                        if (input) {
                            input.checked = !input.checked;
                        }
                    }
                }
                if (url) {
                    this.handleLayer(url);
                }
            }
        }.bind(this));

        document.observe('change', function(e) {
            var el = e.element();
            if (el.hasClassName('dropdown-filter')) {
                var url = el.getValue();
                if (url) {
                    this.handleLayer(url);
                }
            }
        }.bind(this));

        if (o.enableHistory) {
            History.Adapter.bind(window, 'statechange', function() {
                var state   = History.getState();
                var data    = state.data;
                this.updateContent(data.top, data.left, data.right, data.content);
                this.handlePriceSliders();
            }.bind(this));
        }

        Event.observe(window, 'resize', function() {
            this.handlePriceSliders();
        }.bind(this));

        Event.observe(window, 'load', function() {
            $$('.block-layered-nav dt').invoke('observe', 'click', function () {
                this.handlePriceSliders();
            }.bind(this));
        }.bind(this));
    },
    getTopElement: function() {
        return $$(this.options.topSelector).shift();
    },
    getLeftElement: function() {
        return $$(this.options.leftSelector).shift();
    },
    getRightElement: function() {
        return $$(this.options.rightSelector).shift();
    },
    getContentElement: function() {
        return $$(this.options.contentSelector).shift();
    },
    getScrollElement: function() {
        return $$(this.options.scrollSelector).shift();
    },
    setCurrentUrl: function(url) {
        this.options.currentUrl = url;
    },
    saveState: function(url, top, left, right, content, title) {
        if (this.options.enableHistory) {
            var state = {
                top: top,
                left: left,
                right: right,
                content: content
            };
            History.pushState(state, title, url);
        }
    },
    scroll: function() {
        var scrollEl = this.getScrollElement();
        if (this.options.enableAutoScroll && scrollEl) {
            new Effect.ScrollTo(scrollEl);
        }
    },
    updateContent: function(top, left, right, content) {
        var topEl = this.getTopElement();
        var leftEl = this.getLeftElement();
        var rightEl = this.getRightElement();
        var contentEl = this.getContentElement();
        if (topEl) {
            topEl.update(top);
        }
        if (leftEl) {
            leftEl.update(left);
        }
        if (rightEl) {
            rightEl.update(right);
        }
        if (contentEl) {
            contentEl.update(content);
        }
    },
    handleLayer: function(url) {
        if (document.location.href == url) {
            return;
        }
        if (!this.options.enableAjax) {
            setLocation(url);
        } else {
            if (this.loading) {
                return;
            }
            new Ajax.Request(url, {
                method: 'get',
                onCreate: function() {
                    this.loading = true;
                    if ($('bubble-layer-overlay')) {
                        $('bubble-layer-overlay').show();
                    }
                }.bind(this),
                onSuccess: function(response) {
                    var result  = response.responseJSON;
                    if (result.content) {
                        this.updateContent(result.top, result.left, result.right, result.content);
                        this.saveState(url, result.top, result.left, result.right, result.content, result.title);
                        this.handlePriceSliders();
                        this.scroll();
                    }
                    this.setCurrentUrl(url);
                }.bind(this),
                onComplete: function(response) {
                    if ($('bubble-layer-overlay')) {
                        $('bubble-layer-overlay').hide();
                    }
                    this.loading = false;
                    this.options.onUpdateComplete.call(this);
                }.bind(this),
                onFailure: function() {
                    setLocation(url);
                }
            });
        }
    },
    handlePriceSliders: function() {
        $$('.layer-slider').each(function(slider) {
            this.handlePriceSlider(slider);
        }.bind(this));
    },
    handlePriceSlider: function(slider) {
        if (slider) {
            var self = this;
            var handles = slider.select('.handle');
            var requestVar = slider.select('.request-var')[0].value;
            var priceMin = parseInt(slider.select('.price-min')[0].value);
            var priceMax = parseInt(slider.select('.price-max')[0].value);
            var priceValueMin = parseInt(slider.select('.price-value-min')[0].value);
            var priceValueMax = parseInt(slider.select('.price-value-max')[0].value);

            if (typeof(BubblePriceSlider) !== 'undefined') {
                BubblePriceSlider.dispose();
            }

            var BubblePriceSlider = new Control.Slider(handles, slider.select('.price-slider')[0], {
                range: $R(priceMin, priceMax),
                sliderValue: [priceValueMin, priceValueMax],
                restricted: true,
                spans: slider.select('.span'),
                onSlide: function(v) {
                    if (!isNaN(v[0])) {
                        slider.select('.price-range span.price')[0].update(formatCurrency(v[0], self.options.priceFormat, false));
                    }
                    if (!isNaN(v[1])) {
                        slider.select('.price-range span.price')[1].update(formatCurrency(v[1], self.options.priceFormat, false));
                    }
                },
                onChange: function(v) {
                    var min = v[0].toFixed();
                    if (min == 0 || isNaN(min) || min <= this.range.start) {
                        min = '';
                    }
                    var max = v[1].toFixed();
                    if (max >= this.range.end || isNaN(max)) {
                        max = '';
                    }

                    //self.handleLayer(self.buildPriceUrl(requestVar, min, max));
                    slider.select('.filterButton')[0].href = self.buildPriceUrl(requestVar, min, max);
                }
            });

            slider.select('.filterButton')[0].onclick = self.handleLayer(slider.select('.filterButton')[0].href);

            var priceInputs = slider.select('.price-range input.input-text');
            priceInputs.each(function(input) {
                input.observe('keyup', function(event) {
                    if (event.keyCode == Event.KEY_RETURN) {
                        // Handle mininmum price input
                        var min = priceValueMin;
                        if (typeof(priceInputs[0]) !== 'undefined' && priceInputs[0].value != '') {
                            min = parseInt(priceInputs[0].value);
                            if (min == 0 || isNaN(min) || min <= BubblePriceSlider.range.start) {
                                min = priceMin;
                                if (input == priceInputs[0] && min === priceValueMin) {
                                    new Effect.Pulsate(priceInputs[0], { pulses: 3, duration: 0.5 });
                                }
                            }
                        }
                        // Handle maximum price input
                        var max = priceValueMax;
                        if (typeof(priceInputs[1]) !== 'undefined' && priceInputs[1].value != '') {
                            max = parseInt(priceInputs[1].value);
                            if (max >= BubblePriceSlider.range.end || isNaN(max)) {
                                max = priceMax;
                                if (input == priceInputs[1] && max === priceValueMax) {
                                    new Effect.Pulsate(priceInputs[1], { pulses: 3, duration: 0.5 });
                                }
                            }
                        }
                        if (min !== priceValueMin || max !== priceValueMax) {
                            self.handleLayer(self.buildPriceUrl(requestVar, min, max));
                        }
                    }
                });
            });

            // Handle mouseover on slider to modify the closest handle on click
            slider.observe('mousemove', function(event) {
                var posHandleL = handles[0].cumulativeOffset().left;
                var posHandleR = handles[1].cumulativeOffset().left;
                var posPointer = Event.pointerX(event);
                if (posPointer > posHandleR || posPointer > ((posHandleR + posHandleL) / 2)) {
                    BubblePriceSlider.activeHandle = handles[1];
                    BubblePriceSlider.activeHandleIdx = 1;
                } else {
                    BubblePriceSlider.activeHandle = handles[0];
                    BubblePriceSlider.activeHandleIdx = 0;
                }
                BubblePriceSlider.updateStyles();
            });
        }
    },
    buildPriceUrl: function(requestVar, min, max) {
        min = parseInt(min);
        max = parseInt(max);
        if (!isNaN(min) && !isNaN(max) && max < min) {
            var badMin = min;
            min = max;
            max = badMin;
        }
        if (isNaN(min)) {
            min = '';
        }
        if (isNaN(max)) {
            max = '';
        }
        var param = requestVar + '=' + min + '-' + max;
        var pattern = '(&|\\?)' + requestVar + '=\\d*-\\d*';
        var url = this.options.currentUrl;
        if (!min && !max) {
            url = url.replace(new RegExp(pattern), '');
        } else if (url.match(new RegExp(pattern))) {
            url = url.replace(new RegExp(pattern), '$1' + param);
        } else {
            url += url.match(new RegExp('\\?')) ? '&' : '?';
            url += param;
        }
        url = url.replace(new RegExp('(&|\\?)p=\\d+(&|\\?)'), '$1'); // Remove page filter

        return url.replace(/[&\?]+$/g, '');
    },
    adaptHeight: function(selector) {
        var maxHeight = 0;
        var top = null;
        var offsets = {};
        var items = [];
        $$(selector).each(function(item, i) {
            item.setStyle('height', 'auto');
            offsets = item.getBoundingClientRect();
            if (top !== offsets.top) {
                this.setElementsHeight(items, maxHeight);
                items = [];
                maxHeight = 0;
            }
            if (item.offsetHeight > maxHeight) {
                maxHeight = item.offsetHeight;
            }
            items.push(item);
            top = offsets.top;
            this.setElementsHeight(items, maxHeight);
        }.bind(this));
    },
    setElementsHeight: function(elements, height) {
        if (height) {
            elements.each(function(el) {
                el.style.height = height + 'px';
            });
        }
    }
}
