# PFG Core Module v1.1.0

**Automated Module Management for Magento 1.9 through Bitbucket Integration**

## Overview

PFG Core is a comprehensive module management system that provides automated installation, updating, and rollback capabilities for PFG modules through Bitbucket API integration. This module serves as the foundation for the PFG Labs project workspace, enabling seamless deployment and management of custom modules.

## Features

### 🚀 **Automated Module Management**
- **Repository Discovery**: Automatically discovers available modules from Bitbucket workspace
- **One-Click Installation**: Install modules directly from the admin interface
- **Version Management**: Track installed versions and detect available updates
- **Dependency Checking**: Validates module dependencies before installation

### 🔒 **Security & Safety**
- **Pre-Installation Validation**: Comprehensive security checks before module installation
- **Automatic Backups**: Creates backups before any module changes
- **Rollback Functionality**: Complete rollback capability with safety confirmations
- **CSRF Protection**: All admin actions protected with form key validation
- **Encrypted Storage**: Sensitive API credentials stored with encryption

### 📊 **Monitoring & Logging**
- **Installation History**: Complete audit trail of all module operations
- **System Logs**: Comprehensive logging with configurable verbosity levels
- **Real-time Status**: Live connection status and API health monitoring
- **Error Handling**: Standardized error handling with user-friendly messages

### 🎛️ **Admin Interface**
- **Intuitive Configuration**: Clean, responsive admin configuration interface
- **Repository Management**: Visual repository browser with installation status
- **Test Connections**: Built-in API connection testing
- **AJAX Functionality**: Smooth user experience with AJAX-powered updates

## Installation

### Requirements
- Magento 1.9.x
- PHP 5.6+ (recommended: PHP 7.4)
- MySQL 5.6+
- Bitbucket account with API access
- Valid Bitbucket App Password with repository read permissions

### Installation Steps

1. **Upload Files**: Copy all module files to your Magento installation:
   ```
   app/code/local/PFG/Core/
   app/design/adminhtml/default/default/template/pfg_core/
   app/design/adminhtml/default/default/layout/pfg_core.xml
   app/etc/modules/PFG_Core.xml
   app/locale/en_US/PFG_Core.csv
   skin/adminhtml/default/default/pfg_core.css
   js/pfg/
   ```

2. **Clear Cache**: Clear Magento cache and refresh admin session
   ```bash
   rm -rf var/cache/*
   ```

3. **Configure Module**: Navigate to `System > Configuration > PFG > Core Module Management`

4. **API Setup**: Configure Bitbucket API credentials:
   - Username: Your Bitbucket username
   - App Password: Generate from Bitbucket account settings
   - Workspace: `pfg` (default)
   - Project Key: `LABS` (default)

5. **Test Connection**: Use the "Test Connection" button to verify API access

## Configuration

### Basic Settings
- **Enable PFG Core**: Enable/disable the module functionality
- **Bitbucket Credentials**: API authentication settings
- **Workspace & Project**: Repository location settings

### Advanced Settings
- **API Timeout**: Request timeout in seconds (default: 30)
- **Backup Retention**: Days to keep backup files (default: 30)
- **Log Level**: Logging verbosity (Emergency to Debug)

## Usage

### Repository Management
1. Navigate to `System > Configuration > PFG > Core Module Management`
2. Click on "Repository Management" tab
3. Browse available modules with real-time status
4. Install, update, or manage modules with one-click actions

### Installation History
- View complete audit trail of all module operations
- Filter by date range, module, or operation type
- Export history for compliance and reporting

### System Logs
- Monitor module operations in real-time
- Configure log levels for different environments
- Automatic log rotation and cleanup

## Security

### API Security
- Encrypted storage of Bitbucket credentials
- Secure API communication with timeout protection
- Rate limiting and error handling

### Installation Security
- Pre-installation security validation
- Directory traversal protection
- Module namespace verification
- Suspicious pattern detection

### Admin Security
- CSRF protection on all admin actions
- Role-based access control integration
- Secure session handling

## Troubleshooting

### Common Issues

**Connection Failed**
- Verify Bitbucket credentials
- Check network connectivity
- Ensure App Password has correct permissions

**Installation Failed**
- Check file permissions
- Verify available disk space
- Review error logs in `var/log/pfg_core.log`

**Module Not Detected**
- Ensure module follows PFG naming conventions
- Verify repository is in correct workspace/project
- Check module configuration files

### Log Files
- **Main Log**: `var/log/pfg_core.log`
- **Installation Log**: Database table `pfg_core_installation`
- **Backup Log**: Database table `pfg_core_backup`

## Development

### Module Structure
```
PFG/Core/
├── Block/                 # Admin interface blocks
├── controllers/           # Admin controllers
├── Exception/            # Custom exception classes
├── Helper/               # Helper classes
├── Model/                # Core business logic
├── etc/                  # Configuration files
└── sql/                  # Database setup scripts
```

### Extending the Module
- Follow Magento 1.9 coding standards
- Use dependency injection pattern
- Implement proper error handling
- Add comprehensive logging

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for detailed version history.

## License

Open Software License (OSL 3.0) - see LICENSE file for details.

## Support

For technical support and bug reports, please contact the PFG Development Team.

---

**PFG Core Module v1.1.0** - Production Ready  
© 2025 PFG Development Team
